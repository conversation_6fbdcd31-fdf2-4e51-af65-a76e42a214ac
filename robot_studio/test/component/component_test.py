"""
ComponentService测试文件
"""

import unittest
import uuid

from robot_studio.component.api import ComponentService
from robot_studio.component.api.request import ComponentRequest
from robot_studio.component.manage.model import ComponentType, ConfigParam, ProviderType, RuntimeParam, ComponentBase

AGENT_PROMPT = """## 角色
你扮演一位教育培训学校（壹同未来）的售前客服专家（小一老师），具备高效解决家长咨询、引导家长参加学校课程试听、入学测评和课程报名等职能。

## 背景
1. 术语说明：创新思维=数学、人文素养=语文、国际表达=英语、物质理论=物理、科学探究=化学。
2. 不同科目，针对不同年级学生需求，设计了不同班型，各班型难易程度、授课重点不同，可查看具体科目的课程体系介绍。
3. 教育培训有四个期段，分别是暑假班、寒假班，春季学期班，秋季学期班。假期班侧重预习下学期内容，学期班和校内授课互补，进度略快，侧重应用和拓展
4. 具体年级、科目、期段、班型下，会开设多个班级。如初一创新思维2025暑中考班，会开设多期（即多个班级）。这些班级授课内容和费用相同，但上课时间、地点（校区）、老师可能不同，主要是为了给家长更多时间、地点上的灵活选择空间
5. 我们开设在青岛市，共有7各校区，分别是阳光校区、浮山后校区、延安三路一校区、延安三路二校区、南京路校区、新都心和达校区和中山路校区！	
注意：目前正是2025年暑假班和秋季班的报名咨询阶段

## 技能
### 技能1：家长需求识别与细化
- **需求识别**：接收到家长咨询时，迅速理解核心需求。家长的需求通常分为几类：
  1. 课程咨询：了解所在年级某科目的课程内容、课程时间、班型划分等。
  2. 教师咨询：了解具体授课教师情况。
  3. 校区咨询：了解具体上课的地点，大多选择距离自己较近校区
  4. 试听/评测咨询：如试听课程或者入学评测的具体时间、地点等。
- **需求细化**：家长初次咨询的问题往往比较模糊，需要通过进一步询问来明确具体情况和真实诉求。

### 技能2：专业话术回复与引导
- **专业话术**：回复力求精简且口语化，模拟日常聊天语气，如有图片或海报链接，尽可能回复图片，如"这是课程班，您先看下"，然后渲染具体图片。
- **引导报名**：识别用户需求后，除了回答咨询外，还应尽可能引导用户参加试听和测评。报名流程是进行入学测评后按成绩分班。
- **多段回复**：可以给用户发送多条消息，采用渐进式提问或回答的方式引导用户。避免将所有信息糅合在一段很长的内容中。多段回复请使用'\n'换行符分隔，如果有图片链接等内容，请一定单独换行！。

## 注意
1. 图片链接是使用了OSS的URL，请你不要转义，否则链接会异常！请你直接渲染图片
2. 如果你打算询问用户，请不要返回调用工具的指令！明确需要的信息后再进行工具调用！

"""

KNOWLEDGE_PROMPT = """这是目前已有的知识信息，使用了结构化的JSON数组
```json
${knowledge_info}
```
"""


class TestComponentService(unittest.TestCase):
    """ComponentService测试类"""

    def setUp(self):
        """测试前准备"""
        self.component_service = ComponentService()

    def test_create_llm_template(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = [ConfigParam(name='model', desc='模型名', type='string', is_required=True),
                         ConfigParam(name='base_url', desc='模型调用url', type='string', is_required=True),
                         ConfigParam(name='api_key', desc='调用凭证', type='string', is_required=True),
                         ConfigParam(name='model_info', desc='模型信息，Json对象', type='json_object', is_required=True),
                         ]

        component_req = ComponentRequest(
            cid="TEST_CID_" + str(uuid.uuid4())[:8],
            code="llm_openai_template",
            name="LLM组件模版",
            type=ComponentType.TEMPLATE.value,
            desc="openai协议的大模型组件模版",
            scene="通用",
            tags=["模型", "OPENAI"],
            depend_provider="autogen_ext.models.openai.OpenAIChatCompletionClient",
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=True,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_llm_cmp(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = [ConfigParam(name='model', value='qwen-max-latest'),
                         ConfigParam(name='base_url', value='https://dashscope.aliyuncs.com/compatible-mode/v1'),
                         ConfigParam(name='api_key', value='sk-ec2073251be949c8b47015e4e9cec61a'),
                         ConfigParam(name='model_info', value={
                             "family": "Qwen",
                             "function_calling": True,
                             "json_output": True,
                             "vision": False,
                             "structured_output": False
                         }),
                         ]

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="qwen3-max-latest",
            name="qwen3-max",
            type=ComponentType.MODEL.value,
            desc="qwen3-max大模型组件",
            scene="通用",
            tags=["Qwen3", "OPENAI"],
            depend_provider="CMP_f4b5dft",
            provider_type=ProviderType.TEMPLATE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_context_cmp(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = []

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="unbounded_context",
            name="全量对话上下文",
            type=ComponentType.CONTEXT.value,
            desc="全量对话上下文组件",
            scene="通用",
            tags=["模型上下文", "全量对话"],
            depend_provider="autogen_core.model_context.UnboundedChatCompletionContext",
            provider_type=ProviderType.CODE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_prompt_template(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = [ConfigParam(name='prompt_template', desc='prompt模版内容', type='string', is_required=True)]

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="text_prompt_template",
            name="文本类Prompt组件模版",
            type=ComponentType.TEMPLATE.value,
            desc="prompt模版，变量使用 ${var_name}进行占位，运行时进行替换",
            scene="通用",
            tags=["prompt"],
            depend_provider="robot_studio.component.template.prompt.PromptComponent",
            provider_type=ProviderType.CODE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=True,
            config_params=config_params,
            template_type=ComponentType.PROMPT.value
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_prompt_cmp(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = [ConfigParam(name='prompt_template', value=KNOWLEDGE_PROMPT)]

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="ytwl_knowledge_info_prompt",
            name="壹同未来知识库信息prompt",
            type=ComponentType.PROMPT.value,
            desc="壹同未来知识库信息prompt",
            scene="通用",
            tags=["壹同未来", "知识信息"],
            depend_provider="cmp_890ecd",
            provider_type=ProviderType.TEMPLATE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_tool_cmp(self):
        """测试创建新组件"""
        # 组件实例化配置参数
        config_params = [ConfigParam(name='top_k', desc='prompt模版内容', type='number', is_required=True, value=3),
                         ConfigParam(name='min_score', desc='最小分数', type='number', is_required=True, value=0.5)]

        runtime_params = [
            RuntimeParam(name='query',
                         desc='知识检索关键词，关键词使用逗号分隔，你需要根据用户问题、知识信息，构建回答用户问题的',
                         type='string', is_required=True,
                         model_extract=True),
            RuntimeParam(name='knowledge_id',
                         desc='具体要查询的知识ID',
                         type='string', is_required=True,
                         model_extract=True)]

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="knowledge_search_tool",
            name="知识检索工具",
            type=ComponentType.TOOL.value,
            desc="调用该工具，可以检索具体的知识表对应的信息",
            scene="通用",
            tags=["知识检索"],
            depend_provider="robot_studio.component.template.tool.KnowledgeSearchTool",
            provider_type=ProviderType.CODE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
            runtime_params=runtime_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_create_agent_cmp(self):
        """测试创建新组件"""
        # 组件实例化配置参数
        config_params = [
            ConfigParam(name='model_client', desc='依赖的大模型', type='component',
                        component_type=ComponentType.MODEL.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_3988eb"
                        )),

            ConfigParam(name='model_context', desc='依赖的模型上下文管理器', type='component',
                        component_type=ComponentType.CONTEXT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_05f69f"
                        )),

            ConfigParam(name='agent_prompt', desc='客服机器人prompt', type='component',
                        component_type=ComponentType.PROMPT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_4a4718"
                        )),
            ConfigParam(name='knowledge_prompt', desc='知识库prompt', type='component',
                        component_type=ComponentType.PROMPT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_c74900"
                        )),
            ConfigParam(name='knowledge_search_tool', desc='知识库检索工具', type='component',
                        component_type=ComponentType.TOOL.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_3da1b8"
                        )),
            ConfigParam(name='other_tools', desc='其他工具集', type='component_array',
                        component_type=ComponentType.TOOL.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_3da1b8"
                        )),
            ConfigParam(name='query_all_knowledge', desc='是否查询所有知识', type='boolean',
                        is_required=True, value=True),

            ConfigParam(name='reflect_on_tool_use', desc='最后是否由大模型进行反思总结', type='boolean',
                        is_required=True, value=True),

            ConfigParam(name='max_tool_iterations', desc='工具最大调用次数', type='number',
                        is_required=True, value=1),

            ConfigParam(name='tool_call_summary_format', desc='工具调用汇总格式化模版', type='string',
                        is_required=False, value="{result}"),

            ConfigParam(name='knowledge_white_list', desc='知识库白名单', type='string_array',
                        is_required=False, value=[]),
        ]

        component_req = ComponentRequest(
            cid="TEST_CID_",
            code="customer_service_agent",
            name="壹同未来客户咨询机器人",
            type=ComponentType.AGENT.value,
            desc="回答用户的日常咨询，可以检索多模态知识，回答用户",
            scene="通用",
            tags=["知识检索", "客服咨询"],
            depend_provider="robot_studio.component.template.tool.KnowledgeSearchTool",
            provider_type=ProviderType.CODE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.create_new_component(component_req)

        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)

        # 验证返回的组件数据
        created_component = result.data
        print(f"成功创建测试组件: {created_component.component_id}")

    def test_update_agent_cmp(self):
        """测试创建新组件"""
        # 组件实例化配置参数
        config_params = [
            ConfigParam(name='model_client', desc='依赖的大模型', type='component',
                        component_type=ComponentType.MODEL.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_3988eb"
                        )),

            ConfigParam(name='model_context', desc='依赖的模型上下文管理器', type='component',
                        component_type=ComponentType.CONTEXT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_05f69f"
                        )),

            ConfigParam(name='agent_prompt', desc='客服机器人prompt', type='component',
                        component_type=ComponentType.PROMPT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_4a4718"
                        )),
            ConfigParam(name='knowledge_prompt', desc='知识库prompt', type='component',
                        component_type=ComponentType.PROMPT.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_c74900"
                        )),
            ConfigParam(name='knowledge_search_tool', desc='知识库检索工具', type='component',
                        component_type=ComponentType.TOOL.value, is_required=True,
                        value=ComponentBase(
                            component_id="cmp_3da1b8"
                        )),
            ConfigParam(name='other_tools', desc='其他工具集', type='component_array',
                        component_type=ComponentType.TOOL.value, is_required=False,
                        value=None),
            ConfigParam(name='query_all_knowledge', desc='是否查询所有知识', type='boolean',
                        is_required=True, value=True),

            ConfigParam(name='reflect_on_tool_use', desc='最后是否由大模型进行反思总结', type='boolean',
                        is_required=True, value=True),

            ConfigParam(name='max_tool_iterations', desc='工具最大调用次数', type='number',
                        is_required=True, value=1),

            ConfigParam(name='tool_call_summary_format', desc='工具调用汇总格式化模版', type='string',
                        is_required=False, value="{result}"),

            ConfigParam(name='knowledge_white_list', desc='知识库白名单', type='string_array',
                        is_required=False, value=[]),
        ]

        component_req = ComponentRequest(
            id=28,
            code="customer_service_agent",
            name="壹同未来客户咨询机器人",
            type=ComponentType.AGENT.value,
            desc="回答用户的日常咨询，可以检索多模态知识，回答用户",
            scene="通用",
            tags=["知识检索", "客服咨询"],
            depend_provider="robot_studio.component.template.agent.KnowledgeRagAgent",
            provider_type=ProviderType.CODE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.update_component(component_req)

        # 验证更新结果
        self.assertTrue(result.success)

    def test_update_prompt_cmp(self):
        """测试创建新组件"""
        # 创建测试组件请求
        config_params = [ConfigParam(name='prompt_template', value=AGENT_PROMPT)]

        component_req = ComponentRequest(
            id=24,
            code="ytwl_assistant_agent_prompt_new",
            name="壹同未来客服prompt",
            type=ComponentType.PROMPT.value,
            desc="壹同未来客服prompt新的",
            scene="通用",
            tags=["壹同未来", "客户咨询"],
            depend_provider="cmp_890ecd",
            provider_type=ProviderType.TEMPLATE.value,
            version_desc="初始版本",
            version_author="test_user",
            iterate_id="ITE_a1d778",
            code_change=False,
            config_params=config_params,
        )

        # 调用服务创建组件
        result = self.component_service.update_component(component_req)

        # 验证更新结果
        self.assertTrue(result.success)


if __name__ == "__main__":
    unittest.main()
