"""
WebSocket聊天服务测试
"""

import unittest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from robot_studio.chat.websocket.api import ChatWebSocketService
from robot_studio.chat.websocket.api.request import WebSocketChatRequest, MessageContent, ChatProcessRequest
from robot_studio.chat.websocket.api.result import WebSocketChatResult


class TestChatWebSocketService(unittest.TestCase):
    """WebSocket聊天服务测试类"""

    def setUp(self):
        """测试前准备"""
        self.service = ChatWebSocketService()

    def test_create_pong_response(self):
        """测试创建心跳响应"""
        result = self.service.create_pong_response()
        
        self.assertEqual(result.type, "pong")
        self.assertIsInstance(result.timestamp, float)

    def test_create_ack_response(self):
        """测试创建确认响应"""
        message_id = "test_message_123"
        result = self.service.create_ack_response(message_id)
        
        self.assertEqual(result.type, "ack")
        self.assertEqual(result.id, message_id)
        self.assertIsInstance(result.timestamp, float)

    def test_generate_message_ids(self):
        """测试生成消息ID"""
        request = WebSocketChatRequest(
            session_id="test_session",
            user_id="test_user",
            contents=[
                MessageContent(
                    role="user",
                    content="测试消息",
                    message_type="text"
                )
            ]
        )
        
        user_id, assistant_id = self.service.generate_message_ids(request)
        
        self.assertIsInstance(user_id, str)
        self.assertIsInstance(assistant_id, str)
        self.assertNotEqual(user_id, assistant_id)

    @patch('robot_studio.biz.chat.websocket.api.chat_websocket_service.SessionService')
    def test_validate_chat_session_success(self, mock_session_service):
        """测试会话验证成功"""
        # Mock会话服务
        mock_session_instance = Mock()
        mock_session_service.return_value = mock_session_instance
        mock_session_instance.validate_session_access.return_value = (True, Mock(), None)
        
        # 创建测试请求
        request = WebSocketChatRequest(
            session_id="test_session",
            user_id="test_user",
            contents=[
                MessageContent(
                    role="user",
                    content="测试消息",
                    message_type="text"
                )
            ]
        )
        
        # 执行测试
        service = ChatWebSocketService()
        is_valid, session, error_msg = service.validate_chat_session(request)
        
        self.assertTrue(is_valid)
        self.assertIsNotNone(session)
        self.assertIsNone(error_msg)

    @patch('robot_studio.biz.chat.websocket.api.chat_websocket_service.SessionService')
    def test_validate_chat_session_failure(self, mock_session_service):
        """测试会话验证失败"""
        # Mock会话服务
        mock_session_instance = Mock()
        mock_session_service.return_value = mock_session_instance
        mock_session_instance.validate_session_access.return_value = (False, None, "会话不存在")
        
        # 创建测试请求
        request = WebSocketChatRequest(
            session_id="invalid_session",
            user_id="test_user",
            contents=[
                MessageContent(
                    role="user",
                    content="测试消息",
                    message_type="text"
                )
            ]
        )
        
        # 执行测试
        service = ChatWebSocketService()
        is_valid, session, error_msg = service.validate_chat_session(request)
        
        self.assertFalse(is_valid)
        self.assertIsNone(session)
        self.assertEqual(error_msg, "会话不存在")

    def test_websocket_chat_request_get_last_user_message(self):
        """测试获取最后一条用户消息"""
        request = WebSocketChatRequest(
            session_id="test_session",
            user_id="test_user",
            contents=[
                MessageContent(
                    role="assistant",
                    content="助手消息",
                    message_type="text"
                ),
                MessageContent(
                    role="user",
                    content="第一条用户消息",
                    message_type="text"
                ),
                MessageContent(
                    role="user",
                    content="最后一条用户消息",
                    message_type="text"
                )
            ]
        )
        
        last_message = request.get_last_user_message()
        self.assertEqual(last_message, "最后一条用户消息")

    def test_websocket_chat_request_no_user_message(self):
        """测试没有用户消息的情况"""
        request = WebSocketChatRequest(
            session_id="test_session",
            user_id="test_user",
            contents=[
                MessageContent(
                    role="assistant",
                    content="助手消息",
                    message_type="text"
                )
            ]
        )
        
        last_message = request.get_last_user_message()
        self.assertIsNone(last_message)


class TestChatWebSocketServiceAsync(unittest.IsolatedAsyncioTestCase):
    """WebSocket聊天服务异步测试类"""

    def setUp(self):
        """测试前准备"""
        self.service = ChatWebSocketService()

    async def test_save_user_message_success(self):
        """测试保存用户消息成功"""
        # 使用patch作为上下文管理器来正确mock实例方法
        with patch.object(self.service, '_chat_chunk_manager') as mock_manager:
            mock_saved_chunk = Mock()
            mock_saved_chunk.chunk_id = "saved_chunk_123"
            mock_manager.create_chat_chunk.return_value = mock_saved_chunk

            # Mock会话对象
            mock_session = Mock()
            mock_session.session_id = "test_session"

            # 执行测试
            result = await self.service.save_user_message(
                session=mock_session,
                user_content="测试用户消息",
                user_message_id="user_msg_123",
                user_id="test_user",
                user_name="测试用户"
            )

            self.assertTrue(result)
            mock_manager.create_chat_chunk.assert_called_once()

    async def test_save_assistant_message_success(self):
        """测试保存助手消息成功"""
        # 使用patch作为上下文管理器来正确mock实例方法
        with patch.object(self.service, '_chat_chunk_manager') as mock_manager:
            mock_saved_chunk = Mock()
            mock_saved_chunk.chunk_id = "saved_chunk_456"
            mock_manager.create_chat_chunk.return_value = mock_saved_chunk

            # Mock会话对象
            mock_session = Mock()
            mock_session.session_id = "test_session"

            # 执行测试
            result = await self.service.save_assistant_message(
                session=mock_session,
                full_response="测试助手回复",
                assistant_message_id="assistant_msg_456"
            )

            self.assertTrue(result)
            mock_manager.create_chat_chunk.assert_called_once()


if __name__ == "__main__":
    unittest.main()
