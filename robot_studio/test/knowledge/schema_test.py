import unittest

from robot_studio.data_asset.knowledge.core import KnowledgeSchemaManager
from robot_studio.data_asset.knowledge.model import KnowledgeSchema, SchemaColConfig


class SchemaTestCase(unittest.TestCase):
    _schema_manager = KnowledgeSchemaManager()

    def test_insert(self):
        cols = [SchemaColConfig(col_name='课程名称', is_entity=True, entity_type='课程'),
                SchemaColConfig(col_name='年级', is_entity=False),
                SchemaColConfig(col_name='科目',
                                desc='国际表达=英语,创新思维=数学,人文素养=语文,科学探究=化学,物质理论=物理',
                                is_entity=False),
                SchemaColConfig(col_name='期段', desc='期段分为暑假班、寒假班，春季学期班，秋季学期班', is_entity=False),
                SchemaColConfig(col_name='班型', desc='不同科目，针对学生需求，设计了不同班型', is_entity=False),
                SchemaColConfig(col_name='课程大纲', desc='课程内容介绍', is_entity=False),
                SchemaColConfig(col_name='课程时间', desc='课程具体的排课时间', is_entity=False)]

        res = self._schema_manager.create(KnowledgeSchema(type="课程", name="课程知识结构", desc="",
                                                          config=cols, cid="CID_6f6968"))
        print(res)


if __name__ == '__main__':
    unittest.main()
