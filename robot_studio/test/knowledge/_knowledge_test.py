import asyncio
import unittest

from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import OnlineReq
from robot_studio.data_asset.knowledge.bailian import BailianKnowledgeService


class KnowledgeTest(unittest.TestCase):
    _bailian_service = BailianKnowledgeService()
    _knowledge_service = KnowledgeService()

    def test_online(self):
        async def _sync():
            req = OnlineReq(uid='test', user_name='test', cid='test', ids=[65])
            res = await self._knowledge_service.batch_online(req)
            print(res)

        asyncio.run(_sync())

    def test_sync(self):
        async def _sync():
            res = await self._bailian_service.sync_bailian_knowledge(
                "/Users/<USER>/code/mindshake/robot_studio/data_asset/knowledge/2015暑秋课程知识.md",
                "file_618da43311cd478ba001bb2ee1cdb296_11409742", "a2j7h2oess")
            print(res)

        asyncio.run(_sync())

    def test_upload(self):
        async def _upload_file():
            res = await self._bailian_service.apply_file_upload_lease(
                "/Users/<USER>/code/mindshake/robot_studio/data_asset/knowledge/2015暑秋课程知识.md")
            print(res)
            self._bailian_service.upload_file(
                "/Users/<USER>/code/mindshake/robot_studio/data_asset/knowledge/2015暑秋课程知识.md",
                res)
            self.add_file_res = await self._bailian_service.add_file(res.file_upload_lease_id)
            print(self.add_file_res)

        asyncio.run(_upload_file())

    def test_upload_status(self):
        async def _query_status():
            res = await self._bailian_service.query_file_status(
                "file_49b58b7aadc84d5889119abd3a35e690_11409742")
            print(res)
            self.assertTrue(res.parse_ready())

        asyncio.run(_query_status())

    def test_del_file(self):
        async def _del_file():
            res = await self._bailian_service.del_file(
                "file_25db7cc91fbe4550bc056732500921fe_11409742")
            print(res)

        asyncio.run(_del_file())

    def test_create_index_db(self):
        async def _create():
            res = await self._bailian_service.create_index_database(
                "file_49b58b7aadc84d5889119abd3a35e690_11409742", "2025暑秋课程知识库")
            print(res)

        asyncio.run(_create())

    def test_index_job(self):
        async def _create():
            res = await self._bailian_service.submit_index_job(
                "t7b5muybci")
            print(res)

        asyncio.run(_create())

    def test_query_index_job(self):
        async def _create():
            res = await self._bailian_service.query_index_job_status(
                index_database_id="t7b5muybci", job_id="84a7b430032443808fe0632899a23010")
            print(res)

        asyncio.run(_create())


if __name__ == '__main__':
    unittest.main()
