import unittest

from robot_studio.data_asset.knowledge.api import KnowledgeGroupService
from robot_studio.data_asset.knowledge.api.request.group_req import GroupCreateReq


class KnowledgeGroupTest(unittest.TestCase):
    _group_api = KnowledgeGroupService()

    def test_insert(self):
        req = GroupCreateReq(
            uid="UID_test001",  # 用户ID
            cid="CID_6f6968",   # 企业ID
            user_name="test_user",  # 用户名（作为创建人）
            group_name="课程知识",
            group_desc="壹同未来课程相关的知识内容",
            tags=["课程", "教育"]
        )
        res = self._group_api.add_knowledge_group(req)
        print(res)  # add assertion here

    def test_query_all_knowledge_groups(self):
        res = self._group_api.query_groups(cid='CID_6f6968')
        print(res)


if __name__ == '__main__':
    unittest.main()
