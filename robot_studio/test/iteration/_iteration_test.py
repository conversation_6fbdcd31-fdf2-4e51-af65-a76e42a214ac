"""
IterationManager 测试文件
"""

import unittest
import uuid

from robot_studio.component.api import IterationService
from robot_studio.component.api.request import IterationReq
from robot_studio.component.iteration.model import IterationStatus


class TestIterationManager(unittest.TestCase):
    """IterationManager 测试类"""

    def setUp(self):
        """测试前准备"""
        self.iteration_service = IterationService()

    def test_create_new_iteration(self):
        """测试创建新迭代"""
        # 创建测试迭代请求
        iteration_req = IterationReq(
            cid="TEST_CID_" + str(uuid.uuid4())[:8],
            name="测试迭代_" + str(uuid.uuid4())[:8],
            desc="这是一个用于测试的迭代",
            status=IterationStatus.DEVELOPING.value,
            owner="test_user",
            members=["user1", "user2"],
            tags=["test", "integration"],
            code_release=False
        )
        
        # 调用服务创建迭代
        result = self.iteration_service.create_iteration(iteration_req)
        
        # 验证创建结果
        self.assertTrue(result.success)
        self.assertIsNotNone(result.data)
        
        # 验证返回的迭代数据
        created_iteration = result.data
        self.assertIsNotNone(created_iteration.iterate_id)
        self.assertTrue(created_iteration.iterate_id.startswith("ITE_"))
        self.assertEqual(created_iteration.name, iteration_req.name)
        self.assertEqual(created_iteration.desc, iteration_req.desc)
        self.assertEqual(created_iteration.status, IterationStatus.DEVELOPING.value)
        self.assertEqual(created_iteration.owner, "test_user")
        self.assertEqual(created_iteration.members, ["user1", "user2"])
        self.assertEqual(created_iteration.tags, ["test", "integration"])
        self.assertFalse(created_iteration.code_release)
        self.assertIsNotNone(created_iteration.gmt_create)
        
        print(f"成功创建测试迭代: {created_iteration.iterate_id}")


if __name__ == "__main__":
    unittest.main()
