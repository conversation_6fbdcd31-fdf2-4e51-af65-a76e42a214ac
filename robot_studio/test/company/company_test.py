import unittest

from robot_studio.company import CompanyManager
from robot_studio.company.model import Company, CompanyType


class CompanyTest(unittest.TestCase):
    _comp_manager = CompanyManager()

    def test_insert(self):
        res = self._comp_manager.create_company(
            Company(
                name="青岛壹同未来教育咨询有限公司", 
                abbr="壹同未来", 
                logo="https://www.ytfw.com/logo.png",
                industry="教育",
                company_type=CompanyType.REAL_ENTITY
            ))
        print(res)

    # CID_6f6968

    def test_query_all(self):
        # CID_6f6968
        res = self._comp_manager.query_all_companies()
        print(res)

    def test_del(self):
        res = self._comp_manager.delete_company('CID_6f6968')
        print(res)


if __name__ == '__main__':
    unittest.main()
