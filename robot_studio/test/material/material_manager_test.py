import unittest

from robot_studio.data_asset.material.core import MaterialManager
from robot_studio.data_asset.material.model import Material


class MaterialManagerTest(unittest.TestCase):
    _material_manager = MaterialManager()

    def test_get_material_by_mid(self):
        material = self._material_manager.query_material("MID_c8901e")
        print(material)
        self.assertIsNotNone(material)  # add assertion here

    def test_get_material_by_group_id(self):
        materials = self._material_manager.query_materials_by_group("GID_0719552")
        print(materials)
        self.assertIsNotNone(materials)  # add assertion here

    def test_get_valid_material_by_group_id(self):
        materials = self._material_manager.query_valid_materials_by_group("GID_eae300")
        print(materials)
        print(len(materials))
        self.assertIsNotNone(materials)  # add assertion here

    def test_create_material(self):
        material = self._material_manager.create(
            Material(
                name="2025年度小红书暑期初升高物理早鸟班宣传文案",
                type="DOC",
                group_id="GID_eae300",
                source="内部设计",
                content_digest="2025年度小红书暑期初升高物理早鸟班宣传文案",
                tags=["小红书", "初升高", "物理早鸟班", "2025年度暑秋"])
        )
        print(material)
        self.assertIsNotNone(material)

    def test_update_company(self):
        update_data = Material(
            material_content="初升高化学难难难？别慌！2025 暑期初升高化学早鸟班重磅上线，现在报名立享 6 折早鸟价！"
        )
        material = self._material_manager.update_material("MID_3b5017", update_data)
        print(material)
        self.assertIsNotNone(material)

    def test_online(self):
        res = self._material_manager.online(mid="MID_0888461")
        print(res)
