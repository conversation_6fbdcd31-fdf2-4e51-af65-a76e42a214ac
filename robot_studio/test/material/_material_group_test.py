import unittest

from robot_studio.data_asset.material.api import MaterialGroupAPI


class MaterialGroupTest(unittest.TestCase):
    _group_api = MaterialGroupAPI()

    def test_insert(self):
        res = self._group_api.add_material_group(group_name="2025年度小红书暑期早鸟班宣传素材组",
                                                 group_desc="壹同未来2025年度小红书暑期早鸟班宣传素材组",
                                                 cid="CID_6f6968",
                                                 sub_group_type="XHS")
        print(res)  # add assertion here

    def test_delete(self):
        res = self._group_api.del_group(group_id="GID_eae300")
        print(res)

    def test_update(self):
        res = self._group_api.update_group(group_id="GID_a6b82d",
                                           cid="CID_6f6968",
                                           group_name="2025年度抖音暑期早鸟班宣传素材组",
                                           group_desc="壹同未来2025年度抖音暑期早鸟班宣传素材组",
                                           sub_group_type="DY")
        print(res)

    def test_query_all_groups(self):
        res = self._group_api.query_all_material_groups(cid="CID_6f6968")
        print(res)

    def test_query_group_by_platform(self):
        res = self._group_api.query_material_groups_by_platform(cid="CID_6f6968", platform="DY")
        print(res)


if __name__ == '__main__':
    unittest.main()
