import unittest

from robot_studio.data_asset.material.api.request.material_req import MaterialBaseReq
from robot_studio.data_asset.material.api import MaterialService


class MaterialTest(unittest.TestCase):
    _material_api = MaterialService()

    def test_insert(self):
        req = MaterialBaseReq(
            name="2025年度小红书暑期英语早鸟班宣传文案",
            content_type="TEXT",
            content_digest="2025年度小红书暑期英语早鸟班宣传文案",
            group_id="GID_0719552",
            tags=["小红书", "英语早鸟班", "2025年度暑秋"],
            valid_date="2025-06-30",
            invalid_date="2025-11-01",
            material_content="英语渣别逃！是时候逆袭了！2025 暑期英语早鸟班开抢，现在报名立省 30% 。",
            source="INS",
            uid="UID_0542552",
            cid="CID_6f6968"
        )
        res = self._material_api.create_material(req=req)
        print(res)

    def test_delete(self):
        res = self._material_api.del_material(mid="MID_c8901e")
        print(res)

    def test_batch_delete(self):
        """测试批量删除素材"""
        mids = ["MID_test1", "MID_test2", "MID_test3"]
        res = self._material_api.batch_del_material(mids=mids)
        print(res)

    def test_update(self):
        req = MaterialBaseReq(
            name="2025年度快手暑期英语早鸟班宣传文案",
            content_type="TEXT",
            content_digest="2025年度快手暑期英语早鸟班宣传文案",
            group_id="GID_0711525",
            tags=["快手", "英语早鸟班", "2025年度暑秋"],
            valid_date="2025-06-30",
            invalid_date="2025-11-01",
            material_content="英语渣别逃！是时候逆袭了！2025 暑期英语早鸟班开抢，现在报名立省 30% 。",
            source="INS",
            uid="UID_0542552",
            cid="CID_6f6968"
        )
        res = self._material_api.update_material(mid='MID_3b5017', update_data=req)
        print(res)

    def test_query_material_by_id(self):
        material = self._material_api.query_material_by_id(mid="MID_a3c3a6")
        print(material)

    def test_query_material_by_group(self):
        materials = self._material_api.query_material_by_group(group_id="GID_765ac4")
        # materials = self._material_api.query_material_by_group(group_id="GID_667e5a")
        print(materials)

    def test_online(self):
        res = self._material_api.online_material(mid="MID_3b5017")
        print(res)

    def test_batch_online(self):
        """测试批量上线素材"""
        mids = ["MID_test1", "MID_test2", "MID_test3"]
        res = self._material_api.batch_online_material(mids=mids)
        print(res)

    def test_offline(self):
        res = self._material_api.offline_material(mid="MID_3b5017")
        print(res)

    def test_batch_offline(self):
        """测试批量下线素材"""
        mids = ["MID_test1", "MID_test2", "MID_test3"]
        res = self._material_api.batch_offline_material(mids=mids)
        print(res)

    def test_query_online_media_materials_by_cid(self):
        """测试查询企业下所有在线状态的图片和视频素材"""
        cid = "CID_6f6968"
        res = self._material_api.query_online_media_materials_by_cid(cid=cid)
        print("查询结果成功状态:", res.success)
        if res.success and res.data:
            print("查询到的素材组数量:", len(res.data))
            for group_name, materials in res.data.items():
                print(f"素材组名: {group_name}")
                print(f"  素材数量: {len(materials)}")
                for material in materials:
                    print(f"    - 素材ID: {material['material_id']}")
                    print(f"      素材名称: {material['name']}")
                    print(f"      素材类型: {material['type']}")
                    print(f"      素材URL: {material['url']}")
        else:
            print("查询失败或无数据:", res.error_msg if not res.success else "无在线媒体素材")
        print("完整返回结果:", res)

if __name__ == '__main__':
    unittest.main()
