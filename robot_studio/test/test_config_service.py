"""
Test configuration service
"""
import os
import sys
import unittest
from pathlib import Path
from typing import List, Dict

# Add the project root to sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from robot_studio.common.config_service import ConfigurationService, get_config, get_all_configs
from robot_studio.common.encryption_service import encryption_service


class TestConfigurationService(unittest.TestCase):
    """配置服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_service = ConfigurationService()
        # 禁用缓存以确保测试准确性
        self.config_service.disable_cache()
    
    def test_set_and_get_config(self):
        """测试设置和获取配置"""
        # 测试设置配置
        key = "TEST_CONFIG_KEY"
        value = "test_value_123"
        env = "test"
        cid = "test_company"
        
        success = self.config_service.set_config(
            key=key,
            value=value,
            env=env,
            cid=cid,
            description="测试配置"
        )
        
        self.assertTrue(success, "设置配置应该成功")
        
        # 测试获取配置
        retrieved_value = self.config_service.get_config(key, env, cid)
        self.assertEqual(retrieved_value, value, "获取的配置值应该与设置的一致")
        
        # 清理测试数据
        self.config_service.delete_config(key, env, cid)
    
    def test_config_priority(self):
        """测试配置优先级：企业特定 > 通用"""
        key = "PRIORITY_TEST_KEY"
        common_value = "common_value"
        company_value = "company_value"
        env = "test"
        cid = "test_company"
        
        # 设置通用配置
        success1 = self.config_service.set_config(
            key=key,
            value=common_value,
            env=env,
            cid="common",
            description="通用配置"
        )
        self.assertTrue(success1, "设置通用配置应该成功")
        
        # 设置企业特定配置
        success2 = self.config_service.set_config(
            key=key,
            value=company_value,
            env=env,
            cid=cid,
            description="企业特定配置"
        )
        self.assertTrue(success2, "设置企业配置应该成功")
        
        # 获取企业配置（应该返回企业特定值）
        retrieved_value = self.config_service.get_config(key, env, cid)
        self.assertEqual(retrieved_value, company_value, "应该返回企业特定配置值")
        
        # 获取通用配置
        common_retrieved = self.config_service.get_config(key, env, "common")
        self.assertEqual(common_retrieved, common_value, "应该返回通用配置值")
        
        # 清理测试数据
        self.config_service.delete_config(key, env, cid)
        self.config_service.delete_config(key, env, "common")
    
    def test_get_all_configs(self):
        """测试获取所有配置"""
        env = "test"
        cid = "test_company"
        
        # 设置多个配置
        test_configs = {
            "TEST_KEY_1": "value_1",
            "TEST_KEY_2": "value_2",
            "TEST_KEY_3": "value_3"
        }
        
        for key, value in test_configs.items():
            success = self.config_service.set_config(
                key=key,
                value=value,
                env=env,
                cid=cid,
                description=f"测试配置 {key}"
            )
            self.assertTrue(success, f"设置配置 {key} 应该成功")
        
        # 获取所有配置
        all_configs = self.config_service.get_all_configs(env, cid)
        
        # 验证配置存在
        for key, value in test_configs.items():
            self.assertIn(key, all_configs, f"配置 {key} 应该存在于所有配置中")
            self.assertEqual(all_configs[key], value, f"配置 {key} 的值应该正确")
        
        # 清理测试数据
        for key in test_configs.keys():
            self.config_service.delete_config(key, env, cid)
    
    def test_default_values(self):
        """测试默认值"""
        key = "NON_EXISTENT_KEY"
        default_value = "default_value_123"
        env = "test"
        cid = "test_company"
        
        # 获取不存在的配置，应该返回默认值
        value = self.config_service.get_config(key, env, cid, default_value)
        self.assertEqual(value, default_value, "不存在的配置应该返回默认值")
    
    def test_update_config(self):
        """测试更新配置"""
        key = "UPDATE_TEST_KEY"
        initial_value = "initial_value"
        updated_value = "updated_value"
        env = "test"
        cid = "test_company"
        
        # 设置初始配置
        success1 = self.config_service.set_config(
            key=key,
            value=initial_value,
            env=env,
            cid=cid,
            description="初始配置"
        )
        self.assertTrue(success1, "设置初始配置应该成功")
        
        # 更新配置
        success2 = self.config_service.set_config(
            key=key,
            value=updated_value,
            env=env,
            cid=cid,
            description="更新配置"
        )
        self.assertTrue(success2, "更新配置应该成功")
        
        # 验证更新后的值
        retrieved_value = self.config_service.get_config(key, env, cid)
        self.assertEqual(retrieved_value, updated_value, "配置应该已更新")
        
        # 清理测试数据
        self.config_service.delete_config(key, env, cid)
    
    def test_delete_config(self):
        """测试删除配置"""
        key = "DELETE_TEST_KEY"
        value = "delete_test_value"
        env = "test"
        cid = "test_company"
        
        # 设置配置
        success1 = self.config_service.set_config(
            key=key,
            value=value,
            env=env,
            cid=cid,
            description="待删除配置"
        )
        self.assertTrue(success1, "设置配置应该成功")
        
        # 验证配置存在
        retrieved_value = self.config_service.get_config(key, env, cid)
        self.assertEqual(retrieved_value, value, "配置应该存在")
        
        # 删除配置
        success2 = self.config_service.delete_config(key, env, cid)
        self.assertTrue(success2, "删除配置应该成功")
        
        # 验证配置已删除
        retrieved_value_after_delete = self.config_service.get_config(key, env, cid)
        self.assertIsNone(retrieved_value_after_delete, "配置应该已被删除")
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        key = "CONVENIENCE_TEST_KEY"
        value = "convenience_value"
        env = "test"
        cid = "test_company"
        
        # 使用配置服务设置配置
        success = self.config_service.set_config(
            key=key,
            value=value,
            env=env,
            cid=cid,
            description="便捷函数测试"
        )
        self.assertTrue(success, "设置配置应该成功")
        
        # 使用便捷函数获取配置
        # 设置环境变量以便便捷函数使用
        os.environ['ENV'] = env
        os.environ['CID'] = cid
        
        retrieved_value = get_config(key)
        self.assertEqual(retrieved_value, value, "便捷函数应该返回正确的配置值")
        
        # 获取所有配置
        all_configs = get_all_configs()
        self.assertIn(key, all_configs, "便捷函数应该返回包含测试配置的字典")
        
        # 清理测试数据
        self.config_service.delete_config(key, env, cid)
        
        # 清理环境变量
        del os.environ['ENV']
        del os.environ['CID']
    
    def test_tag_functionality(self):
        """测试标签功能"""
        env = "test"
        cid = "test_company"
        tag = "test_tag"
        
        # 设置带标签的配置
        test_configs = {
            "TAG_TEST_KEY_1": "tag_value_1",
            "TAG_TEST_KEY_2": "tag_value_2",
            "TAG_TEST_KEY_3": "tag_value_3"
        }
        
        for key, value in test_configs.items():
            success = self.config_service.set_config(
                key=key,
                value=value,
                env=env,
                cid=cid,
                tag=tag,
                description=f"标签测试配置 {key}"
            )
            self.assertTrue(success, f"设置带标签的配置 {key} 应该成功")
        
        # 测试根据标签获取配置
        tag_configs = self.config_service.get_configs_by_tag(tag, env, cid)
        
        # 验证标签配置
        for key, value in test_configs.items():
            self.assertIn(key, tag_configs, f"配置 {key} 应该存在于标签配置中")
            self.assertEqual(tag_configs[key], value, f"标签配置 {key} 的值应该正确")
        
        # 测试获取所有标签
        all_tags = self.config_service.get_all_tags(env, cid)
        self.assertIn(tag, all_tags, f"标签 {tag} 应该存在于所有标签中")
        
        # 测试更新配置标签
        new_tag = "new_test_tag"
        success = self.config_service.update_config_tag("TAG_TEST_KEY_1", new_tag, env, cid)
        self.assertTrue(success, "更新配置标签应该成功")
        
        # 验证标签更新
        new_tag_configs = self.config_service.get_configs_by_tag(new_tag, env, cid)
        self.assertIn("TAG_TEST_KEY_1", new_tag_configs, "更新标签后的配置应该可以通过新标签找到")
        
        # 验证旧标签下配置减少
        old_tag_configs = self.config_service.get_configs_by_tag(tag, env, cid)
        self.assertNotIn("TAG_TEST_KEY_1", old_tag_configs, "更新标签后的配置不应该在旧标签下找到")
        
        # 清理测试数据
        for key in test_configs.keys():
            self.config_service.delete_config(key, env, cid)
    
    def test_bailian_tag_configs(self):
        """测试百炼相关配置的标签管理"""
        env = "test"
        cid = "test_company"
        bailian_tag = "bailian"
        
        # 设置百炼相关配置
        bailian_configs = {
            "ALIBABA_CLOUD_ACCESS_KEY_ID": "test_access_key_id",
            "ALIBABA_CLOUD_ACCESS_KEY_SECRET": "test_access_key_secret",
            "ALIBABA_CLOUD_BAILIAN_WORK_SPACE_ID": "test_workspace_id"
        }
        
        for key, value in bailian_configs.items():
            success = self.config_service.set_config(
                key=key,
                value=value,
                env=env,
                cid=cid,
                tag=bailian_tag,
                description=f"百炼配置 {key}",
                is_encrypted=(key.endswith("_SECRET") or key.endswith("_ID"))
            )
            self.assertTrue(success, f"设置百炼配置 {key} 应该成功")
        
        # 测试通过标签获取所有百炼配置
        bailian_tag_configs = self.config_service.get_configs_by_tag(bailian_tag, env, cid)
        
        # 验证百炼配置完整性
        for key in bailian_configs.keys():
            self.assertIn(key, bailian_tag_configs, f"百炼配置 {key} 应该可以通过标签找到")
        
        # 测试加载标签配置到环境变量
        self.config_service.load_tag_to_env(bailian_tag, env, cid)
        
        # 验证环境变量
        for key in bailian_configs.keys():
            self.assertIn(key, os.environ, f"百炼配置 {key} 应该已加载到环境变量")
        
        # 清理测试数据
        for key in bailian_configs.keys():
            self.config_service.delete_config(key, env, cid)
            # 清理环境变量
            if key in os.environ:
                del os.environ[key]
    
    def test_encryption_functionality(self):
        """测试加密功能"""
        env = "test"
        cid = "test_company"
        
        # 测试加密配置
        encrypted_key = "ENCRYPTED_TEST_KEY"
        encrypted_value = "sensitive_password_123"
        
        # 设置加密配置
        success = self.config_service.set_config(
            key=encrypted_key,
            value=encrypted_value,
            env=env,
            cid=cid,
            tag="test_encryption",
            description="测试加密配置",
            is_encrypted=True
        )
        self.assertTrue(success, "设置加密配置应该成功")
        
        # 获取配置值（应该自动解密）
        retrieved_value = self.config_service.get_config(encrypted_key, env, cid)
        self.assertEqual(retrieved_value, encrypted_value, "解密后的值应该与原始值相同")
        
        # 检查数据库中存储的是加密值
        from robot_studio.database.mysql.repository._configuration_repository import ConfigurationRepository
        from robot_studio.database.mysql.db_engine import get_common_session
        
        with next(get_common_session()) as session:
            repo = ConfigurationRepository(session)
            config_obj = repo.get_config(encrypted_key, env, cid)
            self.assertNotEqual(config_obj.value, encrypted_value, "数据库中应该存储加密值")
            self.assertTrue(config_obj.is_encrypted, "配置应该标记为已加密")
        
        # 清理测试数据
        self.config_service.delete_config(encrypted_key, env, cid)
    
    def test_encryption_service_directly(self):
        """直接测试加密服务"""
        test_data = "test_sensitive_data_123"
        
        # 测试加密
        encrypted = encryption_service.encrypt(test_data)
        self.assertNotEqual(encrypted, test_data, "加密后的数据应该与原数据不同")
        
        # 测试解密
        decrypted = encryption_service.decrypt(encrypted)
        self.assertEqual(decrypted, test_data, "解密后的数据应该与原数据相同")
        
        # 测试加密检测
        self.assertTrue(encryption_service.is_encrypted(encrypted), "应该能识别加密数据")
        self.assertFalse(encryption_service.is_encrypted(test_data), "应该能识别未加密数据")
    
    def test_batch_encryption(self):
        """测试批量加密功能"""
        env = "test"
        cid = "test_company"
        
        # 设置多个明文配置
        test_configs = {
            "BATCH_TEST_KEY_1": "password_1",
            "BATCH_TEST_KEY_2": "api_key_2", 
            "BATCH_TEST_KEY_3": "secret_3"
        }
        
        # 先以明文形式创建配置
        for key, value in test_configs.items():
            success = self.config_service.set_config(
                key=key,
                value=value,
                env=env,
                cid=cid,
                tag="batch_test",
                description=f"批量加密测试 {key}",
                is_encrypted=False  # 明文存储
            )
            self.assertTrue(success, f"设置明文配置 {key} 应该成功")
        
        # 批量加密
        keys_to_encrypt = list(test_configs.keys())
        results = self.config_service.batch_encrypt_configs(keys_to_encrypt, env, cid)
        
        # 验证加密结果
        for key in keys_to_encrypt:
            self.assertTrue(results[key], f"配置 {key} 应该加密成功")
        
        # 验证配置值仍然可以正确读取（自动解密）
        for key, original_value in test_configs.items():
            retrieved_value = self.config_service.get_config(key, env, cid)
            self.assertEqual(retrieved_value, original_value, f"配置 {key} 解密后应该与原值相同")
        
        # 获取加密状态
        encryption_status = self.config_service.get_encryption_status(env, cid)
        for key in keys_to_encrypt:
            self.assertTrue(encryption_status.get(key, False), f"配置 {key} 应该显示为已加密")
        
        # 清理测试数据
        for key in test_configs.keys():
            self.config_service.delete_config(key, env, cid)
    
    def test_encryption_with_tags(self):
        """测试加密配置的标签功能"""
        env = "test"
        cid = "test_company"
        tag = "encrypted_secrets"
        
        # 设置带标签的加密配置
        encrypted_configs = {
            "SECRET_KEY_1": "super_secret_value_1",
            "SECRET_KEY_2": "super_secret_value_2",
            "SECRET_KEY_3": "super_secret_value_3"
        }
        
        for key, value in encrypted_configs.items():
            success = self.config_service.set_config(
                key=key,
                value=value,
                env=env,
                cid=cid,
                tag=tag,
                description=f"加密密钥 {key}",
                is_encrypted=True
            )
            self.assertTrue(success, f"设置加密配置 {key} 应该成功")
        
        # 通过标签获取配置（应该自动解密）
        tag_configs = self.config_service.get_configs_by_tag(tag, env, cid)
        
        # 验证所有配置都能正确解密
        for key, original_value in encrypted_configs.items():
            self.assertIn(key, tag_configs, f"配置 {key} 应该存在于标签配置中")
            self.assertEqual(tag_configs[key], original_value, f"配置 {key} 应该正确解密")
        
        # 清理测试数据
        for key in encrypted_configs.keys():
            self.config_service.delete_config(key, env, cid)


class TestConfigurationIntegration(unittest.TestCase):
    """配置集成测试"""
    
    def test_database_configuration_loading(self):
        """测试数据库配置加载"""
        # 测试关键配置是否可以从数据库加载
        key_configs = [
            'DB_URL',
            'DB_PORT',
            'DB_NAME',
            'DB_USER',
            'DB_PASSWORD'
        ]
        
        # 设置使用配置服务
        os.environ['USE_CONFIG_SERVICE'] = 'true'
        
        for key in key_configs:
            try:
                value = get_config(key)
                self.assertIsNotNone(value, f"配置 {key} 应该可以从数据库加载")
                self.assertNotEqual(value, "", f"配置 {key} 不应该为空")
            except Exception as e:
                self.fail(f"加载配置 {key} 失败: {str(e)}")
        
        # 清理环境变量
        del os.environ['USE_CONFIG_SERVICE']


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)