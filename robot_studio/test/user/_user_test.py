import unittest

from robot_studio.auth.api.request.user_req import Create<PERSON>serReq, LoginReq, DeleteUserReq
from robot_studio.auth.api import UserService
from robot_studio.auth.core import UserManager


class MaterialTest(unittest.TestCase):
    _user_service = UserService()
    _user_manager = UserManager()

    def test_signup(self):
        req = CreateUserReq(
            telephone="13796536543",
            username="shencj",
            cid="CID_d9bf81",
            password="yage666",
            email='<EMAIL>'
        )
        res = self._user_service.sign_up(req=req)
        print(res)

    def test_login(self):
        req = LoginReq(
            telephone="1876542143",
            password="wmjtc111"
        )
        res = self._user_service.login(req)
        print(res)
    
    def test_query_user_by_telephone(self):
        res = self._user_manager.query_user_by_telephone(telephone="1876542143")
        print(res)

    def test_get_company_info(self):
        res = self._user_service.get_company()
        print(res)
    def test_del_user(self):
        res = self._user_service.delete_user(DeleteUserReq(uid="UID_8a915f"))
        print(res)
