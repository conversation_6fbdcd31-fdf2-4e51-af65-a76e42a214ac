#!/usr/bin/env python3
"""
测试 stream_search 方法并查看从配置表加载的环境变量
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from robot_studio.component.rag.graph_rag.query.api._local_search_service import LocalSearch
from robot_studio.common.config_service import get_config


async def test_stream_search():
    """测试 stream_search 方法"""
    print("=" * 60)
    print("测试 stream_search 方法")
    print("=" * 60)
    
    # 创建 LocalSearch 实例
    local_search = LocalSearch()
    
    # 测试查询
    test_query = "测试查询"
    print(f"测试查询: {test_query}")
    print()
    
    try:
        # 执行搜索
        async for response in local_search.stream_search(test_query):
            print(f"收到响应: {type(response).__name__}")
            if hasattr(response, '__dict__'):
                print(f"响应内容: {response.__dict__}")
            print("-" * 40)
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def check_config_variables():
    """检查从配置表加载的三个环境变量"""
    print("=" * 60)
    print("检查从配置表加载的环境变量")
    print("=" * 60)
    
    # 三个需要检查的环境变量
    config_keys = [
        "GRAPHRAG_API_KEY",
        "GRAPHRAG_API_BASE", 
        "GRAPHRAG_EMBEDDING_BATCH_SIZE"
    ]
    
    print("从配置表获取的GRAPHRAG配置:")
    for key in config_keys:
        try:
            value = get_config(key)
            if value:
                print(f"  {key}: {value}")
                # 检查是否已设置到环境变量
                env_value = os.environ.get(key)
                if env_value:
                    print(f"    环境变量已设置: {env_value}")
                else:
                    print(f"    环境变量未设置")
            else:
                print(f"  {key}: 未找到配置")
        except Exception as e:
            print(f"  {key}: 获取失败 - {e}")
    
    print()
    print("当前环境变量中的GRAPHRAG相关配置:")
    for key, value in os.environ.items():
        if key.startswith("GRAPHRAG"):
            print(f"  {key}: {value}")


def main():
    """主函数"""
    print("开始测试...")
    print()
    
    # 检查配置变量
    check_config_variables()
    print()
    
    # 测试 stream_search 方法
    asyncio.run(test_stream_search())


if __name__ == "__main__":
    main() 