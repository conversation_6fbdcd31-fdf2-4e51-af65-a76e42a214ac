import unittest

from robot_studio.database.mysql.repository import ResourceGroupRepository
from robot_studio.database.mysql.table_schema import ResourceGroupDO


class MaterialGroupRepositoryTest(unittest.TestCase):
    _material_group_repo = ResourceGroupRepository()

    def test_get_material_group_by_type(self):
        material_groups = self._material_group_repo.get_all_groups_by_type("素材组")
        print(material_groups)
        self.assertIsNotNone(material_groups)  # add assertion here

    def test_get_material_group_by_subtype(self):
        material_groups = self._material_group_repo.get_groups_by_sub_group_type(group_type="素材组",
                                                                                 sub_group_type="抖音")
        print(material_groups)
        self.assertIsNotNone(material_groups)  # add assertion here

    def test_create_and_del_material(self):
        material_group = self._material_group_repo.create_group(
            ResourceGroupDO(group_name="2025年度小红书暑期早鸟班宣传素材组",
                            group_type="素材组",
                            sub_group_type="小红书",
                            group_desc="壹同未来2025年度小红书暑期早鸟班宣传素材组",
                            cid="CID_98dc78")
        )
        print(material_group)
        self.assertIsNotNone(material_group)
        # del_res = self._material_repo.delete_material(material.mid)
        # self.assertTrue(del_res)

    def test_update_company(self):
        update_data = {
            "group_type": "素材组"
        }
        material_group = self._material_group_repo.update_group(group_id="GID_eae300",
                                                                update_data=update_data)
        print(material_group)
        self.assertIsNotNone(material_group)
        self.assertEqual(material_group.group_type, update_data["group_type"])


if __name__ == '__main__':
    unittest.main()
