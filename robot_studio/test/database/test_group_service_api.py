#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试知识组服务API
"""

import logging
import requests
import json
import unittest

# 设置日志
logger = logging.getLogger(__name__)

# API基础URL
BASE_URL = "http://localhost:8000/group"
class ResourceGroupServiceFastAPITest(unittest.TestCase):
    def test_add_group(self):
        """测试添加知识组API"""
        try:
            # 准备请求数据
            data = {
                "group_name": "API测试知识组",
                "group_desc": "这是一个通过API测试添加的知识组",
                "group_cid": "CID_TEST001"
            }

            # 发送POST请求
            response = requests.post(f"{BASE_URL}/add", json=data)

            # 打印响应
            logger.info(f"添加知识组响应状态码: {response.status_code}")
            logger.info(f"添加知识组响应内容: {response.text}")

            # 验证响应
            if response.status_code == 200:
                result = response.json()
                if result["success"]:
                    logger.info(f"添加知识组成功，ID: {result['group_id']}")
                    return result["group_id"]
                else:
                    logger.error(f"添加知识组失败: {result['error_msg']}")
                    return None
            else:
                logger.error(f"添加知识组请求失败: {response.text}")
                return None

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            return None

    def test_query_groups(self):
        cid = "CID_TEST001"
        """测试查询知识组API"""
        try:
            # 发送GET请求
            response = requests.get(f"{BASE_URL}/list/{cid}")

            # 打印响应
            logger.info(f"查询知识组响应状态码: {response.status_code}")
            logger.info(f"查询知识组响应内容: {response.text}")

            # 验证响应
            if response.status_code == 200:
                result = response.json()
                if result["success"]:
                    logger.info(f"查询知识组成功，共 {len(result['groups'])} 个知识组")
                    return result["groups"]
                else:
                    logger.error(f"查询知识组失败: {result['error_msg']}")
                    return None
            else:
                logger.error(f"查询知识组请求失败: {response.text}")
                return None

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            return None

    def test_update_group(self):
        """测试更新知识组API"""
        group_id = self.test_add_group()
        try:
            # 准备请求数据
            data = {
                "group_name": "API测试知识组(已更新)",
                "group_desc": "这是一个通过API测试更新的知识组"
            }

            # 发送PUT请求
            response = requests.put(f"{BASE_URL}/{group_id}", json=data)

            # 打印响应
            logger.info(f"更新知识组响应状态码: {response.status_code}")
            logger.info(f"更新知识组响应内容: {response.text}")

            # 验证响应
            if response.status_code == 200:
                result = response.json()
                if result["success"]:
                    logger.info(f"更新知识组成功")
                    return True
                else:
                    logger.error(f"更新知识组失败: {result['error_msg']}")
                    return False
            else:
                logger.error(f"更新知识组请求失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            return False

    def test_delete_group(self):
        """测试删除知识组API"""
        group_id = self.test_add_group()
        try:
            # 发送DELETE请求
            response = requests.delete(f"{BASE_URL}/{group_id}")

            # 打印响应
            logger.info(f"删除知识组响应状态码: {response.status_code}")
            logger.info(f"删除知识组响应内容: {response.text}")

            # 验证响应
            if response.status_code == 200:
                result = response.json()
                if result["success"]:
                    logger.info(f"删除知识组成功")
                    return True
                else:
                    logger.error(f"删除知识组失败: {result['error_msg']}")
                    return False
            else:
                logger.error(f"删除知识组请求失败: {response.text}")
                return False

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            return False

    def test_delete_nonexistent_group(self):
        """测试删除不存在的知识组API"""
        try:
            # 发送DELETE请求
            response = requests.delete(f"{BASE_URL}/GID_NONEXISTENT")

            # 打印响应
            logger.info(f"删除不存在知识组响应状态码: {response.status_code}")
            logger.info(f"删除不存在知识组响应内容: {response.text}")

            # 验证响应
            if response.status_code == 404:
                logger.info(f"删除不存在知识组测试成功，返回了正确的404状态码")
                return True
            else:
                logger.error(f"删除不存在知识组测试失败，未返回404状态码")
                return False

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            return False

if __name__ == "__main__":
    unittest.main()
