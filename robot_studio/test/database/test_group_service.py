#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试知识组服务
"""

import logging
import unittest

from robot_studio.data_asset.knowledge.api import KnowledgeGroupService
from robot_studio.data_asset.knowledge.api.request.group_req import GroupCreateReq

# 设置日志
logger = logging.getLogger(__name__)


class ResourceGroupServiceTest(unittest.TestCase):

    def setUp(self):
        # 创建知识组API实例，在每个测试方法前都会执行
        self.api = KnowledgeGroupService()

    def test_add_group(self):
        """测试添加知识组"""
        try:
            # 添加知识组
            req = GroupCreateReq(
                uid="UID_test001",
                cid="CID_0621",
                user_name="潘帅好",
                group_name="课程知识组",
                group_desc="这是一个课程知识的分组"
            )
            result = self.api.add_knowledge_group(req)

            # 打印结果
            logger.info(f"添加知识组结果: {result}")

            # 验证结果
            if result.success:
                logger.info(f"添加知识组成功，ID: {result.group_id}")
            else:
                logger.error(f"添加知识组失败: {result.error_msg}")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")

    def test_delete_group(self):
        """测试删除知识组"""
        try:
            # 先添加一个知识组
            req = GroupCreateReq(
                uid="UID_test002",
                cid="CID_TEST001",
                user_name="test_user",
                group_name="测试软删除知识组",
                group_desc="这是一个测试软删除的知识组",
                tags=["软删除", "测试"]
            )
            add_result = self.api.add_knowledge_group(req)

            if not add_result.success:
                logger.error(f"添加知识组失败: {add_result.error_msg}")
                return

            group_id = add_result.group_id
            logger.info(f"添加知识组成功，ID: {group_id}")

            # 查询知识组（删除前）
            query_before = self.api.query_groups(cid="CID_TEST001")
            logger.info(f"删除前查询结果: {query_before}")

            # 删除知识组
            result = self.api.del_group(group_id=group_id)

            # 打印结果
            logger.info(f"删除知识组结果: {result}")

            # 验证结果
            if result.success:
                logger.info("删除知识组成功")

                # 再次查询知识组（删除后）
                query_after = self.api.query_groups(cid="CID_TEST001")
                logger.info(f"删除后查询结果: {query_after}")

                # 验证软删除后的知识组不再出现在查询结果中
                deleted_group_found = False
                for group in query_after.data:
                    if group.group_id == group_id:
                        deleted_group_found = True
                        break

                if not deleted_group_found:
                    logger.info("软删除功能测试成功，已删除的知识组不再出现在查询结果中")
                else:
                    logger.error("软删除功能测试失败，已删除的知识组仍然出现在查询结果中")

                # 测试重复删除
                repeat_del = self.api.del_group(group_id=group_id)
                logger.info(f"重复删除结果: {repeat_del}")
                if not repeat_del.success and repeat_del.error_code == "ALREADY_DELETED":
                    logger.info("重复删除测试成功，系统正确识别已删除的知识组")
                else:
                    logger.error("重复删除测试失败，系统未正确识别已删除的知识组")
            else:
                logger.error(f"删除知识组失败: {result.error_msg}")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")

    def test_update_group(self):
        """测试更新知识组"""
        try:
            # 先添加一个知识组
            req = GroupCreateReq(
                uid="UID_test003",
                cid="CID_TEST001",
                user_name="test_user",
                group_name="测试更新知识组",
                group_desc="这是一个测试更新的知识组",
                tags=["更新", "测试"]
            )
            add_result = self.api.add_knowledge_group(req)

            if not add_result.success:
                logger.error(f"添加知识组失败: {add_result.error_msg}")
                return

            group_id = add_result.group_id
            logger.info(f"添加知识组成功，ID: {group_id}")

            # 更新知识组
            result = self.api.update_group(
                group_id=group_id,
                group_name="测试知识组更新",
                group_desc="这是一个测试知识组更新"
            )

            # 打印结果
            logger.info(f"更新知识组结果: {result}")

            # 验证结果
            if result.success:
                logger.info("更新知识组成功")

                # 查询知识组（更新后）
                query_after = self.api.query_groups(cid="CID_TEST001")
                logger.info(f"更新后查询结果: {query_after}")

                # 验证更新后的知识组名称和描述
                for group in query_after.data:
                    if group.group_id == group_id:
                        if group.group_name == "测试知识组更新" and group.group_desc == "这是一个测试知识组更新":
                            logger.info("更新功能测试成功，知识组名称和描述已更新")
                        else:
                            logger.error("更新功能测试失败，知识组名称和描述未更新")

                # 删除知识组
                del_result = self.api.del_group(group_id=group_id)
                if del_result.success:
                    logger.info("删除知识组成功")

                    # 测试更新已删除的知识组
                    update_deleted = self.api.update_group(
                        group_id=group_id,
                        group_name="尝试更新已删除的知识组",
                        group_desc="这是一个尝试更新已删除的知识组"
                    )
                    logger.info(f"更新已删除知识组结果: {update_deleted}")

                    if not update_deleted.success and update_deleted.error_code == "ALREADY_DELETED":
                        logger.info("更新已删除知识组测试成功，系统正确识别已删除的知识组")
                    else:
                        logger.error("更新已删除知识组测试失败，系统未正确识别已删除的知识组")
                else:
                    logger.error(f"删除知识组失败: {del_result.error_msg}")
            else:
                logger.error(f"更新知识组失败: {result.error_msg}")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")

    def test_query_group(self):
        """测试查询知识组"""
        try:
            # 先添加一个知识组
            add_result = self.api.add_knowledge_group(
                group_name="测试查询知识组",
                group_desc="这是一个测试查询的知识组",
                group_cid="CID_TEST001"
            )

            if not add_result.success:
                logger.error(f"添加知识组失败: {add_result.error_msg}")
                return

            group_id = add_result.group_id
            logger.info(f"添加知识组成功，ID: {group_id}")

            # 查询知识组（删除前）
            query_before = self.api.query_groups(cid="CID_TEST001")
            logger.info(f"删除前查询结果: {query_before}")

            # 验证新添加的知识组在查询结果中
            group_found = False
            for group in query_before.data:
                if group.group_id == group_id:
                    group_found = True
                    break

            if group_found:
                logger.info("查询功能测试成功，新添加的知识组在查询结果中")
            else:
                logger.error("查询功能测试失败，新添加的知识组不在查询结果中")

            # 删除知识组
            del_result = self.api.del_group(group_id=group_id)
            if del_result.success:
                logger.info("删除知识组成功")

                # 再次查询知识组（删除后）
                query_after = self.api.query_groups(cid="CID_TEST001")
                logger.info(f"删除后查询结果: {query_after}")

                # 验证软删除后的知识组不再出现在查询结果中
                deleted_group_found = False
                for group in query_after.data:
                    if group.group_id == group_id:
                        deleted_group_found = True
                        break

                if not deleted_group_found:
                    logger.info("查询功能测试成功，已删除的知识组不再出现在查询结果中")
                else:
                    logger.error("查询功能测试失败，已删除的知识组仍然出现在查询结果中")
            else:
                logger.error(f"删除知识组失败: {del_result.error_msg}")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")


if __name__ == "__main__":
    unittest.main()
