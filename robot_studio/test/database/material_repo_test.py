import unittest

from robot_studio.database.mysql.repository import MaterialRepository
from robot_studio.database.mysql.table_schema import MaterialDO


class MaterialRepositoryTest(unittest.TestCase):
    _material_repo = MaterialRepository()

    def test_get_material_by_mid(self):
        material = self._material_repo.get_material_by_mid("MID_7129fb")
        print(material)
        self.assertIsNotNone(material)  # add assertion here

    def test_get_material_by_group_id(self):
        materials = self._material_repo.get_material_by_group_id("GID_0719552")
        print(materials)
        self.assertIsNotNone(materials)  # add assertion here

    def test_create_and_del_material(self):
        material = self._material_repo.create_material(
            MaterialDO(name="2025年度小红书暑期英语早鸟班宣传文案",
                       type="DOC",
                       group_id="GID_0719552",
                       source="内部设计",
                       content_digest="2025年度小红书暑期英语早鸟班宣传文案",
                       tags="[小红书,英语早鸟班,2025年度暑秋]"))
        print(material)
        self.assertIsNotNone(material)
        # del_res = self._material_repo.delete_material(material.mid)
        # self.assertTrue(del_res)

    def test_update_company(self):
        update_data = {
            "name": "2025年度抖音暑期英语早鸟班宣传文案",
            "source": "AI生成"
        }
        material = self._material_repo.update_material("MID_25784b", update_data)
        print(material)
        self.assertIsNotNone(material)
        self.assertEqual(material.name, update_data["name"])
        self.assertEqual(material.source, update_data["source"])


if __name__ == '__main__':
    unittest.main()
