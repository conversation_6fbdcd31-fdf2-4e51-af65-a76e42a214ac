import unittest

from robot_studio.database.mysql.repository import CompanyRepository
from robot_studio.database.mysql.table_schema import CompanyDO


class CompanyRepositoryTest(unittest.TestCase):
    _company_repo = CompanyRepository()

    def test_get_all_companies(self):
        companies = self._company_repo.get_all_companies()
        print(companies)
        self.assertIsNotNone(companies)  # add assertion here

    def test_get_company_by_cid(self):
        company = self._company_repo.get_company_by_cid("CID_c43001")
        print(company)
        self.assertIsNotNone(company)  # add assertion here

    def test_create_and_del_company(self):
        company = self._company_repo.create_company(
            CompanyDO(name="青岛壹同未来教育咨询有限公司", abbr="壹同未来", logo="https://www.ytfw.com/logo.png",
                      industry="教育"))
        print(company)
        self.assertIsNotNone(company)
        del_res = self._company_repo.delete_company(company.cid)
        self.assertTrue(del_res)

    def test_update_company(self):
        update_data = {"name": "测试修改-青岛壹同未来教育科技有限公司", "logo": "https://www.ytfw.com/logo2.png"}
        company = self._company_repo.update_company("CID_c43001", update_data)
        print(company)
        self.assertIsNotNone(company)
        self.assertEqual(company.name, update_data["name"])
        self.assertEqual(company.logo, update_data["logo"])


if __name__ == '__main__':
    unittest.main()
