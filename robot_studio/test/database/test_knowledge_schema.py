
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试知识结构服务
"""

import logging
import unittest

from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import CreateKnowledgeSchemaReq

# 设置日志
logger = logging.getLogger(__name__)

class KnowledgeSchemaServiceTest(unittest.TestCase):

    def setUp(self):
        # 创建知识服务API实例，在每个测试方法前都会执行
        self.api = KnowledgeService()

    def test_query_schema(self):
        """测试查询知识结构"""
        try:
            # 查询知识结构
            result = self.api.query_schema(cid="CID_TEST001")

            # 打印结果
            logger.info(f"查询知识结构结果: {result}")

            # 验证结果
            self.assertTrue(result.success, f"查询知识结构失败: {result.error_msg}")
            self.assertIsNotNone(result.data, "查询结果数据为空")
            logger.info(f"查询知识结构成功，共 {len(result.data)} 个知识结构")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            self.fail(f"测试过程中发生异常: {e}")

    def test_create_schema(self):
        """测试创建知识结构"""
        try:
            # 创建知识结构请求
            schema_req = CreateKnowledgeSchemaReq(
                type="测试类型",
                name="测试知识结构",
                desc="这是一个测试知识结构",
                cid="CID_TEST001",
                config=[
                    {
                        "col_name": "标题",
                        "desc": "文档标题",
                        "is_entity": False,
                        "entity_type": None
                    },
                    {
                        "col_name": "内容",
                        "desc": "文档内容",
                        "is_entity": False,
                        "entity_type": None
                    }
                ]
            )

            # 创建知识结构
            result = self.api.create_schema(req=schema_req)

            # 打印结果
            logger.info(f"创建知识结构结果: {result}")

            # 验证结果
            self.assertTrue(result.success, f"创建知识结构失败: {result.error_msg}")
            self.assertIsNotNone(result.data, "创建结果数据为空")
            logger.info(f"创建知识结构成功，ID: {result.data}")

            # 查询验证是否创建成功
            query_result = self.api.query_schema(cid="CID_TEST001")

            # 验证新创建的结构是否在查询结果中
            schema_found = False
            for schema in query_result.data:
                if schema.get("name") == "测试知识结构":
                    schema_found = True
                    break

            self.assertTrue(schema_found, "新创建的知识结构未在查询结果中找到")
            logger.info("验证创建的知识结构已存在于查询结果中")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            self.fail(f"测试过程中发生异常: {e}")

    def test_create_schema_invalid_data(self):
        """测试创建知识结构时提供无效数据"""
        try:
            # 创建知识结构请求 - 缺少必要字段
            schema_req = CreateKnowledgeSchemaReq(
                name="",  # 空名称
                desc="这是一个测试知识结构",
                cid="CID_TEST001",
                schema=[]  # 空结构
            )

            # 创建知识结构
            result = self.api.create_schema(req=schema_req)

            # 打印结果
            logger.info(f"创建无效知识结构结果: {result}")

            # 验证结果 - 应该失败
            self.assertFalse(result.success, "创建无效知识结构应该失败但成功了")
            logger.info(f"创建无效知识结构测试成功，系统正确拒绝了无效数据")

        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            # 这里我们期望可能会抛出异常，所以不使用self.fail

class KnowledgeSchemaAPITest(unittest.TestCase):
    """测试知识结构API端点"""

    def setUp(self):
        # 这里可以设置API测试的准备工作
        # 例如创建测试客户端等
        pass

    def test_query_schema_api(self):
        """测试查询知识结构API端点"""
        try:
            import requests

            # API基础URL
            BASE_URL = "http://localhost:8000/knowledge/schema"

            # 发送POST请求
            response = requests.post(f"{BASE_URL}/CID_TEST001")

            # 打印响应
            logger.info(f"查询知识结构响应状态码: {response.status_code}")
            logger.info(f"查询知识结构响应内容: {response.text}")

            # 验证响应
            self.assertEqual(response.status_code, 200, "API响应状态码不是200")

            result = response.json()
            self.assertTrue(result.get("success"), f"查询知识结构API失败: {result.get('error_msg')}")
            self.assertIsNotNone(result.get("data"), "查询结果数据为空")

            logger.info(f"查询知识结构API测试成功，共 {len(result.get('data', []))} 个知识结构")

        except requests.RequestException as e:
            logger.error(f"API请求过程中发生异常: {e}")
            self.skipTest(f"API请求失败，可能是服务未启动: {e}")
        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            self.fail(f"测试过程中发生异常: {e}")

if __name__ == "__main__":
    unittest.main()