from typing import cast, List

import chainlit as cl
import yaml
from autogen_agentchat.base import Response
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_core import CancellationToken
from autogen_core.models import ChatCompletionClient

from robot_studio.component.agent import AssistantAgent
from robot_studio.component.tool import *

# 导入认证模块 无引用但不要删除
# from robot_studio.auth.chainlit_auth import auth_callback

PROMPT = """## 角色
你扮演一位教育培训学校（壹同未来）的售前客服专家（小一老师），具备高效解决家长咨询、引导家长参加学校课程试听、入学测评和课程报名等职能。

## 背景
1. 术语说明：创新思维=数学、人文素养=语文、国际表达=英语、物质理论=物理、科学探究=化学。
2. 不同科目，针对不同年级学生需求，设计了不同班型，各班型难易程度、授课重点不同，可查看具体科目的课程体系介绍。
3. 教育培训有四个期段，分别是暑假班、寒假班，春季学期班，秋季学期班。假期班侧重预习下学期内容，学期班和校内授课互补，进度略快，侧重应用和拓展
4. 具体年级、科目、期段、班型下，会开设多个班级。如初一创新思维2025暑中考班，会开设多期（即多个班级）。这些班级授课内容和费用相同，但上课时间、地点（校区）、老师可能不同，主要是为了给家长更多时间、地点上的灵活选择空间
5. 我们开设在青岛市，共有7各校区，分别是阳光校区、浮山后校区、延安三路一校区、延安三路二校区、南京路校区、新都心和达校区和中山路校区！	
注意：目前正是2025年暑假班和秋季班的报名咨询阶段

## 技能
### 技能1：家长需求识别与细化
- **需求识别**：接收到家长咨询时，迅速理解核心需求。家长的需求通常分为几类：
  1. 课程咨询：了解所在年级某科目的课程内容、课程时间、班型划分等。
  2. 教师咨询：了解具体授课教师情况。
  3. 校区咨询：了解具体上课的地点，大多选择距离自己较近校区
  4. 试听/评测咨询：如试听课程或者入学评测的具体时间、地点等。
- **需求细化**：家长初次咨询的问题往往比较模糊，需要通过进一步询问来明确具体情况和真实诉求。

### 技能2：专业话术回复与引导
- **专业话术**：回复力求精简且口语化，模拟日常聊天语气，如有图片或海报链接，尽可能回复图片，如"这是课程班，您先看下"，然后渲染具体图片。
- **引导报名**：识别用户需求后，除了回答咨询外，还应尽可能引导用户参加试听和测评。报名流程是进行入学测评后按成绩分班。
- **多段回复**：可以给用户发送多条消息，采用渐进式提问或回答的方式引导用户。避免将所有信息糅合在一段很长的内容中。多段回复请使用“；”分隔。

## 注意
1. 图片链接是使用了OSS的URL，请你不要转义，否则链接会异常！请你直接渲染图片
2. 如果你打算询问用户，请不要返回调用工具的指令！明确需要的信息后再进行工具调用！

"""


@cl.on_chat_start  # type: ignore
async def start_chat() -> None:
    # 获取当前认证用户信息
    user = cl.user_session.get("user")

    # Load event configuration and create the event client.
    with open("robot_studio/chainlit/model_config.yaml", "r") as f:
        model_config = yaml.safe_load(f)
    model_client = ChatCompletionClient.load_component(model_config)

    # Create the assistant component with tools
    rag_tool = GraphRagTool()
    course_system_tool = CourseSystemSearchTool()
    course_product_tool = CourseProductSearchTool()
    class_info_tool = ClassInfoSearchTool()
    school_tool = SchoolSearchTool()
    teacher_tool = TeacherSearchTool()
    assistant = AssistantAgent(
        name="customer_service_teacher",
        tools=[course_system_tool, course_product_tool, class_info_tool, school_tool, teacher_tool],
        model_client=model_client,
        system_message=PROMPT,
        model_client_stream=True,  # Enable event client streaming.
        reflect_on_tool_use=True,  # Reflect on tool use.
    )
    cl.user_session.set("prompt_history", "")  # type: ignore
    cl.user_session.set("component", assistant)  # type: ignore

    # 根据用户角色发送不同的欢迎消息
    if user:
        role = user.metadata.get("role", "visitor")
        if role == "admin":
            await cl.Message(f"欢迎管理员 {user.identifier} 使用壹同未来客服助手！").send()
        elif role == "teacher":
            await cl.Message(f"欢迎老师 {user.identifier} 使用壹同未来客服助手！").send()
        else:
            await cl.Message(f"欢迎家长 {user.identifier} 使用壹同未来客服助手！您可以咨询课程、教师或校区相关信息。").send()


@cl.on_message  # type: ignore
async def chat(message: cl.Message) -> None:
    # Get the assistant component from the user session.
    agent = cast(AssistantAgent, cl.user_session.get("component"))  # type: ignore
    # Construct the response message.
    response = cl.Message(content="", author="assistant")
    buffer = ""
    async for msg in agent.on_messages_stream(
            messages=[TextMessage(content=message.content, source="user")],
            cancellation_token=CancellationToken(),
    ):
        if isinstance(msg, ModelClientStreamingChunkEvent):
            # 对md中的特殊字段转义，'~' 会导致渲染时出现删除线！
            content = msg.content.replace("~", "\\~")

            # 中文分号，代表模型认为当前需要分段回复
            if '；' in content:
                # 分割内容
                parts = content.split('；')
                # 将第一部分添加到当前缓冲区
                buffer += parts[0]
                # 发送当前消息
                await response.stream_token(parts[0])
                await response.send()

                # 处理剩余部分（如果有多个分号）
                for i in range(1, len(parts) - 1):
                    # 创建新消息并发送
                    new_msg = cl.Message(content=parts[i], author="assistant")
                    await new_msg.send()

                # 开始新响应消息（最后一部分）
                if len(parts) > 1:
                    buffer = parts[-1]
                    response = cl.Message(content=buffer, author="assistant")
                else:
                    buffer = ""
                    response = cl.Message(content="", author="assistant")
            else:
                # 正常累积内容
                buffer += content
                await response.stream_token(content)
        elif isinstance(msg, Response):
            # 发送最后一条消息（如果有内容）
            if buffer.strip():
                await response.send()


@cl.set_starters  # type: ignore
async def set_starts() -> List[cl.Starter]:
    return [
        cl.Starter(
            label="整体课程体系",
            message="小一你好，我想咨询下具体科目的课程体系~",
        ),
        cl.Starter(
            label="暑秋课程咨询",
            message="小一你好，我想咨询今年的课程~",
        ),
        cl.Starter(
            label="教师信息咨询",
            message="小一你好，我想咨询教师信息~",
        ),
        cl.Starter(
            label="校区信息咨询",
            message="小一你好，我想咨询校区信息~",
        )
    ]
