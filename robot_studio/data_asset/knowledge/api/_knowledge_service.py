import logging

from robot_studio.auth.core import UserManager
from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.knowledge.api.request.knowledge_req import CreateKnowledgeReq, UpdateKnowledgeReq, \
    GroupKnowledgeReq, DelKnowledgeReq, OnlineReq, CreateTableSchemaReq, KnowledgeVO, UpdateSchemaReq, \
    RetrieveKnowledgeReq
from robot_studio.data_asset.knowledge.api.result.knowledge_res import CreateKnowledgeRes, KnowledgeSchemaRes, \
    CreateKnowledgeSchemaRes, KnowledgeRes, KnowledgeListRes, KnowledgeUpdateRes, KnowledgeSchemaListRes
from robot_studio.data_asset.knowledge.bailian import BailianKnowledgeService, RetrivalRes
from robot_studio.data_asset.knowledge.core import KnowledgeManager, KnowledgeSchemaManager

logger = logging.getLogger(__name__)


class KnowledgeService:

    def __init__(self):
        self._knowledge_manager = KnowledgeManager()
        self._schema_manager = KnowledgeSchemaManager()
        self._user_manager = UserManager()
        self._bailian_service = BailianKnowledgeService()

    @api_handler
    def create_knowledge(self, req: CreateKnowledgeReq):
        """
        创建知识
        Args:
            req:

        Returns:

        """
        knowledge = self._knowledge_manager.create(req.to_model())
        return CreateKnowledgeRes(data=knowledge.knowledge_id)

    @api_handler
    def query_schema_by_cid(self, cid):
        """
        查询知识schema
        :param cid: 企业ID
        :return: 知识Schema列表
        """
        knowledge_schemas = self._schema_manager.query_schema_by_cid(cid)
        return KnowledgeSchemaListRes(data=[schema.model_dump() for schema in knowledge_schemas])

    @api_handler
    def query_schema_by_sid(self, schema_id: str):
        """
        查询知识schema
        :param schema_id: 知识SchemaID
        :return: 知识Schema
        """
        knowledge_schema = self._schema_manager.query_schema_by_sid(schema_id)
        if not knowledge_schema:
            return KnowledgeSchemaRes(data=None)
        return KnowledgeSchemaRes(data=knowledge_schema.model_dump())

    def create_schema(self, req: CreateTableSchemaReq):
        """
        创建知识Schema
        Args:
            req:

        Returns:

        """
        schema = self._schema_manager.create(req.to_model())
        return CreateKnowledgeSchemaRes(data=schema.schema_id)

    @api_handler
    def query_knowledge_detail(self, knowledge_id: str):
        """
        查询知识详情
        Args:
            knowledge_id: 知识ID

        Returns:
            KnowledgeRes: 包含KnowledgeVO的响应
        """
        knowledge = self._knowledge_manager.query_knowledge_by_kid(knowledge_id)
        if not knowledge:
            return KnowledgeRes(data=None)

        # 转换为KnowledgeVO
        knowledge_vo = KnowledgeVO.to_vo(knowledge)

        # 根据create_uid查询用户信息并设置create_name
        if knowledge.create_uid:
            user = self._user_manager.query_user_by_uid(knowledge.create_uid)
            if user:
                knowledge_vo.create_name = user.username

        return KnowledgeRes(data=knowledge_vo)

    @api_handler
    def query_group_knowledge(self, req: GroupKnowledgeReq):
        """
        根据知识组ID查询知识
        Args:
            req:
        Returns:
            知识列表
        """
        if not req.group_id:
            return KnowledgeListRes(data=[])

        knowledge_list = self._knowledge_manager.query_group_knowledge(req.group_id)
        if not knowledge_list:
            return KnowledgeListRes(data=[])

        # 转换为KnowledgeVO列表
        vo_list = []
        for knowledge in knowledge_list:
            # 创建基础KnowledgeVO
            knowledge_vo = KnowledgeVO.to_vo(knowledge)

            # 根据create_uid查询用户信息并设置create_name
            if knowledge.create_uid:
                user = self._user_manager.query_user_by_uid(knowledge.create_uid)
                if user:
                    knowledge_vo.create_name = user.username

            vo_list.append(knowledge_vo)

        return KnowledgeListRes(data=vo_list)

    @api_handler
    async def batch_online(self, req: OnlineReq):
        """
        上线知识
        Args:
            req:
        Returns:

        """
        try:
            result = await self._knowledge_manager.batch_online(req.ids)
            return KnowledgeUpdateRes(data=result)
        except Exception as e:
            logger.error(f"online knowledge failed: {str(e)}")
            return KnowledgeUpdateRes(data=False, error_msg=f"上线知识失败: {str(e)}")

    @api_handler
    async def offline_knowledge_by_id(self, id: int):
        """
        下线知识
        Args:
            id: 知识id

        Returns:

        """
        try:
            result = await self._knowledge_manager.offline(id)
            return KnowledgeUpdateRes(data=result)
        except Exception as e:
            logger.error(f"offline knowledge failed: {str(e)}")
            return KnowledgeUpdateRes(data=False, error_msg=f"下线知识失败: {str(e)}")

    @api_handler
    async def batch_del(self, req: DelKnowledgeReq):
        """
        批量删除知识
        Args:
            req:
            id: 知识id

        Returns:

        """
        try:
            await self._knowledge_manager.batch_del(req.del_ids)
            return BaseResult.success_result()
        except Exception as e:
            logger.error(f"delete knowledge failed: {str(e)}")
            return BaseResult(success=False, error_msg=f"删除知识失败: {str(e)}")

    @api_handler
    def update_knowledge(self, update_knowledge: UpdateKnowledgeReq):
        """
        更新知识内容
        Args:
            update_knowledge: 待更新的知识内容

        Returns:

        """
        knowledge = update_knowledge.to_model()
        knowledge = self._knowledge_manager.update_knowledge_content(knowledge)
        assert knowledge, "知识更新失败"
        return KnowledgeUpdateRes(data=True)

    @api_handler
    def delete_schema_by_id(self, schema_id: str):
        """
        根据schema_id物理删除知识Schema
        Args:
            schema_id: 知识Schema ID

        Returns:
            BaseResult: 删除结果
        """
        try:
            result = self._schema_manager.delete_schema_by_id(schema_id)
            return BaseResult(success=result, data=result)
        except Exception as e:
            logger.error(f"delete schema failed: {str(e)}")
            return BaseResult(success=False, error_msg=f"删除知识Schema失败: {str(e)}")

    @api_handler
    def update_schema(self, req: UpdateSchemaReq):
        """
        更新知识Schema
        Args:
            req: UpdateSchemaReq

        Returns:
            BaseResult: 更新结果
        """
        try:
            schema = req.to_model()
            updated_schema = self._schema_manager.update_schema(schema)
            if updated_schema:
                return BaseResult.success_result(data=updated_schema.model_dump())
            else:
                return BaseResult(success=False, error_msg="知识Schema不存在或更新失败")
        except Exception as e:
            logger.error(f"update schema failed: {str(e)}")
            return BaseResult(success=False, error_msg=f"更新知识Schema失败: {str(e)}")

    @api_handler
    async def retrieve_knowledge(self, req: RetrieveKnowledgeReq) -> RetrivalRes:
        """
        根据知识ID和查询内容召回知识
        Args:
            req: 知识检索请求

        Returns:
            RetrivalRes: 检索结果
        """
        try:
            # 1. 根据knowledge_id直接查询线上版本的知识
            knowledge = self._knowledge_manager.query_online_knowledge_by_kid(req.knowledge_id)
            if not knowledge:
                return RetrivalRes(success=False, message=f"线上知识不存在，知识ID: {req.knowledge_id}")

            # 2. 获取bailian_index_id
            if not knowledge.bailian_index_id:
                return RetrivalRes(success=False, message=f"知识未同步到百炼知识库，无法检索，知识ID: {req.knowledge_id}")

            # 3. 调用BailianKnowledgeService的run_retrival_async方法
            retrieval_result = await self._bailian_service.run_retrival_async(
                knowledge_index_id=knowledge.bailian_index_id,
                query=req.query
            )

            return retrieval_result

        except Exception as e:
            logger.error(f"retrieve knowledge failed: {str(e)}")
            return RetrivalRes(success=False, message=f"检索知识失败: {str(e)}")

    @api_handler
    def query_online_knowledge_by_cid(self, cid: str) -> KnowledgeListRes:
        """
        查询某个企业下所有在线状态的知识列表，排除大字段以提升查询效率，同时要求知识在有效期内！
        Args:
            cid: 企业ID

        Returns:
            KnowledgeListRes: 在线知识列表响应
        """
        knowledge_list = self._knowledge_manager.query_online_knowledge_by_cid(cid)
        if not knowledge_list:
            return KnowledgeListRes(data=[])

        # 转换为KnowledgeVO列表，只返回当前处于有效期内的知识
        vo_list = []
        for knowledge in knowledge_list:
            # 检查知识是否当前处于有效期内
            if not knowledge.now_is_valid():
                continue

            # 创建基础KnowledgeVO
            knowledge_vo = KnowledgeVO.to_vo(knowledge)

            # 根据create_uid查询用户信息并设置create_name
            if knowledge.create_uid:
                user = self._user_manager.query_user_by_uid(knowledge.create_uid)
                if user:
                    knowledge_vo.create_name = user.username

            vo_list.append(knowledge_vo)

        return KnowledgeListRes(data=vo_list)

    @api_handler
    def query_online_knowledge_by_kid_list(self, knowledge_ids: list[str]) -> KnowledgeListRes:
        """
        根据知识ID列表查询在线状态的知识列表，排除大字段以提升查询效率，同时要求知识在有效期内！
        Args:
            knowledge_ids: 知识ID列表

        Returns:
            KnowledgeListRes: 在线知识列表响应
        """
        if not knowledge_ids:
            return KnowledgeListRes(data=[])

        # 转换为KnowledgeVO列表，只返回当前处于有效期内的知识
        vo_list = []
        for knowledge_id in knowledge_ids:
            # 查询线上版本的知识
            knowledge = self._knowledge_manager.query_online_knowledge_by_kid(knowledge_id)
            if not knowledge:
                continue
                
            # 检查知识是否当前处于有效期内
            if not knowledge.now_is_valid():
                continue

            # 创建基础KnowledgeVO
            knowledge_vo = KnowledgeVO.to_vo(knowledge)

            # 根据create_uid查询用户信息并设置create_name
            if knowledge.create_uid:
                user = self._user_manager.query_user_by_uid(knowledge.create_uid)
                if user:
                    knowledge_vo.create_name = user.username

            vo_list.append(knowledge_vo)

        return KnowledgeListRes(data=vo_list)
