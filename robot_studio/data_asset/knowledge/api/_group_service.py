import logging

from robot_studio.common.base_result import BaseResult
from robot_studio.common.api_handler import api_handler
from robot_studio.data_asset.knowledge.api.request.group_req import GroupCreateReq, GroupUpdateReq, GroupQueryReq, \
    GroupDeleteReq
from robot_studio.data_asset.knowledge.api.result.group_res import (
    GroupCreateRes,
    GroupListRes,
    GroupRes,
)
from robot_studio.data_asset.knowledge.core import KnowledgeGroupManager
from robot_studio.data_asset.knowledge.core import KnowledgeManager

logger = logging.getLogger(__name__)


class KnowledgeGroupService:

    def __init__(self):
        self._group_manager = KnowledgeGroupManager()
        self._knowledge_manager = KnowledgeManager()

    @api_handler
    def add_knowledge_group(self, req: GroupCreateReq) -> GroupCreateRes:
        """添加知识组
        Args:
            req: 创建知识组请求
                group_name: 分组名称
                group_desc: 分组描述
                cid: 企业ID
                tags: 标签数组
                create_user: 创建人
        Returns:
            "group_id":"GID_0928726", # 分组ID
            "success":true, # 分组描述
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 使用请求对象创建知识组模型
            new_group = req.to_model()
            if not new_group.tags:
                new_group.tags = []
            new_group.tags.append("内部数据")

            # 添加到数据库
            result = self._group_manager.create_knowledge_group(new_group)

            # 返回成功结果
            return GroupCreateRes(group_id=result.group_id or "", success=True)

        except Exception as e:
            logger.error(f"添加知识组失败: {e}")
            # 返回失败结果
            return GroupCreateRes(
                group_id="",
                success=False,
                error_msg=f"添加知识组失败: {str(e)}",
                error_code="DB_ERROR",
            )

    @api_handler
    def del_group(self, req: GroupDeleteReq) -> BaseResult:
        """删除知识组（软删除）
        Args:
            req: 删除知识组请求
                group_id: 知识组ID
                uid: 用户ID
                cid: 企业ID
                user_name: 用户名
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 软删除知识组记录
            result = self._group_manager.delete_group(req.group_id)

            if not result:
                return BaseResult(
                    success=False,
                    error_msg=f"删除知识组失败，可能是知识组不存在或已被删除: {req.group_id}",
                    error_code="DELETE_FAILED",
                )

            # 返回成功结果
            return BaseResult.success_result()

        except Exception as e:
            logger.error(f"删除知识组失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"删除知识组失败: {str(e)}",
                error_code="DB_ERROR",
            )

    @api_handler
    def update_group(self, req: GroupUpdateReq) -> BaseResult:
        """更新知识组
        Args:
            req: 更新知识组请求
                group_id: 知识组ID
                group_name: 知识组名称
                group_desc: 知识组描述
                tags: 标签数组
                uid: 用户ID
                cid: 企业ID
                user_name: 用户名
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 更新知识组记录
            update_data = {
                "group_name": req.group_name,
                "group_desc": req.group_desc,
                "custom_tags": req.tags,
                "username": req.user_name,
                "uid": req.uid,
                "tags": req.tags
            }

            result = self._group_manager.update_group(req.group_id, update_data)

            if not result:
                return BaseResult(
                    success=False,
                    error_msg=f"更新知识组失败，可能是知识组不存在或已被删除: {req.group_id}",
                    error_code="UPDATE_FAILED",
                )

            # 返回成功结果
            return BaseResult.success_result()

        except Exception as e:
            logger.error(f"更新知识组失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"更新知识组失败: {str(e)}",
                error_code="DB_ERROR",
            )

    @api_handler
    def query_groups(self, req: GroupQueryReq) -> GroupListRes:
        """查询一个cid下的所有知识组
        Args:
            req: 查询知识组请求
                cid: 企业id
                uid: 用户ID
                user_name: 用户名
        Returns:
            "group_id":"GID_0928726", # 分组ID
            "group_name":"课程知识组", # 分组名称
            "group_desc":"壹同未来开设的课程相关知识", # 分组描述
            "group_type":"知识组", # 分组类型
            "cid":"CID_0001", # 企业ID
            "gmt_create":"2024-01-01 00:00:00", # 创建时间
            "create_user":"创建人", # 创建人
            "num":5, # 分组下的文档数量
            "tags":["标签1","标签2"], # 标签数组
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 查询知识组记录（只返回未删除的记录）
            groups = self._group_manager.get_groups_by_cid(req.cid)

            # 将KnowledgeGroup对象转换为GroupRes对象
            group_res_list = []
            for group in groups:
                group_data = group.model_dump()

                # 获取分组下的知识数量
                knowledge_count = 0
                if group.group_id:
                    knowledge_list = self._knowledge_manager.query_group_knowledge(group.group_id)
                    knowledge_count = len(knowledge_list) if knowledge_list else 0

                # 获取tags字段（现在直接就是数组）
                tags_list = group.tags if group.tags else []

                # 确保所有必需字段都存在
                group_res = GroupRes(
                    group_id=group_data.get("group_id", ""),
                    group_name=group_data.get("group_name", ""),
                    group_desc=group_data.get("group_desc", ""),
                    group_type=group_data.get("group_type", ""),
                    cid=group_data.get("cid", ""),
                    gmt_create=str(group_data.get("gmt_create", "")),
                    gmt_modified=str(group_data.get("gmt_modified", "")),
                    create_user=group.create_user,  # 获取创建人
                    num=knowledge_count,  # 分组下的知识数量
                    tags=tags_list,  # 标签数组
                )
                group_res_list.append(group_res)

            # 返回成功结果
            return GroupListRes(data=group_res_list, success=True)

        except Exception as e:
            logger.error(f"查询知识组失败: {e}")
            # 返回失败结果
            return GroupListRes(
                data=[],
                success=False,
                error_msg=f"查询知识组失败: {str(e)}",
                error_code="DB_ERROR",
            )
