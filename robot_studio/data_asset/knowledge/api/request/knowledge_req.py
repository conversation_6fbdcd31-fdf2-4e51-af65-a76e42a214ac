from datetime import datetime
from typing import List, Any, Self

from pydantic import BaseModel, Field

from robot_studio.common.base_request import BaseRequest
from robot_studio.data_asset.knowledge.model import (
    Knowledge,
    KnowledgeSchema,
    SchemaColConfig,
    SchemaType,
    KnowledgeItem,
    ColItem,
)


class Col(BaseModel):
    """列Item"""

    col_name: str = Field(..., description="表格的列名")
    col_value: Any | None = Field(..., description="具体列值")


class Row(BaseModel):
    """列Item"""

    cols: List[Col] | None = Field(description="表格单行的所有列")


class BaseKnowledgeVO(BaseModel):
    """创建知识请求"""

    name: str = Field(..., description="知识名称")

    desc: str = Field(..., description="知识摘要")

    tags: List[str] | None = Field(default=None, description="自定义标签")

    group_id: str = Field(..., description="关联的知识组ID")

    longtime_valid: bool = Field(default=False, description="是否长期有效")

    valid_date: datetime = Field(
        default_factory=datetime.now, description="知识生效日期"
    )

    invalid_date: datetime | None = Field(default=None, description="知识失效日期")

    content_type: str = Field(..., description="知识格式，文档DOCUMENT/表格TABLE")

    schema_id: str | None = Field(
        default=None, description="表格模式下，具体的表格结构ID"
    )

    table_data: List[Row] | None = Field(default_factory=list, description="表格数据")

    document_content: str | None = Field(default="", description="自定义文本内容")

    def to_base_model(self) -> Knowledge:
        """转换为模型"""
        knowledge_items = []
        if self.table_data:
            for row in self.table_data:
                # 将每个 KnowledgeItemReq 转换为 KnowledgeItem
                col_items = [
                    ColItem(col_name=col.col_name, col_value=col.col_value)
                    for col in (row.cols or [])
                ]
                knowledge_item = KnowledgeItem(content=col_items)
                knowledge_item.gen_content_uuid()  # 生成 UUID
                knowledge_items.append(knowledge_item)

        return Knowledge(
            name=self.name,
            desc=self.desc,
            tags=self.tags if self.tags else [],
            longtime_valid=self.longtime_valid,
            valid_date=self.valid_date,
            invalid_date=self.invalid_date,
            content_type=self.content_type,
            content_document=self.document_content,
            schema_id=self.schema_id,
            content_structure=knowledge_items,
            group_id=self.group_id,
        )


class KnowledgeVO(BaseKnowledgeVO):
    id: int | None = Field(description="资产ID")
    """资产ID"""

    knowledge_id: str | None = Field(description="知识ID")
    """知识ID"""

    status: str | None = Field(description="知识状态")
    """关联资产状态"""

    version: int | None = Field(description="知识的版本号，每次修改->推进上线后会+1")
    """知识的版本号，每次修改->推进上线后会+1"""

    gmt_create: datetime | None = Field(description="知识的创建日期")
    """创建日期"""

    gmt_modified: datetime | None = Field(description="知识的修改日期")
    """修改日期"""

    create_uid: str | None = Field(description="知识的创建日期")
    """创建人UID"""

    create_name: str | None = Field(default=None, description="知识的创建人姓名")
    """创建人姓名"""

    @classmethod
    def to_vo(cls, knowledge: Knowledge) -> Self:
        """将Knowledge模型转换为KnowledgeVO实例"""
        # 处理table_data - 将content_structure转换为table_data格式
        table_data = []
        if knowledge.content_structure:
            for item in knowledge.content_structure:
                if item.content:
                    cols = [
                        Col(col_name=col.col_name, col_value=col.col_value)
                        for col in item.content
                    ]
                    table_data.append(Row(cols=cols))

        return cls(
            # BaseKnowledgeVO字段
            name=knowledge.name or "",
            desc=knowledge.desc or "",
            tags=knowledge.tags or [],
            group_id=knowledge.group_id or "",
            longtime_valid=knowledge.longtime_valid or False,
            valid_date=knowledge.valid_date or datetime.now(),
            invalid_date=knowledge.invalid_date,
            content_type=knowledge.content_type or "",
            schema_id=knowledge.schema_id or "",
            table_data=table_data,
            document_content=knowledge.content_document or "",
            # KnowledgeVO特有字段
            id=knowledge.id,
            knowledge_id=knowledge.knowledge_id,
            status=knowledge.status,
            version=knowledge.version,
            gmt_create=knowledge.gmt_create,
            gmt_modified=knowledge.gmt_modified,
            create_uid=knowledge.create_uid,
            create_name=knowledge.create_name,
        )


class CreateKnowledgeReq(BaseKnowledgeVO, BaseRequest):
    """创建知识请求"""

    def to_model(self) -> Knowledge:
        k = self.to_base_model()
        k.create_uid = self.uid
        k.source = "DATA"
        return k


class UpdateKnowledgeReq(BaseKnowledgeVO, BaseRequest):
    """更新知识请求"""

    knowledge_id: str = Field(..., description="待更新的知识ID")

    def to_model(self) -> Knowledge:
        k = self.to_base_model()
        k.knowledge_id = self.knowledge_id
        return k


class DelKnowledgeReq(BaseRequest):
    del_ids: List[int] = Field(..., description="待删除的知识列表")


class OnlineReq(BaseRequest):
    ids: List[int] = Field(..., description="待上线的知识列表")


class GroupKnowledgeReq(BaseRequest):
    group_id: str | None = Field(..., description="查询的具体分组名")


class SingleKnowledgeReq(BaseRequest):
    knowledge_id: str | None = Field(..., description="知识ID")


class ColConfig(BaseModel):
    name: str = Field(..., description="列名")
    """具体列名，如课程名称"""

    desc: str | None = Field(default="", description="列名描述")
    """列名描述"""

    is_entity: bool = Field(default=False, description="是否主体列")

    entity_name: str | None = Field(description="主体名称")


class CreateTableSchemaReq(BaseRequest):
    """知识schema创建请求"""

    type: str | None = Field(description="知识结构类型")
    name: str = Field(..., description="知识结构名称")
    desc: str = Field(..., description="知识结构描述")
    config: List[ColConfig] = Field(..., description="知识结构配置")

    def to_model(self) -> KnowledgeSchema:
        """转换为模型"""
        return KnowledgeSchema(
            type=self.type if self.type else SchemaType.DEFAULT.value,
            name=self.name,
            desc=self.desc,
            config=[
                SchemaColConfig(
                    col_name=item.name,
                    desc=item.desc,
                    is_entity=item.is_entity,
                    entity_type=item.entity_name,
                )
                for item in self.config
            ],
            cid=self.cid,
        )


class UpdateSchemaReq(CreateTableSchemaReq):
    schema_id: str = Field(..., description="具体要更新的表格结构配置ID")

    def to_model(self) -> KnowledgeSchema:
        """转换为模型"""
        return KnowledgeSchema(
            type=self.type if self.type else SchemaType.DEFAULT.value,
            name=self.name,
            desc=self.desc,
            config=[
                SchemaColConfig(
                    col_name=item.name,
                    desc=item.desc,
                    is_entity=item.is_entity,
                    entity_type=item.entity_name,
                )
                for item in self.config
            ],
            cid=self.cid,
            schema_id=self.schema_id,
        )


class DelSchemaReq(BaseRequest):
    schema_id: str = Field(..., description="具体要删除的表格结构配置ID")


class RetrieveKnowledgeReq(BaseRequest):
    """知识检索请求"""
    
    knowledge_id: str = Field(..., description="知识ID")
    query: str = Field(..., description="查询内容")
    top_k:int = Field(default=10, description="返回数量")
    min_score: float = Field(default=0.5, description="最小得分")
