from typing import List, Optional
from pydantic import BaseModel

from robot_studio.common.base_request import BaseRequest
from robot_studio.data_asset.common.base_group import GroupType


# 请求模型
class GroupCreateReq(BaseRequest):
    group_name: str
    group_desc: str
    tags: Optional[List[str]] = []  # 标签数组

    def to_model(self):
        """转换为KnowledgeGroup模型"""
        from robot_studio.data_asset.knowledge.model import KnowledgeGroup

        return KnowledgeGroup(
            group_name=self.group_name,
            group_desc=self.group_desc,
            cid=self.cid,  # 继承自BaseRequest
            tags=self.tags,
            create_user=self.user_name,  # 使用BaseRequest的user_name作为创建人
            create_uid=self.uid,  # 使用BaseRequest的uid作为创建人ID
            group_type=GroupType.KNOWLEDGE.name,
        )


class GroupUpdateReq(BaseRequest):
    group_id: str
    group_name: str
    group_desc: str
    tags: Optional[List[str]] = []  # 标签数组


class GroupQueryReq(BaseRequest):
    pass  # 查询只需要BaseRequest中的cid字段


class GroupDeleteReq(BaseRequest):
    group_id: str  # 要删除的知识组ID
