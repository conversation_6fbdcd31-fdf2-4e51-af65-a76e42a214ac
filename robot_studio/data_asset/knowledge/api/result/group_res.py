from typing import Optional, List

from robot_studio.common.base_result import BaseResult


class GroupRes(BaseResult):
    group_id: str
    group_name: str
    group_desc: Optional[str] = None
    group_type: str
    cid: str
    gmt_create: str
    gmt_modified: str
    create_user: Optional[str] = None  # 创建人
    num: int = 0  # 分组下的文档数量
    tags: List[str] = []  # 标签数组


class GroupListRes(BaseResult):
    data: List[GroupRes] = []


class GroupCreateRes(BaseResult):
    group_id: str = ""
