from typing import List

from pydantic import Field

from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.knowledge.api.request.knowledge_req import KnowledgeVO
from robot_studio.data_asset.knowledge.model import Knowledge


class CreateKnowledgeRes(BaseResult):
    data: str | None = Field(default=None, description="创建的具体知识的ID")


class CreateKnowledgeSchemaRes(BaseResult):
    data: str | None = Field(default=None, description="创建的知识Schema的ID")


class KnowledgeSchemaListRes(BaseResult):
    data: list[dict] | None = Field(default=None, description="知识Schema列表")


class KnowledgeSchemaRes(BaseResult):
    data: dict | None = Field(default=None, description="知识Schema列表")


class KnowledgeRes(BaseResult):
    data: KnowledgeVO | None = Field(default=None, description="知识详情")


class KnowledgeListRes(BaseResult):
    data: List[KnowledgeVO] | None = Field(default=None, description="具体的知识列表")


class KnowledgeUpdateRes(BaseResult):
    data: bool | None = Field(default=None, description="更新结果")
