import logging
from typing import List, Dict, Any, Optional

from robot_studio.data_asset.common.base_group import GroupType
from robot_studio.data_asset.knowledge.model import KnowledgeGroup
from robot_studio.database.mysql.repository import ResourceGroupRepository

logger = logging.getLogger(__name__)


class KnowledgeGroupManager:
    """知识组管理器
    提供服务如下：
    1. 新增知识组
    2. 更新知识组
    3. 软删除知识组
    4. 查询知识组
    """

    def __init__(self):
        self._group_repo = ResourceGroupRepository()

    def create_knowledge_group(self, group: KnowledgeGroup) -> KnowledgeGroup:
        """
        创建知识组
        Args:
            group: 知识组信息

        Returns:
            ResourceGroup: 创建后的知识组信息
        """
        logger.info(f"添加知识组: {group.group_name}")

        # 确保类型为知识组
        group.group_type = GroupType.KNOWLEDGE.name

        # 转换为DO对象并创建
        group_do = group.to_do()
        result = self._group_repo.create_group(group_do)
        return KnowledgeGroup.from_do(result)

    def delete_group(self, group_id: str) -> bool:
        """
        软删除知识组
        Args:
            group_id: 知识组ID

        Returns:
            bool: 删除结果
        """
        logger.info(f"软删除知识组: {group_id}")

        # 查询知识组是否存在（包括已删除的）
        group = self._group_repo.get_group_by_id_ignore_deleted(group_id)

        if not group:
            logger.warning(f"知识组不存在: {group_id}")
            return False

        # 检查知识组是否已经被删除
        if group.is_deleted:
            logger.warning(f"知识组已经被删除: {group_id}")
            return False

        # 软删除知识组记录
        return self._group_repo.delete_group(group_id)

    def update_group(self, group_id: str, update_data: Dict[str, Any]) -> Optional[KnowledgeGroup]:
        """
        更新知识组
        Args:
            group_id: 知识组ID
            update_data: 更新数据字典

        Returns:
            ResourceGroup: 更新后的知识组信息
        """
        logger.info(f"更新知识组: {group_id}, 数据: {update_data}")

        # 查询知识组是否存在（包括已删除的）
        group = self._group_repo.get_group_by_id_ignore_deleted(group_id)

        if not group:
            logger.warning(f"知识组不存在: {group_id}")
            return None

        # 检查知识组是否已经被删除
        if group.is_deleted:
            logger.warning(f"知识组已经被删除: {group_id}")
            return None

        # 更新知识组记录
        result = self._group_repo.update_group(group_id, update_data)

        if result:
            updated_group = self._group_repo.get_group_by_id(group_id)
            return KnowledgeGroup.from_do(updated_group) if updated_group else None
        return None

    def get_group_by_id(self, group_id: str) -> Optional[KnowledgeGroup]:
        """
        根据ID获取知识组
        Args:
            group_id: 知识组ID

        Returns:
            ResourceGroup: 知识组信息
        """
        group = self._group_repo.get_group_by_id(group_id)
        return KnowledgeGroup.from_do(group) if group else None

    def get_groups_by_cid(self, cid: str) -> List[KnowledgeGroup]:
        """
        获取企业下的所有知识组
        Args:
            cid: 企业ID

        Returns:
            List[KnowledgeGroup]: 知识组列表
        """
        logger.info(f"查询企业知识组: {cid}")

        # 查询知识组记录（只返回未删除的记录）
        groups = self._group_repo.get_all_groups_by_type(group_type=GroupType.KNOWLEDGE.name, cid=cid)

        return [KnowledgeGroup.from_do(group) for group in groups]
