import logging
import os
import random
from datetime import datetime
from typing import List

from robot_studio.data_asset.common.base_asset import Status
from robot_studio.data_asset.knowledge.bailian import BailianKnowledgeService, KnowledgeSyncRes
from robot_studio.data_asset.knowledge.model import Knowledge, Source, SchemaColConfig, KnowledgeSchema, KnowledgeItem, \
    ContentType
from robot_studio.database.mysql.repository import KnowledgeRepository
from robot_studio.database.mysql.repository import KnowledgeSchemaRepository

logger = logging.getLogger(__name__)


class KnowledgeManager:

    def __init__(self):
        self._knowledge_repo = KnowledgeRepository()
        self._bailian_service = BailianKnowledgeService()
        self._schema_manager = KnowledgeSchemaManager()

    def create(self, knowledge: Knowledge) -> Knowledge:
        """
        创建知识
        Args:
            knowledge:

        Returns:

        """

        # 构建知识的uuid，md，graph衍生数据
        knowledge.content_uuid = knowledge.content_uuid or knowledge.build_knowledge_uuid()
        knowledge.content_md = knowledge.content_md or knowledge.build_knowledge_md()
        if knowledge.content_type == ContentType.TABLE.name:
            assert knowledge.schema_id, "知识的schema_id为空！"
            schema = self._schema_manager.query_schema_by_sid(knowledge.schema_id)
            assert schema, "知识Schema不存在！"
            # 检查 knowledge.content_structure 结构是否和schema.config匹配
            if not validate_content_structure_fields(knowledge.content_structure, schema.config):
                logger.info(f'content_structure: {knowledge.content_structure},  schema.config: {schema.config}')
                raise Exception("知识结构不匹配！")
            knowledge.content_graph = knowledge.content_graph or knowledge.build_knowledge_graph(schema)
        knowledge.valid_date = knowledge.valid_date or datetime.now()  # 生效日期默认为当前时刻
        knowledge.version = knowledge.version or 1  # 版本号默认为1
        # 创建时默认草稿态
        knowledge.status = Status.DRAFT.name
        do = knowledge.to_do()
        do = self._knowledge_repo.create_knowledge(do)
        return Knowledge.from_do(do)

    def create_by_material_group(self, material_group_id: str) -> Knowledge:
        """
        把素材组转换为具体知识 TODO : 创建素材组知识，1. 创建特定知识组 2. 创建特定知识Schema 2. 创建固定知识 3. 知识上线
        Args:
            material_group_id:

        Returns:

        """
        pass

    def query_knowledge_by_kid(self, knowledge_id: str) -> Knowledge:
        """
        查询知识，返回当前知识ID的最新版本
        Args:
            knowledge_id:

        Returns:

        """
        do = self._knowledge_repo.get_max_version_knowledge_by_kid(knowledge_id)
        return Knowledge.from_do(do)

    def query_online_knowledge_by_kid(self, knowledge_id: str) -> Knowledge:
        """
        查询线上版本的知识
        Args:
            knowledge_id: 知识ID
        Returns:
            Knowledge: 线上版本的知识，不存在则返回None
        """
        do = self._knowledge_repo.get_online_knowledge_by_kid(knowledge_id)
        return Knowledge.from_do(do)

    def query_group_knowledge(self, group_id: str) -> List[Knowledge]:
        """
        查询分组下的知识列表，按照创建时间倒排，无需查询和处理content_structure等大字段，只查基础信息。
        Args:
            group_id:
        Returns:
            List[Knowledge]: 精简知识对象列表
        """
        rows = self._knowledge_repo.get_basic_knowledge_list_by_gid(group_id)
        knowledge_list = [Knowledge.from_basic_row(row) for row in rows]
        return knowledge_list

    def query_online_knowledge_by_cid(self, cid: str) -> List[Knowledge]:
        """
        查询某个企业下所有在线状态的知识列表，排除大字段以提升查询效率
        Args:
            cid: 企业ID
        Returns:
            List[Knowledge]: 在线知识精简对象列表
        """
        rows = self._knowledge_repo.get_online_knowledge_list_by_cid(cid)
        knowledge_list = [Knowledge.from_basic_row(row) for row in rows]
        return knowledge_list

    def update_knowledge_content(self, knowledge: Knowledge) -> Knowledge:
        """
        更新知识内容
        Args:
            knowledge:

        Returns:

        """
        # 查询知识的最新版本
        exist_knowledge_do = self._knowledge_repo.get_max_version_knowledge_by_kid(knowledge.knowledge_id)
        assert exist_knowledge_do is not None, f"待更新的知识不存在！知识uid={knowledge.id}"
        exist_knowledge = Knowledge.from_do(exist_knowledge_do)

        # 更新基础信息
        exist_knowledge.name = knowledge.name
        exist_knowledge.desc = knowledge.desc
        exist_knowledge.tags = knowledge.tags

        # 更新有效期设置
        exist_knowledge.longtime_valid = knowledge.longtime_valid
        exist_knowledge.valid_date = knowledge.valid_date
        exist_knowledge.invalid_date = knowledge.invalid_date

        # 知识内容发生变更，需要更新知识内容
        content_changed = False
        if exist_knowledge.content_uuid != knowledge.build_knowledge_uuid():
            content_changed = True
            exist_knowledge.content_type = knowledge.content_type
            exist_knowledge.content_structure = knowledge.content_structure  # 表格内容
            exist_knowledge.schema_id = knowledge.schema_id  # 表格模式的schema配置
            exist_knowledge.content_document = knowledge.content_document  # 文档内容
            exist_knowledge.build_knowledge_uuid()

            # 更新md内容
            exist_knowledge.build_knowledge_md()

            # 如果是表格模式，更新图数据
            if exist_knowledge.content_type == ContentType.TABLE.name:
                schema = self._schema_manager.query_schema_by_sid(knowledge.schema_id)
                exist_knowledge.build_knowledge_graph(schema)

            # 当前状态为线上态，内容发生变更需要创建草稿态副本
            if exist_knowledge.status == Status.ONLINE.name:
                exist_knowledge.add_version()  # 副本版本号+1
                exist_knowledge.status = Status.DRAFT.name  # 副本状态为草稿态
                exist_knowledge.gmt_modified = datetime.now()  # 副本更新时间为最新，创建时间保留原本
                exist_knowledge.bailian_data_id = None  # 草稿态的百炼ID均为空值
                exist_knowledge.bailian_index_id = None
                exist_knowledge.id = None
                return self.create(exist_knowledge)  # 创建副本

        # 当前副本直接修改即可
        update_dict = {
            "name": exist_knowledge.name,
            "desc": exist_knowledge.desc,
            "tags": exist_knowledge.tags,
            "longtime_valid": exist_knowledge.longtime_valid,
            "valid_date": exist_knowledge.valid_date,
            "invalid_date": exist_knowledge.invalid_date,
        }
        if content_changed:  # 内容变更，需要更新知识内容相关属性
            update_dict["content_uuid"] = exist_knowledge.content_uuid
            update_dict["content_type"] = exist_knowledge.content_type
            update_dict["content_structure"] = [item.model_dump() for item in
                                                exist_knowledge.content_structure] if exist_knowledge.content_structure else []
            update_dict["schema_id"] = exist_knowledge.schema_id
            update_dict["content_document"] = exist_knowledge.content_document
            update_dict["content_md"] = exist_knowledge.content_md
            update_dict["content_graph"] = exist_knowledge.content_graph

        # 更新当前版本的知识ID信息
        do = self._knowledge_repo.update_knowledge(exist_knowledge_do.id, update_dict)
        return Knowledge.from_do(do)

    async def del_knowledge_by_id(self, id: int):
        """
        删除知识
        Args:
            id: 知识唯一Uid

        Returns:

        """
        knowledge_do = self._knowledge_repo.get_knowledge_by_id(id)
        assert knowledge_do, f"待删除的知识不存在，知识表ID={id}"
        knowledge = Knowledge.from_do(knowledge_do)
        #  删除远端的bailian知识
        if knowledge.bailian_index_id:
            del_res = await self._bailian_service.del_bailian_knowledge(knowledge.bailian_data_id,
                                                                        knowledge.bailian_index_id)
            assert del_res.success, "删除百炼知识失败！"
        return self._knowledge_repo.del_knowledge(id)

    async def batch_del(self, uids: List[int]):
        """
        批量删除知识
        Args:
            uids:

        Returns:

        """
        for uid in uids:
            await self.del_knowledge_by_id(uid)

    async def online(self, uid: int) -> bool:
        """
        上线知识
        Args:
            uid: 知识唯一Uid

        Returns:

        """
        # 查询当前草稿态知识
        knowledge_do = self._knowledge_repo.get_knowledge_by_id(uid)
        assert knowledge_do, f"待上线的知识不存在，知识表ID={uid}"
        knowledge = Knowledge.from_do(knowledge_do)
        assert knowledge.status == Status.DRAFT.name, f"待上线的知识不是草稿态，无法上线，知识ID={uid}"

        # 查询上个版本正式态知识
        pre_knowledge_do = self._knowledge_repo.get_knowledge_by_kid_version(knowledge.knowledge_id,
                                                                             knowledge.version - 1)

        pre_version_knowledge = Knowledge.from_do(pre_knowledge_do)

        # 当前是否首个正式版本，当前版本是否处于生效期内
        is_first_version = pre_version_knowledge is None
        now_version_in_valid_date = knowledge.now_is_valid()

        # 清理前版本的数据
        if not is_first_version:

            # 1. 清理前版本在百炼的向量库
            if pre_version_knowledge.bailian_index_id:
                del_res = await self._bailian_service.del_bailian_knowledge(pre_version_knowledge.bailian_data_id,
                                                                            pre_version_knowledge.bailian_index_id)
                assert del_res.success, "删除上一版本的百炼知识失败！"
            # 2. 软删除前版本
            self._knowledge_repo.del_knowledge(pre_version_knowledge.id)

        # 新增当前版本百炼向量库（当前确认生效的话）
        if now_version_in_valid_date:
            sync_res = await self._create_bailian_index(knowledge)
            assert sync_res, "创建百炼知识失败！"
            knowledge = sync_res

        # 更新状态为ONLINE和相关的百炼ID
        update_file = {
            "bailian_data_id": knowledge.bailian_data_id,
            "bailian_index_id": knowledge.bailian_index_id,
            "status": Status.ONLINE.name,
        }
        update_res = self._knowledge_repo.update_knowledge(uid, update_file)
        return update_res is not None

    async def batch_online(self, uids: List[int]) -> bool:
        for uid in uids:
            res = await self.online(uid)
            if not res:
                return res
        return True

    async def offline(self, uid: int) -> bool:
        """
        下线知识
        Args:
            uid: 知识唯一Uid

        Returns:

        """
        # 查询当前草稿态知识
        knowledge_do = self._knowledge_repo.get_knowledge_by_id(uid)
        assert knowledge_do, f"待下线的知识不存在，知识表ID={uid}"
        knowledge = Knowledge.from_do(knowledge_do)
        assert knowledge.status == Status.ONLINE.name, f"待下线的知识不是线上态，无法下线，知识ID={uid}"

        # 删除百炼知识
        if knowledge.bailian_index_id:
            del_res = await self._bailian_service.del_bailian_knowledge(knowledge.bailian_data_id,
                                                                        knowledge.bailian_index_id)
            if not del_res.success:
                logger.error(
                    f"删除百炼知识失败，百炼知识ID={knowledge.bailian_data_id}, 百炼索引ID={knowledge.bailian_index_id}, 错误信息={del_res}")
            assert del_res.success, "删除百炼知识失败！"
        # 更新状态为ONLINE和相关的百炼ID
        update_file = {
            "bailian_data_id": None,
            "bailian_index_id": None,
            "status": Status.DRAFT.name,
        }
        update_res = self._knowledge_repo.update_knowledge(uid, update_file)
        return update_res is not None

    async def sync_bailian_index(self):

        # 当前处于有效期，但是未同步百炼知识的任务，执行同步
        need_sync_knowledge_dos = self._knowledge_repo.get_valid_knowledge_need_sync(source=Source.DATA.name)
        if need_sync_knowledge_dos:
            need_sync_knowledge = [Knowledge.from_do(_do) for _do in need_sync_knowledge_dos]
            for knowledge in need_sync_knowledge:
                knowledge = await self._create_bailian_index(knowledge)
                if knowledge:
                    update_dict = {
                        "bailian_data_id": knowledge.bailian_data_id,
                        "bailian_index_id": knowledge.bailian_index_id,
                    }
                    self._knowledge_repo.update_knowledge(knowledge.id, update_dict)

        # 当前未生效（或者过期），但是百炼知识非空，执行删除
        need_clean_knowledge_dos = self._knowledge_repo.get_invalid_knowledge_need_clean(source=Source.DATA.name)
        if need_clean_knowledge_dos:
            need_clean_knowledge = [Knowledge.from_do(_do) for _do in need_clean_knowledge_dos]
            for knowledge in need_clean_knowledge:
                del_res = await self._bailian_service.del_bailian_knowledge(knowledge.bailian_data_id,
                                                                            knowledge.bailian_index_id)
                assert del_res.success, "删除过期知识的百炼知识失败！"
                self._knowledge_repo.del_knowledge(knowledge.id)

    @staticmethod
    def _build_md_file(knowledge: Knowledge) -> str | None:
        import os
        assert knowledge and knowledge.content_md, "知识内容为空，无法生成MD文件"
        # 确保知识ID存在且内容不为空
        unique_id = random.randint(1000, 9999)
        # 百炼要求文件名必须唯一
        # todo 还有长度要求限制，具体长度待确认
        file_name = f"{knowledge.name}_v{knowledge.version}_{unique_id}.md"
        file_path = os.path.abspath(file_name)
        try:
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(knowledge.content_md)
            return file_path
        except Exception as e:
            # 处理文件写入异常
            print(f"保存知识Markdown文件失败: {str(e)}")
            return None

    async def _create_bailian_index(self, knowledge: Knowledge) -> Knowledge | None:
        """
        同步百炼知识库
        Args:
            knowledge:

        Returns:

        """
        md_file_path = self._build_md_file(knowledge)
        try:
            sync_res = await  self._bailian_service.sync_bailian_knowledge(md_file_path)
            if isinstance(sync_res, KnowledgeSyncRes):  # 同步成功，更新ID
                knowledge.bailian_index_id = sync_res.index_id
                knowledge.bailian_data_id = sync_res.file_id
                return knowledge
            else:
                logger.error(f"同步百炼知识库失败！{sync_res}")
                return None
        finally:
            # 同步完成后删除临时md文件
            if md_file_path and os.path.exists(md_file_path):
                try:
                    os.remove(md_file_path)
                    logger.info(f"已删除临时文件: {md_file_path}")
                except Exception as e:
                    logger.error(f"删除临时文件失败: {md_file_path}, 错误: {e}")


def validate_content_structure_fields(content_structure: List[KnowledgeItem],
                                      schema_config: List[SchemaColConfig]) -> bool:
    """
    校验 content_structure 是否符合 schema.config 的结构要求。
    只有当 content_structure 中的字段都在 schema.config 定义时才返回 True。

    Args:
        content_structure (List[KnowledgeItem]): 知识内容结构
        schema_config (List[SchemaColConfig]): 知识 Schema 配置

    Returns:
        bool: 校验是否通过
    """
    if not content_structure:
        return True
    if not schema_config:
        raise ValueError("知识结构配置不能为空")

    # 提取所有允许的列名
    allowed_col_names = {config.col_name for config in schema_config}

    # 遍历每个 KnowledgeItem 及其内容
    for item in content_structure:
        for col_item in item.content:
            if col_item.col_name not in allowed_col_names:
                logger.error(f"发现非法列名: {col_item.col_name}，不在 schema 配置中")
                return False
    return True


class KnowledgeSchemaManager:

    def __init__(self):
        self._knowledge_schema_repo = KnowledgeSchemaRepository()

    def query_schema_by_cid(self, cid: str) -> list[KnowledgeSchema]:
        """
        查询企业下的知识Schema
        Args:
            cid: 企业ID

        Returns:
            list[KnowledgeSchema]: 知识Schema列表
        """
        schema_dos = self._knowledge_schema_repo.get_schemas_by_cid(cid)
        return [KnowledgeSchema.from_do(schema_do) for schema_do in schema_dos]

    def query_schema_by_sid(self, schema_id: str) -> KnowledgeSchema | None:
        """
        根据sid查询知识结构
        Args:
            schema_id:

        Returns:

        """
        schema_do = self._knowledge_schema_repo.get_schemas_by_sid(schema_id)
        if schema_do:
            return KnowledgeSchema.from_do(schema_do)
        return None

    def create(self, schema: KnowledgeSchema) -> KnowledgeSchema:
        """
        创建知识Schema
        Args:
            schema: 知识Schema信息

        Returns:
            KnowledgeSchema: 创建后的知识Schema
        """
        schema_do = schema.to_do()
        schema_do = self._knowledge_schema_repo.create_schema(schema_do)
        return KnowledgeSchema.from_do(schema_do)

    def delete_schema_by_id(self, schema_id: str) -> bool:
        """
        根据schema_id物理删除知识Schema
        Args:
            schema_id: 知识Schema ID

        Returns:
            bool: 删除结果
        """
        return self._knowledge_schema_repo.delete_schema_by_sid(schema_id)

    def update_schema(self, schema: KnowledgeSchema) -> KnowledgeSchema | None:
        """
        更新知识Schema
        Args:
            schema: 知识Schema信息（包含schema_id）

        Returns:
            KnowledgeSchema | None: 更新后的知识Schema，如果不存在则返回None
        """
        if not schema.schema_id:
            return None
        
        schema_do = schema.to_do()
        updated_schema_do = self._knowledge_schema_repo.update_schema_by_sid(schema.schema_id, schema_do)
        if updated_schema_do:
            return KnowledgeSchema.from_do(updated_schema_do)
        return None
