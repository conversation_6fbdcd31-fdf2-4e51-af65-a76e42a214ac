from typing import Self

from robot_studio.data_asset.common.base_group import BaseGroup, GroupType
from robot_studio.database.mysql.table_schema import ResourceGroupDO


class KnowledgeGroup(BaseGroup):
    """知识组模型"""

    group_type: str = GroupType.KNOWLEDGE.value
    """分组类型"""

    @classmethod
    def from_do(cls, resource_group: ResourceGroupDO) -> Self:
        return cls(
            group_id=resource_group.group_id,
            group_name=resource_group.group_name,
            group_desc=resource_group.group_desc,
            group_type=resource_group.group_type,
            cid=resource_group.cid,
            create_user=resource_group.create_user,
            create_uid=resource_group.create_uid,
            tags=resource_group.tags,
            gmt_create=resource_group.gmt_create,
            gmt_modified=resource_group.gmt_modified,
        )

    def to_do(self) -> ResourceGroupDO:
        return ResourceGroupDO(
            group_id=self.group_id,
            group_name=self.group_name,
            group_desc=self.group_desc,
            group_type=self.group_type,
            cid=self.cid,
            create_user=self.create_user,
            create_uid=self.create_uid,
            tags=self.tags,
            sub_group_type=None,
            rel_resource_id=None,
            rel_resource_type=None,
        )
