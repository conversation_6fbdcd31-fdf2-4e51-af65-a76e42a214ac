import hashlib
from datetime import datetime
from enum import Enum
from typing import List, Any, Self, Dict

from pydantic import BaseModel

from robot_studio.data_asset.common.base_asset import BaseAsset, Status
from robot_studio.database.mysql.table_schema import KnowledgeDO
from robot_studio.database.mysql.table_schema import KnowledgeSchemaDO


class Source(Enum):
    DATA = "企业数据"
    MATERIAL = "企业素材"


class SchemaColConfig(BaseModel):
    """知识结构的具体配置项"""

    col_name: str | None = None
    """具体列名，如课程名称"""

    desc: str | None = None
    """列名描述"""

    is_entity: bool | None = None
    """是否实体"""

    entity_type: str | None = None
    """实体类型，课程"""


class KnowledgeSchema(BaseModel):
    """知识Schema列"""
    schema_id: str | None = None
    """知识结构ID，SID_XXX"""

    type: str | None = None
    """知识结构类型，如课程、教师、班级"""

    name: str | None = None
    """知识结构名称，壹同未来课程知识结构"""

    desc: str | None = None
    """知识结构描述"""

    config: List[SchemaColConfig] | None = None
    """知识结构配置，参照一维表格列配置"""

    cid: str | None = None
    """企业ID"""

    @classmethod
    def from_do(cls, do: KnowledgeSchemaDO) -> Self:
        """
        DO实例转为知识Schema模型
        Args:
            do: 数据库DO实例

        Returns:
            KnowledgeSchema: 知识Schema模型
        """
        schema = cls(
            schema_id=do.schema_id,
            type=do.type,
            name=do.name,
            desc=do.desc,
            cid=do.cid
        )
        if do.config:
            schema.config = [SchemaColConfig.model_validate(item) for item in do.config]
        return schema

    def to_do(self):
        """
        知识Schema模型转为DO实例
        Returns:
            KnowledgeSchemaDO: 数据库DO实例
        """
        from datetime import datetime
        return KnowledgeSchemaDO(
            schema_id=self.schema_id,
            type=self.type,
            name=self.name,
            desc=self.desc,
            config=[item.model_dump() for item in self.config] if self.config else None,
            cid=self.cid,
            gmt_modified=datetime.now()
        )


class SchemaType(Enum):
    DEFAULT = "default"
    """知识schema类型"""


class ContentType(Enum):
    DOCUMENT = "自定义文本"
    """文本"""

    TABLE = "结构化表格"
    """表格"""


class GraphEntity(BaseModel):
    entity_type: str
    """主体类型，如课程、教师、班级"""

    entity_name: str
    """主体名称，如3年级、黄俊超老师"""

    entity_desc: str
    """主体的信息描述，其他信息列的JSON结构"""

    def to_dict(self) -> Dict[str, str]:
        """
        将 GraphEntity 实例转换为可 JSON 序列化的字典。
        """
        return {
            "entity_type": self.entity_type,
            "entity_name": self.entity_name,
            "entity_desc": self.entity_desc
        }


class ColItem(BaseModel):
    col_name: str
    """具体schema配置中的列名"""

    col_value: Any
    """具体列值"""


class KnowledgeItem(BaseModel):
    """知识的单行Item"""
    content: List[ColItem] | None = None
    uuid: str | None = None
    """内容的UUID"""

    def gen_content_uuid(self):
        """生成内容的UUID，基于content列表内容"""
        if not self.content:
            self.uuid = ""
            return

        # 按照列名排序
        sorted_content = sorted(self.content, key=lambda x: x.col_name)
        # 使用分号join成字符串
        content_str = ";".join([f"{item.col_name}:{item.col_value}" for item in sorted_content])
        # 生成16位UUID
        md5 = hashlib.md5(content_str.encode()).hexdigest()
        self.uuid = md5[:16]  # 取前16位


class Knowledge(BaseAsset):
    knowledge_id: str | None = None
    """知识ID"""

    source: str | None = None
    """知识来源，Source枚举"""

    group_id: str | None = None
    """知识组ID"""

    schema_id: str | None = None
    """表格模式下，具体关联的scheme配置ID"""

    rel_material_group_id: str | None = None
    """关联的素材组ID，知识来源source=MATERIAL时，配置对应的素材组"""

    content_uuid: str | None = None
    """知识内容的UUID，基于content_structure/content_document生成"""

    content_type: str | None = None
    """内容类型 ContentType枚举"""

    content_structure: List[KnowledgeItem] | None = None
    """内容类型为TABLE时，存放按照表格schema上传的知识内容"""

    content_document: str | None = None
    """内容类型为DOCUMENT时，存放用户的自定义文本内容"""

    content_md: str | None = None
    """非结构化知识内容，markdown格式，用于同步向量库使用"""

    content_graph: List[GraphEntity] | None = None
    """图知识数据，按照知识主体抽象"""

    bailian_data_id: str | None = None
    """关联的阿里云百炼知识库ID"""

    bailian_index_id: str | None = None
    """关联的阿里云百炼向量库ID"""

    @classmethod
    def from_do(cls, do: KnowledgeDO) -> Self | None:
        """
        DO实例转为知识模型
        Args:
            do:

        Returns:

        """
        if not do:
            return None

        content_structure = []
        if do.content_structure and len(do.content_structure) > 0:
            content_structure = [KnowledgeItem.model_validate(item) for item in do.content_structure]

        content_graph = []
        if do.content_graph and len(do.content_graph) > 0:
            content_graph = [GraphEntity.model_validate(item) for item in do.content_graph]

        instance = cls()
        instance.knowledge_id = do.knowledge_id
        instance.name = do.name
        instance.desc = do.desc
        instance.source = do.source
        instance.schema_id = do.schema_id
        instance.valid_date = do.valid_date
        instance.invalid_date = do.invalid_date
        instance.status = do.status
        instance.version = do.version
        instance.group_id = do.group_id
        instance.rel_material_group_id = do.rel_material_group_id
        instance.content_uuid = do.content_uuid
        instance.content_structure = content_structure
        instance.content_md = do.content_md
        instance.content_graph = content_graph
        instance.bailian_data_id = do.bailian_data_id
        instance.bailian_index_id = do.bailian_index_id
        instance.gmt_create = do.gmt_create
        instance.gmt_modified = do.gmt_modified
        instance.id = do.id
        instance.create_uid = do.create_uid
        instance.content_type = do.content_type
        instance.content_document = do.content_document
        instance.longtime_valid = do.longtime_valid
        instance.tags = do.tags
        return instance

    def to_do(self) -> KnowledgeDO:
        """
        知识模型转为数据库DO实例
        Returns:

        """
        return KnowledgeDO(knowledge_id=self.knowledge_id,
                           name=self.name,
                           desc=self.desc,
                           source=self.source,
                           schema_id=self.schema_id,
                           valid_date=self.valid_date,
                           invalid_date=self.invalid_date,
                           status=self.status,
                           version=self.version,
                           group_id=self.group_id,
                           rel_material_group_id=self.rel_material_group_id,
                           content_uuid=self.content_uuid,
                           content_structure=[item.model_dump() for item in
                                              self.content_structure] if self.content_structure else None,
                           content_md=self.content_md,
                           content_graph=self.content_graph,
                           bailian_data_id=self.bailian_data_id,
                           bailian_index_id=self.bailian_index_id,
                           id=self.id,
                           gmt_create=self.gmt_create,
                           gmt_modified=self.gmt_modified,
                           longtime_valid=self.longtime_valid,
                           create_uid=self.create_uid,
                           content_type=self.content_type,
                           content_document=self.content_document,
                           tags=self.tags,
                           )

    def now_is_valid(self) -> bool:
        """
        判断当前知识是否处于有效期内
        Returns:

        """
        if self.longtime_valid:
            return True

        if self.valid_date is None or self.invalid_date is None:
            return True
        now = datetime.now()
        if now >= self.valid_date and (self.invalid_date is None or now <= self.invalid_date):
            return True
        return False

    def is_sync_bailian(self) -> bool:
        """是否同步百炼知识库"""
        return self.status == Status.ONLINE.name and self.now_is_valid() and self.bailian_index_id is not None

    def build_knowledge_uuid(self) -> str:
        """
        生成知识内容的UUID
        Returns:

        """
        # 新增逻辑：文本类型内容处理
        if self.content_type == ContentType.DOCUMENT.name:
            if not self.content_document:
                self.content_uuid = ""
                return self.content_uuid
            md5 = hashlib.md5(self.content_document.encode()).hexdigest()
            self.content_uuid = md5[:16]  # 取前16位
            return self.content_uuid

        if self.content_structure is None or len(self.content_structure) == 0:
            self.content_uuid = ""
            return self.content_uuid

        # 每个知识项生成知识UUID
        for _item in self.content_structure:
            if _item.uuid is None:
                _item.gen_content_uuid()

        # 排序后进行MD5生成
        uuid_list = [_item.uuid for _item in self.content_structure]
        sorted_uuid_list = sorted(uuid_list)
        # 使用分号join成字符串
        content_str = ";".join([_uuid for _uuid in sorted_uuid_list])
        # 生成16位UUID
        md5 = hashlib.md5(content_str.encode()).hexdigest()
        self.content_uuid = md5[:16]  # 取前16位
        return self.content_uuid

    def build_knowledge_md(self) -> str:
        """
        生成知识内容的MD
        Returns:

        """
        if self.content_type == ContentType.DOCUMENT.name:
            self.content_md = self.content_document
            return self.content_md

        if self.content_structure is None or len(self.content_structure) == 0:
            self.content_md = ""
            return self.content_md

        md_parts = []
        for item in self.content_structure:
            # 确保每个item有uuid
            if item.uuid is None:
                item.gen_content_uuid()

            # 创建二级标题
            md_parts.append(f"## {item.uuid}")

            # 添加内容
            if item.content:
                for col_item in item.content:
                    md_parts.append(f"- **{col_item.col_name}**: {col_item.col_value}")

            md_parts.append("")  # 添加空行分隔

        self.content_md = "\n".join(md_parts)
        return self.content_md

    def build_knowledge_graph(self, schema: KnowledgeSchema) -> List[GraphEntity]:
        """
        生成知识内容的图数据
        Returns:

        """

        # 自定义文本类型无法抽取图主体，直接返回
        if self.content_type == ContentType.DOCUMENT.name:
            return []

        if self.content_structure is None or len(self.content_structure) == 0 or self.schema_id is None:
            self.content_graph = []
            return self.content_graph
        if not schema or not schema.config:
            self.content_graph = []
            return self.content_graph

        # 找出主体列配置
        entity_col_config = next((col for col in schema.config if col.is_entity), None)
        if not entity_col_config:
            self.content_graph = []
            return self.content_graph
        entity_col_name = entity_col_config.col_name
        entity_type = entity_col_config.entity_type or "未知类型"

        graph_entities = []
        for item in self.content_structure:
            if not item.content:
                continue

            # 查找主体列的值
            entity_col_item = next((col for col in item.content if col.col_name == entity_col_name), None)
            if not entity_col_item:
                continue

            # 构建其他列的描述信息
            other_cols = {col.col_name: col.col_value for col in item.content if col.col_name != entity_col_name}
            import json
            try:
                entity_desc = json.dumps(other_cols, ensure_ascii=False)
            except (TypeError, ValueError) as e:
                # 如果序列化失败，使用字符串表示
                entity_desc = str(other_cols)

            # 创建图实体
            graph_entity = GraphEntity(
                entity_type=entity_type,
                entity_name=str(entity_col_item.col_value),
                entity_desc=entity_desc
            )
            graph_entities.append(graph_entity.to_dict())

        self.content_graph = graph_entities
        return self.content_graph

    def add_version(self) -> int:
        """
        更新知识版本号
        Returns:

        """
        if self.version is None:
            self.version = 1
        else:
            self.version += 1
        return self.version

    @staticmethod
    def from_basic_row(row) -> "Knowledge":
        """
        从只包含基础字段的row对象构造Knowledge实例
        """
        knowledge = Knowledge()
        knowledge.id = getattr(row, 'id', None)
        knowledge.knowledge_id = getattr(row, 'knowledge_id', None)
        knowledge.name = getattr(row, 'name', None)
        knowledge.desc = getattr(row, 'desc', None)
        knowledge.source = getattr(row, 'source', None)
        knowledge.schema_id = getattr(row, 'schema_id', None)
        knowledge.valid_date = getattr(row, 'valid_date', None)
        knowledge.invalid_date = getattr(row, 'invalid_date', None)
        knowledge.status = getattr(row, 'status', None)
        knowledge.version = getattr(row, 'version', None)
        knowledge.group_id = getattr(row, 'group_id', None)
        knowledge.rel_material_group_id = getattr(row, 'rel_material_group_id', None)
        knowledge.content_uuid = getattr(row, 'content_uuid', None)
        knowledge.bailian_data_id = getattr(row, 'bailian_data_id', None)
        knowledge.bailian_index_id = getattr(row, 'bailian_index_id', None)
        knowledge.gmt_create = getattr(row, 'gmt_create', None)
        knowledge.gmt_modified = getattr(row, 'gmt_modified', None)
        knowledge.create_uid = getattr(row, 'create_uid', None)
        knowledge.content_type = getattr(row, 'content_type', None)
        knowledge.longtime_valid = bool(getattr(row, 'longtime_valid', False))
        tags = getattr(row, 'tags', None)
        knowledge.tags = tags if tags is not None else []
        return knowledge
