import hashlib
import json
import os
from typing import Any, List

import requests
from alibabacloud_bailian20231229 import models as bailian_models
from alibabacloud_bailian20231229.client import Client as BailianClient
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_credentials.models import Config as CredentialConfig
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from pydantic import BaseModel

from robot_studio.common.config_service import get_configs_by_tag


class BaseResult(BaseModel):
    success: bool = True
    """执行结果"""

    message: str | None = None
    """异常信息"""


class ApplyFileUploadLeaseRes(BaseResult):
    """上传租约返回结果"""

    file_upload_lease_id: str | None = None
    """租约ID"""

    headers: Any | None = None
    """上传请求头"""

    url: str | None = None
    """上传地址"""

    method: str | None = None
    """上传方法"""


class AddFileRes(BaseResult):
    """上传租约返回结果"""

    file_id: str | None = None
    """上传的文件ID"""

    parser: str | None = None
    """上传请求头"""


class FileStatus(BaseResult):
    """文件状态"""

    status: str | None = None
    """文件状态,PARSE_SUCCESS代表解析成功,INIT,PARSING,PARSE_FAILED"""

    def parse_ready(self):
        return self.status == 'PARSE_SUCCESS'

    def parse_fail(self):
        return self.status == 'PARSE_FAILED'


class CreateIndexDataBaseRes(BaseResult):
    """文件状态"""

    index_database_id: str | None = None
    """知识库ID"""


class SubmitIndexRes(BaseResult):
    """文件状态"""

    index_job_id: str | None = None
    """知识库索引任务ID"""


class IndexJobStatus(BaseResult):
    """索引任务状态"""

    status: str | None = None
    """索引任务状态,COMPLETED代表成功,PENDING,FAILED,RUNNING,COMPLETED"""

    def index_ready(self):
        return self.status == 'COMPLETED'

    def index_fail(self):
        return self.status == 'FAILED'

    def index_running(self):
        return self.status == 'RUNNING' or self.status == 'PENDING'


class KnowledgeSyncRes(BaseResult):
    """知识同步结果"""

    index_id: str | None = None
    """知识库ID"""

    file_id: str | None = None
    """文件ID"""


class RetrivalItem(BaseModel):
    text: str
    """具体切片内容"""

    score: float
    """得分"""


class RetrivalRes(BaseResult):
    items: List[RetrivalItem] | None = None
    """召回结果"""

    def json_str_for_llm(self):
        """召回结果转为模型消费的json字符串"""
        if not self.items:
            return json.dumps([])
        content_list = [item.text for item in self.items]
        return json.dumps(content_list)


class BailianKnowledgeService:

    def __init__(self):
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        try:
            bailian_configs = get_configs_by_tag('bailian')
            access_key_id = bailian_configs.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
            access_key_secret = bailian_configs.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
            work_space_id = bailian_configs.get('ALIBABA_CLOUD_BAILIAN_WORK_SPACE_ID')
            default_category_id = bailian_configs.get('ALIBABA_CLOUD_BAILIAN_DEFAULT_CATEGORY_ID')

            # 检查必要的配置是否存在
            if not all([access_key_id, access_key_secret, work_space_id]):
                raise ValueError("Missing required bailian configuration")

            credential_config = CredentialConfig(
                type='access_key',
                access_key_id=access_key_id,
                access_key_secret=access_key_secret
            )
            credential = CredentialClient(credential_config)
            config = open_api_models.Config(
                credential=credential
            )
            # 参考 https://api.aliyun.com/product/bailian
            config.endpoint = f'bailian.cn-beijing.aliyuncs.com'
            self._client = BailianClient(config)
            self._work_space_id = work_space_id
            self._default_category_id = default_category_id
            self._initialized = True
        except Exception as e:
            # 配置不可用时，设置初始化标志为False
            self._initialized = False
            self._client = None
            self._work_space_id = None
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Bailian service initialization failed: {str(e)}")

    async def sync_bailian_knowledge(self, file_path: str, bailian_file_id: str | None = None,
                                     bailian_index_id: str | None = None,
                                     desc: str | None = None
                                     ) -> BaseResult:
        """
        同步百炼知识库，每次更新本质上都是新建数据和索引，删除老的数据和索引！
        Args:
            bailian_file_id: 老的文件ID
            bailian_index_id: 老的知识库ID
            file_path: 同步的文件路径
            desc: 文件描述，可为空

        Returns:

        """
        if not hasattr(self, '_initialized') or not self._initialized:
            return BaseResult(success=False, message="Bailian service not initialized. Please check configuration.")

        # Step0 删除旧的索引库和知识库
        if bailian_index_id:
            await self.del_index_database(bailian_index_id)
        if bailian_file_id:
            await self.del_file(bailian_file_id)

        # Step1 上传文件到阿里云临时空间
        # Step1.1 获取租约ID
        apply_lease_res = await self.apply_file_upload_lease(file_path)
        if not apply_lease_res.success:
            return apply_lease_res

        # Step1.2 上传文件到租约空间
        upload_res = self.upload_file(file_path, apply_lease_res)
        if not upload_res:
            return BaseResult(success=False, message="上传文件失败！")

        # Step2 文件添加到百炼数据管理
        add_file_res = await self.add_file(apply_lease_res.file_upload_lease_id)
        if not add_file_res.success:
            return add_file_res

        # Step3 等待文件解析完成
        file_ready = False
        for _ in range(10):
            import asyncio
            await asyncio.sleep(10)
            file_status = await self.query_file_status(add_file_res.file_id)
            if not file_status.success or file_status.parse_fail():
                return file_status
            if file_status.parse_ready():  # 解析成功直接退出
                file_ready = True
                break

        if not file_ready:
            return BaseResult(success=False, message="文件解析超时！")

        # Step4 创建知识索引库
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        file_name = str(file_name).replace(" ", "_")
        bailian_index = await self.create_index_database(file_id=add_file_res.file_id, name=file_name, desc=desc)
        if not bailian_index.success:
            return bailian_index

        # Step4 提交索引任务
        submit_index_res = await self.submit_index_job(bailian_index.index_database_id)
        if not submit_index_res.success:
            return submit_index_res

        # Step5 等待索引任务完成
        index_ready = False
        for _ in range(10):
            import asyncio
            await asyncio.sleep(10)
            index_status = await self.query_index_job_status(bailian_index.index_database_id,
                                                             submit_index_res.index_job_id)
            if not index_status.success or index_status.index_fail():
                return index_status
            if index_status.index_ready():
                index_ready = True
                break
        if not index_ready:
            return BaseResult(success=False, message="索引任务超时！")

        # Step6 返回结果
        return KnowledgeSyncRes(index_id=bailian_index.index_database_id, file_id=add_file_res.file_id)

    async def del_bailian_knowledge(self, bailian_file_id: str,
                                    bailian_index_id: str
                                    ) -> BaseResult:
        """删除百炼知识库和文件"""
        if not hasattr(self, '_initialized') or not self._initialized:
            return BaseResult(success=False, message="Bailian service not initialized. Please check configuration.")
        index_res = await self.del_index_database(bailian_index_id)
        file_res = await self.del_file(bailian_file_id)
        return BaseResult(success=index_res.success and file_res.success, message=file_res.message or index_res.message)

    async def apply_file_upload_lease(self, file_path: str) -> ApplyFileUploadLeaseRes:
        if not hasattr(self, '_initialized') or not self._initialized:
            return ApplyFileUploadLeaseRes(success=False,
                                           message="Bailian service not initialized. Please check configuration.")

        # 上传文件租约
        _md5, _size = self._calculate_md5_and_size(file_path)
        _file_name = os.path.basename(file_path)
        # 确保 md5 是字符串类型
        if isinstance(_md5, bytes):
            _md5 = _md5.decode('utf-8')
        apply_file_upload_lease_request = bailian_models.ApplyFileUploadLeaseRequest(
            file_name=str(_file_name),
            md_5=str(_md5),
            size_in_bytes=str(_size),
            category_type='UNSTRUCTURED'
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = await self._client.apply_file_upload_lease_with_options_async(
                self._default_category_id,
                self._work_space_id,
                apply_file_upload_lease_request, headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return ApplyFileUploadLeaseRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return ApplyFileUploadLeaseRes(success=False, message="业务处理失败！")
            data = res.body.data
            return ApplyFileUploadLeaseRes(file_upload_lease_id=data.file_upload_lease_id, headers=data.param.headers
                                           , url=data.param.url, method=data.param.method)
        except Exception as error:
            # 错误 message
            return ApplyFileUploadLeaseRes(success=False, message=str(error))

    async def add_file(self, lease_id: str, tags: list[str] | None = None
                       ) -> AddFileRes:
        """
        把租约中的文件上传到百炼数据管理中
        Args:
            lease_id: 租约ID
            tags: 标签列表

        Returns:

        """
        add_file_request = bailian_models.AddFileRequest(
            lease_id=lease_id,
            parser='DASHSCOPE_DOCMIND',
            category_id=self._default_category_id,
            tags=tags or [],
            category_type='UNSTRUCTURED'
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.add_file_with_options_async(self._work_space_id, add_file_request, headers,
                                                                 runtime)

            if res.status_code != 200 or res.body is None:  # 异常处理
                return AddFileRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return AddFileRes(success=False, message="业务处理失败！")
            data = res.body.data
            return AddFileRes(file_id=data.file_id, parser=data.parser)
        except Exception as error:
            return AddFileRes(success=False, message=str(error))

    async def query_file_status(
            self, file_id: str
    ) -> FileStatus:
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = await self._client.describe_file_with_options_async(self._work_space_id,
                                                                      file_id,
                                                                      headers,
                                                                      runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return FileStatus(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return FileStatus(success=False, message="业务处理失败！")
            data = res.body.data
            return FileStatus(status=data.status)  # PARSE_SUCCESS
        except Exception as error:
            # 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            # 错误 message
            return FileStatus(success=False, message=str(error))

    async def del_file(
            self, file_id: str
    ) -> BaseResult:
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = await self._client.delete_file_with_options_async(file_id, self._work_space_id, headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return BaseResult(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return BaseResult(success=False, message="业务处理失败！")
            data = res.body.data
            return BaseResult(success=data is not None)
        except Exception as error:
            return BaseResult(success=False, message=str(error))

    async def create_index_database(
            self, file_id: str, name: str, rerank_min_score: int = 0.4, desc: str | None = None
    ) -> CreateIndexDataBaseRes:
        create_index_request = bailian_models.CreateIndexRequest(
            structure_type='unstructured',
            name=name,
            embedding_model_name='text-embedding-v2',
            rerank_model_name='gte-rerank-hybrid',
            rerank_min_score=rerank_min_score,
            source_type='DATA_CENTER_FILE',
            document_ids=[
                file_id
            ],
            sink_type='BUILT_IN',
            description=desc or name,
            chunk_mode='h2',
            chunk_size=5000
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.create_index_with_options_async(self._work_space_id, create_index_request, headers,
                                                                     runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return CreateIndexDataBaseRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return CreateIndexDataBaseRes(success=False, message=f"业务处理失败！返回结果={res.body}")
            data = res.body.data
            return CreateIndexDataBaseRes(index_database_id=data.id)
        except Exception as error:
            return CreateIndexDataBaseRes(success=False, message=str(error))

    async def del_index_database(
            self, index_database_id: str
    ) -> BaseResult:
        """
        删除知识向量库
        Args:
            index_database_id:

        Returns:

        """
        delete_index_request = bailian_models.DeleteIndexRequest(
            index_id=index_database_id
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.delete_index_with_options_async(self._work_space_id, delete_index_request, headers,
                                                                     runtime)
            return BaseResult(success=res.status_code == 200)
        except Exception as error:
            return BaseResult(success=False, message=str(error))

    async def submit_index_job(
            self, index_database_id: str
    ) -> SubmitIndexRes:
        """
        提交索引任务
        Args:
            index_database_id:

        Returns:

        """
        submit_index_job_request = bailian_models.SubmitIndexJobRequest(
            index_id=index_database_id
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.submit_index_job_with_options_async(self._work_space_id, submit_index_job_request,
                                                                         headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return SubmitIndexRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return SubmitIndexRes(success=False, message="业务处理失败！")
            data = res.body.data
            return SubmitIndexRes(index_job_id=data.id)
        except Exception as error:
            return SubmitIndexRes(success=False, message=str(error))

    async def query_index_job_status(
            self, index_database_id: str, job_id: str
    ) -> IndexJobStatus:
        """
        索引任务的执行状态！
        Args:
            index_database_id:
            job_id:

        Returns:

        """
        get_index_job_status_request = bailian_models.GetIndexJobStatusRequest(
            job_id=job_id,
            index_id=index_database_id
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.get_index_job_status_with_options_async(self._work_space_id,
                                                                             get_index_job_status_request,
                                                                             headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return IndexJobStatus(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return IndexJobStatus(success=False, message="业务处理失败！")
            data = res.body.data
            return IndexJobStatus(status=data.status)
        except Exception as error:
            return IndexJobStatus(success=False, message=str(error))

    def run_retrival(
            self, knowledge_index_id: str, query: str, top_k: int = 10, min_score: float = 0.5
    ) -> RetrivalRes:
        """
        同步召回文档内容
        Args:
            knowledge_index_id: 百炼知识库ID
            query: 检索关键词
            top_k: 返回数量
            min_score: 最小得分

        Returns:
            RetrivalRes: 检索结果

        """
        if not hasattr(self, '_initialized') or not self._initialized:
            return RetrivalRes(success=False, message="Bailian service not initialized. Please check configuration.")

        # 用户query改写模型
        rewrite_model = bailian_models.RetrieveRequestRewrite(
            model_name='conv-rewrite-qwen-1.8b'
        )

        # 召回后排序模型
        rerank_model = bailian_models.RetrieveRequestRerank(
            model_name='gte-rerank-hybrid'
        )

        # 检索请求构建
        retrieve_request = bailian_models.RetrieveRequest(
            query=query,
            dense_similarity_top_k=50,  # 向量检索相似度限制
            sparse_similarity_top_k=50,  # 关键词相似度限制
            enable_reranking=True,
            enable_rewrite=True,
            rerank=[
                rerank_model
            ],
            rerank_min_score=min_score,  # 控制最小相似度阈值
            rerank_top_n=top_k,  # 控制排序后的返回数量
            rewrite=[
                rewrite_model
            ],
            index_id=knowledge_index_id,  # 知识库ID
            save_retriever_history=True  # 是否保存检索历史
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = self._client.retrieve_with_options(self._work_space_id, retrieve_request, headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return RetrivalRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return RetrivalRes(success=False, message="召回处理失败！")
            nodes = res.body.data.nodes  # 数组对象，里面是具体切面
            if nodes is None or len(nodes) == 0:
                return RetrivalRes(success=False, message="召回结果为空！")
            return RetrivalRes(items=[RetrivalItem(text=node.text, score=node.score) for node in nodes])
        except Exception as error:
            return RetrivalRes(success=False, message=f"发生预期外异常！error = {str(error)}")

    async def run_retrival_async(
            self, knowledge_index_id: str, query: str, top_k: int = 10, min_score: float = 0.5
    ) -> RetrivalRes:
        """
        异步召回文档结果
        Args:
            knowledge_index_id: 百炼知识库ID
            query: 检索关键词
            top_k: 返回数量
            min_score: 最小得分

        Returns:
            RetrivalRes: 检索结果

        """
        if not hasattr(self, '_initialized') or not self._initialized:
            return RetrivalRes(success=False, message="Bailian service not initialized. Please check configuration.")
        # 用户query改写模型
        rewrite_model = bailian_models.RetrieveRequestRewrite(
            model_name='conv-rewrite-qwen-1.8b'
        )

        # 召回后排序模型
        rerank_model = bailian_models.RetrieveRequestRerank(
            model_name='gte-rerank-hybrid'
        )

        # 检索请求构建
        retrieve_request = bailian_models.RetrieveRequest(
            query=query,
            dense_similarity_top_k=50,  # 向量检索相似度限制
            sparse_similarity_top_k=50,  # 关键词相似度限制
            enable_reranking=True,
            enable_rewrite=True,
            rerank=[
                rerank_model
            ],
            rerank_min_score=min_score,  # 控制最小相似度阈值
            rerank_top_n=top_k,  # 控制排序后的返回数量
            rewrite=[
                rewrite_model
            ],
            index_id=knowledge_index_id,  # 知识库ID
            save_retriever_history=True  # 是否保存检索历史
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = await self._client.retrieve_with_options_async(self._work_space_id, retrieve_request, headers,
                                                                 runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return RetrivalRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return RetrivalRes(success=False, message="召回处理失败！")
            nodes = res.body.data.nodes  # 数组对象，里面是具体切面
            if nodes is None or len(nodes) == 0:
                return RetrivalRes(success=False, message="召回结果为空！")
            return RetrivalRes(items=[RetrivalItem(text=node.text, score=node.score) for node in nodes])
        except Exception as error:
            return RetrivalRes(success=False, message=f"发生预期外异常！error = {str(error)}")

    @staticmethod
    def upload_file(file_path: str, apply_lease_res: ApplyFileUploadLeaseRes) -> bool:
        """
        上传文件到阿里云的临时租约空间，有效期为12小时
        Args:
            file_path:
            apply_lease_res:

        Returns:

        """
        try:
            # 设置请求头
            headers = apply_lease_res.headers

            # 读取文档并上传
            with open(file_path, 'rb') as file:
                # 下方设置请求方法用于文档上传，需与您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param中Method字段的值一致
                response = requests.put(apply_lease_res.url, data=file, headers=headers)

            # 检查响应状态码
            if response.status_code == 200:
                print("File uploaded successfully.")
                return True
            else:
                print(f"Failed to upload the file. ResponseCode: {response.status_code}")
                return False

        except Exception as e:
            print(f"An error occurred: {str(e)}")
            return False

    @staticmethod
    def _calculate_md5_and_size(file_path):
        """计算文档的 MD5 值。

        Args:
            file_path (str): 文档的路径。

        Returns:
            str: 文档的 MD5 值。
        """
        md5_hash = hashlib.md5()

        # 以二进制形式读取文件
        with open(file_path, "rb") as f:
            # 按块读取文件，避免大文件占用过多内存
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)

        size = os.path.getsize(file_path)
        return md5_hash.hexdigest(), size
