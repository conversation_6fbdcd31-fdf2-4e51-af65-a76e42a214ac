from datetime import datetime
from enum import Enum
from typing import List

from openai import BaseModel


class Status(Enum):
    DRAFT = "草稿"
    """草稿状态，每次修改后均会设置变更为草稿态"""

    ONLINE = "正式"
    """确认编辑生效，由草稿态推进为线上正式态"""


class BaseAsset(BaseModel):
    """资产模型的基类"""

    id: int | None = None
    """资产ID"""

    name: str | None = None
    """资产名称"""

    desc: str | None = None
    """资产描述"""

    group_id: str | None = None
    """归属的知识组ID"""

    valid_date: datetime | None = None
    """资产生效日期"""

    invalid_date: datetime | None = None
    """资产失效日期，为空代表永久有效"""

    longtime_valid: bool = False
    """是否长期有效"""

    status: str | None = None
    """关联资产状态"""

    version: int | None = None
    """知识的版本号，每次修改->推进上线后会+1"""

    gmt_create: datetime | None = None
    """创建日期"""

    gmt_modified: datetime | None = None
    """修改日期"""

    create_uid: str | None = None
    """创建人UID"""

    create_name: str | None = None
    """创建人姓名"""

    tags: List[str] = []
    """标签"""
