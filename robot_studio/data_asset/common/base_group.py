from datetime import datetime
from enum import Enum
from typing import List

from pydantic import BaseModel


class GroupType(str, Enum):
    KNOWLEDGE = "知识组"
    MATERIAL = "素材组"


class SubGroupType(str, Enum):
    """素材子类型"""
    XHS = "小红书"
    DY = "抖音"
    KS = "快手"

    """知识子类型"""
    pass


class BaseGroup(BaseModel):
    """资源分组基类"""

    group_id: str | None = None
    """分组ID"""

    group_name: str | None = None
    """分组名称"""

    group_desc: str | None = None
    """分组描述"""

    group_type: str | None = None
    """分组类型，GroupType枚举"""

    cid: str | None = None
    """关联的企业ID"""

    create_user: str | None = None
    """创建人"""

    create_uid: str | None = None
    """创建人的用户ID"""

    tags: list[str] | None = None
    """素材组或知识组标签"""

    gmt_create: datetime | None = None
    """创建时间"""

    gmt_modified: datetime | None = None
    """更新时间"""
