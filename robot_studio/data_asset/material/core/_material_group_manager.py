from robot_studio.data_asset.common.base_group import GroupType
from robot_studio.data_asset.material.model import MaterialGroup
from robot_studio.database.mysql.repository import ResourceGroupRepository


class MaterialGroupManager:
    """素材组信息管理器
    提供服务如下：
    1. 新增素材组信息
    2. 更新素材组信息
    3. 软删除素材组信息
    4. 获取所有素材组信息
    5. 获取指定投放平台下的素材信息
    """
    def __init__(self):
        self._material_group_repo = ResourceGroupRepository()

    def create_material_group(self, material_group: MaterialGroup) -> MaterialGroup:
        """
        创建素材组
        material_group: 待创建的素材组对象

        Returns:
            创建的素材组对象
        """
        material_group.group_type = GroupType.MATERIAL.name
        material_group.group_desc = material_group.group_desc or None
        material_group.group_name = material_group.group_name or None
        material_group.sub_group_type = material_group.sub_group_type or None
        material_group.rel_resource_id = material_group.rel_resource_id or None
        material_group.rel_resource_type = material_group.rel_resource_type or None
        material_group.create_uid = material_group.create_uid or None
        material_group.create_user = material_group.create_user or None
        material_group.tags = material_group.tags or []
        do = material_group.to_do()
        do = self._material_group_repo.create_group(do)
        return MaterialGroup.from_do(do)

    def query_material_group_by_id(self, group_id) -> MaterialGroup:
        """
        根据组ID获取素材组信息

        Returns:
            MaterialGroup: 组ID对应的素材组信息
        """
        _group = self._material_group_repo.get_group_by_id(group_id=group_id)
        assert _group is not None, f"素材组信息不存在"
        return MaterialGroup.from_do(_group)

    def query_all_material_groups(self, cid) -> list[MaterialGroup]:
        """
        获取指定cid下的所有素材组信息

        Returns:
            list[MaterialGroup]: 素材组信息列表

        """
        _groups = self._material_group_repo.get_all_groups_by_type(group_type=GroupType.MATERIAL.name,
                                                                   cid=cid)
        assert _groups is not None, f"素材组信息不存在"
        return [MaterialGroup.from_do(_group) for _group in _groups]

    def query_material_groups_by_platform(self, cid, platform) -> list[MaterialGroup]:
        """
        获取指定企业ID和投放平台下的全部素材信息
        Returns:
            list[MaterialGroup]: 素材组信息列表

        """
        _groups = self._material_group_repo.get_groups_by_sub_group_type(cid=cid,
                                                                         group_type=GroupType.MATERIAL.name,
                                                                         sub_group_type=platform)
        _groups = _groups or []
        return [MaterialGroup.from_do(_group) for _group in _groups]

    def update_material_group(self,
                              group_id,
                              update_data: MaterialGroup) -> MaterialGroup:
        """
        更新素材信息
        Args:
            group_id: 需要被更新的素材组Gid
            update_data: 更新数据字典

        Returns:
            MaterialGroup: 更新后的素材组信息

        """
        _group = self._material_group_repo.update_group(group_id, update_data.model_dump(mode="json"))
        assert _group is not None, f"更新素材信息失败！gid={group_id}"
        return MaterialGroup.from_do(_group)

    def delete_material_group(self, group_id: str) -> bool:
        """
        软删除素材信息
        Args:
            group_id: 素材组GID

        Returns:
            bool: 删除结果

        """
        return self._material_group_repo.delete_group(group_id=group_id)
