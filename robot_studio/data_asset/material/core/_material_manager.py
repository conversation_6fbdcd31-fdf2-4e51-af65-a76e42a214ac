import logging
from datetime import datetime

from robot_studio.auth.core import UserManager
from robot_studio.data_asset.common.base_asset import Status
from robot_studio.data_asset.material.model import Material, MaterialType
from robot_studio.database.mysql.repository import MaterialRepository, ResourceGroupRepository
from robot_studio.database.oss import OssClient
from robot_studio.utils.version_util import add_version

logger = logging.getLogger(__name__)


class MaterialManager:
    """素材信息管理器
    提供服务如下：
    1. 新增素材信息
    2. 更新素材信息
    3. 软删除素材信息
    4. 获取匹配指定mid的素材信息
    5. 获取指定group_id分组下的素材信息
    """

    def __init__(self):
        self._material_repo = MaterialRepository()
        self._group_repo = ResourceGroupRepository()
        self._user_manager = UserManager()

    def create(self, material: Material) -> Material:
        """
        创建素材
        Args:
            material: 待创建的素材对象

        Returns:

        """
        # 素材初始化
        material.id = None
        material.valid_date = material.valid_date or datetime.now()  # 生效日期默认为当前时刻
        material.version = material.version or "1.0"  # 版本号默认为0
        material.status = material.status or Status.DRAFT.name

        # 视频类型需要生成首祯预览图，过期时间7天
        if material.type == MaterialType.VIDEO.name:
            oss = OssClient()
            preview_url = oss.generate_video_preview_url(video_oss_path=material.oss_path, expires=604800)
            assert preview_url is not None, f"生成视频预览图失败！"
            material.oss_preview_url = preview_url

        do = material.to_do()
        do = self._material_repo.create_material(do)
        return Material.from_do(do)

    def query_material(self, mid: str) -> Material:
        """
        获取具体素材信息
        Args:
            mid: 素材的MID

        Returns:

        """
        _mat = self._material_repo.get_material_by_mid(mid=mid)
        assert _mat is not None, f"素材信息不存在！mid={mid}"
        return self.update_oss_url(Material.from_do(_mat))

    def query_materials_by_group(self, group_id: str) -> list[Material]:
        """
        获取指定分组下的全部素材信息
        Returns:
            list[Material]: 素材信息列表

        """
        _materials = self._material_repo.get_material_by_group_id(group_id=group_id)
        _materials = _materials or []
        materials = [self.update_oss_url(Material.from_do(_material)) for _material in _materials]
        
        # 使用字典缓存用户信息，避免重复查询
        user_cache = {}
        
        # 查询准确的用户名
        for material in materials:
            if material.uid:
                # 如果缓存中已有该用户信息，直接使用
                if material.uid in user_cache:
                    material.user_name = user_cache[material.uid]
                else:
                    # 查询用户信息并缓存
                    user = self._user_manager.query_user_by_uid(material.uid)
                    if user:
                        user_cache[material.uid] = user.username
                        material.user_name = user.username
        
        return materials

    def query_valid_materials_by_group(self, group_id: str) -> list[Material]:
        """
        获取指定分组下全部有效期内的素材信息
        Returns:
            list[Material]: 素材信息列表
        """
        _materials = self._material_repo.get_material_valid_by_group_id(group_id=group_id)
        _materials = _materials or []
        materials = [self.update_oss_url(Material.from_do(_material)) for _material in _materials]
        
        # 使用字典缓存用户信息，避免重复查询
        user_cache = {}
        
        # 查询准确的用户名
        for material in materials:
            if material.uid:
                # 如果缓存中已有该用户信息，直接使用
                if material.uid in user_cache:
                    material.user_name = user_cache[material.uid]
                else:
                    # 查询用户信息并缓存
                    user = self._user_manager.query_user_by_uid(material.uid)
                    if user:
                        user_cache[material.uid] = user.username
                        material.user_name = user.username
        
        return materials

    def count_valid_materials_by_group(self, group_id: str) -> int:
        count = self._material_repo.count_valid_materials_by_group(group_id=group_id)
        return count if count else 0

    def update_material(self, mid: str, update_data: Material) -> Material:
        """
        更新素材信息
        Args:
            mid: 素材Mid
            update_data: 更新数据

        Returns:
            更新后的素材信息

        """
        exist_material = self._material_repo.get_material_by_mid(mid=mid)
        assert exist_material is not None, f"待更新素材不存在！mid={mid}"
        update_data = update_data.to_do()

        # 更新基础信息
        exist_material.name = update_data.name
        exist_material.tags = update_data.tags
        exist_material.source = update_data.source
        exist_material.content_digest = update_data.content_digest

        # 更新生效时间范围
        exist_material.longtime_valid = update_data.longtime_valid
        exist_material.valid_date = update_data.valid_date
        exist_material.invalid_date = update_data.invalid_date

        # 文本类型内容是否变更
        text_content_changed = False
        if exist_material.material_content != update_data.material_content:
            text_content_changed = True
            exist_material.material_content = update_data.material_content

        # oss相关信息更新
        media_content_changed = False
        if update_data.oss_url and update_data.oss_url != exist_material.oss_url:
            media_content_changed = True
            exist_material.oss_url = update_data.oss_url
            exist_material.oss_path = update_data.oss_path
            exist_material.oss_expiration = update_data.oss_expiration

            # 如果视频类的oss_url变更，重新生成视频的首祯预览图
            if update_data.type == MaterialType.VIDEO.name:
                oss = OssClient()
                preview_url = oss.generate_video_preview_url(video_oss_path=update_data.oss_path, expires=604800)
                assert preview_url is not None, f"生成视频预览图失败！"
                exist_material.oss_preview_url = preview_url

        # 当前素材状态为线上态，内容变更，需要将素材状态变为草稿以及版本号+1
        if exist_material.status == Status.ONLINE.name and (text_content_changed or media_content_changed):
            exist_material.version = add_version(exist_material.version)  # 版本号+1
            exist_material.status = Status.DRAFT.name  # 状态变为草稿态

        # 基础信息直接变更
        update_dict = {
            "name": exist_material.name,
            "tags": exist_material.tags,
            "source": exist_material.source,
            "content_digest": exist_material.content_digest,
            "longtime_valid": exist_material.longtime_valid,
            "valid_date": exist_material.valid_date,
            "invalid_date": exist_material.invalid_date,
            "version": exist_material.version,
            "status": exist_material.status
        }
        if text_content_changed:  # 文本内容变更，更新内容
            update_dict["material_content"] = exist_material.material_content

        if media_content_changed:  # 图片/视频上传的内容变更，更新素材
            update_dict["oss_url"] = exist_material.oss_url
            update_dict["oss_expiration"] = exist_material.oss_expiration
            update_dict["oss_path"] = exist_material.oss_path
            update_dict["oss_preview_url"] = exist_material.oss_preview_url

        # 更新当前版本的素材信息
        do = self._material_repo.update_material(mid, update_dict)
        return Material.from_do(do)

    def delete_material(self, mid: str) -> bool:
        """
        软删除素材信息
        Args:
            mid: 素材MID

        Returns:
            bool: 删除结果

        """
        return self._material_repo.delete_material(mid=mid)

    def online(self, mid: str) -> bool:
        """
        素材上线

        Args:
            mid: 素材MID

        Returns:

        """
        # 确认当前素材是否符合上线条件：状态为草稿态
        exist_material_do = self._material_repo.get_material_by_mid(mid=mid)
        assert exist_material_do is not None, f"待上线素材不存在！mid={mid}"
        exist_material = Material.from_do(exist_material_do)
        assert exist_material.status == Status.DRAFT.name, f"待上线素材状态错误！mid={mid}, status={exist_material.status}"

        update_dict = {
            "status": Status.ONLINE.name
        }
        do = self._material_repo.update_material(mid, update_dict)

        if do:
            return True
        else:
            return False

    def offline(self, mid: str) -> bool:
        """
        素材下线

        Args:
            mid: 素材MID

        Returns:
        """
        # 确认当前素材是否符合下线条件：状态为正式态
        exist_material_do = self._material_repo.get_material_by_mid(mid=mid)
        assert exist_material_do is not None, f"待下线素材不存在！mid={mid}"
        exist_material = Material.from_do(exist_material_do)
        assert exist_material.status == Status.ONLINE.name, f"待下线素材状态错误！mid={mid}, status={exist_material.status}"

        update_dict = {
            "status": Status.DRAFT.name
        }
        do = self._material_repo.update_material(mid, update_dict)

        if do:
            return True
        else:
            return False

    def update_oss_url(self, material: Material) -> Material:
        """
        判断oss_url是否过期，如果过期则更新素材oss_url
        Args:
            material: 素材对象

        Returns:

        """
        try:
            if not material.oss_url or not material.oss_expiration:
                return material
            oss_expiration = material.oss_expiration
            # 如果当前时间在oss_expiration之后，则更新oss_url
            update_dict = {}
            if datetime.now() > oss_expiration:
                logger.info(f"素材{material.material_id}的oss_url已过期，开始更新...")
                # 重新生成预签名URL，有效期最大设置为7天
                oss = OssClient()
                oss_url, expiration = oss.generate_url(key=material.oss_path, expires=604800)
                if not oss_url or not expiration:
                    logger.error(f"oss_url过期后生成预签名URL失败: {material.oss_path}")
                    raise RuntimeError()
                material.oss_url = oss_url
                material.oss_expiration = expiration
                update_dict["oss_url"] = oss_url
                update_dict["oss_expiration"] = expiration

                # 视频类的预览图也需要重新生成
                if material.type == MaterialType.VIDEO.name:
                    preview_url = oss.generate_video_preview_url(video_oss_path=material.oss_path, expires=604800)
                    assert preview_url is not None, f"生成视频预览图失败！"
                    material.oss_preview_url = preview_url
                    update_dict["oss_preview_url"] = preview_url
                self._material_repo.update_material(material.material_id, update_dict)
                logger.info(f"更新素材{material.material_id}的oss_url为: {material.oss_url}")
            return material
        except Exception as e:
            logger.error(f"更新oss_url失败: {e}")
        return material

    def query_online_media_materials_by_cid(self, cid: str) -> dict[str, list[dict]]:
        """
        查询指定企业下所有在线状态的图片和视频素材，按素材组分组
        Args:
            cid: 企业ID

        Returns:
            dict[str, list[dict]]: 按素材组名分组的素材列表
        """
        try:
            # 1. 先查询当前cid下所有的在线类的图片、视频类的素材
            material_dos = self._material_repo.get_online_media_materials_by_cid(cid)

            if not material_dos:
                return {}

            # 2. 按照group_id对素材进行分组
            from collections import defaultdict
            grouped_materials = defaultdict(list)

            for material_do in material_dos:
                material = self.update_oss_url(Material.from_do(material_do))
                grouped_materials[material.group_id].append({
                    "material_id": material.material_id,
                    "name": material.name,
                    "url": material.oss_preview_url if material.type == MaterialType.VIDEO.name else material.oss_url,
                    "type": material.type
                })

            # 3. 根据group_id查询group_name，组装最终格式
            result = {}
            for group_id, materials in grouped_materials.items():
                # 直接调用repo层查询分组信息
                group_do = self._group_repo.get_group_by_id(group_id)
                if group_do and not group_do.is_deleted:
                    group_name = group_do.group_name
                    result[group_name] = materials

            return result

        except Exception as e:
            logger.error(f"查询企业在线媒体素材失败: {e}")
            return {}
