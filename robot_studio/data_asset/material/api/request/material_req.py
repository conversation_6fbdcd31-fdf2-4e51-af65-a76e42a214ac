from datetime import datetime
from typing import List, Optional

from pydantic import Field

from robot_studio.common.base_request import BaseRequest
from robot_studio.data_asset.material.model import Material, Source


class MaterialBaseReq(BaseRequest):
    """创建素材请求"""
    name: str = Field(..., description="素材名称")

    material_id: str | None = Field(default=None, description="素材的MID")

    content_type: str = Field(..., description="内容类型")

    content_digest: str | None = Field(default=None, description="内容摘要")

    group_id: str = Field(..., description="素材组ID")

    tags: List[str] = Field(..., description="素材自定义标签")

    longtime_valid: Optional[bool] = Field(default=False, description="是否长期有效")

    valid_date: Optional[datetime] = Field(default_factory=datetime.now, description="素材生效日期")

    invalid_date: Optional[datetime] = Field(default=None, description="素材失效日期，空代表永久有效")

    material_content: str | None = Field(default=None, description="素材内容")

    source: str = Field(default=Source.INTERNAL.name, description="素材来源，外部素材，内部设计或AIGC")

    uid: str = Field(None, description="用户ID")

    user_name: str = Field(None, description="创建用户名")

    oss_url: str | None = Field(default=None, description="oss访问链接")

    oss_expiration: datetime | None = Field(default=None, description="oss访问链接")

    oss_path: str | None = Field(default=None, description="oss的文件路径")

    def to_model(self) -> Material:
        """转化为素材类对象"""
        return Material(
            material_id=self.material_id,
            name=self.name,
            type=self.content_type,
            source=self.source,
            group_id=self.group_id,
            tags=self.tags,
            longtime_valid=self.longtime_valid,
            valid_date=self.valid_date,
            invalid_date=self.invalid_date,
            oss_url=self.oss_url,
            oss_path=self.oss_path,
            oss_expiration=self.oss_expiration,
            uid=self.uid,
            user_name=self.user_name,
            content_digest=self.content_digest,
            material_content=self.material_content,  # 文本类型的素材内容
        )
