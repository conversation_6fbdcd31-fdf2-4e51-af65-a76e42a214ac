import logging
from collections import defaultdict
from typing import Optional

from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.common.base_group import GroupType, SubGroupType
from robot_studio.common.api_handler import api_handler
from robot_studio.data_asset.material.api.result.group_res import GroupCreateRes, GroupListRes, GroupRes
from robot_studio.data_asset.material.api.result.group_res import SubTypeGroupListRes, SubTypeGroupRes
from robot_studio.data_asset.material.core import MaterialGroupManager
from robot_studio.data_asset.material.core import MaterialManager
from robot_studio.data_asset.material.model import MaterialGroup

logger = logging.getLogger(__name__)


class MaterialGroupAPI:
    def __init__(self):
        self._group_manager = MaterialGroupManager()
        self._material_manager = MaterialManager()

    @staticmethod
    def get_sub_group_key_from_value(display_value: str) -> Optional[str]:
        for key, value in SubGroupType.__members__.items():
            if value == display_value:
                return key
        return None

    @api_handler
    def add_material_group(self,
                           group_name: str,
                           group_desc: str,
                           cid: str,
                           sub_group_type: str,
                           uid: str,
                           username: str,
                           tags: list[str]) -> GroupCreateRes:
        """添加素材组
        Args:
            "group_name": "2025年度小红书暑期早鸟班宣传素材组", # 分组名称
            "group_desc":"壹同未来2025年度小红书暑期早鸟班宣传素材组", # 分组描述
            "cid":"CID_0001", # 企业ID
            "sub_group_type":"XHS", # 素材组投放平台类型
        Returns:
            "group_id":"GID_0719552", # 分组ID
            "success":true, # 分组描述
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:

            # 创建素材组模型
            new_group = MaterialGroup(
                group_name=group_name,
                group_desc=group_desc,
                cid=cid,
                sub_group_type=sub_group_type,
                create_uid=uid,
                create_user=username,
                tags=tags,
            )

            # 添加到数据库
            result = self._group_manager.create_material_group(new_group)

            # 返回成功结果
            return GroupCreateRes(
                group_id=result.group_id,
                success=True
            )

        except Exception as e:
            logger.error(f"添加知识组失败: {e}")
            # 返回失败结果
            return GroupCreateRes(
                group_id="",
                success=False,
                error_msg=f"添加知识组失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def del_group(self, group_id: str) -> BaseResult:
        """删除素材组（软删除）
        Args:
            group_id: 素材组ID
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 软删除知识组记录
            result = self._group_manager.delete_material_group(group_id)

            if not result:
                # 检查素材组是否存在或状态是否已删除
                group = self._group_manager.query_material_group_by_id(group_id)

                if not group:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材组不存在: {group_id}",
                        error_code="NOT_FOUND"
                    )

                if group.is_deleted:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材组已经被删除: {group_id}",
                        error_code="ALREADY_DELETED"
                    )

                return BaseResult(
                    success=False,
                    error_msg=f"删除素材组失败: {group_id}",
                    error_code="DB_ERROR"
                )
            else:
                # 返回成功结果
                return BaseResult(
                    success=True
                )

        except Exception as e:
            logger.error(f"删除素材组失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"删除素材组失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def update_group(self,
                     group_id: str,
                     cid: str,
                     group_name: str,
                     group_desc: str,
                     uid: str,
                     username: str,
                     tags: list[str],
                     sub_group_type: str) -> BaseResult:
        """
        更新素材组
        Args:
            group_id: 素材组ID
            cid: 企业ID
            group_name: 素材组名称
            group_desc: 素材组描述
            sub_group_type: 素材组投放平台
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 更新素材组记录
            update_data = MaterialGroup(
                group_id=group_id,
                group_name=group_name,
                cid=cid,
                group_type=GroupType.MATERIAL.name,
                group_desc=group_desc,
                create_uid=uid,
                create_user=username,
                tags=tags,
                sub_group_type=sub_group_type,
            )

            result = self._group_manager.update_material_group(group_id, update_data=update_data)

            if not result:
                # 检查素材组是否存在或状态是否已删除
                group = self._group_manager.query_material_group_by_id(group_id)

                if not group:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材组不存在: {group_id}",
                        error_code="NOT_FOUND"
                    )

                if group.is_deleted:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材组已经被删除: {group_id}",
                        error_code="ALREADY_DELETED"
                    )

                return BaseResult(
                    success=False,
                    error_msg=f"更新素材组失败: {group_id}",
                    error_code="DB_ERROR"
                )
            else:
                # 返回成功结果
                return BaseResult(
                    success=True
                )

        except Exception as e:
            logger.error(f"更新素材组失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"更新素材组失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def query_all_material_groups(self, cid: str) -> SubTypeGroupListRes:
        """
        查询指定企业下的所有素材组
        Args:
            cid: 企业ID
        Returns:
            "data":[
                {
                    "sub_type": "XHS",
                    "groups": [
                        {
                            "group_id":"GID_0719552", # 分组ID
                            "group_name":"2025年度小红书暑期早鸟班宣传素材", # 分组名称
                            "group_desc":"壹同未来2025年度小红书暑期早鸟班宣传素材", # 分组描述
                            "sub_group_type":"XHS", # 素材组类型
                            "cid": "CID_98dc78", # 关联公司id
                            "rel_resource_id": "KID_08293482", # 关联资源id
                            "rel_resource_type": "Knowledge" # 关联资源类型
                        }，
                        {...}
                    ]
                },
                {
                    "sub_type": "DY",
                    "groups": [
                        {
                            "group_id":"GID_0782773", # 分组ID
                            "group_name":"2025年度抖音暑期进阶班宣传素材", # 分组名称
                            "group_desc":"壹同未来2025年度抖音暑期进阶班宣传素材", # 分组描述
                            "sub_group_type":"DY", # 素材组类型
                            "cid": "CID_98dc78", # 关联公司id
                            "rel_resource_id": "KID_77899874", # 关联资源id
                            "rel_resource_type": "Knowledge" # 关联资源类型
                        }，
                        {...}
                    ]
                },
            ]
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 查询素材组记录（只返回未删除的记录）
            groups = self._group_manager.query_all_material_groups(cid)
            groups_dict = defaultdict(list)
            for group in groups:
                # 查询一遍当前group_id下知识或素材数量
                resource_cnt = self._material_manager.count_valid_materials_by_group(group_id=group.group_id)
                group_res = GroupRes(
                    group_id=group.group_id,
                    group_name=group.group_name,
                    group_desc=group.group_desc,
                    group_type=group.group_type,
                    cid=group.cid,
                    sub_group_type=group.sub_group_type,
                    rel_resource_id=group.rel_resource_id,
                    rel_resource_type=group.rel_resource_type,
                    uid=group.create_uid,
                    username=group.create_user,
                    tags=group.tags,
                    resource_count=resource_cnt,
                    gmt_create=group.gmt_create.strftime("%Y-%m-%d %H:%M:%S"),
                )
                # 按照 sub_group_type 归类
                groups_dict[group.sub_group_type].append(group_res)


            # 将分类结果转化为SubTypeGroupRes对象
            group_res_list = []
            for k in groups_dict.keys():
                group_list = groups_dict[k]
                sub_type_group_res = SubTypeGroupRes(
                    sub_type=k,
                    groups=group_list
                )
                group_res_list.append(sub_type_group_res)
            # 返回成功结果
            return SubTypeGroupListRes(
                groups=group_res_list,
                success=True
            )

        except Exception as e:
            logger.error(f"查询素材组失败: {e}")
            # 返回失败结果
            return SubTypeGroupListRes(
                groups=[],
                success=False,
                error_msg=f"查询素材组失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def query_material_groups_by_platform(self, cid: str, platform: str) -> GroupListRes:
        """
        查询指定企业下，按照指定投放平台筛选得到的素材组
        Args:
            cid: 企业id
            platform: 投放平台类型
        Returns:
            [
                {
                    "group_id":"GID_0719552", # 分组ID
                    "group_name":"2025年度小红书暑期早鸟班宣传素材", # 分组名称
                    "group_desc":"壹同未来2025年度小红书暑期早鸟班宣传素材", # 分组描述
                    "group_type":"素材组", # 分组类型
                    "cid":"CID_0001", # 企业ID
                    "sub_group_type":"XHS" # 投放平台
                },
                ...
            ],
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            # 查询符合条件的素材组记录（只返回未删除的记录）
            groups = self._group_manager.query_material_groups_by_platform(cid, platform=platform)

            # 将MaterialGroup对象转换为GroupRes对象
            group_res_list = []
            for group in groups:
                group_data = group.model_dump()
                # 查询一遍当前group_id下知识或素材数量
                group_id = group_data.get("group_id", "")
                resource_cnt = self._material_manager.count_valid_materials_by_group(group_id=group_id)
                # 确保所有必需字段都存在
                group_res = GroupRes(
                    group_id=group_data.get("group_id", ""),
                    group_name=group_data.get("group_name", ""),
                    group_desc=group_data.get("group_desc", ""),
                    group_type=group_data.get("group_type", ""),
                    cid=group_data.get("cid", ""),
                    sub_group_type=group_data.get("sub_group_type", ""),
                    rel_resource_id=group_data.get("rel_resource_id", ""),
                    rel_resource_type=group_data.get("rel_resource_type", ""),
                    uid=group_data.get("uid", ""),
                    username=group_data.get("username", ""),
                    tags=group_data.get("tags", ""),
                    resource_cnt=resource_cnt
                )
                group_res_list.append(group_res)

            # 返回成功结果
            return GroupListRes(
                groups=group_res_list,
                success=True
            )

        except Exception as e:
            logger.error(f"查询知识组失败: {e}")
            # 返回失败结果
            return GroupListRes(
                groups=[],
                success=False,
                error_msg=f"查询知识组失败: {str(e)}",
                error_code="DB_ERROR"
            )
