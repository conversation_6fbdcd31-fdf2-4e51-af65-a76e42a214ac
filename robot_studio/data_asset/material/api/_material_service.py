import logging

from robot_studio.common.base_result import BaseResult
from robot_studio.common.api_handler import api_handler
from robot_studio.data_asset.material.api.request.material_req import MaterialBaseReq
from robot_studio.data_asset.material.api.result.material_res import CreateMaterialRes, MaterialRes, MaterialListRes
from robot_studio.data_asset.material.core import MaterialManager
from robot_studio.data_asset.material.model import MaterialSegment

logger = logging.getLogger(__name__)


class MaterialService:
    def __init__(self):
        self._material_manager = MaterialManager()

    @api_handler
    def create_material(self, req: MaterialBaseReq) -> CreateMaterialRes:
        """
        创建素材
        """
        material = self._material_manager.create(req.to_model())
        return CreateMaterialRes(data=material.material_id)

    @api_handler
    def del_material(self, mid: str) -> BaseResult:
        """
        删除素材
        Args:
            mid: 素材ID
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            result = self._material_manager.delete_material(mid)
            if result:
                # 删除成功，返回成功结果
                return BaseResult.success_result()
            else:
                return BaseResult(
                    success=False,
                    error_msg=f"删除素材失败: {mid}",
                    error_code="DB_ERROR"
                )
        except Exception as e:
            logger.error(f"删除素材失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"删除素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def batch_del_material(self, mids: list[str]) -> BaseResult:
        """
        批量删除素材
        Args:
            mids: 素材ID列表
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        if not mids:
            return BaseResult(
                success=False,
                error_msg="素材ID列表不能为空",
                error_code="VALIDATION_ERROR"
            )

        success_count = 0
        failed_mids = []

        for mid in mids:
            try:
                result = self._material_manager.delete_material(mid)
                if result:
                    success_count += 1
                else:
                    failed_mids.append(mid)
            except Exception as e:
                logger.error(f"删除素材失败: {mid}, 错误: {e}")
                failed_mids.append(mid)

        if not failed_mids:
            return BaseResult.success_result()
        else:
            return BaseResult(
                success=len(failed_mids) < len(mids),  # 部分成功也算成功
                error_msg=f"成功删除{success_count}个素材，失败{len(failed_mids)}个: {', '.join(failed_mids)}",
                error_code="PARTIAL_FAILURE"
            )

    @api_handler
    def online_material(self, mid: str) -> BaseResult:
        """
        上线素材
        Args:
            mid: 素材ID
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            result = self._material_manager.online(mid)
            if result:
                # 删除成功，返回成功结果
                return BaseResult.success_result()
            else:
                return BaseResult(
                    success=False,
                    error_msg=f"上线素材失败: {mid}",
                    error_code="DB_ERROR"
                )
        except Exception as e:
            logger.error(f"上线素材失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"上线素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def batch_online_material(self, mids: list[str]) -> BaseResult:
        """
        批量上线素材
        Args:
            mids: 素材ID列表
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        if not mids:
            return BaseResult(
                success=False,
                error_msg="素材ID列表不能为空",
                error_code="VALIDATION_ERROR"
            )

        success_count = 0
        failed_mids = []

        for mid in mids:
            try:
                result = self._material_manager.online(mid)
                if result:
                    success_count += 1
                else:
                    failed_mids.append(mid)
            except Exception as e:
                logger.error(f"上线素材失败: {mid}, 错误: {e}")
                failed_mids.append(mid)

        if not failed_mids:
            return BaseResult.success_result()
        else:
            return BaseResult(
                success=len(failed_mids) < len(mids),  # 部分成功也算成功
                error_msg=f"成功上线{success_count}个素材，失败{len(failed_mids)}个: {', '.join(failed_mids)}",
                error_code="PARTIAL_FAILURE"
            )

    @api_handler
    def offline_material(self, mid: str) -> BaseResult:
        """
        下线素材
        :param mid: 素材ID
        :return: BaseResult
        """
        try:
            result = self._material_manager.offline(mid)
            if result:
                # 删除成功，返回成功结果
                return BaseResult.success_result()
            else:
                return BaseResult(
                    success=False,
                    error_msg=f"下线素材失败: {mid}",
                    error_code="DB_ERROR"
                )
        except Exception as e:
            logger.error(f"下线素材失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"下线素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def batch_offline_material(self, mids: list[str]) -> BaseResult:
        """
        批量下线素材
        Args:
            mids: 素材ID列表
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        if not mids:
            return BaseResult(
                success=False,
                error_msg="素材ID列表不能为空",
                error_code="VALIDATION_ERROR"
            )

        success_count = 0
        failed_mids = []

        for mid in mids:
            try:
                result = self._material_manager.offline(mid)
                if result:
                    success_count += 1
                else:
                    failed_mids.append(mid)
            except Exception as e:
                logger.error(f"下线素材失败: {mid}, 错误: {e}")
                failed_mids.append(mid)

        if not failed_mids:
            return BaseResult.success_result()
        else:
            return BaseResult(
                success=len(failed_mids) < len(mids),  # 部分成功也算成功
                error_msg=f"成功下线{success_count}个素材，失败{len(failed_mids)}个: {', '.join(failed_mids)}",
                error_code="PARTIAL_FAILURE"
            )

    @api_handler
    def update_material(self, mid: str, update_data: MaterialBaseReq) -> BaseResult:
        """
        更新素材
        Args:
            mid: 素材ID
            update_data: 待更新素材信息
        Returns:
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            new_update_data = update_data.to_model()

            result = self._material_manager.update_material(mid, update_data=new_update_data)

            if not result:
                # 检查素材是否存在或状态是否已经删除
                material_res = self._material_manager.query_material(mid)

                if not material_res:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材不存在: {mid}",
                        error_code="NOT_FOUND"
                    )

                if material_res.is_deleted:
                    return BaseResult(
                        success=False,
                        error_msg=f"素材已经被删除: {mid}",
                        error_code="ALREADY_DELETED"
                    )

                return BaseResult(
                    success=False,
                    error_msg=f"更新素材失败: {mid}",
                    error_code="DB_ERROR"
                )
            else:
                # 返回成功结果
                return BaseResult(
                    success=True
                )
        except Exception as e:
            logger.error(f"更新素材失败: {e}")
            # 返回失败结果
            return BaseResult(
                success=False,
                error_msg=f"更新素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def query_material_by_id(self, mid: str) -> MaterialRes:
        """
        查询指定id对应的素材
        Args:
            mid: 素材ID
        Returns:
            {
                "material_id":"MID_0519654", # 分组ID
                "name": "2025年度小红书暑期英语早鸟班宣传文案", # 素材名称
                "type": "DOC", # 素材类型：DOC->文本，PIC->图片
                "content_digest": "2025年度小红书暑期英语早鸟班宣传文案", # 内容摘要
                "group_id": "GID_0719552", # 关联素材组id
                "tags": ["小红书", "英语早鸟班", "2025年度暑秋"], # 素材自定义标签
                "valid_date": "2025-06-30", # 生效日期
                "invalid_date": "2025-09-01", # 过期日期【修改了】
                "material_content": "英语渣别逃！是时候逆袭了！2025 暑期英语早鸟班开抢，现在报名立省 30% 。
                    课程由剑桥认证外教搭配 985 学霸中教，双语教学助你沉浸式练口语，告别开口社死；趣味场景化课程涵盖刷剧、旅游、留学干货，
                    学完秒变行走的翻译器；小班制 1v6 教学，老师全程盯学，从音标到写作全面提升。
                    早鸟特惠倒计时 5 天，前 20 名报名还送定制英语学习大礼包，暑假偷偷卷起来，
                    开学惊艳所有人，点击即可抢占名额。", # 素材内容
                "source": "INS", # 来源
            },
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            material_res = self._material_manager.query_material(mid)
            if material_res:
                # 新建MaterialSegment对象作为接口返回数据
                _material_segment = MaterialSegment.from_model(material_res)
                # 返回成功结果
                return MaterialRes(
                    data=_material_segment,
                    success=True
                )
            else:
                return MaterialRes(
                    data=None,
                    success=False,
                    error_msg=f"素材未存在: {mid}",
                )
        except Exception as e:
            logger.error(f"查询素材失败: {e}")
            # 返回失败结果
            return MaterialRes(
                data=None,
                success=False,
                error_msg=f"查询素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def query_material_by_group(self, group_id: str) -> MaterialListRes:
        """
        根据指定素材组ID查询下属的素材
        Args:
            group_id: 素材组ID
        Returns:
            [
                {
                    "material_id":"MID_0519654", # 分组ID
                    "name": "2025年度小红书暑期英语早鸟班宣传文案", # 素材名称
                    "type": "DOC", # 素材类型：DOC->文本，PIC->图片
                    "content_digest": "2025年度小红书暑期英语早鸟班宣传文案", # 内容摘要
                    "group_id": "GID_0719552", # 关联素材组id
                    "tags": ["小红书", "英语早鸟班", "2025年度暑秋"], # 素材自定义标签
                    "valid_date": "2025-06-30", # 生效日期
                    "invalid_date": "2025-09-01", # 过期日期【修改了】
                    "material_content": "英语渣别逃！是时候逆袭了！2025 暑期英语早鸟班开抢，现在报名立省 30% 。
                        课程由剑桥认证外教搭配 985 学霸中教，双语教学助你沉浸式练口语，告别开口社死；趣味场景化课程涵盖刷剧、旅游、留学干货，
                        学完秒变行走的翻译器；小班制 1v6 教学，老师全程盯学，从音标到写作全面提升。
                        早鸟特惠倒计时 5 天，前 20 名报名还送定制英语学习大礼包，暑假偷偷卷起来，
                        开学惊艳所有人，点击即可抢占名额。", # 素材内容
                    "source": "INS", # 来源
                },
                ...
            ],
            "success":true, # 是否成功
            "error_msg":"异常原因报错", # 错误码信息
            "error_code":"错误码" # 错误码
        """
        try:
            material_res_list = self._material_manager.query_materials_by_group(group_id)
            if material_res_list:
                # 构建MaterialSegment类作为接口返回结果
                _material_segment_lst = []
                for material_res in material_res_list:
                    _material_segment = MaterialSegment.from_model(material_res)
                    _material_segment_lst.append(_material_segment)
                # 返回成功结果
                return MaterialListRes(
                    data=_material_segment_lst,
                    success=True
                )
            elif material_res_list is not None:
                # 仅有素材组，但素材组下不存在素材的情况下，返回空列表
                return MaterialListRes(
                    data=[],
                    success=True
                )
            else:
                return MaterialListRes(
                    data=[],
                    success=False,
                    error_msg="素材组不存在",
                    error_code="NOT_FOUND"
                )
        except Exception as e:
            logger.error(f"查询素材失败: {e}")
            # 返回失败结果
            return MaterialListRes(
                data=[],
                success=False,
                error_msg=f"查询素材失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def query_online_media_materials_by_cid(self, cid: str) -> BaseResult:
        """
        查询指定企业下所有在线状态的图片和视频素材，按素材组分组
        Args:
            cid: 企业ID
        Returns:
            BaseResult with data containing grouped materials by group name
        """
        try:
            result_data = self._material_manager.query_online_media_materials_by_cid(cid)
            return BaseResult(
                data=result_data,
                success=True
            )
        except Exception as e:
            logger.error(f"查询企业在线媒体素材失败: {e}")
            return BaseResult(
                data={},
                success=False,
                error_msg=f"查询企业在线媒体素材失败: {str(e)}",
                error_code="DB_ERROR"
            )
