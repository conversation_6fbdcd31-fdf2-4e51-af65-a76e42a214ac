from typing import List

from pydantic import Field

from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.material.model import MaterialSegment


class CreateMaterialRes(BaseResult):
    data: str | None = Field(default=None, description="创建素材时返回的素材MID")


class MaterialRes(BaseResult):
    data: MaterialSegment | None = Field(default=None, description="素材")


class MaterialListRes(BaseResult):
    data: List[MaterialSegment] | None = Field(default=None, description="素材列表")
