from typing import Optional, List

from pydantic import BaseModel

from robot_studio.common.base_result import BaseResult


class GroupRes(BaseModel):
    group_id: str
    group_name: str
    group_desc: Optional[str] = None
    group_type: str
    cid: str
    sub_group_type: Optional[str]
    rel_resource_id: Optional[str]
    rel_resource_type: Optional[str]
    uid: str | None = None
    username: str | None = None
    tags: list[str] | None = None
    resource_count: int
    gmt_create: str


class SubTypeGroupRes(BaseModel):
    sub_type: str
    groups: List[GroupRes]


class GroupListRes(BaseResult):
    groups: List[GroupRes]


class SubTypeGroupListRes(BaseResult):
    groups: List[SubTypeGroupRes]


class GroupCreateRes(BaseResult):
    group_id: str = ""
