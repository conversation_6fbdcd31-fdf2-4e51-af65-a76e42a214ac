import json
from typing import Optional

from robot_studio.data_asset.common.base_group import BaseGroup
from robot_studio.database.mysql.table_schema import ResourceGroupDO


class MaterialGroup(BaseGroup):
    """素材组模型"""
    sub_group_type: Optional[str] = None
    """分组类型，SubGroupType"""

    rel_resource_id: Optional[str] = None
    """关联的资源ID, 仅素材组有此字段"""

    rel_resource_type: Optional[str] = None
    """关联的资源类型，Knowledge/Material"""

    @classmethod
    def from_do(cls, material_group: ResourceGroupDO):
        """
        数据库DO模型转为Model实例
        Args:
            material_group:数据库DO模型

        Returns:

        """
        instance = cls()
        instance.group_id = material_group.group_id
        instance.group_name = material_group.group_name
        instance.group_desc = material_group.group_desc
        instance.group_type = material_group.group_type
        instance.cid = material_group.cid
        instance.sub_group_type = material_group.sub_group_type
        instance.rel_resource_id = material_group.rel_resource_id
        instance.rel_resource_type = material_group.rel_resource_type
        instance.create_uid = material_group.create_uid
        instance.create_user = material_group.create_user
        instance.gmt_create = material_group.gmt_create
        instance.tags = material_group.tags if material_group.tags else None
        return instance

    def to_do(self) -> ResourceGroupDO:
        """
        Model实例转为DO模型
        Returns:
            ResourceGroupDO: 转换的DO模型

        """
        return ResourceGroupDO(group_id=self.group_id,
                               group_name=self.group_name,
                               group_desc=self.group_desc,
                               group_type=self.group_type,
                               cid=self.cid,
                               sub_group_type=self.sub_group_type,
                               rel_resource_id=self.rel_resource_id,
                               rel_resource_type=self.rel_resource_type,
                               create_uid=self.create_uid,
                               create_user=self.create_user,
                               tags=self.tags if self.tags else None
                               )
