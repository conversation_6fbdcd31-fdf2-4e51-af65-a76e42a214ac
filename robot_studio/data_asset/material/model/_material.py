import json
from datetime import datetime
from enum import Enum
from typing import Optional, Self

from pydantic import BaseModel

from robot_studio.data_asset.common.base_asset import BaseAsset
from robot_studio.database.mysql.table_schema import MaterialDO


class MaterialType(Enum):
    TEXT = "文本",
    IMAGE = "图片",
    VIDEO = "视频",


class Source(Enum):
    EXTERNAL = "外部素材"
    INTERNAL = "内部设计"
    AIGC = "AI生成"


class Material(BaseAsset):
    """素材的核心模型"""
    material_id: str | None = None
    """素材ID，MID开头"""

    type: str | None = None
    """素材模态，关联枚举MaterialType"""

    tags: list[str] | None = None
    """素材内容标签"""

    source: str | None = None
    """素材来源，关联Source枚举"""

    content_digest: str | None = None
    """内容摘要"""

    material_content: str | None = None
    """素材内容，素材类型为文本时取该字段"""

    oss_url: str | None = None
    """oss资源地址，oss类型存储时，使用该字段"""

    oss_path: str | None = None
    """oss路径，类型存储时，使用该字段"""

    oss_expiration: datetime | None = None
    """oss_url的过期时间"""

    oss_preview_url : str | None = None
    """视频素材的首祯预览图，过期时间和oss_url一致"""

    uid: str | None = None
    """用户ID"""

    user_name: str | None = None
    """用户名"""

    gmt_create: Optional[datetime] = None
    """创建时间"""

    gmt_modified: Optional[datetime] = None
    """修改时间"""

    @classmethod
    def from_do(cls, material: MaterialDO):
        """
        数据库DO模型转为Model实例
        Args:
            material:数据库DO模型

        Returns:

        """
        instance = cls()
        instance.material_id = material.mid
        instance.name = material.name
        instance.type = material.type
        instance.group_id = material.group_id
        instance.source = material.source
        instance.content_digest = material.content_digest
        instance.tags = json.loads(material.tags) if material.tags else None
        instance.valid_date = material.valid_date
        instance.invalid_date = material.invalid_date
        instance.longtime_valid = material.longtime_valid
        instance.status = material.status
        instance.version = int(float(material.version))   if material.version else 1
        instance.material_content = material.material_content
        instance.oss_url = material.oss_url
        instance.oss_path = material.oss_path
        instance.oss_expiration = material.oss_expiration
        instance.oss_preview_url = material.oss_preview_url
        instance.uid = material.create_uid
        instance.user_name = material.create_user
        instance.gmt_create = material.gmt_create
        instance.gmt_modified = material.gmt_modified
        return instance

    def to_do(self) -> MaterialDO:
        """
        Model实例转为DO模型
        Returns:
            CompanyDO: 转换的DO模型

        """
        return MaterialDO(mid=self.material_id,
                          name=self.name,
                          type=self.type,
                          group_id=self.group_id,
                          source=self.source,
                          content_digest=self.content_digest,
                          tags=json.dumps(self.tags) if self.tags else None,
                          valid_date=self.valid_date,
                          invalid_date=self.invalid_date,
                          longtime_valid=self.longtime_valid,
                          status=self.status,
                          version= str( self.version) if self.version else None,
                          material_content=self.material_content,
                          oss_url=self.oss_url,
                          oss_path=self.oss_path,
                          oss_expiration=self.oss_expiration,
                          create_uid=self.uid,
                          create_user=self.user_name,
                          oss_preview_url=self.oss_preview_url
                          )


class MaterialSegment(BaseModel):
    """用于返回素材关键字段的模型"""
    material_id: str | None = None
    """素材ID，MID开头"""

    name: str | None = None
    """素材名称"""

    type: str | None = None
    """素材模态，关联枚举MaterialType"""

    content_digest: str | None = None
    """内容摘要"""

    group_id: str | None = None
    """归属的素材组ID"""

    tags: list[str] | None = None
    """素材内容标签"""

    valid_date: Optional[datetime] = None
    """素材生效日期"""

    invalid_date: Optional[datetime] = None
    """素材失效日期，为空代表永久有效"""

    longtime_valid: Optional[bool] = None
    """是否长期有效"""

    material_content: str | None = None
    """素材内容，素材类型为文本时取该字段"""

    source: str | None = None
    """素材来源，关联Source枚举"""

    oss_url: str | None = None
    """oss类型存储时，使用该字段"""

    oss_preview_url: str | None = None
    """视频素材的预览URL"""

    status: str
    """素材状态"""

    uid: str | None = None
    """用户ID"""

    user_name: str | None = None
    """用户名"""

    gmt_create: str | None = None
    """创建时间"""

    gmt_modified: str | None = None
    """修改时间"""

    @classmethod
    def from_model(cls, model: Material) -> Self:
        return cls(
            material_id=model.material_id,
            name=model.name,
            type=model.type,
            content_digest=model.content_digest,
            group_id=model.group_id,
            tags=model.tags,
            valid_date=model.valid_date,
            invalid_date=model.invalid_date,
            longtime_valid=model.longtime_valid,
            material_content=model.material_content,
            source=model.source,
            oss_url=model.oss_url,
            oss_preview_url=model.oss_preview_url,
            status=model.status,
            uid=model.uid,
            user_name=model.user_name,
            gmt_create=model.gmt_create.strftime("%Y-%m-%d %H:%M:%S"),
            gmt_modified=model.gmt_modified.strftime("%Y-%m-%d %H:%M:%S"),
        )