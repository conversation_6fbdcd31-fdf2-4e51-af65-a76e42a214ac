"""
聊天块管理器
提供聊天块的CRUD功能，专注于纯粹的数据操作
"""

import logging
from typing import Optional, List, Literal
from datetime import datetime

from robot_studio.component.chat_chunk.model._chat_chunk import ChatChunk
from robot_studio.database.mysql.repository._chat_chunk_repository import ChatChunkRepository
from robot_studio.database.mysql.table_schema._chat_chunk import RoleType, ChunkType

logger = logging.getLogger(__name__)


class ChatChunkManager:
    """聊天块管理器，提供聊天块的CRUD功能"""

    def __init__(self):
        self._chat_chunk_repository = ChatChunkRepository()
        self._session_repository = None  # 延迟初始化避免循环导入

    def create_chat_chunk(self, chat_chunk: ChatChunk) -> ChatChunk:
        """
        创建聊天块
        Args:
            chat_chunk: 聊天块模型

        Returns:
            ChatChunk: 创建后的聊天块模型
        """
        try:
            # 使用to_do方法转换为ChatChunkDO
            chat_chunk_do = chat_chunk.to_do()

            # 创建聊天块
            created_chat_chunk = self._chat_chunk_repository.create_chat_chunk(chat_chunk_do)

            # 使用from_do方法转换回ChatChunk
            return ChatChunk.from_do(created_chat_chunk)
        except Exception as e:
            logger.error(f"创建聊天块失败: {e}")
            raise

    def get_chat_chunk_by_id(self, chunk_id: str) -> Optional[ChatChunk]:
        """
        根据ID获取聊天块
        Args:
            chunk_id: 聊天块ID

        Returns:
            Optional[ChatChunk]: 聊天块模型
        """
        try:
            chat_chunk_do = self._chat_chunk_repository.get_chat_chunk_by_id(chunk_id)
            if chat_chunk_do:
                return ChatChunk.from_do(chat_chunk_do)
            return None
        except Exception as e:
            logger.error(f"获取聊天块失败: {e}")
            raise

    def get_chat_chunks_by_session_id(self, session_id: str, chunk_type: Optional[str] = None, 
                                     limit: Optional[int] = None, offset: Optional[int] = None) -> List[ChatChunk]:
        """
        根据会话ID获取聊天块列表
        Args:
            session_id: 会话ID
            chunk_type: 聊天块类型，可选
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            List[ChatChunk]: 聊天块列表
        """
        try:
            if chunk_type:
                chat_chunks_do = self._chat_chunk_repository.get_chat_chunks_by_type(session_id, chunk_type, limit)
            else:
                chat_chunks_do = self._chat_chunk_repository.get_chat_chunks_by_session_id(session_id, limit, offset)
            
            return [ChatChunk.from_do(chunk_do) for chunk_do in chat_chunks_do]
        except Exception as e:
            logger.error(f"获取聊天块列表失败: {e}")
            raise

    def search_chat_chunks(self, session_id: Optional[str] = None, keyword: Optional[str] = None,
                          chunk_type: Optional[Literal['Event', 'Message']] = None, start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None, task_id: Optional[str] = None,
                          span_id: Optional[str] = None, role_type: Optional[Literal['user', 'assistant']] = None,
                          chunk_sub_type: Optional[str] = None, chunk_status: Optional[str] = None,
                          limit: Optional[int] = None, offset: Optional[int] = None) -> List[ChatChunk]:
        """
        搜索聊天块
        Args:
            session_id: 会话ID
            keyword: 内容关键词
            chunk_type: 聊天块类型
            start_time: 开始时间
            end_time: 结束时间
            task_id: 任务ID
            span_id: 逻辑单元ID
            role_type: 角色类型
            chunk_sub_type: 聊天块子类型
            chunk_status: 聊天块状态
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            List[ChatChunk]: 聊天块列表
        """
        try:
            chat_chunks_do = self._chat_chunk_repository.search_chat_chunks(
                session_id=session_id,
                keyword=keyword,
                chunk_type=chunk_type,
                start_time=start_time,
                end_time=end_time,
                task_id=task_id,
                span_id=span_id,
                role_type=role_type,
                chunk_sub_type=chunk_sub_type,
                chunk_status=chunk_status,
                limit=limit,
                offset=offset
            )
            return [ChatChunk.from_do(chunk_do) for chunk_do in chat_chunks_do]
        except Exception as e:
            logger.error(f"搜索聊天块失败: {e}")
            raise

    def update_chat_chunk(self, chunk_id: str, update_data: dict) -> Optional[ChatChunk]:
        """
        更新聊天块
        Args:
            chunk_id: 聊天块ID
            update_data: 更新数据字典

        Returns:
            Optional[ChatChunk]: 更新后的聊天块模型
        """
        try:
            # 先获取聊天块DO对象
            chat_chunk_do = self._chat_chunk_repository.get_chat_chunk_by_id(chunk_id)
            if not chat_chunk_do:
                return None

            # 更新聊天块，使用数据库主键ID
            updated_chat_chunk_do = self._chat_chunk_repository.update_chat_chunk(chat_chunk_do.id, update_data)
            if updated_chat_chunk_do:
                return ChatChunk.from_do(updated_chat_chunk_do)
            return None
        except Exception as e:
            logger.error(f"更新聊天块失败: {e}")
            raise

    def delete_chat_chunk(self, chunk_id: str) -> bool:
        """
        删除聊天块
        Args:
            chunk_id: 聊天块ID

        Returns:
            bool: 删除是否成功
        """
        try:
            return self._chat_chunk_repository.delete_chat_chunk(chunk_id)
        except Exception as e:
            logger.error(f"删除聊天块失败: {e}")
            raise

    def get_session_chunk_count(self, session_id: str) -> int:
        """
        获取会话的聊天块数量
        Args:
            session_id: 会话ID

        Returns:
            int: 聊天块数量
        """
        try:
            return self._chat_chunk_repository.get_session_chunk_count(session_id)
        except Exception as e:
            logger.error(f"获取会话聊天块数量失败: {e}")
            raise

    def get_session_chunk_stats(self, session_id: str) -> dict:
        """
        获取会话聊天块统计信息
        Args:
            session_id: 会话ID

        Returns:
            dict: 统计信息
        """
        try:
            # 获取各类型聊天块数量
            user_chunks = self._chat_chunk_repository.get_chat_chunks_by_role_type(session_id, RoleType.USER)
            assistant_chunks = self._chat_chunk_repository.get_chat_chunks_by_role_type(session_id, RoleType.ASSISTANT)

            # 计算总数量
            all_chunks = self._chat_chunk_repository.get_chat_chunks_by_session_id(session_id)

            return {
                "session_id": session_id,
                "total_chunks": len(all_chunks),
                "user_chunks": len(user_chunks),
                "assistant_chunks": len(assistant_chunks)
            }
        except Exception as e:
            logger.error(f"获取会话聊天块统计失败: {e}")
            raise

    def get_session_info(self, session_id: str):
        """
        获取会话信息
        Args:
            session_id: 会话ID

        Returns:
            会话信息对象
        """
        try:
            if self._session_repository is None:
                from robot_studio.database.mysql.repository._component_session_repository import ComponentSessionRepository
                self._session_repository = ComponentSessionRepository()
            return self._session_repository.get_session_by_id(session_id)
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            raise

    def get_chat_chunks_by_task_id(self, task_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[ChatChunk]:
        """
        根据任务ID获取聊天块列表
        Args:
            task_id: 任务ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            List[ChatChunk]: 聊天块列表
        """
        try:
            chat_chunks_do = self._chat_chunk_repository.get_chat_chunks_by_task_id(task_id, limit, offset)
            return [ChatChunk.from_do(chunk_do) for chunk_do in chat_chunks_do]
        except Exception as e:
            logger.error(f"根据任务ID获取聊天块列表失败: {e}")
            raise

    def get_chat_chunks_by_span_id(self, span_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> List[ChatChunk]:
        """
        根据逻辑单元ID获取聊天块列表
        Args:
            span_id: 逻辑单元ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            List[ChatChunk]: 聊天块列表
        """
        try:
            chat_chunks_do = self._chat_chunk_repository.get_chat_chunks_by_span_id(span_id, limit, offset)
            return [ChatChunk.from_do(chunk_do) for chunk_do in chat_chunks_do]
        except Exception as e:
            logger.error(f"根据逻辑单元ID获取聊天块列表失败: {e}")
            raise

    def get_chat_chunks_by_role_type(self, session_id: str, role_type: Literal['user', 'assistant'], limit: Optional[int] = None) -> List[ChatChunk]:
        """
        根据会话ID和角色类型获取聊天块列表
        Args:
            session_id: 会话ID
            role_type: 角色类型
            limit: 限制返回数量

        Returns:
            List[ChatChunk]: 聊天块列表
        """
        try:
            chat_chunks_do = self._chat_chunk_repository.get_chat_chunks_by_role_type(session_id, role_type, limit)
            return [ChatChunk.from_do(chunk_do) for chunk_do in chat_chunks_do]
        except Exception as e:
            logger.error(f"根据角色类型获取聊天块列表失败: {e}")
            raise
