from datetime import datetime
from typing import Optional, Any, Dict, Self

from pydantic import BaseModel
from dataclasses import dataclass

from robot_studio.database.mysql.table_schema import ComponentSessionDO

class ComponentSession(BaseModel):
    """会话模型"""
    session_id: str | None = None
    """会话id"""

    user_id: str | None = None
    """用户id"""

    title: str | None = None
    """会话标题"""



    status: int | None = None
    """会话状态"""

    component_config: Dict[str, Any] | None = None
    """组件配置"""

    message_count: int | None = None
    """消息数量"""

    artifact_count: int | None = None
    """产物数量"""

    last_message_time: datetime | None = None
    """最后消息时间"""

    is_deleted: bool | None = None
    """是否删除"""

    gmt_create: datetime | None = None
    """创建时间"""

    gmt_modified: datetime | None = None
    """修改时间"""

    session_metadata: Dict[str, Any] | None = None
    """会话元数据"""

    @classmethod
    def from_do(cls, component_session: ComponentSessionDO) -> Self:
        import json
        instance = cls()
        instance.session_id = component_session.session_id
        instance.user_id = component_session.user_id
        instance.title = component_session.title
        instance.status = component_session.status

        # 处理JSON字符串字段
        if component_session.component_config:
            if isinstance(component_session.component_config, str):
                try:
                    instance.component_config = json.loads(component_session.component_config)
                except (json.JSONDecodeError, TypeError):
                    instance.component_config = component_session.component_config
            else:
                instance.component_config = component_session.component_config
        else:
            instance.component_config = None

        instance.message_count = component_session.message_count
        instance.artifact_count = component_session.artifact_count
        instance.last_message_time = component_session.last_message_time
        instance.is_deleted = component_session.is_deleted
        instance.gmt_create = component_session.gmt_create
        instance.gmt_modified = component_session.gmt_modified

        # 处理JSON字符串字段
        if component_session.session_metadata:
            if isinstance(component_session.session_metadata, str):
                try:
                    instance.session_metadata = json.loads(component_session.session_metadata)
                except (json.JSONDecodeError, TypeError):
                    instance.session_metadata = component_session.session_metadata
            else:
                instance.session_metadata = component_session.session_metadata
        else:
            instance.session_metadata = None

        return instance

    def to_do(self) -> ComponentSessionDO:
        import json

        # 处理component_config字段
        component_config_str = None
        if self.component_config:
            if isinstance(self.component_config, dict):
                component_config_str = json.dumps(self.component_config, ensure_ascii=False)
            else:
                component_config_str = self.component_config

        # 处理session_metadata字段
        session_metadata_str = None
        if self.session_metadata:
            if isinstance(self.session_metadata, dict):
                session_metadata_str = json.dumps(self.session_metadata, ensure_ascii=False)
            else:
                session_metadata_str = self.session_metadata

        return ComponentSessionDO(
            session_id=self.session_id,
            user_id=self.user_id,
            title=self.title,
            status=self.status,
            component_config=component_config_str,
            message_count=self.message_count,
            artifact_count=self.artifact_count,
            last_message_time=self.last_message_time,
            is_deleted=self.is_deleted,
            gmt_create=self.gmt_create,
            gmt_modified=self.gmt_modified,
            session_metadata=session_metadata_str
        )

# 会话状态枚举常量
class SessionStatus:
    ACTIVE = 1  # 活跃
    ARCHIVED = 2  # 归档
    DELETED = 3  # 删除
    @staticmethod
    def get_status_name(status_code: int) -> str:
        status_names = {
            1: "active",
            2: "archived",
            3: "deleted",
        }
        return status_names.get(status_code, "unknown")
    @staticmethod
    def get_status_code(status: str) -> int:
        status_name_codes = {
            "active": 1,
            "archived": 2,
            "deleted": 3,
        }
        return status_name_codes.get(status, "unknown")

@dataclass
class CreateSessionResult:
    """创建会话结果"""
    session_id:str
    user_id:str
    title:str
    status: str = "active"
    created_at: Optional[str] = None

@dataclass
class CreateSessionCommand:
    """创建会话命令"""
    user_id: str
    title: Optional[str] = None
    component_config: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """初始化后处理，设置默认标题"""
        if self.title is None:
            self.title = f"对话 - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

    def validate(self) -> bool:
        """验证命令参数"""
        if not self.user_id or not self.user_id.strip():
            return False
        return True

@dataclass
class GetSessionListQuery:
    page: int = 1
    size: int = 20
    status: Optional[str] = None
    keyword: Optional[str] = None

    def get_offset(self):
        return (self.page - 1) * self.size

    def get_limit(self):
        return self.size

@dataclass
class SessionListItemResult:
    """会话列表项结果"""
    session_id: str
    title:str
    status: str = "active"
    message_count: int = 0
    artifact_count: int = 0
    last_message_time: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

@dataclass
class SessionListResult:
    """会话列表结果"""
    user_id: str
    total: int
    page: int
    size: int
    sessions: list[SessionListItemResult]

@dataclass
class SessionDetailResult:
    """会话详情结果"""
    session_id: str
    user_id: str
    title: str
    status: str = "active"
    message_count: int = 0
    artifact_count: int = 0
    last_message_time: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    session_metadata: Optional[Dict[str, Any]] = None
    token_stats: Optional[Dict[str, Any]] = None

@dataclass
class UpdateSessionCommand:
    """更新会话命令"""
    session_id: str
    title: Optional[str] = None
    component_config: Optional[Dict[str, Any]] = None
    session_metadata: Optional[Dict[str, Any]] = None

@dataclass
class UpdateSessionResult:
    """更新会话结果"""
    session_id: str
    title: str
    updated_at: Optional[str] = None

@dataclass
class DeleteSessionResult:
    """删除会话结果"""
    session_id: str
    success: bool
    message: str

@dataclass
class ArchiveSessionResult:
    """归档会话结果"""
    session_id: str
    success: bool
    message: str

@dataclass
class SessionMessageResult:
    """会话消息结果"""
    message_id: str
    session_id: str
    parent_message_id: Optional[str] = None
    content: Optional[str] = None
    token_count: Optional[int] = None
    model_info: Optional[Dict[str, Any]] = None
    usage_info: Optional[Dict[str, Any]] = None
    message_metadata: Optional[Dict[str, Any]] = None
    artifact_count: int = 0
    artifacts: Optional[list[Dict[str, Any]]] = None
    status: Optional[int] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

@dataclass
class SearchSessionsQuery:
    """搜索会话查询"""
    user_id: str
    keyword: Optional[str] = None
    status: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    limit: int = 20
    offset: int = 0

@dataclass
class SearchSessionsResult:
    """搜索会话结果"""
    sessions: list[SessionListItemResult]
    total_count: int
    limit: int
    offset: int
    search_params: Dict[str, Any]

