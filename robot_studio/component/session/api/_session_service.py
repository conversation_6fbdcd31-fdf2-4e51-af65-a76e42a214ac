import logging
from typing import Op<PERSON>, Dict, <PERSON>, <PERSON><PERSON>

from robot_studio.component.session.manager._session_manager import SessionManager
from robot_studio.component.session.model import (
    CreateSessionCommand, CreateSessionResult, GetSessionListQuery, SessionListResult,
    UpdateSessionCommand, UpdateSessionResult, DeleteSessionResult, ArchiveSessionResult,
    SearchSessionsQuery, SearchSessionsResult,
    ComponentSession, SessionDetailResult, SessionListItemResult, SessionStatus
)
from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import BaseResult

logger = logging.getLogger(__name__)


class SessionService:
    """会话服务层，提供对外API接口"""

    def __init__(self):
        self._session_manager = SessionManager()

    @api_handler
    def create_session(self, command: CreateSessionCommand) -> BaseResult:
        """
        创建新会话
        Args:
            command: 创建会话命令

        Returns:
            Result[CreateSessionResult]: 创建结果
        """
        # 验证命令
        if not command.validate():
            return BaseResult.param_error_result("无效的创建会话命令")

        # 创建ComponentSession对象
        session = ComponentSession(
            session_id=None,  # 由manager生成
            user_id=command.user_id,
            title=command.title,
            component_config=command.component_config,
            session_metadata=command.metadata
        )

        # 调用manager创建会话
        created_session = self._session_manager.create_session(session)
        if not created_session:
            return BaseResult.server_error_result("创建会话失败")

        # 构造返回结果
        result = CreateSessionResult(
            session_id=created_session.session_id,
            user_id=created_session.user_id,
            title=created_session.title,
            status=SessionStatus.get_status_name(created_session.status)
        )

        return BaseResult.success_result(result)

    @api_handler
    def get_user_sessions(self, user_id: str, query: GetSessionListQuery) -> BaseResult:
        """
        获取用户会话列表
        Args:
            user_id: 用户ID
            query: 查询参数

        Returns:
            Result[SessionListResult]: 会话列表结果
        """
        # 调用manager查询会话
        sessions = self._session_manager.query_user_sessions(
            user_id=user_id,
            status=query.status,
            limit=query.get_limit()
        )

        # 应用分页
        offset = query.get_offset()
        total_count = len(sessions)
        paged_sessions = sessions[offset:offset + query.size] if offset < len(sessions) else []

        # 构造会话列表项
        session_list = []
        for session in paged_sessions:
            # 获取最新消息作为预览
            latest_message = self._session_manager.get_session_latest_message(session.session_id)

            session_item = SessionListItemResult(
                session_id=session.session_id,
                title=session.title,
                status=SessionStatus.get_status_name(session.status),
                message_count=session.message_count or 0,
                artifact_count=session.artifact_count or 0,
                last_message_time=session.last_message_time.isoformat() if session.last_message_time else None,
                created_at=session.gmt_create.isoformat() if session.gmt_create else None,
                updated_at=session.gmt_modified.isoformat() if session.gmt_modified else None,
            )
            session_list.append(session_item)

        # 构造返回结果
        result = SessionListResult(
            user_id=user_id,
            total=total_count,
            page=query.page,
            size=query.size,
            sessions=session_list
        )

        return BaseResult.success_result(result)

    @api_handler
    def get_session_detail(self, session_id: str) -> BaseResult:
        """
        获取会话详情
        Args:
            session_id: 会话ID

        Returns:
            Result[SessionDetailResult]: 会话详情结果
        """
        # 查询会话基本信息
        session = self._session_manager.query_session_by_id(session_id)
        if not session:
            return BaseResult.server_error_result("会话不存在")

        # 获取统计信息
        message_count = self._session_manager.get_session_message_count(session_id)
        artifact_count = self._session_manager.get_session_artifact_count(session_id)
        token_stats = self._session_manager.get_session_token_stats(session_id)

        # 构造返回结果
        result = SessionDetailResult(
            session_id=session.session_id,
            user_id=session.user_id,
            title=session.title,
            status=SessionStatus.get_status_name(session.status),
            message_count=message_count,
            artifact_count=artifact_count,
            last_message_time=session.last_message_time.isoformat() if session.last_message_time else None,
            created_at=session.gmt_create.isoformat() if session.gmt_create else None,
            updated_at=session.gmt_modified.isoformat() if session.gmt_modified else None,
            session_metadata=session.session_metadata,
            token_stats=token_stats
        )

        return BaseResult.success_result(result)

    @api_handler
    def update_session(self, command: UpdateSessionCommand) -> BaseResult:
        """
        更新会话信息
        Args:
            command: 更新会话命令

        Returns:
            Result[UpdateSessionResult]: 更新结果
        """
        # 查询会话是否存在
        session = self._session_manager.query_session_by_id(command.session_id)
        if not session:
            return BaseResult.server_error_result("会话不存在")

        # 更新会话信息
        if command.title is not None:
            session.title = command.title
        if command.component_config is not None:
            session.component_config = command.component_config
        if command.session_metadata is not None:
            session.session_metadata = command.session_metadata

        # 调用manager更新
        success = self._session_manager.update_session(session)
        if not success:
            return BaseResult.server_error_result("更新会话失败")

        # 重新查询更新后的会话
        updated_session = self._session_manager.query_session_by_id(command.session_id)

        # 构造返回结果
        result = UpdateSessionResult(
            session_id=updated_session.session_id,
            title=updated_session.title,
            updated_at=updated_session.gmt_modified.isoformat() if updated_session.gmt_modified else None
        )

        return BaseResult.success_result(result)

    @api_handler
    def delete_session(self, session_id: str) -> BaseResult:
        """
        删除会话
        Args:
            session_id: 会话ID

        Returns:
            Result[DeleteSessionResult]: 删除结果
        """
        # 调用manager删除会话
        success = self._session_manager.delete_session(session_id)

        # 构造返回结果
        result = DeleteSessionResult(
            session_id=session_id,
            success=success,
            message="会话删除成功" if success else "会话删除失败，会话不存在"
        )

        return BaseResult.success_result(result)

    @api_handler
    def archive_session(self, session_id: str) -> BaseResult:
        """
        归档会话
        Args:
            session_id: 会话ID

        Returns:
            Result[ArchiveSessionResult]: 归档结果
        """
        # 调用manager归档会话
        success = self._session_manager.archive_session(session_id)

        # 构造返回结果
        result = ArchiveSessionResult(
            session_id=session_id,
            success=success,
            message="会话归档成功" if success else "会话归档失败，会话不存在"
        )

        return BaseResult.success_result(result)



    @api_handler
    def search_sessions(self, query: SearchSessionsQuery) -> BaseResult:
        """
        搜索用户会话
        Args:
            query: 搜索查询

        Returns:
            Result[SearchSessionsResult]: 搜索结果
        """
        # 根据关键词搜索会话
        if query.keyword:
            sessions = self._session_manager.search_sessions_by_keyword(
                user_id=query.user_id,
                keyword=query.keyword,
                limit=query.limit + query.offset  # 获取更多数据用于分页
            )
        else:
            sessions = self._session_manager.query_user_sessions(
                user_id=query.user_id,
                status=str(query.status) if query.status else None,
                limit=query.limit + query.offset
            )

        # 应用日期过滤
        if query.start_date or query.end_date:
            from datetime import datetime
            filtered_sessions = []
            for session in sessions:
                # 日期过滤
                if query.start_date:
                    start_dt = datetime.fromisoformat(query.start_date.replace('Z', '+00:00'))
                    if session.gmt_create < start_dt:
                        continue

                if query.end_date:
                    end_dt = datetime.fromisoformat(query.end_date.replace('Z', '+00:00'))
                    if session.gmt_create > end_dt:
                        continue

                filtered_sessions.append(session)
            sessions = filtered_sessions

        # 应用分页
        total_count = len(sessions)
        paged_sessions = sessions[query.offset:query.offset + query.limit] if query.offset < len(sessions) else []

        # 构造会话列表项
        session_list = []
        for session in paged_sessions:
            session_item = SessionListItemResult(
                session_id=session.session_id,
                title=session.title,
                status=SessionStatus.get_status_name(session.status),
                message_count=session.message_count or 0,
                artifact_count=session.artifact_count or 0,
                last_message_time=session.last_message_time.isoformat() if session.last_message_time else None,
                created_at=session.gmt_create.isoformat() if session.gmt_create else None,
                updated_at=session.gmt_modified.isoformat() if session.gmt_modified else None,
            )
            session_list.append(session_item)

        # 构造返回结果
        result = SearchSessionsResult(
            sessions=session_list,
            total_count=total_count,
            limit=query.limit,
            offset=query.offset,
            search_params={
                "keyword": query.keyword,
                "status": query.status,
                "start_date": query.start_date,
                "end_date": query.end_date
            }
        )

        return BaseResult.success_result(result)

    def validate_session_access(self, session_id: str, user_id: str) -> Tuple[bool, Optional[ComponentSession], Optional[str]]:
        """验证session访问权限"""

        try:
            session = self._session_manager.query_session_by_id(session_id)
            if not session:
                return False, None, "会话不存在"

            # 验证用户权限
            if session.user_id != user_id:
                return False, None, "会话不存在"

            # 检查会话是否已删除
            if session.is_deleted == 1:
                return False, None, "会话不存在"

            return True, session, None
        except Exception as e:
            logger.error(f"验证会话访问权限失败: {e}")
            return False, None, "系统错误"
