import json
import logging
import random
import string
import time
from datetime import datetime
from typing import Optional, List

from robot_studio.component.session.model import ComponentSession, SessionStatus
from robot_studio.database.mysql.repository import ComponentSessionRepository, MessagesRepository, ArtifactsRepository
from robot_studio.component.chat_chunk.manager import ChatChunkManager
from robot_studio.utils.uuid import DataType

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器，提供会话的CRUD功能"""

    def __init__(self):
        self._session_repository = ComponentSessionRepository()
        self._messages_repository = MessagesRepository()
        self._artifacts_repository = ArtifactsRepository()
        self._chat_chunk_manager = ChatChunkManager()

    @staticmethod
    def generate_session_id(prefix: str = DataType.SESSION.value) -> str:
        """
        生成软随机的会话ID
        格式: {prefix}_{timestamp}_{random_string}
        """
        timestamp = int(time.time() * 1000)
        random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        return f"{prefix}{timestamp}_{random_str}"

    def create_session(self, session: ComponentSession) -> ComponentSession:
        """
        创建新会话
        Args:
            session: 会话模型

        Returns:
            ComponentSession: 创建后的会话模型
        """
        try:
            # 生成会话ID
            if not session.session_id:
                session.session_id = self.generate_session_id()

            # 设置默认值
            now = datetime.now()
            if not session.status:
                session.status = SessionStatus.ACTIVE
            if session.message_count is None:
                session.message_count = 0
            if session.artifact_count is None:
                session.artifact_count = 0
            if not session.last_message_time:
                session.last_message_time = now
            if session.is_deleted is None:
                session.is_deleted = False
            if not session.gmt_create:
                session.gmt_create = now
            if not session.gmt_modified:
                session.gmt_modified = now

            # 使用to_do方法转换为SessionDO
            session_do = session.to_do()

            # 创建会话
            created_session = self._session_repository.create_session(session_do)

            # 使用from_do方法转换回ComponentSession
            return ComponentSession.from_do(created_session)
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise

    def query_user_sessions(self, user_id: str, status: Optional[str] = None, limit: Optional[int] = None) -> List[ComponentSession]:
        """
        根据用户ID查询会话列表
        Args:
            user_id: 用户ID
            status: 会话状态过滤
            limit: 限制返回数量

        Returns:
            List[ComponentSession]: 会话列表
        """
        try:
            if status:
                status_code = SessionStatus.get_status_code(status)
                sessions = self._session_repository.get_sessions_by_status(status_code, limit)
                # 过滤出指定用户的会话
                sessions = [s for s in sessions if s.user_id == user_id]
            else:
                sessions = self._session_repository.get_sessions_by_user_id(user_id, limit)

            return [ComponentSession.from_do(session) for session in sessions]
        except Exception as e:
            logger.error(f"查询用户会话列表失败: {e}")
            return []

    def query_session_by_id(self, session_id: str) -> Optional[ComponentSession]:
        """
        根据会话ID查询会话详情
        Args:
            session_id: 会话ID

        Returns:
            Optional[ComponentSession]: 会话信息，不存在则返回None
        """
        try:
            session = self._session_repository.get_session_by_id(session_id)
            if not session:
                return None
            return ComponentSession.from_do(session)
        except Exception as e:
            logger.error(f"查询会话详情失败: {e}")
            return None

    def get_session_message_count(self, session_id: str) -> int:
        """
        获取会话的消息数量
        Args:
            session_id: 会话ID

        Returns:
            int: 消息数量
        """
        try:
            return self._messages_repository.get_session_message_count(session_id)
        except Exception as e:
            logger.error(f"获取会话消息数量失败: {e}")
            return 0

    def get_session_artifact_count(self, session_id: str) -> int:
        """
        获取会话的产物数量
        Args:
            session_id: 会话ID

        Returns:
            int: 产物数量
        """
        try:
            return self._artifacts_repository.count_artifacts_by_session(session_id)
        except Exception as e:
            logger.error(f"获取会话产物数量失败: {e}")
            return 0

    def get_session_token_stats(self, session_id: str) -> dict:
        """
        获取会话的token统计信息
        Args:
            session_id: 会话ID

        Returns:
            dict: token统计信息
        """
        try:
            return self._messages_repository.get_session_token_stats(session_id)
        except Exception as e:
            logger.error(f"获取会话token统计失败: {e}")
            return {}

    def update_session(self, session: ComponentSession) -> bool:
        """
        更新会话信息
        Args:
            session: 包含更新信息的会话模型

        Returns:
            bool: 更新是否成功
        """
        try:
            session_do = self._session_repository.get_session_by_id(session.session_id)
            if not session_do:
                logger.error("更新会话失败：会话不存在")
                return False

            # 更新可修改的字段
            update_data = {}
            if session.title is not None:
                update_data["title"] = session.title
            if session.component_config is not None:
                if isinstance(session.component_config, dict):
                    update_data["component_config"] = json.dumps(session.component_config, ensure_ascii=False)
                else:
                    update_data["component_config"] = session.component_config
            if session.session_metadata is not None:
                if isinstance(session.session_metadata, dict):
                    update_data["session_metadata"] = json.dumps(session.session_metadata, ensure_ascii=False)
                else:
                    update_data["session_metadata"] = session.session_metadata

            # 更新会话
            updated_session = self._session_repository.update_session(session_do.id, update_data)
            return updated_session is not None
        except Exception as e:
            logger.error(f"更新会话信息失败: {e}")
            return False

    def delete_session(self, session_id: str) -> bool:
        """
        删除会话（软删除）
        Args:
            session_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        try:
            return self._session_repository.delete_session(session_id)
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False

    def archive_session(self, session_id: str) -> bool:
        """
        归档会话
        Args:
            session_id: 会话ID

        Returns:
            bool: 归档是否成功
        """
        try:
            return self._session_repository.archive_session(session_id)
        except Exception as e:
            logger.error(f"归档会话失败: {e}")
            return False

    def query_session_messages(self, session_id: str, limit: int = 50, offset: int = 0) -> List:
        """
        查询会话的消息列表 - 使用ChatChunkManager替代MessagesRepository
        Args:
            session_id: 会话ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            List: 聊天块列表（ChatChunk对象）
        """
        try:
            return self._chat_chunk_manager.get_chat_chunks_by_session_id(session_id, limit=limit, offset=offset)
        except Exception as e:
            logger.error(f"查询会话消息失败: {e}")
            return []

    def query_message_artifacts(self, message_id: str) -> List:
        """
        查询消息的产物列表
        Args:
            message_id: 消息ID

        Returns:
            List: 产物列表
        """
        try:
            return self._artifacts_repository.get_artifacts_by_message_id(message_id)
        except Exception as e:
            logger.error(f"查询消息产物失败: {e}")
            return []

    def search_sessions_by_keyword(self, user_id: str, keyword: str, limit: int = 20) -> List[ComponentSession]:
        """
        根据关键词搜索用户会话
        Args:
            user_id: 用户ID
            keyword: 搜索关键词
            limit: 限制数量

        Returns:
            List[ComponentSession]: 匹配的会话列表
        """
        try:
            # 获取用户的所有会话
            sessions = self._session_repository.get_sessions_by_user_id(user_id)

            # 过滤匹配关键词的会话
            filtered_sessions = []
            for session in sessions:
                # 检查标题是否匹配
                if keyword.lower() in (session.title or "").lower():
                    filtered_sessions.append(session)
                    continue

                # 检查消息内容是否匹配
                messages = self._messages_repository.search_messages(
                    session_id=session.session_id,
                    keyword=keyword,
                    limit=1
                )
                if messages:
                    filtered_sessions.append(session)

                if len(filtered_sessions) >= limit:
                    break

            return [ComponentSession.from_do(session) for session in filtered_sessions[:limit]]
        except Exception as e:
            logger.error(f"搜索会话失败: {e}")
            return []

    def get_session_latest_message(self, session_id: str):
        """
        获取会话的最新消息
        Args:
            session_id: 会话ID

        Returns:
            消息对象或None
        """
        try:
            return self._messages_repository.get_session_latest_message(session_id)
        except Exception as e:
            logger.error(f"获取最新消息失败: {e}")
            return None

