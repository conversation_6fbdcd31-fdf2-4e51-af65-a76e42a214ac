from robot_studio.component.chat_chunk.model import ChatChunk


class ChatChunkManager:

    def __init__(self):
        pass

    def query_chunk(self, chunk_id: str) -> ChatChunk | None:
        """
        根据chunk_id查询具体会话块
        Args:
            chunk_id:

        Returns:

        """
        pass

    def query_session_chunks(self, session_id: str) -> list[ChatChunk | None]:
        """
        根据会话ID查询会话chunk列表
        Args:
            session_id:

        Returns:

        """
        pass

    def query_task_chunks(self, task_id: str) -> list[ChatChunk | None]:
        """
        根据具体任务ID查询会话chunk列表
        Args:
            task_id:

        Returns:

        """
        pass
