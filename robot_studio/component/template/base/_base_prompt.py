from abc import ABC, abstractmethod
from typing import Dict, Any

from autogen_core import ComponentBase
from pydantic import BaseModel


class BasePrompt(ABC, ComponentBase[BaseModel]):
    """prompt模版基类"""

    component_type = "prompt"

    def __init__(self, prompt: str) -> None:
        """Initialize the prompt component"""
        self._prompt = prompt

    @property
    def prompt(self) -> str:
        """prompt实例"""
        return self._prompt

    @abstractmethod
    def build_prompt(self, params: Dict[str, Any] | None = None) -> str:
        """
        构建prompt实例
        Args:
            params:

        Returns:

        """
        ...
