import json
import uuid
from abc import abstractmethod
from typing import Type, TypeVar, Any, Mapping, Literal

from autogen_core import CancellationToken
from autogen_core.models import UserMessage, FunctionExecutionResult
from autogen_core.tools import BaseTool
from autogen_core.tools._base import ParametersSchema, ToolSchema
from pydantic import BaseModel, Field

from robot_studio.component.runtime.model import RuntimeContext, Status
from robot_studio.component.template.base import ComponentRunner
from robot_studio.component.template.base._base_component import ComponentTaskResult
from robot_studio.component.template.message import BaseComponentMessage, ToolCallRequestMessage


class BaseResult(BaseModel):
    """组件工具执行结果带状态机"""

    status: str | None = Field(default=Status.SUCCESS.value)
    """函数执行状态，默认为成功态"""

    error_msg: str | None = None
    """报错异常信息"""


ArgsT = TypeVar("ArgsT", bound=BaseModel, contravariant=True)
ReturnT = TypeVar("ReturnT", bound=BaseResult, covariant=True)


class ToolExecuteResult(BaseModel):
    """包装函数执行结果"""

    name: str
    """(New in v0.4.8) The name of the function that was called."""

    call_id: str
    """The ID of the function call. Note this ID may be empty for some models."""

    args: str | None = None
    """函数执行入参"""

    origin_result: BaseResult | None = None
    """原始执行结果"""

    content: str
    """执行结果转为字符串"""

    is_error: bool | None = None
    """Whether the function call resulted in an error."""

    def convert_function_result(self) -> FunctionExecutionResult:
        """转换为大模型消费的函数执行结果模型"""
        return FunctionExecutionResult(content=self.content, call_id=self.call_id, name=self.name,
                                       is_error=self.is_error)


class ToolResultMessage(BaseComponentMessage):
    """组件执行消息事件"""

    content: ToolExecuteResult
    """The tool call result."""

    def to_model_text(self) -> str:
        pass

    def to_model_message(self) -> UserMessage:
        pass

    type: Literal["ToolResultMessage"] = "ToolResultMessage"

    def to_text(self) -> str:
        return str(self.content.content)


class BaseComponentTool(ComponentRunner, BaseTool[ArgsT, ReturnT]):

    def __init__(self, args_type: Type[ArgsT], return_type: Type[ReturnT], name: str, description: str,
                 strict: bool = False) -> None:
        BaseTool.__init__(self, args_type, return_type, name, description, strict)
        ComponentRunner.__init__(self)

    @property
    def schema(self) -> ToolSchema:
        """重写BaseTool中的schema方法，基于组件配置的runtime_params构建ToolSchema，大模型最终消费该Schema"""
        # 从组件配置中获取runtime_params
        if not self._component_config or not self._component_config.runtime_params:
            # 如果没有运行时参数配置，使用父类默认行为
            return super().schema

        # 筛选出model_extract=True的参数
        extracted_params = [
            param for param in self._component_config.runtime_params
            if param.model_extract is True
        ]

        if not extracted_params:
            # 如果没有需要模型提取的参数，使用父类默认行为
            return super().schema

        # 构建properties字典
        properties = {}
        required = []

        for param in extracted_params:
            # 构建参数的schema属性
            param_schema = {"type": self._convert_param_type(param.type)}

            if param.desc:
                param_schema["description"] = param.desc

            if param.default_value is not None:
                param_schema["default"] = param.default_value

            properties[param.name] = param_schema

            # 如果是必填参数，添加到required列表
            if param.is_required:
                required.append(param.name)

        # 构建ParametersSchema
        parameters = ParametersSchema(
            type="object",
            properties=properties,
            required=required if required else [],
            additionalProperties=False
        )

        # 构建完整的ToolSchema
        tool_schema = ToolSchema(
            name=self._component_config.code,
            description=self._component_config.desc,
            parameters=parameters,
            strict=self._strict
        )
        return tool_schema

    @staticmethod
    def _convert_param_type(param_type: str | None) -> str:
        """将组件参数类型转换为JSON Schema类型"""
        if param_type == "string" or param_type == "string_array":
            return "string"
        elif param_type == "number":
            return "number"
        elif param_type == "boolean":
            return "boolean"
        elif param_type == "json_object":
            return "object"
        else:
            return "string"  # 默认为string类型

    def return_value_as_string(self, value: Any) -> str:
        if isinstance(value, ToolResultMessage):
            return value.content.content
        if isinstance(value, ToolExecuteResult):
            return value.content
        # 走工具自定义实现
        content_str = self.convert_str(value)
        if content_str:
            return content_str
        # 默认转为json
        if isinstance(value, BaseModel):
            dumped = value.model_dump()
            if isinstance(dumped, dict):
                return json.dumps(dumped)
            return str(dumped)
        return str(value)

    async def run_component(self, ctx: RuntimeContext,
                            task: ToolCallRequestMessage | None = None) -> ComponentTaskResult:
        """
        执行任务
        Args:
            ctx:
            task:

        Returns:

        """
        assert task is not None, "工具组件调用入参为空！"
        try:
            args_json_str = task.content.arguments
            arguments = json.loads(args_json_str)
        except json.JSONDecodeError as e:
            return ComponentTaskResult(
                status=Status.FAILED.value,
                error_msg='入参解析异常！',
                messages=[]
            )
        ctx = ctx or RuntimeContext()
        ctx.parent_span_id = ctx.span_id or '-'
        ctx.span_id = str(uuid.uuid4())
        self.ctx = ctx
        tool_res = await self.run_json(arguments, CancellationToken(), task.content.id, ctx)
        message = ToolResultMessage(content=tool_res, status=tool_res.origin_result.status,
                                    source=tool_res.name)
        message = self.fill_session_chunk(message, ctx)
        return ComponentTaskResult(
            status=message.status,
            error_msg=tool_res.origin_result.error_msg,
            messages=[message]
        )

    async def run_json(
            self, args: Mapping[str, Any], cancellation_token: CancellationToken, call_id: str | None = None,
            ctx: RuntimeContext | None = None
    ) -> ToolExecuteResult:
        """Run the tool with the provided arguments in a dictionary.

        Args:
            ctx:
            args (Mapping[str, Any]): The arguments to pass to the tool.
            cancellation_token (CancellationToken): A token to cancel the operation if needed.
            call_id (str | None): An optional identifier for the tool call, used for tracing.

        Returns:
            ToolResultMessage: 返回函数调用事件
        """

        # Execute the tool's run method
        # 初始化父调用节点和当前节点ID

        # 调用工具的具体执行
        value = await self.run(self._args_type.model_validate(args), cancellation_token, ctx)

        # 构造组件执行结果
        tool_res = ToolExecuteResult(call_id=call_id, name=self._component_config.name, args=json.dumps(args),
                                     content=self.return_value_as_string(value), origin_result=value,
                                     is_error=value.status == Status.FAILED.value)

        # 返回消息
        return tool_res

    @abstractmethod
    async def run(self, args: ArgsT, cancellation_token: CancellationToken,
                  ctx: RuntimeContext | None = None) -> ReturnT:
        ...

    @abstractmethod
    def convert_str(self, args: ReturnT) -> str:
        """工具本身实现转为字符串的能力"""
        ...
