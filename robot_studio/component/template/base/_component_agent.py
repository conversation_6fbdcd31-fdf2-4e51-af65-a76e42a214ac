import uuid
from abc import ABC
from typing import AsyncGenerator, List, Sequence, Mapping, Any

from autogen_agentchat.agents import BaseChatAgent
from autogen_agentchat.base import Response, TaskResult
from autogen_agentchat.messages import BaseChatMessage, BaseAgentEvent
from autogen_core import CancellationToken, trace_invoke_agent_span

from robot_studio.component.runtime.model import RuntimeContext
from robot_studio.component.template.base import ComponentRunner
from robot_studio.component.template.base._base_component import ComponentTaskResult
from robot_studio.component.template.message import (
    ModelClientStreamingChunkEvent,
    TextMessage, BaseComponentMessage, BaseComponentEvent,
)


class BaseComponentAgent(BaseChatAgent, ComponentRunner, ABC):
    """Agent的组件基类实现"""

    def __init__(self, name: str, description: str) -> None:
        """Initialize the agent with a name and description."""
        BaseChatAgent.__init__(self, name, description)
        ComponentRunner.__init__(self)

    async def run_component_stream(self, ctx: RuntimeContext,
                                   task: BaseComponentMessage | Sequence[BaseComponentMessage] | None = None) -> \
            AsyncGenerator[
                BaseComponentEvent | BaseComponentMessage | ComponentTaskResult, None]:
        """组件执行逻辑"""
        async  for message in self.run_stream(task=task, ctx=ctx, output_task_messages=False):
            if isinstance(message, BaseComponentEvent) or isinstance(message, BaseComponentMessage):
                yield message

            if isinstance(message, TaskResult):
                last_message = message.messages[len(message.messages) - 1]
                await self.save_state()
                self.clean_ctx()
                yield ComponentTaskResult(status=last_message.status, messages=message.messages,
                                          stop_reason=message.stop_reason)

    async def run_component(self, ctx: RuntimeContext,
                            task: BaseComponentMessage | Sequence[
                                BaseComponentMessage] | None = None) -> ComponentTaskResult | None:
        """组件执行"""

        async for message in self.run_component_stream(ctx=ctx, task=task):
            if isinstance(message, ComponentTaskResult):
                return message
        return None

    async def run_stream(
            self,
            *,
            task: str | BaseChatMessage | Sequence[BaseChatMessage] | None = None,
            cancellation_token: CancellationToken | None = None,
            output_task_messages: bool = True,
            ctx: RuntimeContext = None,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | TaskResult, None]:
        """Run the agent with the given task and return a stream of messages
        and the final task result as the last item in the stream.

        Args:
            ctx: run_context
            task: The task to run. Can be a string, a single message, or a sequence of messages.
            cancellation_token: The cancellation token to kill the task immediately.
            output_task_messages: Whether to include task messages in the output stream. Defaults to True for backward compatibility.
        """
        with trace_invoke_agent_span(
                agent_name=self.name,
                agent_description=self.description,
        ):
            if cancellation_token is None:
                cancellation_token = CancellationToken()
            input_messages: List[BaseChatMessage] = []
            output_messages: List[BaseAgentEvent | BaseChatMessage] = []

            # 输入Message处理
            if task is None:
                pass
            elif isinstance(task, str):
                text_msg = TextMessage(content=task, source="user")
                input_messages.append(text_msg)
                if output_task_messages:
                    output_messages.append(text_msg)
                    yield text_msg
            elif isinstance(task, BaseChatMessage):
                input_messages.append(task)
                if output_task_messages:
                    output_messages.append(task)
                    yield task
            else:
                if not task:
                    raise ValueError("Task list cannot be empty.")
                for msg in task:
                    if isinstance(msg, BaseChatMessage):
                        input_messages.append(msg)
                        if output_task_messages:
                            output_messages.append(msg)
                            yield msg
                    else:
                        raise ValueError(f"Invalid message type in sequence: {type(msg)}")

            # 初始化父调用节点和当前节点ID
            ctx = ctx or RuntimeContext()
            ctx.parent_span_id = ctx.span_id or '-'
            ctx.span_id = str(uuid.uuid4())
            self.ctx = ctx
            async for message in self.on_messages_stream(input_messages, cancellation_token, ctx):

                # 会话块返回
                if isinstance(message, BaseComponentMessage) or isinstance(message, BaseComponentEvent):
                    # 补充
                    self.fill_session_chunk(message, ctx)
                    yield message

                    # 流式不落入整体输出
                    if isinstance(message, ModelClientStreamingChunkEvent):
                        # Skip the model client streaming chunk events.
                        continue
                    output_messages.append(message)

                # 流式终止类型
                elif isinstance(message, Response):
                    yield TaskResult(messages=output_messages)
                else:  # 其他消息默认返回
                    yield message

    async def on_messages_stream(
            self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken,
            ctx: RuntimeContext | None = None
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        """Handles incoming messages and returns a stream of messages and
        and the final item is the response. The base implementation in
        :class:`BaseChatAgent` simply calls :meth:`on_messages` and yields
        the messages in the response.

        .. note::

            Agents are stateful and the messages passed to this method should
            be the new messages since the last call to this method. The agent
            should maintain its state between calls to this method. For example,
            if the agent needs to remember the previous messages to respond to
            the current message, it should store the previous messages in the
            agent state.

        """
        response = await self.on_messages(messages, cancellation_token)
        for inner_message in response.inner_messages or []:
            yield inner_message
        yield response

    async def save_state(self) -> Mapping[str, Any]:
        """Export state. Default implementation for stateless agents."""
        ...

    async def load_state(self, state: Mapping[str, Any]) -> None:
        """Restore agent from saved state. Default implementation for stateless agents."""
        ...
