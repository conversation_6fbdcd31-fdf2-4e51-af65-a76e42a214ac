from typing import AsyncGenerator, Sequence, TypeVar

from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import BaseChatMessage
from pydantic import BaseModel

from robot_studio.component.manage.model import Component
from robot_studio.component.runtime.model import RuntimeContext
from robot_studio.component.template.message import BaseComponentMessage, BaseComponentEvent


class ComponentTaskResult(TaskResult):
    """组件当次执行任务状态"""
    status: str | None = None
    """任务执行状态"""

    error_msg: str | None = None
    """状态异常时的错误信息"""

    def get_latest_message(self) -> BaseChatMessage | None:
        """获取最小的返回消息"""
        if len(self.messages) == 0:
            return None
        return self.messages[len(self.messages) - 1]


ConfigT = TypeVar("ConfigT", bound=BaseModel)


class ComponentRunner:

    def __init__(self, config: Component | None = None) -> None:
        self._component_config: Component | None = None
        self._ctx: RuntimeContext | None = None
        """组件的配置信息"""

    @property
    def component_config(self) -> Component | None:
        """当前运行组件的配置态信息."""
        return self._component_config

    @component_config.setter
    def component_config(self, value: Component | None) -> None:
        self._component_config = value

    @property
    def ctx(self) -> RuntimeContext | None:
        """当前运行组件的配置态信息."""
        return self._ctx

    @ctx.setter
    def ctx(self, value: RuntimeContext | None) -> None:
        self._ctx = value

    def clean_ctx(self):
        self._ctx = None

    async def run_component_stream(self, ctx: RuntimeContext,
                                   task: BaseComponentMessage | Sequence[BaseComponentMessage] | None = None) -> \
            AsyncGenerator[
                BaseComponentEvent | BaseComponentMessage | ComponentTaskResult, None]:
        """
        组件逻辑流式输出执行，出入参都为消息结构，TaskResult是中止符
        Args:
            task:
            ctx:

        Returns:
            AsyncGenerator[
        BaseAgentEvent | BaseChatMessage | TaskResult, None]: TaskResult时流式的终止类型

        """
        pass

    async def run_component(self, ctx: RuntimeContext,
                            task: BaseComponentMessage | Sequence[
                                BaseComponentMessage] | None = None) -> ComponentTaskResult | None:
        """
        执行任务
        Args:
            ctx:
            task:

        Returns:

        """
        pass

    def fill_session_chunk(self, message: BaseComponentMessage | BaseComponentMessage,
                           ctx: RuntimeContext) -> BaseComponentMessage | BaseComponentMessage:
        """
        对消息的会话块基础信息进行填充
        Args:
            message:
            ctx:

        Returns:

        """
        _component = self._component_config or Component()
        message.session_id = message.session_id or ctx.session_id
        message.task_id = message.task_id or ctx.task_id
        message.component_id = message.component_id or _component.component_id
        message.version = message.version or _component.version
        message.span_id = message.span_id or ctx.span_id
        message.parent_span_id = message.parent_span_id or ctx.parent_span_id
        return message
