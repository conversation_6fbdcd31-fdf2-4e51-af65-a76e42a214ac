from typing import Mapping, Any, List

from autogen_agentchat.state import BaseState
from autogen_core.models import SystemMessage
from pydantic import Field


class KnowledgeRagAgentState(BaseState):
    """知识检索问题Agent的上下文模型"""

    llm_context: Mapping[str, Any] = Field(default_factory=lambda: dict([("messages", [])]))
    """大模型上下文"""

    knowledge_white_list: List[str] | None = Field(default_factory=list)
    """上下文中的知识列表"""

    query_all_knowledge: bool
    """是否要求查询所有知识"""

    system_message: List[SystemMessage] | None = None
    """系统消息"""

    type: str = Field(default="AssistantAgentState")
    """state类型"""
