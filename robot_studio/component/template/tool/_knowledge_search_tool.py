import logging
import re
from typing import Self

from autogen_core import CancellationToken, Component
from pydantic import BaseModel, Field

from robot_studio.component.runtime.model import RuntimeContext, Status
from robot_studio.component.template.base import BaseComponentTool, BaseResult
from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import RetrieveKnowledgeReq
from robot_studio.data_asset.material.api import MaterialService

logger = logging.getLogger(__name__)

# 素材引用替换模板
MATERIAL_REFERENCE_TEMPLATE = """@引用素材:
```json
{{
    "引用素材类型": "{material_type}",
    "引用素材名称": "{material_name}",
    "引用素材描述": "{material_description}",
    "引用素材标签": "{material_tags}",
    "引用素材链接": "{material_url}"
}}
```"""


class KnowledgeSearchConfig(BaseModel):
    """知识检索工具配置"""
    top_k: int = Field(default=10, description="top k")
    """召回条数限制"""

    min_score: float = Field(default=0.5, description="min score")
    """最低召回得分"""


class KnowledgeSearchArgs(BaseModel):
    """知识检索参数"""
    knowledge_id: str = Field(..., description="知识ID")

    query: str = Field(..., description="查询内容")


class KnowledgeSearchResult(BaseResult):
    """知识检索结果"""
    items: list = Field(default_factory=list, description="检索到的知识片段")
    total_count: int = Field(default=0, description="检索结果总数")


class KnowledgeSearchTool(BaseComponentTool[KnowledgeSearchArgs, KnowledgeSearchResult],
                          Component[KnowledgeSearchConfig]):
    """知识检索工具"""
    component_config_schema = KnowledgeSearchConfig

    def __init__(self, top_k: int = 10, min_score: float = 0.5):
        super().__init__(
            args_type=KnowledgeSearchArgs,
            return_type=KnowledgeSearchResult,
            name="knowledge_search",
            description="根据知识ID和查询内容检索相关知识片段",
        )
        self._knowledge_service = KnowledgeService()
        self._material_service = MaterialService()
        self._top_k = top_k
        self._min_score = min_score

    async def run(
            self,
            args: KnowledgeSearchArgs,
            cancellation_token: CancellationToken,
            ctx: RuntimeContext | None = None
    ) -> KnowledgeSearchResult:
        """执行知识检索"""
        try:
            # 构建检索请求
            retrieve_req = RetrieveKnowledgeReq(
                knowledge_id=args.knowledge_id,
                query=args.query,
                top_k=self._top_k,
                min_score=self._min_score,
            )

            # 调用知识服务进行检索
            retrieval_result = await self._knowledge_service.retrieve_knowledge(retrieve_req)

            if not retrieval_result.success:
                return KnowledgeSearchResult(
                    status=Status.FAILED.value,
                    error_msg=retrieval_result.message or "知识检索失败",
                    items=[],
                    total_count=0
                )

            # 处理检索结果
            items = []
            if retrieval_result.items:
                for item in retrieval_result.items:
                    # 替换素材引用占位符
                    processed_text = await self._replace_material_references(item.text)
                    items.append({
                        "text": processed_text,
                        "score": item.score
                    })

            return KnowledgeSearchResult(
                status=Status.SUCCESS.value,
                items=items,
                total_count=len(items)
            )

        except Exception as e:
            logger.error(f"Knowledge search failed: {str(e)}")
            return KnowledgeSearchResult(
                status=Status.FAILED.value,
                error_msg=f"知识检索异常: {str(e)}",
                items=[],
                total_count=0
            )

    async def _replace_material_references(self, text: str) -> str:
        """替换文本中的素材引用占位符"""
        if not text:
            return text

        # 匹配素材引用的正则表达式: @[图片:牛再再:mid_d6b577]
        pattern = r'@\[([^:]+):([^:]+):([^]]+)\]'

        def replace_reference(match):
            material_type = match.group(1)  # 图片
            material_name = match.group(2)  # 牛再再
            material_id = match.group(3)  # mid_d6b577

            try:
                # 调用MaterialService查询素材信息
                result = self._material_service.query_material_by_id(mid=material_id)

                if result.success and result.data:
                    material = result.data
                    # 构建替换内容
                    replacement = MATERIAL_REFERENCE_TEMPLATE.format(
                        material_type=material_type,
                        material_name=material.name or material_name,
                        material_description=material.content_digest or '',
                        material_tags=','.join(material.tags) if material.tags else '',
                        material_url=material.oss_url or material.oss_preview_url or ''
                    )
                    return replacement
                else:
                    # 如果查询失败，保持原始占位符
                    logger.warning(f"Failed to query material {material_id}: {result.error_msg}")
                    return match.group(0)

            except Exception as e:
                logger.error(f"Error replacing material reference {material_id}: {str(e)}")
                return match.group(0)

        # 使用正则表达式替换所有匹配的素材引用
        return re.sub(pattern, replace_reference, text)

    def convert_str(self, result: KnowledgeSearchResult) -> str:
        """将结果转换为字符串"""
        if not result.items:
            return "未找到相关知识内容"

        # 将检索到的知识片段组合成文本
        knowledge_texts = [item["text"] for item in result.items]
        return "\n\n".join(knowledge_texts)

    def _to_config(self) -> KnowledgeSearchConfig:
        pass

    @classmethod
    def _from_config(cls, config: KnowledgeSearchConfig) -> Self:
        """工具实例化！"""
        return cls(top_k=config.top_k, min_score=config.min_score)
