from __future__ import annotations

import asyncio
import json
import uuid
import warnings
from typing import (
    Any,
    AsyncGenerator,
    Awaitable,
    Callable,
    Dict,
    List,
    Mapping,
    Optional,
    Sequence,
    Tuple,
    Union,
)

from autogen_agentchat.base import Handoff as HandoffBase
from autogen_agentchat.base import Response
from autogen_agentchat.messages import BaseChatMessage, BaseAgentEvent
from autogen_agentchat.utils import remove_images
from autogen_core import CancellationToken, FunctionCall, Component
from autogen_core.memory import Memory
from autogen_core.model_context import (
    ChatCompletionContext,
    UnboundedChatCompletionContext,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    CreateResult,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    LLMMessage,
    SystemMessage,
)
from pydantic import BaseModel, Field
from typing_extensions import Self

from robot_studio.component.template.base import BaseComponentAgent, BaseComponentTool, ToolResultMessage, BasePrompt, \
    ToolExecuteResult
from robot_studio.component.template.message import (
    HandoffMessage,
    MemoryQueryEvent,
    ModelClientStreamingChunkEvent,
    OssUrlMessage,
    StructuredMessage,
    StructuredMessageFactory,
    TextMessage,
    ThoughtEvent,
    ToolCallRequestEvent,
    ToolCallSummaryMessage, ToolCallRequestMessage, StopMessage, BaseComponentMessage,
)
from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import KnowledgeVO
from robot_studio.database.mysql.repository import ComponentStateRepository
from robot_studio.database.mysql.table_schema import ComponentStateDO
from ..state import KnowledgeRagAgentState
from ...manage.model import ComponentBase
from ...runtime import ComponentFactory
from ...runtime.model import RuntimeContext, Status


class KnowledgeRagAgentConfig(BaseModel):
    """助理智能体配置模版参数定义"""

    model_client: ComponentBase
    """LLM大模型组件"""

    knowledge_search_tool: ComponentBase
    """知识查询组件"""

    other_tools: List[ComponentBase] | None = None
    """其他工具"""

    model_context: ComponentBase | None = None
    """模型上下文组件"""

    agent_prompt: ComponentBase | None = None
    """系统prompt组件"""

    knowledge_prompt: ComponentBase | None = None
    """知识库prompt模版"""

    reflect_on_tool_use: bool
    """最后是否由大模型进行反思总结"""

    tool_call_summary_format: str | None  # 工具使用汇总结构化模版
    """工具调用汇总格式化模版"""

    max_tool_iterations: int = Field(default=1, ge=1)  # 工具最大调用次数
    """工具最大调用次数"""

    query_all_knowledge: bool
    """是否查询所有知识"""

    knowledge_white_list: List[str] | None = None
    """知识列表白名单"""


class KnowledgeRagAgent(BaseComponentAgent, Component[KnowledgeRagAgentConfig]):
    """知识检索Agent"""

    component_config_schema = KnowledgeRagAgentConfig

    def __init__(
            self,
            name: str,
            model_client: ChatCompletionClient,
            *,
            tools: List[BaseComponentTool[Any, Any] | Callable[..., Any] | Callable[..., Awaitable[Any]]] | None = None,
            model_context: ChatCompletionContext | None = None,
            description: str = "An agent that provides assistance with ability to use tools.",
            agent_prompt: (
                    BasePrompt | str | None
            ) = "You are a helpful AI assistant. Solve tasks using your tools. Reply with TERMINATE when the task has been completed.",
            knowledge_prompt: BasePrompt,
            model_client_stream: bool = False,
            reflect_on_tool_use: bool | None = None,
            max_tool_iterations: int = 1,
            tool_call_summary_format: str = "{result}",
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None = None,
            output_content_type: type[BaseModel] | None = None,
            output_content_type_format: str | None = None,
            memory: Sequence[Memory] | None = None,
            knowledge_white_list: List[str] | None = None,
            query_all_knowledge: bool = False,
    ):
        super().__init__(name=name, description=description)
        self._knowledge_service = KnowledgeService()  # 知识服务
        self._state_repository = ComponentStateRepository()
        self._query_all_knowledge = query_all_knowledge  # 是否查询所有知识
        self._knowledge_white_list = knowledge_white_list  # 知识指定的白名单
        self._knowledge_prompt_template = knowledge_prompt  # 知识prompt模版
        self._agent_prompt_template = agent_prompt  # agent prompt模版
        self._model_client = model_client
        self._model_client_stream = model_client_stream
        self._output_content_type: type[BaseModel] | None = output_content_type
        self._output_content_type_format = output_content_type_format
        self._structured_message_factory: StructuredMessageFactory | None = None
        if output_content_type is not None:
            self._structured_message_factory = StructuredMessageFactory(
                input_model=output_content_type, format_string=output_content_type_format
            )

        self._memory = None
        if memory is not None:
            if isinstance(memory, list):
                self._memory = memory
            else:
                raise TypeError(f"Expected Memory, List[Memory], or None, got {type(memory)}")

        self._system_messages: List[SystemMessage] = []

        self._tools: List[BaseComponentTool[Any, Any]] = []
        if tools is not None:
            if model_client.model_info["function_calling"] is False:
                raise ValueError("The model does not support function calling.")
            for tool in tools:
                if isinstance(tool, BaseComponentTool):
                    self._tools.append(tool)

        # Check if tool names are unique.
        tool_names = [tool.name for tool in self._tools]
        if len(tool_names) != len(set(tool_names)):
            raise ValueError(f"Tool names must be unique: {tool_names}")

        if model_context is not None:
            self._model_context = model_context
        else:
            self._model_context = UnboundedChatCompletionContext()

        if self._output_content_type is not None and reflect_on_tool_use is None:
            # If output_content_type is set, we need to reflect on tool use by default.
            self._reflect_on_tool_use = True
        elif reflect_on_tool_use is None:
            self._reflect_on_tool_use = False
        else:
            self._reflect_on_tool_use = reflect_on_tool_use

        # Tool call loop
        self._max_tool_iterations = max_tool_iterations
        if self._max_tool_iterations < 1:
            raise ValueError(
                f"Maximum number of tool iterations must be greater than or equal to 1, got {max_tool_iterations}"
            )

        self._tool_call_summary_format = tool_call_summary_format
        self._tool_call_summary_formatter = tool_call_summary_formatter
        self._is_running = False

    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        """Get the types of messages this agent can produce.

        Returns:
            Sequence of message types this agent can generate
        """
        types: List[type[BaseChatMessage]] = [TextMessage, ToolCallSummaryMessage, HandoffMessage]
        if self._structured_message_factory is not None:
            types.append(StructuredMessage)
        return types

    @property
    def model_context(self) -> ChatCompletionContext:
        """Get the model context used by this agent.

        Returns:
            The chat completion context for this agent
        """
        return self._model_context

    async def on_messages(
            self,
            messages: Sequence[BaseChatMessage],
            cancellation_token: CancellationToken,
    ) -> Response:
        """Process incoming messages and generate a response.

        Args:
            messages: Sequence of messages to process
            cancellation_token: Token for cancelling operation

        Returns:
            Response containing the agent's reply
        """
        async for message in self.on_messages_stream(messages, cancellation_token):
            if isinstance(message, Response):
                return message
        raise AssertionError("The stream should have returned the final result.")

    async def on_messages_stream(
            self,
            messages: Sequence[BaseChatMessage],
            cancellation_token: CancellationToken,
            ctx: RuntimeContext | None = None
    ) -> AsyncGenerator[Union[BaseAgentEvent, BaseChatMessage, Response], None]:
        """Process messages and stream the response.

        Args:
            ctx:
            messages: Sequence of messages to process
            cancellation_token: Token for cancelling operation

        Yields:
            Events, messages and final response during processing
        """

        # Gather all relevant state here
        agent_name = self.name
        model_context = self._model_context
        memory = self._memory
        model_client = self._model_client
        model_client_stream = self._model_client_stream
        reflect_on_tool_use = self._reflect_on_tool_use
        max_tool_iterations = self._max_tool_iterations
        tool_call_summary_format = self._tool_call_summary_format
        tool_call_summary_formatter = self._tool_call_summary_formatter
        output_content_type = self._output_content_type
        tools = self._tools

        # STEP 0: 构建系统初始化上下文
        if not self._system_messages:
            _system_message_list = []
            agent_prompt = self._agent_prompt_template.prompt
            _system_message_list.append(SystemMessage(content=agent_prompt))
            # 构建知识的初始上下文
            knowledge_query_res = None
            if self._query_all_knowledge:
                knowledge_query_res = self._knowledge_service.query_online_knowledge_by_cid(self._component_config.cid)
            else:
                knowledge_query_res = self._knowledge_service.query_online_knowledge_by_kid_list(
                    self._knowledge_white_list)
            if knowledge_query_res.data:
                knowledge_prompt = self._build_knowledge_context(knowledge_query_res.data)
                _system_message_list.append(SystemMessage(content=knowledge_prompt))
            self._system_messages = _system_message_list

        # STEP 1: 用户消息添加到模型上下文
        await self._add_messages_to_context(
            model_context=model_context,
            messages=messages,
        )

        # STEP 2: 根据模型上下文从存储Memory召回数据，添加为System消息
        inner_messages: List[BaseAgentEvent | BaseChatMessage] = []
        for event_msg in await self._update_model_context_with_memory(
                memory=memory,
                model_context=model_context,
                agent_name=agent_name,
        ):
            # 输出内存数据/知识召回事件
            inner_messages.append(event_msg)
            yield event_msg

        # STEP 3: 生成消息ID，流式消息和最终消息共享同一个ID
        message_id = str(uuid.uuid4())

        # STEP 4: 进行用户任务的执行推理
        model_result = None
        async for inference_output in self._call_llm(
                model_client=model_client,
                model_client_stream=model_client_stream,
                system_messages=self._system_messages,
                model_context=model_context,
                tools=tools,
                agent_name=agent_name,
                cancellation_token=cancellation_token,
                output_content_type=output_content_type,
                message_id=message_id,
        ):
            if isinstance(inference_output, CreateResult):
                model_result = inference_output
            else:
                # 输出大模型流式事件
                yield inference_output

        assert model_result is not None, "大模型没有生成内容返回！"

        # --- 判断是否有推理内容 ---
        if model_result.thought:
            # 输出大模型流式事件
            thought_event = ThoughtEvent(content=model_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # 模型推理内容塞入山下文中
        await model_context.add_message(
            AssistantMessage(
                content=model_result.content,
                source=agent_name,
                thought=getattr(model_result, "thought", None),
            )
        )

        # STEP 5: 处理模型的推理结果
        async for output_event in self._process_model_result(
                model_result=model_result,
                inner_messages=inner_messages,
                cancellation_token=cancellation_token,
                agent_name=agent_name,
                system_messages=self._system_messages,
                model_context=model_context,
                tools=self._tools,
                model_client=model_client,
                model_client_stream=model_client_stream,
                reflect_on_tool_use=reflect_on_tool_use,
                max_tool_iterations=max_tool_iterations,
                tool_call_summary_format=tool_call_summary_format,
                tool_call_summary_formatter=tool_call_summary_formatter,
                output_content_type=output_content_type,
                message_id=message_id,
                format_string=self._output_content_type_format,
                ctx=ctx,
        ):
            yield output_event

    @staticmethod
    async def _add_messages_to_context(
            model_context: ChatCompletionContext,
            messages: Sequence[BaseChatMessage],
    ) -> None:
        """
        Add incoming messages to the model context.
        """
        for msg in messages:
            if isinstance(msg, HandoffMessage):
                for llm_msg in msg.context:
                    await model_context.add_message(llm_msg)
            await model_context.add_message(msg.to_model_message())

    @staticmethod
    async def _update_model_context_with_memory(
            memory: Optional[Sequence[Memory]],
            model_context: ChatCompletionContext,
            agent_name: str,
    ) -> List[MemoryQueryEvent]:
        """Update model context with memory content.

        Args:
            memory: Optional sequence of memory stores to query
            model_context: Context to update with memory content
            agent_name: Name of the agent for event tracking

        Returns:
            List of memory query events generated during update
        """
        events: List[MemoryQueryEvent] = []
        if memory:
            for mem in memory:
                update_context_result = await mem.update_context(model_context)
                if update_context_result and len(update_context_result.memories.results) > 0:
                    memory_query_event_msg = MemoryQueryEvent(
                        content=update_context_result.memories.results,
                        source=agent_name,
                    )
                    events.append(memory_query_event_msg)
        return events

    @classmethod
    async def _call_llm(
            cls,
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            system_messages: List[SystemMessage],
            model_context: ChatCompletionContext,
            tools: List[BaseComponentTool[Any, Any]] | None,
            agent_name: str,
            cancellation_token: CancellationToken,
            output_content_type: type[BaseModel] | None,
            message_id: str,
    ) -> AsyncGenerator[Union[CreateResult, ModelClientStreamingChunkEvent], None]:
        """Call the language model with given context and configuration.

        Args:
            model_client: Client for model inference
            model_client_stream: Whether to stream responses
            system_messages: System messages to include
            model_context: Context containing message history
            agent_name: Name of the agent
            cancellation_token: Token for cancelling operation
            output_content_type: Optional type for structured output

        Returns:
            Generator yielding model results or streaming chunks
        """
        all_messages = await model_context.get_messages()
        llm_messages = cls._get_compatible_context(model_client=model_client, messages=system_messages + all_messages)

        # 构造工具Schema定义
        tool_schemas = [tool.schema for tool in tools] if tools else []

        # 判断是否需要流式输出
        if model_client_stream:
            model_result: Optional[CreateResult] = None
            async for chunk in model_client.create_stream(
                    llm_messages,
                    tools=tool_schemas,
                    json_output=output_content_type,
                    cancellation_token=cancellation_token,
            ):
                if isinstance(chunk, CreateResult):
                    model_result = chunk
                elif isinstance(chunk, str):
                    # 流式输出的ID均保持一致
                    yield ModelClientStreamingChunkEvent(content=chunk, source=agent_name, id=message_id,
                                                         full_message_id=message_id, )
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")
            if model_result is None:
                raise RuntimeError("No final model result in streaming mode.")
            yield model_result
        else:
            model_result = await model_client.create(
                llm_messages,
                tools=tool_schemas,
                cancellation_token=cancellation_token,
                json_output=output_content_type,
            )
            yield model_result

    @classmethod
    async def _process_model_result(
            cls,
            model_result: CreateResult,
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            cancellation_token: CancellationToken,
            agent_name: str,
            system_messages: List[SystemMessage],
            model_context: ChatCompletionContext,
            tools: List[BaseComponentTool[Any, Any]],
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            reflect_on_tool_use: bool,
            tool_call_summary_format: str,
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None,
            max_tool_iterations: int,
            output_content_type: type[BaseModel] | None,
            message_id: str,
            format_string: str | None = None,
            ctx: RuntimeContext | None = None,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        """
        Handle final or partial responses from model_result, including tool calls, handoffs,
        and reflection if needed. Supports tool call loops when enabled.
        """

        # Tool call loop implementation with streaming support
        current_model_result = model_result
        # This variable is needed for the final summary/reflection step
        executed_calls_and_results: List[Tuple[FunctionCall, ToolResultMessage]] = []

        # Step1: 循环调用工具
        for loop_iteration in range(max_tool_iterations):
            # 模型直接返回了生成结果，无需工具调用，直接组装结果返回
            if isinstance(current_model_result.content, str):
                # 可以根据用户指定的结构化模型组装结果
                if output_content_type:
                    content = output_content_type.model_validate_json(current_model_result.content)
                    yield Response(
                        chat_message=StructuredMessage[output_content_type](  # type: ignore[valid-type]
                            content=content,
                            source=agent_name,
                            models_usage=current_model_result.usage,
                            format_string=format_string,
                            id=message_id,
                        ),
                        inner_messages=inner_messages,
                    )
                else:
                    _message = TextMessage(
                        content=current_model_result.content,
                        source=agent_name,
                        models_usage=current_model_result.usage,
                        id=message_id,
                    )
                    yield _message
                    yield Response(
                        chat_message=_message,
                        inner_messages=inner_messages,
                    )
                return

            # 执行FunctionCall
            assert isinstance(current_model_result.content, list) and all(
                isinstance(item, FunctionCall) for item in current_model_result.content
            )

            # 1. 返回FunctionCall的请求事件
            tool_call_msg = ToolCallRequestEvent(
                content=current_model_result.content,
                source=agent_name,
                models_usage=current_model_result.usage,
            )
            inner_messages.append(tool_call_msg)
            yield tool_call_msg

            # 2. 并发执行工具调用
            async def _execute_tool_calls(
                    function_calls: List[FunctionCall],
            ) -> List[Tuple[FunctionCall, ToolResultMessage]]:
                results = await asyncio.gather(
                    *[
                        cls._execute_tool_call(
                            tool_call=call,
                            agent_name=agent_name,
                            tools=tools,
                            ctx=ctx
                        )
                        for call in function_calls
                    ]
                )
                return results

            task = asyncio.create_task(_execute_tool_calls(current_model_result.content))

            # 等待所有工具执行结束
            executed_calls_and_results = await task

            # 3. 工具执行结果处理
            ready_results = [result for _, result in executed_calls_and_results if
                             (result and Status.is_ready(result.status))]
            # 3.1 工具执行完成，返回工具执行消息，并更新模型上下文
            if ready_results:
                for _msg in ready_results:
                    inner_messages.append(_msg)
                    yield _msg
                function_results = [res.content.convert_function_result() for res in ready_results]
                await model_context.add_message(FunctionExecutionResultMessage(content=function_results))

            # 3.2 有部分工具未执行完成，返回消息，模型上下文不更新
            pending_results = [result for _, result in executed_calls_and_results if
                               (result and Status.not_ready(result.status))]
            if pending_results:
                for _msg in pending_results:
                    inner_messages.append(_msg)
                    yield _msg
                yield Response(inner_messages=inner_messages,
                               chat_message=StopMessage(content='有长耗时的工具正在执行中，暂未获取结果，请您耐心等待~ ',
                                                        source=agent_name))
                return

            # 4. 判断循环是否结束，循环结束后进入最终汇总阶段
            # If we are on the last iteration, break to the summary/reflection step.
            if loop_iteration == max_tool_iterations - 1:
                break

            # 5. 继续循环调用大模型
            next_model_result: Optional[CreateResult] = None
            new_message_id = str(uuid.uuid4())
            async for llm_output in cls._call_llm(
                    model_client=model_client,
                    model_client_stream=model_client_stream,
                    system_messages=system_messages,
                    model_context=model_context,
                    tools=tools,
                    agent_name=agent_name,
                    cancellation_token=cancellation_token,
                    output_content_type=output_content_type,
                    message_id=new_message_id,  # Use same message ID for consistency
            ):
                if isinstance(llm_output, CreateResult):
                    next_model_result = llm_output
                else:
                    # Streaming chunk event
                    yield llm_output

            assert next_model_result is not None, "No model result was produced in tool call loop."
            current_model_result = next_model_result

            # 5.1 反思消息
            if current_model_result.thought:
                thought_event = ThoughtEvent(content=current_model_result.thought, source=agent_name)
                yield thought_event
                inner_messages.append(thought_event)

            # 5.2 模型推理内容塞入上下文中
            await model_context.add_message(
                AssistantMessage(
                    content=current_model_result.content,
                    source=agent_name,
                    thought=getattr(current_model_result, "thought", None),
                )
            )

        # Step2: 人工format结果 OR 模型最终总结
        if reflect_on_tool_use:
            async for reflection_response in cls._reflect_on_tool_use_flow(
                    system_messages=system_messages,
                    model_client=model_client,
                    model_client_stream=model_client_stream,
                    model_context=model_context,
                    agent_name=agent_name,
                    inner_messages=inner_messages,
                    output_content_type=output_content_type,
                    cancellation_token=cancellation_token,
            ):
                yield reflection_response
        else:
            _calls_and_results = [(call, result.content.convert_function_result()) for call, result in
                                  executed_calls_and_results]
            yield cls._summarize_tool_use(
                executed_calls_and_results=_calls_and_results,
                inner_messages=inner_messages,
                tool_call_summary_format=tool_call_summary_format,
                tool_call_summary_formatter=tool_call_summary_formatter,
                agent_name=agent_name,
            )
        return

    @staticmethod
    def _check_and_handle_handoff(
            model_result: CreateResult,
            executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]],
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            handoffs: Dict[str, HandoffBase],
            agent_name: str,
    ) -> Optional[Response]:
        """Check for and handle any handoff requests in the model result.

        Args:
            model_result: Result from model inference
            executed_calls_and_results: List of executed tool calls and their results
            inner_messages: List of messages generated during processing
            handoffs: Dictionary of available handoff configurations
            agent_name: Name of the agent

        Returns:
            Optional response containing handoff message if handoff detected
        """
        handoff_reqs = [
            call for call in model_result.content if isinstance(call, FunctionCall) and call.name in handoffs
        ]
        if len(handoff_reqs) > 0:
            # We have at least one handoff function call
            selected_handoff = handoffs[handoff_reqs[0].name]

            if len(handoff_reqs) > 1:
                warnings.warn(
                    (
                        f"Multiple handoffs detected. Only the first is executed: "
                        f"{[handoffs[c.name].name for c in handoff_reqs]}. "
                        "Disable parallel tool calls in the model client to avoid this warning."
                    ),
                    stacklevel=2,
                )

            # Collect normal tool calls (not handoff) into the handoff context
            tool_calls: List[FunctionCall] = []
            tool_call_results: List[FunctionExecutionResult] = []
            # Collect the results returned by handoff_tool. By default, the message attribute will returned.
            selected_handoff_message = selected_handoff.message
            for exec_call, exec_result in executed_calls_and_results:
                if exec_call.name not in handoffs:
                    tool_calls.append(exec_call)
                    tool_call_results.append(exec_result)
                elif exec_call.name == selected_handoff.name:
                    selected_handoff_message = exec_result.content

            handoff_context: List[LLMMessage] = []
            if len(tool_calls) > 0:
                # Include the thought in the AssistantMessage if model_result has it
                handoff_context.append(
                    AssistantMessage(
                        content=tool_calls,
                        source=agent_name,
                        thought=getattr(model_result, "thought", None),
                    )
                )
                handoff_context.append(FunctionExecutionResultMessage(content=tool_call_results))
            elif model_result.thought:
                # If no tool calls, but a thought exists, include it in the context
                handoff_context.append(
                    AssistantMessage(
                        content=model_result.thought,
                        source=agent_name,
                    )
                )

            # Return response for the first handoff
            return Response(
                chat_message=HandoffMessage(
                    content=selected_handoff_message,
                    target=selected_handoff.target,
                    source=agent_name,
                    context=handoff_context,
                ),
                inner_messages=inner_messages,
            )
        return None

    @classmethod
    async def _reflect_on_tool_use_flow(
            cls,
            system_messages: List[SystemMessage],
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            model_context: ChatCompletionContext,
            agent_name: str,
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            output_content_type: type[BaseModel] | None,
            cancellation_token: CancellationToken,
    ) -> AsyncGenerator[Response | BaseComponentMessage | ModelClientStreamingChunkEvent | ThoughtEvent, None]:
        """
        If reflect_on_tool_use=True, we do another inference based on tool results
        and yield the final text response (or streaming chunks).
        """
        all_messages = system_messages + await model_context.get_messages()
        llm_messages = cls._get_compatible_context(model_client=model_client, messages=all_messages)

        reflection_result: Optional[CreateResult] = None

        # Generate a message ID for correlation between chunks and final message in reflection flow
        reflection_message_id = str(uuid.uuid4())

        if model_client_stream:
            buffer = ""  # 缓冲区用于累积内容
            async for chunk in model_client.create_stream(
                    llm_messages,
                    json_output=output_content_type,
                    cancellation_token=cancellation_token,
                    tool_choice="none",  # Do not use tools in reflection flow.
            ):
                if isinstance(chunk, CreateResult):
                    # 如果缓冲区有内容，发送最后一个完整的TextMessage
                    if buffer.strip():
                        yield TextMessage(
                            content=buffer.strip(),
                            source=agent_name,
                            id=reflection_message_id,
                        )
                    reflection_result = chunk
                    yield Response(
                        chat_message=TextMessage(
                            content=reflection_result.content,
                            source=agent_name,
                            id=reflection_message_id,
                        ),
                        inner_messages=inner_messages,
                    )
                elif isinstance(chunk, str):
                    # 检查当前chunk是否包含换行符
                    if '\n' in chunk:
                        # 分割内容
                        parts = chunk.split('\n')
                        # 将第一部分添加到当前缓冲区
                        buffer += parts[0]

                        # 如果缓冲区有内容，发送一个完整的TextMessage或OssUrlMessage
                        if buffer.strip():
                            if cls._is_oss_url(buffer.strip()):
                                yield OssUrlMessage(
                                    content=buffer.strip(),
                                    source=agent_name,
                                    id=reflection_message_id,
                                )
                            else:
                                yield TextMessage(
                                    content=buffer.strip(),
                                    source=agent_name,
                                    id=reflection_message_id,
                                )
                            # 发送完消息后需要更新message_id
                            reflection_message_id = str(uuid.uuid4())

                            # 处理中间的段落（如果有多个换行符）
                        for i in range(1, len(parts) - 1):
                            if parts[i].strip():
                                yield TextMessage(
                                    content=parts[i].strip(),
                                    source=agent_name,
                                    id=str(uuid.uuid4()),
                                )
                                reflection_message_id = str(uuid.uuid4())

                        # 开始新缓冲区（最后一部分）
                        buffer = parts[-1] if len(parts) > 1 else ""
                    else:
                        # 正常累积内容到缓冲区
                        buffer += chunk
                        # 依然发送流式事件以保持原有的流式体验
                        yield ModelClientStreamingChunkEvent(
                            content=chunk, source=agent_name, full_message_id=reflection_message_id,
                            id=reflection_message_id
                        )
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")

            # 处理剩余缓冲区内容（如果有的话）
            if buffer.strip():
                yield Response(
                    chat_message=TextMessage(
                        content=buffer.strip(),
                        source=agent_name,
                        id=str(uuid.uuid4()),
                    ),
                    inner_messages=inner_messages,
                )
        else:
            reflection_result = await model_client.create(
                llm_messages,
                json_output=output_content_type,
                cancellation_token=cancellation_token,
                tool_choice="none",  # Do not use tools in reflection flow.
            )

        if not reflection_result or not isinstance(reflection_result.content, str):
            raise RuntimeError("Reflect on tool use produced no valid text response.")

        # --- NEW: If the reflection produced a thought, yield it ---
        if reflection_result.thought:
            thought_event = ThoughtEvent(content=reflection_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # Add to context (including thought if present)
        await model_context.add_message(
            AssistantMessage(
                content=reflection_result.content,
                source=agent_name,
                thought=getattr(reflection_result, "thought", None),
            )
        )

        if output_content_type:
            content = output_content_type.model_validate_json(reflection_result.content)
            yield Response(
                chat_message=StructuredMessage[output_content_type](  # type: ignore[valid-type]
                    content=content,
                    source=agent_name,
                    models_usage=reflection_result.usage,
                    id=reflection_message_id,
                ),
                inner_messages=inner_messages,
            )
        else:
            yield Response(
                chat_message=TextMessage(
                    content=reflection_result.content,
                    source=agent_name,
                    models_usage=reflection_result.usage,
                    id=reflection_message_id,
                ),
                inner_messages=inner_messages,
            )

    @staticmethod
    def _summarize_tool_use(
            executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]],
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            tool_call_summary_format: str,
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None,
            agent_name: str,
    ) -> Response:
        """
        If reflect_on_tool_use=False, create a summary message of all tool calls.
        """
        # Filter out calls which were actually handoffs
        normal_tool_calls = [(call, result) for call, result in executed_calls_and_results]

        def default_tool_call_summary_formatter(call: FunctionCall, result: FunctionExecutionResult) -> str:
            return tool_call_summary_format.format(
                tool_name=call.name,
                arguments=call.arguments,
                result=result.content,
                is_error=result.is_error,
            )

        summary_formatter = tool_call_summary_formatter or default_tool_call_summary_formatter

        tool_call_summaries = [summary_formatter(call, result) for call, result in normal_tool_calls]

        tool_call_summary = "\n".join(tool_call_summaries)
        return Response(
            chat_message=ToolCallSummaryMessage(
                content=tool_call_summary,
                source=agent_name,
                tool_calls=[call for call, _ in normal_tool_calls],
                results=[result for _, result in normal_tool_calls],
            ),
            inner_messages=inner_messages,
        )

    @staticmethod
    async def _execute_tool_call(
            tool_call: FunctionCall,
            tools: List[BaseComponentTool[Any, Any]],
            agent_name: str,
            ctx: RuntimeContext,
    ) -> Tuple[FunctionCall, ToolResultMessage]:
        """Execute a single tool call and return the result."""
        # Load the arguments from the tool call.
        try:
            if tool_call.id is None:
                tool_call.id = str(uuid.uuid4())
            arguments = json.loads(tool_call.arguments)
        except json.JSONDecodeError as e:
            return (
                tool_call,
                ToolResultMessage(content=ToolExecuteResult(content=f"Error: {e}",
                                                            call_id=tool_call.id,
                                                            is_error=True,
                                                            name=tool_call.name,
                                                            ),
                                  source=agent_name)
            )

        # 寻找工具并执行
        for _tool in tools:
            if tool_call.name == _tool.component_config.code:
                _toll_call_msg = ToolCallRequestMessage(content=tool_call, source=agent_name)
                # 深拷贝ctx，防止工具内部修改影响外部ctx
                ctx_copy = ctx.deep_copy() if ctx else None
                tool_task_res = await _tool.run_component(ctx_copy, _toll_call_msg)
                tool_message = tool_task_res.get_latest_message()
                if isinstance(tool_message, ToolResultMessage):
                    return tool_call, tool_message

        return (
            tool_call,
            ToolResultMessage(content=ToolExecuteResult(content=f"Error: tool '{tool_call.name}' not found",
                                                        call_id=tool_call.id,
                                                        is_error=True,
                                                        name=tool_call.name,
                                                        ),
                              source=agent_name,
                              )
        )

    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        """Reset the assistant agent to its initialization state."""
        await self._model_context.clear()

    async def save_state(self) -> Mapping[str, Any]:
        """存储Agent上下文信息"""
        model_context_state = await self._model_context.save_state()

        state = KnowledgeRagAgentState(llm_context=model_context_state,
                                       knowledge_white_list=self._knowledge_white_list,
                                       query_all_knowledge=self._query_all_knowledge,
                                       system_message=self._system_messages,
                                       ).model_dump()
        self._state_repository.create_or_update_state(
            ComponentStateDO(
                session_id=self._ctx.session_id,
                component_id=self.component_config.component_id,
                version=self.component_config.version,
                state=state,
            )
        )
        return state

    async def load_state(self, state: Mapping[str, Any]) -> None:
        """加载Agent上下文"""
        if not state:
            cmp_state = self._state_repository.query_state(session_id=self._ctx.session_id,
                                                           component_id=self.component_config.component_id,
                                                           version=self.component_config.version)
            state = cmp_state.state or {}
        rag_agent_state = KnowledgeRagAgentState.model_validate(state)
        # 1. 两种情况需要重新查询知识构建上下文，1. 要求查询全量知识 2. 白名单有变化，否则不需要每次都重新查询
        knowledge_query_res = None
        if self._query_all_knowledge:
            knowledge_query_res = self._knowledge_service.query_online_knowledge_by_cid(self._component_config.cid)
        elif set(self._knowledge_white_list) != set(rag_agent_state.knowledge_white_list):
            knowledge_query_res = self._knowledge_service.query_online_knowledge_by_kid_list(
                self._knowledge_white_list)
        if knowledge_query_res:  # 需要重新初始化上下文
            knowledge_prompt = self._build_knowledge_context(knowledge_query_res.data)
            agent_prompt = self._agent_prompt_template.prompt
            self._system_messages = [SystemMessage(content=agent_prompt),
                                     SystemMessage(content=knowledge_prompt)]
        else:  # 不需要重新查询知识上下文
            self._system_messages = rag_agent_state.system_message

        # 2. 加载模型对话上下文（不含SystemMessage）
        await self._model_context.load_state(rag_agent_state.llm_context)

    @staticmethod
    def _is_oss_url(content: str) -> bool:
        """检查内容是否为OSS URL链接"""
        if not content:
            return False

        # 去除前后空格
        content = content.strip()

        # 检查是否为URL格式（简单检查）
        if not (content.startswith('http://') or content.startswith('https://')):
            return False

        # 检查是否包含OSS相关域名或路径特征
        oss_indicators = [
            '.aliyuncs.com',  # 阿里云OSS
            '.oss-',  # OSS区域标识
            '/oss/',  # OSS路径标识
            'oss.',  # OSS子域名
        ]

        return any(indicator in content.lower() for indicator in oss_indicators)

    @staticmethod
    def _get_compatible_context(model_client: ChatCompletionClient, messages: List[LLMMessage]) -> Sequence[LLMMessage]:
        """Ensure that the messages are compatible with the underlying client, by removing images if needed."""
        if model_client.model_info["vision"]:
            return messages
        else:
            return remove_images(messages)

    def _build_knowledge_context(self, knowledge_list: List[KnowledgeVO]) -> str:
        """构建知识上下文，根据传入的知识列表，集合prompt组件转为最终的知识库prompt"""
        import json

        if not knowledge_list:
            return "[]"

        context_list = []
        for knowledge in knowledge_list:
            context_item = {
                "知识ID": knowledge.knowledge_id or "",
                "知识名称": knowledge.name or "",
                "知识描述": knowledge.desc or "",
                "标签": knowledge.tags or []
            }
            context_list.append(context_item)

        knowledge_info = json.dumps(context_list, ensure_ascii=False, indent=None)
        self._knowledge_prompt_template.build_prompt({"knowledge_info": knowledge_info})
        return self._knowledge_prompt_template.prompt

    def _to_config(self) -> KnowledgeRagAgentConfig:
        """Convert the assistant agent to a declarative config."""
        ...

    @classmethod
    def _from_config(cls, config: KnowledgeRagAgentConfig) -> Self:
        """Create an assistant agent from a declarative config."""

        component_factory = ComponentFactory()

        # 构建工具集
        tools = [component_factory.load_instance(tool) for tool in
                 config.other_tools] if config.other_tools else []
        tools.append(component_factory.load_instance(config.knowledge_search_tool))

        # 构建初始化实例
        return cls(
            name="knowledge_rag_agent",
            model_client=component_factory.load_instance(config.model_client),
            model_context=component_factory.load_instance(config.model_context) if config.model_context else None,
            tools=tools,
            description="knowledge_rag_agent",
            model_client_stream=True,
            reflect_on_tool_use=config.reflect_on_tool_use,
            max_tool_iterations=config.max_tool_iterations,
            tool_call_summary_format=config.tool_call_summary_format,
            agent_prompt=component_factory.load_instance(config.agent_prompt),
            knowledge_prompt=component_factory.load_instance(config.knowledge_prompt),
            knowledge_white_list=config.knowledge_white_list,
            query_all_knowledge=config.query_all_knowledge,
        )
