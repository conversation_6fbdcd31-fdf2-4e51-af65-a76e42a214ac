from __future__ import annotations

import asyncio
import json
import logging
import uuid
import warnings
from typing import (
    Any,
    AsyncGenerator,
    Awaitable,
    Callable,
    Dict,
    List,
    Mapping,
    Optional,
    Sequence,
    Tuple,
    TypeVar,
    Union,
)

from autogen_agentchat import EVENT_LOGGER_NAME
from autogen_agentchat.base import Handoff as HandoffBase
from autogen_agentchat.base import Response
from autogen_agentchat.messages import BaseChatMessage, BaseAgentEvent
from autogen_agentchat.state import AssistantAgentState
from autogen_agentchat.utils import remove_images
from autogen_core import CancellationToken, ComponentModel, FunctionCall
from autogen_core.memory import Memory
from autogen_core.model_context import (
    ChatCompletionContext,
    UnboundedChatCompletionContext,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    CreateResult,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    LLMMessage,
    SystemMessage,
)
from autogen_core.tools import <PERSON>Tool, FunctionTool, StaticStreamWorkbench, Workbench
from pydantic import BaseModel, <PERSON>
from typing_extensions import Self

from robot_studio.component.template.base import BaseComponentAgent, BaseComponentTool, ToolResultMessage, \
    ComponentConfig
from robot_studio.component.template.message import (
    HandoffMessage,
    MemoryQueryEvent,
    ModelClientStreamingChunkEvent,
    StructuredMessage,
    StructuredMessageFactory,
    TextMessage,
    ThoughtEvent,
    ToolCallRequestEvent,
    ToolCallSummaryMessage, ToolCallRequestMessage, StopMessage,
)
from ...manage.model import ComponentBase
from ...runtime import ComponentFactory
from ...runtime.model import RuntimeContext, Status

event_logger = logging.getLogger(EVENT_LOGGER_NAME)

# Add type variables for more specific typing
T = TypeVar("T", bound=BaseModel)
R = TypeVar("R", bound=BaseModel)


class AssistantAgentConfig(BaseModel):
    """助理智能体配置模版参数定义"""

    name: str
    description: str
    model_client: ComponentBase  # 大模型组件
    tools: List[ComponentBase] | None = None  # 工具组件
    workbench: List[ComponentModel] | None = None  # 工作台组件
    handoffs: List[HandoffBase | str] | None = None
    model_context: ComponentModel | None = None  # 模型上下文组件
    memory: List[ComponentModel] | None = None  # 记忆组件
    system_message: str | None = None  # 系统prompt（抽成组件）
    model_client_stream: bool = False  # 是否流式输出
    reflect_on_tool_use: bool  # 是否模型总结工具调用
    tool_call_summary_format: str  # 工具使用汇总结构化模版
    max_tool_iterations: int = Field(default=1, ge=1)  # 工具最大调用次数
    metadata: Dict[str, str] | None = None
    structured_message_factory: ComponentModel | None = None  # 结构化消息工厂组件


class AssistantAgent(BaseComponentAgent, ComponentConfig[AssistantAgentConfig]):
    """助理Agent"""

    component_version = 2
    component_config_schema = AssistantAgentConfig

    def __init__(
            self,
            name: str,
            model_client: ChatCompletionClient,
            *,
            tools: List[BaseTool[Any, Any] | Callable[..., Any] | Callable[..., Awaitable[Any]]] | None = None,
            workbench: Workbench | Sequence[Workbench] | None = None,
            handoffs: List[HandoffBase | str] | None = None,
            model_context: ChatCompletionContext | None = None,
            description: str = "An agent that provides assistance with ability to use tools.",
            system_message: (
                    str | None
            ) = "You are a helpful AI assistant. Solve tasks using your tools. Reply with TERMINATE when the task has been completed.",
            model_client_stream: bool = False,
            reflect_on_tool_use: bool | None = None,
            max_tool_iterations: int = 1,
            tool_call_summary_format: str = "{result}",
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None = None,
            output_content_type: type[BaseModel] | None = None,
            output_content_type_format: str | None = None,
            memory: Sequence[Memory] | None = None,
            metadata: Dict[str, str] | None = None,
    ):
        super().__init__(name=name, description=description)
        self._metadata = metadata or {}
        self._model_client = model_client
        self._model_client_stream = model_client_stream
        self._output_content_type: type[BaseModel] | None = output_content_type
        self._output_content_type_format = output_content_type_format
        self._structured_message_factory: StructuredMessageFactory | None = None
        if output_content_type is not None:
            self._structured_message_factory = StructuredMessageFactory(
                input_model=output_content_type, format_string=output_content_type_format
            )

        self._memory = None
        if memory is not None:
            if isinstance(memory, list):
                self._memory = memory
            else:
                raise TypeError(f"Expected Memory, List[Memory], or None, got {type(memory)}")

        self._system_messages: List[SystemMessage] = []
        if system_message is None:
            self._system_messages = []
        else:
            self._system_messages = [SystemMessage(content=system_message)]
        self._tools: List[BaseTool[Any, Any]] = []
        if tools is not None:
            if model_client.model_info["function_calling"] is False:
                raise ValueError("The model does not support function calling.")
            for tool in tools:
                if isinstance(tool, BaseTool):
                    self._tools.append(tool)
                elif callable(tool):
                    if hasattr(tool, "__doc__") and tool.__doc__ is not None:
                        description = tool.__doc__
                    else:
                        description = ""
                    self._tools.append(FunctionTool(tool, description=description))
                else:
                    raise ValueError(f"Unsupported tool type: {type(tool)}")
        # Check if tool names are unique.
        tool_names = [tool.name for tool in self._tools]
        if len(tool_names) != len(set(tool_names)):
            raise ValueError(f"Tool names must be unique: {tool_names}")

        # Handoff tools.
        self._handoff_tools: List[BaseTool[Any, Any]] = []
        self._handoffs: Dict[str, HandoffBase] = {}
        if handoffs is not None:
            if model_client.model_info["function_calling"] is False:
                raise ValueError("The model does not support function calling, which is needed for handoffs.")
            for handoff in handoffs:
                if isinstance(handoff, str):
                    handoff = HandoffBase(target=handoff)
                if isinstance(handoff, HandoffBase):
                    self._handoff_tools.append(handoff.handoff_tool)
                    self._handoffs[handoff.name] = handoff
                else:
                    raise ValueError(f"Unsupported handoff type: {type(handoff)}")
        # Check if handoff tool names are unique.
        handoff_tool_names = [tool.name for tool in self._handoff_tools]
        if len(handoff_tool_names) != len(set(handoff_tool_names)):
            raise ValueError(f"Handoff names must be unique: {handoff_tool_names}")
        # Create sets for faster lookup
        tool_names_set = set(tool_names)
        handoff_tool_names_set = set(handoff_tool_names)

        # Check if there's any overlap between handoff tool names and tool names
        overlap = tool_names_set.intersection(handoff_tool_names_set)

        # Also check if any handoff target name matches a tool name
        # This handles the case where a handoff is specified directly with a string that matches a tool name
        for handoff in handoffs or []:
            if isinstance(handoff, str) and handoff in tool_names_set:
                raise ValueError("Handoff names must be unique from tool names")
            elif isinstance(handoff, HandoffBase) and handoff.target in tool_names_set:
                raise ValueError("Handoff names must be unique from tool names")

        if overlap:
            raise ValueError("Handoff names must be unique from tool names")

        if workbench is not None:
            if self._tools:
                raise ValueError("Tools cannot be used with a workbench.")
            if isinstance(workbench, Sequence):
                self._workbench = workbench
            else:
                self._workbench = [workbench]
        else:
            self._workbench = [StaticStreamWorkbench(self._tools)]  # 默认构建了一个workbench

        if model_context is not None:
            self._model_context = model_context
        else:
            self._model_context = UnboundedChatCompletionContext()

        if self._output_content_type is not None and reflect_on_tool_use is None:
            # If output_content_type is set, we need to reflect on tool use by default.
            self._reflect_on_tool_use = True
        elif reflect_on_tool_use is None:
            self._reflect_on_tool_use = False
        else:
            self._reflect_on_tool_use = reflect_on_tool_use

        # Tool call loop
        self._max_tool_iterations = max_tool_iterations
        if self._max_tool_iterations < 1:
            raise ValueError(
                f"Maximum number of tool iterations must be greater than or equal to 1, got {max_tool_iterations}"
            )

        self._tool_call_summary_format = tool_call_summary_format
        self._tool_call_summary_formatter = tool_call_summary_formatter
        self._is_running = False

    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        """Get the types of messages this agent can produce.

        Returns:
            Sequence of message types this agent can generate
        """
        types: List[type[BaseChatMessage]] = [TextMessage, ToolCallSummaryMessage, HandoffMessage]
        if self._structured_message_factory is not None:
            types.append(StructuredMessage)
        return types

    @property
    def model_context(self) -> ChatCompletionContext:
        """Get the model context used by this agent.

        Returns:
            The chat completion context for this agent
        """
        return self._model_context

    async def on_messages(
            self,
            messages: Sequence[BaseChatMessage],
            cancellation_token: CancellationToken,
    ) -> Response:
        """Process incoming messages and generate a response.

        Args:
            messages: Sequence of messages to process
            cancellation_token: Token for cancelling operation

        Returns:
            Response containing the agent's reply
        """
        async for message in self.on_messages_stream(messages, cancellation_token):
            if isinstance(message, Response):
                return message
        raise AssertionError("The stream should have returned the final result.")

    async def on_messages_stream(
            self,
            messages: Sequence[BaseChatMessage],
            cancellation_token: CancellationToken,
            ctx: RuntimeContext | None = None
    ) -> AsyncGenerator[Union[BaseAgentEvent, BaseChatMessage, Response], None]:
        """Process messages and stream the response.

        Args:
            ctx:
            messages: Sequence of messages to process
            cancellation_token: Token for cancelling operation

        Yields:
            Events, messages and final response during processing
        """

        # Gather all relevant state here
        agent_name = self.name
        model_context = self._model_context
        memory = self._memory
        system_messages = self._system_messages
        workbench = self._workbench
        handoff_tools = self._handoff_tools
        handoffs = self._handoffs
        model_client = self._model_client
        model_client_stream = self._model_client_stream
        reflect_on_tool_use = self._reflect_on_tool_use
        max_tool_iterations = self._max_tool_iterations
        tool_call_summary_format = self._tool_call_summary_format
        tool_call_summary_formatter = self._tool_call_summary_formatter
        output_content_type = self._output_content_type

        # STEP 1: Add new user/handoff messages to the model context
        await self._add_messages_to_context(
            model_context=model_context,
            messages=messages,
        )

        # STEP 2: Update model context with any relevant memory
        # 从存储中召回数据，添加为System消息
        inner_messages: List[BaseAgentEvent | BaseChatMessage] = []
        for event_msg in await self._update_model_context_with_memory(
                memory=memory,
                model_context=model_context,
                agent_name=agent_name,
        ):
            # 输出内存数据/知识召回事件
            inner_messages.append(event_msg)
            yield event_msg

        # STEP 3: Generate a message ID for correlation between streaming chunks and final message
        message_id = str(uuid.uuid4())

        # STEP 4: Run the first inference
        model_result = None
        async for inference_output in self._call_llm(
                model_client=model_client,
                model_client_stream=model_client_stream,
                system_messages=system_messages,
                model_context=model_context,
                workbench=workbench,
                handoff_tools=handoff_tools,
                agent_name=agent_name,
                cancellation_token=cancellation_token,
                output_content_type=output_content_type,
                message_id=message_id,
        ):
            if isinstance(inference_output, CreateResult):
                model_result = inference_output
            else:
                # 输出大模型流式事件
                yield inference_output

        assert model_result is not None, "No model result was produced."

        # --- 判断是否有推理内容 ---
        if model_result.thought:
            # 输出大模型流式事件
            thought_event = ThoughtEvent(content=model_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # Add the assistant message to the model context (including thought if present)
        # 需要把思考内容塞入模型上下文窗口
        await model_context.add_message(
            AssistantMessage(
                content=model_result.content,
                source=agent_name,
                thought=getattr(model_result, "thought", None),
            )
        )

        # STEP 5: Process the model output
        async for output_event in self._process_model_result(
                model_result=model_result,
                inner_messages=inner_messages,
                cancellation_token=cancellation_token,
                agent_name=agent_name,
                system_messages=system_messages,
                model_context=model_context,
                workbench=workbench,
                handoff_tools=handoff_tools,
                tools=self._tools,
                handoffs=handoffs,
                model_client=model_client,
                model_client_stream=model_client_stream,
                reflect_on_tool_use=reflect_on_tool_use,
                max_tool_iterations=max_tool_iterations,
                tool_call_summary_format=tool_call_summary_format,
                tool_call_summary_formatter=tool_call_summary_formatter,
                output_content_type=output_content_type,
                message_id=message_id,
                format_string=self._output_content_type_format,
        ):
            yield output_event

    @staticmethod
    async def _add_messages_to_context(
            model_context: ChatCompletionContext,
            messages: Sequence[BaseChatMessage],
    ) -> None:
        """
        Add incoming messages to the model context.
        """
        for msg in messages:
            if isinstance(msg, HandoffMessage):
                for llm_msg in msg.context:
                    await model_context.add_message(llm_msg)
            await model_context.add_message(msg.to_model_message())

    @staticmethod
    async def _update_model_context_with_memory(
            memory: Optional[Sequence[Memory]],
            model_context: ChatCompletionContext,
            agent_name: str,
    ) -> List[MemoryQueryEvent]:
        """Update model context with memory content.

        Args:
            memory: Optional sequence of memory stores to query
            model_context: Context to update with memory content
            agent_name: Name of the agent for event tracking

        Returns:
            List of memory query events generated during update
        """
        events: List[MemoryQueryEvent] = []
        if memory:
            for mem in memory:
                update_context_result = await mem.update_context(model_context)
                if update_context_result and len(update_context_result.memories.results) > 0:
                    memory_query_event_msg = MemoryQueryEvent(
                        content=update_context_result.memories.results,
                        source=agent_name,
                    )
                    events.append(memory_query_event_msg)
        return events

    @classmethod
    async def _call_llm(
            cls,
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            system_messages: List[SystemMessage],
            model_context: ChatCompletionContext,
            workbench: Sequence[Workbench],
            handoff_tools: List[BaseTool[Any, Any]],
            agent_name: str,
            cancellation_token: CancellationToken,
            output_content_type: type[BaseModel] | None,
            message_id: str,
    ) -> AsyncGenerator[Union[CreateResult, ModelClientStreamingChunkEvent], None]:
        """Call the language model with given context and configuration.

        Args:
            model_client: Client for model inference
            model_client_stream: Whether to stream responses
            system_messages: System messages to include
            model_context: Context containing message history
            workbench: Available workbenches
            handoff_tools: Tools for handling handoffs
            agent_name: Name of the agent
            cancellation_token: Token for cancelling operation
            output_content_type: Optional type for structured output

        Returns:
            Generator yielding model results or streaming chunks
        """
        all_messages = await model_context.get_messages()
        llm_messages = cls._get_compatible_context(model_client=model_client, messages=system_messages + all_messages)

        tools = [tool for wb in workbench for tool in await wb.list_tools()] + handoff_tools

        if model_client_stream:
            model_result: Optional[CreateResult] = None

            async for chunk in model_client.create_stream(
                    llm_messages,
                    tools=tools,
                    json_output=output_content_type,
                    cancellation_token=cancellation_token,
            ):
                if isinstance(chunk, CreateResult):
                    model_result = chunk
                elif isinstance(chunk, str):
                    # 修改点：流式输出的id保持一致
                    yield ModelClientStreamingChunkEvent(content=chunk, source=agent_name, id=message_id,
                                                         full_message_id=message_id, )
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")
            if model_result is None:
                raise RuntimeError("No final model result in streaming mode.")
            yield model_result
        else:
            model_result = await model_client.create(
                llm_messages,
                tools=tools,
                cancellation_token=cancellation_token,
                json_output=output_content_type,
            )
            yield model_result

    @classmethod
    async def _process_model_result(
            cls,
            model_result: CreateResult,
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            cancellation_token: CancellationToken,
            agent_name: str,
            system_messages: List[SystemMessage],
            model_context: ChatCompletionContext,
            workbench: Sequence[Workbench],
            handoff_tools: List[BaseTool[Any, Any]],
            tools: List[BaseTool[Any, Any]],
            handoffs: Dict[str, HandoffBase],
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            reflect_on_tool_use: bool,
            tool_call_summary_format: str,
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None,
            max_tool_iterations: int,
            output_content_type: type[BaseModel] | None,
            message_id: str,
            format_string: str | None = None,
            ctx: RuntimeContext | None = None,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        """
        Handle final or partial responses from model_result, including tool calls, handoffs,
        and reflection if needed. Supports tool call loops when enabled.
        """

        # Tool call loop implementation with streaming support
        current_model_result = model_result
        # This variable is needed for the final summary/reflection step
        executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]] = []

        for loop_iteration in range(max_tool_iterations):
            # If direct text response (string), we're done
            # 模型未返回继续调用工具，结束流程
            if isinstance(current_model_result.content, str):
                # Use the passed message ID for the final message
                if output_content_type:  # 自定义结构化输出
                    content = output_content_type.model_validate_json(current_model_result.content)
                    yield Response(
                        chat_message=StructuredMessage[output_content_type](  # type: ignore[valid-type]
                            content=content,
                            source=agent_name,
                            models_usage=current_model_result.usage,
                            format_string=format_string,
                            id=message_id,
                        ),
                        inner_messages=inner_messages,
                    )
                else:
                    yield Response(
                        chat_message=TextMessage(
                            content=current_model_result.content,
                            source=agent_name,
                            models_usage=current_model_result.usage,
                            id=message_id,
                        ),
                        inner_messages=inner_messages,
                    )
                return

            # 执行FunctionCall
            assert isinstance(current_model_result.content, list) and all(
                isinstance(item, FunctionCall) for item in current_model_result.content
            )

            # STEP 4A: Yield ToolCallRequestEvent
            # 输出FunctionCall的请求事件
            tool_call_msg = ToolCallRequestEvent(
                content=current_model_result.content,
                source=agent_name,
                models_usage=current_model_result.usage,
            )
            event_logger.debug(tool_call_msg)
            inner_messages.append(tool_call_msg)
            yield tool_call_msg

            # STEP 4B: Execute tool calls with streaming support
            # Use a queue to handle streaming results from tool calls.
            # 工具调用的流式处理
            stream = asyncio.Queue[BaseAgentEvent | BaseChatMessage | None]()

            async def _execute_tool_calls(
                    function_calls: List[FunctionCall],
                    stream_queue: asyncio.Queue[BaseAgentEvent | BaseChatMessage | None],
            ) -> List[Tuple[FunctionCall, FunctionExecutionResult, ToolResultMessage | None]]:
                results = await asyncio.gather(
                    *[
                        cls._execute_tool_call(
                            tool_call=call,
                            handoff_tools=handoff_tools,
                            agent_name=agent_name,
                            tools=tools,
                            cancellation_token=cancellation_token,
                            stream=stream_queue,
                            ctx=ctx
                        )
                        for call in function_calls
                    ]
                )
                # Signal the end of streaming by putting None in the queue.
                stream_queue.put_nowait(None)
                return results

            task = asyncio.create_task(_execute_tool_calls(current_model_result.content, stream))

            # 流式内容put进了stream的队列中，进行轮询输出
            while True:
                event = await stream.get()
                if event is None:
                    # End of streaming, break the loop.
                    break
                if isinstance(event, BaseAgentEvent) or isinstance(event, BaseChatMessage):
                    yield event
                    inner_messages.append(event)
                else:
                    raise RuntimeError(f"Unexpected event type: {type(event)}")

            # 等待所有工具执行结束
            executed_calls_and_results = await task
            exec_results = [result for _, result, msg, in executed_calls_and_results]

            # 工具执行完成的结果
            exec_ready_res, exec_ready_msg = [(result, msg) for _, result, msg, in executed_calls_and_results if
                                              (msg and Status.is_ready(msg.status))]
            # 工具执行成功，返回消息，更新inner_messages，更新模型上下文
            if exec_ready_res:
                for _msg in exec_ready_msg:
                    inner_messages.append(_msg)
                    yield _msg
                await model_context.add_message(FunctionExecutionResultMessage(content=exec_results))

            # 如果存在未完成的工具，返回消息，但是模型上下文不更新，循环结束，任务中止
            not_ready_res, not_ready_msg = [(result, msg) for _, result, msg, in executed_calls_and_results if
                                            (msg and Status.not_ready(msg.status))]
            if not_ready_msg:
                for _msg in not_ready_msg:
                    inner_messages.append(_msg)
                    yield _msg
                yield Response(inner_messages=inner_messages,
                               chat_message=StopMessage(content='有长耗时的工具正在执行中，暂未获取结果，请您耐心等待~ ',
                                                        source=agent_name))
                return

            # STEP 4C: Check for handoff
            handoff_output = cls._check_and_handle_handoff(
                model_result=current_model_result,
                executed_calls_and_results=executed_calls_and_results,
                inner_messages=inner_messages,
                handoffs=handoffs,
                agent_name=agent_name,
            )
            if handoff_output:
                yield handoff_output
                return

            # STEP 4D: Check if we should continue the loop.
            # If we are on the last iteration, break to the summary/reflection step.
            if loop_iteration == max_tool_iterations - 1:
                break

            # Continue the loop: make another model call using _call_llm
            next_model_result: Optional[CreateResult] = None
            async for llm_output in cls._call_llm(
                    model_client=model_client,
                    model_client_stream=model_client_stream,
                    system_messages=system_messages,
                    model_context=model_context,
                    workbench=workbench,
                    handoff_tools=handoff_tools,
                    agent_name=agent_name,
                    cancellation_token=cancellation_token,
                    output_content_type=output_content_type,
                    message_id=message_id,  # Use same message ID for consistency
            ):
                if isinstance(llm_output, CreateResult):
                    next_model_result = llm_output
                else:
                    # Streaming chunk event
                    yield llm_output

            assert next_model_result is not None, "No model result was produced in tool call loop."
            current_model_result = next_model_result

            # Yield thought event if present
            if current_model_result.thought:
                thought_event = ThoughtEvent(content=current_model_result.thought, source=agent_name)
                yield thought_event
                inner_messages.append(thought_event)

            # Add the assistant message to the model context (including thought if present)
            await model_context.add_message(
                AssistantMessage(
                    content=current_model_result.content,
                    source=agent_name,
                    thought=getattr(current_model_result, "thought", None),
                )
            )

        # After the loop, reflect or summarize tool results
        if reflect_on_tool_use:
            async for reflection_response in cls._reflect_on_tool_use_flow(
                    system_messages=system_messages,
                    model_client=model_client,
                    model_client_stream=model_client_stream,
                    model_context=model_context,
                    workbench=workbench,
                    handoff_tools=handoff_tools,
                    agent_name=agent_name,
                    inner_messages=inner_messages,
                    output_content_type=output_content_type,
                    cancellation_token=cancellation_token,
            ):
                yield reflection_response
        else:
            yield cls._summarize_tool_use(
                executed_calls_and_results=executed_calls_and_results,
                inner_messages=inner_messages,
                handoffs=handoffs,
                tool_call_summary_format=tool_call_summary_format,
                tool_call_summary_formatter=tool_call_summary_formatter,
                agent_name=agent_name,
            )
        return

    @staticmethod
    def _check_and_handle_handoff(
            model_result: CreateResult,
            executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]],
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            handoffs: Dict[str, HandoffBase],
            agent_name: str,
    ) -> Optional[Response]:
        """Check for and handle any handoff requests in the model result.

        Args:
            model_result: Result from model inference
            executed_calls_and_results: List of executed tool calls and their results
            inner_messages: List of messages generated during processing
            handoffs: Dictionary of available handoff configurations
            agent_name: Name of the agent

        Returns:
            Optional response containing handoff message if handoff detected
        """
        handoff_reqs = [
            call for call in model_result.content if isinstance(call, FunctionCall) and call.name in handoffs
        ]
        if len(handoff_reqs) > 0:
            # We have at least one handoff function call
            selected_handoff = handoffs[handoff_reqs[0].name]

            if len(handoff_reqs) > 1:
                warnings.warn(
                    (
                        f"Multiple handoffs detected. Only the first is executed: "
                        f"{[handoffs[c.name].name for c in handoff_reqs]}. "
                        "Disable parallel tool calls in the model client to avoid this warning."
                    ),
                    stacklevel=2,
                )

            # Collect normal tool calls (not handoff) into the handoff context
            tool_calls: List[FunctionCall] = []
            tool_call_results: List[FunctionExecutionResult] = []
            # Collect the results returned by handoff_tool. By default, the message attribute will returned.
            selected_handoff_message = selected_handoff.message
            for exec_call, exec_result in executed_calls_and_results:
                if exec_call.name not in handoffs:
                    tool_calls.append(exec_call)
                    tool_call_results.append(exec_result)
                elif exec_call.name == selected_handoff.name:
                    selected_handoff_message = exec_result.content

            handoff_context: List[LLMMessage] = []
            if len(tool_calls) > 0:
                # Include the thought in the AssistantMessage if model_result has it
                handoff_context.append(
                    AssistantMessage(
                        content=tool_calls,
                        source=agent_name,
                        thought=getattr(model_result, "thought", None),
                    )
                )
                handoff_context.append(FunctionExecutionResultMessage(content=tool_call_results))
            elif model_result.thought:
                # If no tool calls, but a thought exists, include it in the context
                handoff_context.append(
                    AssistantMessage(
                        content=model_result.thought,
                        source=agent_name,
                    )
                )

            # Return response for the first handoff
            return Response(
                chat_message=HandoffMessage(
                    content=selected_handoff_message,
                    target=selected_handoff.target,
                    source=agent_name,
                    context=handoff_context,
                ),
                inner_messages=inner_messages,
            )
        return None

    @classmethod
    async def _reflect_on_tool_use_flow(
            cls,
            system_messages: List[SystemMessage],
            model_client: ChatCompletionClient,
            model_client_stream: bool,
            model_context: ChatCompletionContext,
            workbench: Sequence[Workbench],
            handoff_tools: List[BaseTool[Any, Any]],
            agent_name: str,
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            output_content_type: type[BaseModel] | None,
            cancellation_token: CancellationToken,
    ) -> AsyncGenerator[Response | ModelClientStreamingChunkEvent | ThoughtEvent, None]:
        """
        If reflect_on_tool_use=True, we do another inference based on tool results
        and yield the final text response (or streaming chunks).
        """
        all_messages = system_messages + await model_context.get_messages()
        llm_messages = cls._get_compatible_context(model_client=model_client, messages=all_messages)

        reflection_result: Optional[CreateResult] = None

        # Generate a message ID for correlation between chunks and final message in reflection flow
        reflection_message_id = str(uuid.uuid4())

        if model_client_stream:
            async for chunk in model_client.create_stream(
                    llm_messages,
                    json_output=output_content_type,
                    cancellation_token=cancellation_token,
                    tool_choice="none",  # Do not use tools in reflection flow.
            ):
                if isinstance(chunk, CreateResult):
                    reflection_result = chunk
                elif isinstance(chunk, str):
                    yield ModelClientStreamingChunkEvent(
                        content=chunk, source=agent_name, full_message_id=reflection_message_id
                    )
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")
        else:
            reflection_result = await model_client.create(
                llm_messages,
                json_output=output_content_type,
                cancellation_token=cancellation_token,
                tool_choice="none",  # Do not use tools in reflection flow.
            )

        if not reflection_result or not isinstance(reflection_result.content, str):
            raise RuntimeError("Reflect on tool use produced no valid text response.")

        # --- NEW: If the reflection produced a thought, yield it ---
        if reflection_result.thought:
            thought_event = ThoughtEvent(content=reflection_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # Add to context (including thought if present)
        await model_context.add_message(
            AssistantMessage(
                content=reflection_result.content,
                source=agent_name,
                thought=getattr(reflection_result, "thought", None),
            )
        )

        if output_content_type:
            content = output_content_type.model_validate_json(reflection_result.content)
            yield Response(
                chat_message=StructuredMessage[output_content_type](  # type: ignore[valid-type]
                    content=content,
                    source=agent_name,
                    models_usage=reflection_result.usage,
                    id=reflection_message_id,
                ),
                inner_messages=inner_messages,
            )
        else:
            yield Response(
                chat_message=TextMessage(
                    content=reflection_result.content,
                    source=agent_name,
                    models_usage=reflection_result.usage,
                    id=reflection_message_id,
                ),
                inner_messages=inner_messages,
            )

    @staticmethod
    def _summarize_tool_use(
            executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]],
            inner_messages: List[BaseAgentEvent | BaseChatMessage],
            handoffs: Dict[str, HandoffBase],
            tool_call_summary_format: str,
            tool_call_summary_formatter: Callable[[FunctionCall, FunctionExecutionResult], str] | None,
            agent_name: str,
    ) -> Response:
        """
        If reflect_on_tool_use=False, create a summary message of all tool calls.
        """
        # Filter out calls which were actually handoffs
        normal_tool_calls = [(call, result) for call, result in executed_calls_and_results if call.name not in handoffs]

        def default_tool_call_summary_formatter(call: FunctionCall, result: FunctionExecutionResult) -> str:
            return tool_call_summary_format.format(
                tool_name=call.name,
                arguments=call.arguments,
                result=result.content,
                is_error=result.is_error,
            )

        summary_formatter = tool_call_summary_formatter or default_tool_call_summary_formatter

        tool_call_summaries = [summary_formatter(call, result) for call, result in normal_tool_calls]

        tool_call_summary = "\n".join(tool_call_summaries)
        return Response(
            chat_message=ToolCallSummaryMessage(
                content=tool_call_summary,
                source=agent_name,
                tool_calls=[call for call, _ in normal_tool_calls],
                results=[result for _, result in normal_tool_calls],
            ),
            inner_messages=inner_messages,
        )

    @staticmethod
    async def _execute_tool_call(
            tool_call: FunctionCall,
            tools: List[BaseTool[Any, Any]],
            handoff_tools: List[BaseTool[Any, Any]],
            agent_name: str,
            cancellation_token: CancellationToken,
            ctx: RuntimeContext,
            stream: asyncio.Queue[BaseAgentEvent | BaseChatMessage | None],
    ) -> Tuple[FunctionCall, FunctionExecutionResult, ToolResultMessage | None]:
        """Execute a single tool call and return the result."""
        # Load the arguments from the tool call.
        try:
            arguments = json.loads(tool_call.arguments)
        except json.JSONDecodeError as e:
            return (
                tool_call,
                FunctionExecutionResult(
                    content=f"Error: {e}",
                    call_id=tool_call.id,
                    is_error=True,
                    name=tool_call.name,
                ),
                None
            )

        # Check if the tool call is a handoff.
        tools.extend(handoff_tools)
        for _tool in tools:
            if tool_call.name == _tool.name:
                # 当前工具是组件型工具，返回的是组件工具执行消息
                if isinstance(_tool, BaseComponentTool):
                    _toll_call_msg = ToolCallRequestMessage(content=tool_call, source=agent_name)
                    tool_task_res = await _tool.run_component(ctx, _toll_call_msg)
                    tool_message = tool_task_res.get_latest_message()
                    return (
                        tool_call,
                        FunctionExecutionResult(
                            content=tool_message.content.content,
                            call_id=tool_call.id,
                            name=tool_call.name,
                            is_error=tool_message.content.is_error,
                        ),
                        tool_message
                    )
                result = await _tool.run_json(arguments, cancellation_token, call_id=tool_call.id)
                result_as_str = _tool.return_value_as_string(result)
                return (
                    tool_call,
                    FunctionExecutionResult(
                        content=result_as_str,
                        call_id=tool_call.id,
                        is_error=False,
                        name=tool_call.name,
                    ),
                    None
                )

        return (
            tool_call,
            FunctionExecutionResult(
                content=f"Error: tool '{tool_call.name}' not found in any workbench",
                call_id=tool_call.id,
                is_error=True,
                name=tool_call.name,
            ),
            None
        )

    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        """Reset the assistant agent to its initialization state."""
        await self._model_context.clear()

    async def save_state(self) -> Mapping[str, Any]:
        """存储Agent上下文信息"""
        model_context_state = await self._model_context.save_state()
        return AssistantAgentState(llm_context=model_context_state).model_dump()

    async def load_state(self, state: Mapping[str, Any]) -> None:
        """Load the state of the assistant agent"""
        assistant_agent_state = AssistantAgentState.model_validate(state)
        # Load the model context state.
        await self._model_context.load_state(assistant_agent_state.llm_context)

    @staticmethod
    def _get_compatible_context(model_client: ChatCompletionClient, messages: List[LLMMessage]) -> Sequence[LLMMessage]:
        """Ensure that the messages are compatible with the underlying client, by removing images if needed."""
        if model_client.model_info["vision"]:
            return messages
        else:
            return remove_images(messages)

    def _to_config(self) -> AssistantAgentConfig:
        """Convert the assistant agent to a declarative config."""
        ...

    @classmethod
    def _from_config(cls, config: AssistantAgentConfig, iteration: str | None = None) -> Self:
        """Create an assistant agent from a declarative config."""
        if config.structured_message_factory:
            structured_message_factory = StructuredMessageFactory.load_component(config.structured_message_factory)
            format_string = structured_message_factory.format_string
            output_content_type = structured_message_factory.ContentModel

        else:
            format_string = None
            output_content_type = None

        component_factory = ComponentFactory()
        return cls(
            name=config.name,
            model_client=component_factory.load_instance(config.model_client),
            workbench=[Workbench.load_component(wb) for wb in config.workbench] if config.workbench else None,
            handoffs=config.handoffs,
            model_context=ChatCompletionContext.load_component(config.model_context) if config.model_context else None,
            tools=[component_factory.load_instance(tool) for tool in
                   config.tools] if config.tools else None,
            memory=[Memory.load_component(memory) for memory in config.memory] if config.memory else None,
            description=config.description,
            system_message=config.system_message,
            model_client_stream=config.model_client_stream,
            reflect_on_tool_use=config.reflect_on_tool_use,
            max_tool_iterations=config.max_tool_iterations,
            tool_call_summary_format=config.tool_call_summary_format,
            output_content_type=output_content_type,
            output_content_type_format=format_string,
            metadata=config.metadata,
        )
