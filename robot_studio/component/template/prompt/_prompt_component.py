from typing import Self, Dict, Any

from autogen_core import Component
from pydantic import BaseModel, Field

from robot_studio.component.template.base import BasePrompt


class PromptConfig(BaseModel):
    """prompt组件配置"""
    prompt_template: str = Field(default="")
    """prompt模版"""


class PromptComponent(BasePrompt, Component[PromptConfig]):
    component_provider_override = "robot_studio.component.template.prompt.PromptComponent"
    component_config_schema = PromptConfig

    def __init__(self, prompt_template: str):
        super().__init__(prompt_template)
        self._prompt_template = prompt_template

    def build_prompt(self, params: Dict[str, Any] | None = None) -> str:
        if params is None:
            params = {}

        # 替换变量
        self._prompt = self._prompt_template
        for key, value in params.items():
            placeholder = f"${{{key}}}"
            self._prompt = self._prompt.replace(placeholder, str(value))
        return self._prompt

    def _to_config(self) -> PromptConfig:
        return PromptConfig()

    @classmethod
    def _from_config(cls, config: PromptConfig) -> Self:
        return cls(config.prompt_template)
