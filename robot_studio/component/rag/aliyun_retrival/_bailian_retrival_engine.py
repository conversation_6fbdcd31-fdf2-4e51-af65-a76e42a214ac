# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import json
from typing import List

from alibabacloud_bailian20231229 import models as bailian_models
from alibabacloud_bailian20231229.client import Client as BailianClient
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_credentials.models import Config as CredentialConfig
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from robot_studio.common.config_service import get_configs_by_tag
from pydantic import BaseModel


class RetrivalItem(BaseModel):
    text: str
    """具体切片内容"""

    score: float
    """得分"""


class RetrivalRes(BaseModel):
    items: List[RetrivalItem] | None = None
    """召回结果"""

    success: bool = True
    """是否成功"""

    message: str | None = None
    """错误信息"""

    def json_str_for_llm(self):
        """召回结果转为模型消费的json字符串"""
        content_list = [item.text for item in self.items]
        return json.dumps(content_list)


class BailianRetrivalEngine:
    """
    BailianRetrival
    """

    def __init__(self):

        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        try:
            bailian_configs = get_configs_by_tag('bailian')
            access_key_id = bailian_configs.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
            access_key_secret = bailian_configs.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
            work_space_id = bailian_configs.get('ALIBABA_CLOUD_BAILIAN_WORK_SPACE_ID')
            
            # 检查必要的配置是否存在
            if not all([access_key_id, access_key_secret, work_space_id]):
                raise ValueError("Missing required bailian configuration")
                
            credential_config = CredentialConfig(
                type='access_key',
                access_key_id=access_key_id,
                access_key_secret=access_key_secret,
            )
            credential = CredentialClient(credential_config)
            config = open_api_models.Config(
                credential=credential
            )
            # 参考 https://api.aliyun.com/product/bailian
            config.endpoint = f'bailian.cn-beijing.aliyuncs.com'
            self._client = BailianClient(config)
            self._work_space_id = work_space_id
            self._initialized = True
        except Exception as e:
            # 配置不可用时，设置初始化标志为False
            self._initialized = False
            self._client = None
            self._work_space_id = None
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Bailian retrieval engine initialization failed: {str(e)}")

    def run_retrival(
            self, knowledge_index_id: str, query: str, top_k: int = 10, min_score: float = 0.5
    ) -> RetrivalRes:
        """
        同步召回文档内容
        Args:
            knowledge_index_id: 百炼知识库ID
            query: 检索关键词
            top_k: 返回数量
            min_score: 最小得分

        Returns:
            RetrivalRes: 检索结果

        """
        if not hasattr(self, '_initialized') or not self._initialized:
            return RetrivalRes(success=False, message="Bailian retrieval engine not initialized. Please check configuration.")

        # 用户query改写模型
        rewrite_model = bailian_models.RetrieveRequestRewrite(
            model_name='conv-rewrite-qwen-1.8b'
        )

        # 召回后排序模型
        rerank_model = bailian_models.RetrieveRequestRerank(
            model_name='gte-rerank-hybrid'
        )

        # 检索请求构建
        retrieve_request = bailian_models.RetrieveRequest(
            query=query,
            dense_similarity_top_k=50,  # 向量检索相似度限制
            sparse_similarity_top_k=50,  # 关键词相似度限制
            enable_reranking=True,
            enable_rewrite=True,
            rerank=[
                rerank_model
            ],
            rerank_min_score=min_score,  # 控制最小相似度阈值
            rerank_top_n=top_k,  # 控制排序后的返回数量
            rewrite=[
                rewrite_model
            ],
            index_id=knowledge_index_id,  # 知识库ID  'xqzcnr6k3j'
            save_retriever_history=True  # 是否保存检索历史
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            res = self._client.retrieve_with_options(self._work_space_id, retrieve_request, headers, runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return RetrivalRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return RetrivalRes(success=False, message="召回处理失败！")
            nodes = res.body.data.nodes  # 数组对象，里面是具体切面
            if nodes is None or len(nodes) == 0:
                return RetrivalRes(success=False, message="召回结果为空！")
            return RetrivalRes(items=[RetrivalItem(text=node.text, score=node.score) for node in nodes])
        except Exception as error:
            return RetrivalRes(success=False, message=f"发生预期外异常！error = {str(error)}")

    async def run_retrival_async(
            self, knowledge_index_id: str, query: str, top_k: int = 10, min_score: float = 0.5
    ) -> RetrivalRes:
        """
        异步召回文档结果
        Args:
            knowledge_index_id: 百炼知识库ID
            query: 检索关键词
            top_k: 返回数量
            min_score: 最小得分

        Returns:
            RetrivalRes: 检索结果

        """
        if not hasattr(self, '_initialized') or not self._initialized:
            return RetrivalRes(success=False, message="Bailian retrieval engine not initialized. Please check configuration.")
        # 用户query改写模型
        rewrite_model = bailian_models.RetrieveRequestRewrite(
            model_name='conv-rewrite-qwen-1.8b'
        )

        # 召回后排序模型
        rerank_model = bailian_models.RetrieveRequestRerank(
            model_name='gte-rerank-hybrid'
        )

        # 检索请求构建
        retrieve_request = bailian_models.RetrieveRequest(
            query=query,
            dense_similarity_top_k=50,  # 向量检索相似度限制
            sparse_similarity_top_k=50,  # 关键词相似度限制
            enable_reranking=True,
            enable_rewrite=True,
            rerank=[
                rerank_model
            ],
            rerank_min_score=min_score,  # 控制最小相似度阈值
            rerank_top_n=top_k,  # 控制排序后的返回数量
            rewrite=[
                rewrite_model
            ],
            index_id=knowledge_index_id,  # 知识库ID  'xqzcnr6k3j'
            save_retriever_history=True  # 是否保存检索历史
        )
        runtime = util_models.RuntimeOptions()
        headers = {}
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = await self._client.retrieve_with_options_async(self._work_space_id, retrieve_request, headers,
                                                                 runtime)
            if res.status_code != 200 or res.body is None:  # 异常处理
                return RetrivalRes(success=False, message="网络处理异常！")
            if not res.body.success or res.body.code != 'Success' or res.body.data is None:
                return RetrivalRes(success=False, message="召回处理失败！")
            nodes = res.body.data.nodes  # 数组对象，里面是具体切面
            if nodes is None or len(nodes) == 0:
                return RetrivalRes(success=False, message="召回结果为空！")
            return RetrivalRes(items=[RetrivalItem(text=node.text, score=node.score) for node in nodes])
        except Exception as error:
            return RetrivalRes(success=False, message=f"发生预期外异常！error = {str(error)}")


if __name__ == '__main__':
    bailian = BailianRetrivalEngine()
    result = bailian.run_retrival_async('xqzcnr6k3j', '初一,英语')
    print(result)
