from typing import List, Dict, Any, NamedTuple, Optional

from graphrag.data_model.entity import Entity


class ColumnHeader(NamedTuple):
    id: str  # 列ID
    name: str  # 列的中文名称


def _sanitize_table_cell(value: str) -> str:
    """
    处理表格单元格中的特殊字符

    Args:
        value: 单元格的值

    Returns:
        处理后的字符串
    """
    if not isinstance(value, str):
        value = str(value)

    # 替换换行符为空格
    value = value.replace('\n', ' ').replace('\r', ' ')
    # 替换竖线为中文竖线（防止破坏表格结构）
    value = value.replace('|', '｜')
    # 移除首尾空格
    value = value.strip()
    return value


def build_markdown_table(columns: List[ColumnHeader], data: List[Dict[str, Any]],
                         table_title: Optional[str] = None) -> str:
    """
    构建 Markdown 表格
    
    Args:
        table_title: 表格标题
        columns: 列定义列表，每个元素包含列ID和中文名称
        data: 数据列表，每个元素是一个字典，key为列ID，value为对应的值
    
    Returns:
        str: Markdown格式的表格字符串
    
    Example:
        columns = [
            ColumnHeader("id", "编号"),
            ColumnHeader("title", "标题"),
            ColumnHeader("description", "描述")
        ]
        
        data = [
            {"id": "1", "title": "标题1", "description": "描述1"},
            {"id": "2", "title": "标题2", "description": "描述2"}
        ]
        
        table = build_markdown_table(columns, data)
    """
    if not columns or not data:
        return "No data available."

    # 构建表格内容
    table_lines = []

    # 如果有表格说明，添加到最前面
    if table_title:
        table_lines.append(f"**{table_title}**\n")

    # 构建表头
    header = "| " + " | ".join(_sanitize_table_cell(col.name) for col in columns) + " |"
    # 构建分隔行
    separator = "| " + " | ".join(["---"] * len(columns)) + " |"

    # 构建数据行
    rows = []
    for row in data:
        # 对每个列ID获取对应的值，如果没有则返回空字符串
        row_values = [_sanitize_table_cell(str(row.get(col.id, ""))) for col in columns]
        rows.append("| " + " | ".join(row_values) + " |")

    # 组合所有行
    table_lines.extend([header, separator] + rows)

    return "\n".join(table_lines)
