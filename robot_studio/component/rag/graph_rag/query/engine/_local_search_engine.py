import logging
import time
from collections.abc import AsyncGenerator
from typing import Any, Self

import tiktoken
from autogen_agentchat.messages import BaseAgentEvent, ModelClientStreamingChunkEvent
from graphrag.callbacks.query_callbacks import QueryCallbacks
from graphrag.config.models.graph_rag_config import GraphRagConfig
from graphrag.data_model.community_report import CommunityReport
from graphrag.data_model.covariate import Covariate
from graphrag.data_model.entity import Entity
from graphrag.data_model.relationship import Relationship
from graphrag.data_model.text_unit import TextUnit
from graphrag.language_model.manager import ModelManager
from graphrag.language_model.protocol.base import ChatModel
from graphrag.language_model.providers.fnllm.utils import get_openai_model_parameters_from_config
from graphrag.prompts.query.local_search_system_prompt import (
    LOCAL_SEARCH_SYSTEM_PROMPT,
)
from graphrag.query.context_builder.conversation_history import (
    ConversationHistory,
)
from graphrag.query.llm.text_utils import num_tokens
from graphrag.vector_stores.base import BaseVectorStore

from ._base_engine import BaseSearch, SearchResult
from ..context import EntityVectorStoreKey, LocalSearchContextBuilder, LocalContextBuilder, ContextBuilderResult

log = logging.getLogger(__name__)


class LocalSearchEngine(BaseSearch[LocalContextBuilder]):
    """GraphRag的本地检索引擎自定义实现"""

    def __init__(
            self,
            model: ChatModel,  # 大模型
            context_builder: LocalContextBuilder,  # 上下文构建器
            token_encoder: tiktoken.Encoding | None = None,  # token分词器
            system_prompt: str | None = None,  # 系统提示
            response_type: str = "multiple paragraphs",  # 响应类型
            callbacks: list[QueryCallbacks] | None = None,  # 回调实现
            model_params: dict[str, Any] | None = None,  # 模型参数
            context_builder_params: dict | None = None,  # 上下文构建入参
    ):
        super().__init__(
            model=model,
            context_builder=context_builder,
            token_encoder=token_encoder,
            model_params=model_params,
            context_builder_params=context_builder_params or {},
        )
        self.system_prompt = system_prompt or LOCAL_SEARCH_SYSTEM_PROMPT
        self.callbacks = callbacks or []
        self.response_type = response_type

    async def search(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,
            **kwargs,
    ) -> SearchResult:
        """Build local search context that fits a single context window and generate answer for the user query."""
        log.info(
            f'--STEP_0 接受到用户检索请求-- \n Query:{query}')
        start_time = time.time()
        search_prompt = ""
        llm_calls, prompt_tokens, output_tokens = {}, {}, {}

        # STEP 1. 构建上下文信息
        context_result = await self.context_builder.build_context(
            query=query,
            conversation_history=conversation_history,
            **kwargs,
            **self.context_builder_params,
        )
        llm_calls["build_context"] = context_result.llm_calls
        prompt_tokens["build_context"] = context_result.prompt_tokens
        output_tokens["build_context"] = context_result.output_tokens
        step1_time = time.time()
        log.info(
            f'--STEP_1 构建上下文信息-- \n 耗时:{step1_time - start_time} \n 上下文内容:{context_result.context_chunks}')

        ## 1.1 上下文构建后的回调函数
        for callback in self.callbacks:
            callback.on_context(context_result.context_records)

        try:
            # STEP 2. 组装最终调用大模型生成的Prompt
            # 如果请求参数中有"drift_query" = "查询内容"
            if "drift_query" in kwargs:
                drift_query = kwargs["drift_query"]
                search_prompt = self.system_prompt.format(
                    context_data=context_result.context_chunks,
                    response_type=self.response_type,
                    global_query=drift_query,
                )
            else:
                search_prompt = self.system_prompt.format(
                    context_data=context_result.context_chunks,
                    response_type=self.response_type,
                )
            history_messages = [
                {"role": "system", "content": search_prompt},
            ]

            full_response = ""
            step2_time = time.time()
            log.info(
                f'--STEP_2 组装模型调用Prompt-- \n 耗时:{step2_time - step1_time} \n Prompt:{search_prompt}')

            # STEP 3. 调用模型生成
            async for response in self.model.achat_stream(
                    prompt=query,
                    history=history_messages,
                    model_parameters=self.model_params,
            ):
                full_response += response
                ## 3.3 模型生成的回调函数
                for callback in self.callbacks:
                    callback.on_llm_new_token(response)

            step3_time = time.time()
            log.info(
                f'--STEP_3 模型返回-- \n 耗时:{step3_time - step2_time} \n RAG生成返回:{full_response}')

            # STEP 4. 计算token消耗品
            llm_calls["response"] = 1
            prompt_tokens["response"] = num_tokens(search_prompt, self.token_encoder)
            output_tokens["response"] = num_tokens(full_response, self.token_encoder)

            log.info(
                f'--STEP_4 最终汇总-- \n 总耗时:{time.time() - start_time} \n 输入tokens:{sum(prompt_tokens.values())} \n 输出tokens:{sum(output_tokens.values())}')

            return SearchResult(
                response=full_response,
                context_data=context_result.context_records,
                context_text=context_result.context_chunks,
                completion_time=time.time() - start_time,  # 搜索执行耗时
                llm_calls=sum(llm_calls.values()),  # 大模型调用次数
                prompt_tokens=sum(prompt_tokens.values()),  # 请求token消耗
                output_tokens=sum(output_tokens.values()),  # 输出token消耗
                llm_calls_categories=llm_calls,
                prompt_tokens_categories=prompt_tokens,
                output_tokens_categories=output_tokens,
            )

        except Exception as e:
            log.exception(f"检索异常={e}")
            return SearchResult(
                response="",
                context_data=context_result.context_records,
                context_text=context_result.context_chunks,
                completion_time=time.time() - start_time,
                llm_calls=1,
                prompt_tokens=num_tokens(search_prompt, self.token_encoder),
                output_tokens=0,
            )

    async def stream_search(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,
    ) -> AsyncGenerator[BaseAgentEvent | SearchResult, None]:
        """
        流式输出检索信息内容
        Args:
            query:
            conversation_history:

        Returns:
            AsyncGenerator[BaseAgentEvent, SearchResult]: 流式输出大模型的中间事件信息和最终结果

        """
        print(
            f'--接受到用户检索请求-- \n Query:{query}')
        start_time = time.time()
        llm_calls, prompt_tokens, output_tokens = {}, {}, {}

        # STEP 1. 流式构建任务
        context_result = None
        async for event in self.context_builder.build_streaming_context(
                query=query,
                conversation_history=conversation_history,
                **self.context_builder_params,
        ):
            if isinstance(event, ContextBuilderResult):
                context_result = event
            else:
                yield event

        prompt_tokens["build_context"] = context_result.prompt_tokens
        output_tokens["build_context"] = context_result.output_tokens
        step1_time = time.time()
        print(
            f'--STEP_1 构建上下文信息-- \n 耗时:{step1_time - start_time} \n 上下文内容:{context_result.context_chunks}')

        # STEP 2. 组装最终调用大模型生成的Prompt
        search_prompt = self.system_prompt.format(
            context_data=context_result.context_chunks,
            response_type=self.response_type,
        )
        history_messages = [
            {"role": "system", "content": search_prompt},
        ]

        full_response = ""
        step2_time = time.time()
        print(
            f'--STEP_2 组装模型调用Prompt-- \n 耗时:{step2_time - step1_time} \n Prompt:{search_prompt}')

        # STEP 3. 调用模型生成
        async for response in self.model.achat_stream(
                prompt=query,
                history=history_messages,
                model_parameters=self.model_params,
        ):
            full_response += response
            yield ModelClientStreamingChunkEvent(source="助理机器人", content=response)
            ## 3.3 模型生成的回调函数
            for callback in self.callbacks:
                callback.on_llm_new_token(response)

        step3_time = time.time()
        print(
            f'--STEP_3 模型返回-- \n 耗时:{step3_time - step2_time} \n RAG生成返回:{full_response}')

        # STEP 4. 计算token消耗品
        llm_calls["response"] = 1
        prompt_tokens["response"] = num_tokens(search_prompt, self.token_encoder)
        output_tokens["response"] = num_tokens(full_response, self.token_encoder)

        print(
            f"--STEP_4 最终汇总-- \n 总耗时:{time.time() - start_time} \n 输入tokens:{sum(prompt_tokens.values())} \n 输出tokens:{sum(output_tokens.values())}")
        yield SearchResult(
            response=full_response,
            context_data=context_result.context_records,
            context_text=context_result.context_chunks,
            completion_time=time.time() - start_time,  # 搜索执行耗时
            llm_calls=sum(llm_calls.values()),  # 大模型调用次数
            prompt_tokens=sum(prompt_tokens.values()),  # 请求token消耗
            output_tokens=sum(output_tokens.values()),  # 输出token消耗
            llm_calls_categories=llm_calls,
            prompt_tokens_categories=prompt_tokens,
            output_tokens_categories=output_tokens,
        )

    @classmethod
    def get_local_search_engine(cls,
                                config: GraphRagConfig,
                                reports: list[CommunityReport],
                                text_units: list[TextUnit],
                                entities: list[Entity],
                                relationships: list[Relationship],
                                covariates: dict[str, list[Covariate]],
                                response_type: str,
                                description_embedding_store: BaseVectorStore,
                                system_prompt: str | None = None,
                                callbacks: list[QueryCallbacks] | None = None
                                ) -> Self:
        """
        获取局部搜索引擎
        Args:
            config: 全局配置
            reports: 报告数据
            text_units: 文本数据
            entities: 实体数据
            relationships: 关系数据
            covariates: 协变量事件数据
            response_type: 回复类型说明
            description_embedding_store: 文本描述的向量存储
            system_prompt: 系统prompt
            callbacks: 回调函数

        Returns:
            Self: 返回搜索引擎实例

        """

        # 1. 获取大模型配置
        model_settings = config.get_language_model_config(config.local_search.chat_model_id)
        if model_settings.max_retries == -1:
            model_settings.max_retries = 3  # 默认重试3次

        chat_model = ModelManager().get_or_create_chat_model(
            name="local_search_chat",
            model_type=model_settings.type,
            config=model_settings,
        )

        # 2. 获取Embedding模型配置
        embedding_settings = config.get_language_model_config(
            config.local_search.embedding_model_id
        )
        if embedding_settings.max_retries == -1:
            embedding_settings.max_retries = 3  # 默认重试3次
        embedding_model = ModelManager().get_or_create_embedding_model(
            name="local_search_embedding",
            model_type=embedding_settings.type,
            config=embedding_settings,
        )

        # 3. 获取token分词器模型
        token_encoder = tiktoken.get_encoding(model_settings.encoding_model)

        # 4. 局部搜索相关配置
        ls_config = config.local_search

        # 5. 根据上述配置初始化返回
        return LocalSearchEngine(
            model=chat_model,
            system_prompt=system_prompt,
            context_builder=LocalSearchContextBuilder(
                community_reports=reports,
                text_units=text_units,
                entities=entities,
                relationships=relationships,
                covariates=covariates,
                entity_text_embeddings=description_embedding_store,
                embedding_vectorstore_key=EntityVectorStoreKey.ID,
                # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE
                text_embedder=embedding_model,
                token_encoder=token_encoder,
            ),
            token_encoder=token_encoder,
            model_params=get_openai_model_parameters_from_config(model_settings),
            context_builder_params={
                "text_unit_prop": ls_config.text_unit_prop,
                "community_prop": ls_config.community_prop,
                "conversation_history_max_turns": ls_config.conversation_history_max_turns,
                "conversation_history_user_turns_only": True,
                "top_k_mapped_entities": ls_config.top_k_entities,
                "top_k_relationships": ls_config.top_k_relationships,
                "include_entity_rank": True,
                "include_relationship_weight": True,
                "include_community_rank": False,
                "return_candidate_context": False,
                "embedding_vectorstore_key": EntityVectorStoreKey.ID,
                # set this to EntityVectorStoreKey.TITLE if the vectorstore uses entity title as ids
                "max_context_tokens": ls_config.max_context_tokens,
                # change this based on the token limit you have on your event (if you are using a event with 8k limit, a good setting could be 5000)
            },
            response_type=response_type,
            callbacks=callbacks,
        )
