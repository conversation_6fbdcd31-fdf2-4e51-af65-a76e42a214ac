# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License
"""Algorithms to build context data for local search prompt."""

import logging
from copy import deepcopy
from typing import Any, <PERSON>ync<PERSON><PERSON><PERSON>, Dict, List

import pandas as pd
import tiktoken
from autogen_agentchat.messages import BaseAgentEvent
from graphrag.data_model.community_report import CommunityReport
from graphrag.data_model.covariate import Covariate
from graphrag.data_model.entity import Entity
from graphrag.data_model.relationship import Relationship
from graphrag.data_model.text_unit import TextUnit
from graphrag.language_model.protocol.base import EmbeddingModel
from graphrag.query.context_builder.community_context import (
    build_community_context,
)
from graphrag.query.context_builder.conversation_history import (
    ConversationHistory,
)
from graphrag.query.input.retrieval.community_reports import (
    get_candidate_communities,
)
from graphrag.query.llm.text_utils import num_tokens
from graphrag.vector_stores.base import BaseVectorStore

from ._base_builder import LocalContextBuilder, ContextBuilderResult
from ._entity_retrieval import EntityVectorStoreKey, map_query_to_entities
from ._local_context import (
    build_entity_table_data, build_relationship_table_data, build_covariates_table_data,
)
from ._source_context import (
    count_relationships, build_text_unit_table_data,
)
from ..event.rag_event import LocalContextBuildEvent, SourceContextBuildEvent

log = logging.getLogger(__name__)


class LocalSearchContextBuilder(LocalContextBuilder):
    """本地搜索的上下文构建，此处流程会根据用户query关联entity、relationship等数据"""

    def __init__(
            self,
            entities: list[Entity],  # 所有实体模型实例
            entity_text_embeddings: BaseVectorStore,  # 实体的文本向量嵌入
            text_embedder: EmbeddingModel,  # 文本embedding模型
            text_units: list[TextUnit] | None = None,
            community_reports: list[CommunityReport] | None = None,  # 组织的具体报告
            relationships: list[Relationship] | None = None,  # 关系
            covariates: dict[str, list[Covariate]] | None = None,  # 协变量（事件），key是类型
            token_encoder: tiktoken.Encoding | None = None,  # 分词器
            embedding_vectorstore_key: str = EntityVectorStoreKey.ID,  # 向量检索时的主要检索键
    ):
        if community_reports is None:
            community_reports = []
        if relationships is None:
            relationships = []
        if covariates is None:
            covariates = {}
        if text_units is None:
            text_units = []
        self.entities = {entity.id: entity for entity in entities}
        self.community_reports = {
            community.community_id: community for community in community_reports
        }
        self.text_units = {unit.id: unit for unit in text_units}
        self.relationships = {
            relationship.id: relationship for relationship in relationships
        }
        self.covariates = covariates
        self.entity_text_embeddings = entity_text_embeddings
        self.text_embedder = text_embedder
        self.token_encoder = token_encoder
        self.embedding_vectorstore_key = embedding_vectorstore_key

    def filter_by_entity_keys(self, entity_keys: list[int] | list[str]):
        """Filter entity text embeddings by entity keys."""
        self.entity_text_embeddings.filter_by_id(entity_keys)

    async def build_context(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,  # 会话历史
            include_entity_names: list[str] | None = None,  # 包含的实体名
            exclude_entity_names: list[str] | None = None,  # 剔除的实体名
            conversation_history_max_turns: int | None = 5,  # 会话历史保留的轮次
            conversation_history_user_turns_only: bool = True,  # 只保留用户会话
            max_context_tokens: int = 3000,  # 最大的上下文tokens窗口数量
            text_unit_prop: float = 0.5,  # 上下文最终内容中文本信息的占比（事实性内容）
            community_prop: float = 0.25,  # 组织信息占比
            top_k_mapped_entities: int = 10,  # 根据用户query映射召回的实体数量
            top_k_relationships: int = 10,  # 召回的每个实体召回的关系数量
            include_community_rank: bool = False,  # 上下文信息是否包含排名信息
            include_entity_rank: bool = False,
            rank_description: str = "number of relationships",  # 排序逻辑描述，给到大模型使用
            include_relationship_weight: bool = False,  # 是否在关系中显示权重信息
            relationship_ranking_attribute: str = "rank",  # 关系排序的属性
            return_candidate_context: bool = False,  # 是否返回所有候选信息
            use_community_summary: bool = True,  # 是否使用组织报告摘要信息
            min_community_rank: int = 0,  # 组织报告的最小排名，过滤不重要的报告
            community_context_name: str = "Reports",  # 最终给大模型的组织报告的名称
            column_delimiter: str = "|",  # 表格数据的分隔符
            **kwargs: dict[str, Any],
    ) -> ContextBuilderResult:
        """
        构建上下文逻辑
        Args:
            query:
            conversation_history:
            include_entity_names:
            exclude_entity_names:
            conversation_history_max_turns:
            conversation_history_user_turns_only:
            max_context_tokens:
            text_unit_prop:
            community_prop:
            top_k_mapped_entities:
            top_k_relationships:
            include_community_rank:
            include_entity_rank:
            rank_description:
            include_relationship_weight:
            relationship_ranking_attribute:
            return_candidate_context:
            use_community_summary:
            min_community_rank:
            community_context_name:
            column_delimiter:
            **kwargs:

        Returns:
        """
        """构建上下文逻辑的异步版本"""
        final_result = None
        async for event in self.build_streaming_context(query=query, conversation_history=conversation_history,
                                                        include_entity_names=include_entity_names,
                                                        exclude_entity_names=exclude_entity_names,
                                                        conversation_history_max_turns=conversation_history_max_turns,
                                                        conversation_history_user_turns_only=conversation_history_user_turns_only,
                                                        max_context_tokens=max_context_tokens,
                                                        text_unit_prop=text_unit_prop, community_prop=community_prop,
                                                        top_k_mapped_entities=top_k_mapped_entities,
                                                        top_k_relationships=top_k_relationships,
                                                        include_entity_rank=include_entity_rank,
                                                        include_relationship_weight=include_relationship_weight,
                                                        relationship_ranking_attribute=relationship_ranking_attribute,
                                                        column_delimiter=column_delimiter, **kwargs):
            if isinstance(event, ContextBuilderResult):
                final_result = event
        if final_result is None:
            return ContextBuilderResult(
                context_chunks="",
                context_records={}
            )
        return final_result

    async def build_streaming_context(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,  # 会话历史
            include_entity_names: list[str] | None = [],  # 包含的实体名
            exclude_entity_names: list[str] | None = [],  # 剔除的实体名
            conversation_history_max_turns: int | None = 5,  # 会话历史保留的轮次
            conversation_history_user_turns_only: bool = True,  # 只保留用户会话
            max_context_tokens: int = 3000,  # 最大的上下文tokens窗口数量
            text_unit_prop: float = 0.5,  # 上下文最终内容中文本信息的占比（事实性内容）
            community_prop: float = 0,  # 组织信息占比
            top_k_mapped_entities: int = 10,  # 根据用户query映射召回的实体数量
            top_k_relationships: int = 10,  # 召回的每个实体召回的关系数量
            include_entity_rank: bool = False,
            include_relationship_weight: bool = False,  # 是否在关系中显示权重信息
            relationship_ranking_attribute: str = "rank",  # 关系排序的属性
            column_delimiter: str = "|",  # 表格数据的分隔符
            **kwargs: dict[str, Any],
    ) -> AsyncGenerator[BaseAgentEvent | ContextBuilderResult, None]:
        """
        通过流式的方式构建上下文结果
        Args:
            query:
            conversation_history:
            include_entity_names:
            exclude_entity_names:
            conversation_history_max_turns:
            conversation_history_user_turns_only:
            max_context_tokens:
            text_unit_prop:
            community_prop:
            top_k_mapped_entities:
            top_k_relationships:
            include_entity_rank:
            include_relationship_weight:
            relationship_ranking_attribute:
            column_delimiter:
            **kwargs:

        Returns:
            AsyncGenerator[BaseAgentEvent | ContextBuilderResult, None]:

        """

        # 占比基础判断
        if community_prop + text_unit_prop > 1:
            raise ValueError("The sum of community_prop and text_unit_prop should not exceed 1.")

        # STEP 1. 根据用户会话召回具体的实体
        if conversation_history:
            pre_user_questions = "\n".join(
                conversation_history.get_user_turns(conversation_history_max_turns)
            )
            query = f"{query}\n{pre_user_questions}"

        selected_entities = map_query_to_entities(
            query=query,
            text_embedding_vectorstore=self.entity_text_embeddings,
            text_embedder=self.text_embedder,
            all_entities_dict=self.entities,
            embedding_vectorstore_key=self.embedding_vectorstore_key,
            include_entity_names=include_entity_names,
            exclude_entity_names=exclude_entity_names,
            k=top_k_mapped_entities,
            oversample_scaler=2,
        )

        ## 1.1 构造实体召回事件返回
        # entity_content = build_markdown_table_from_entity(entity_list=selected_entities, table_title='相关实体信息')
        # yield EntityRetrievalEvent(
        #     content=entity_content,
        #     source="知识检索助手"
        # )

        # STEP 2. 开始构建上下文内容
        final_context: List[str] = []
        final_context_data: Dict[str, pd.DataFrame] = {}

        ## 2.1 根据会话内容上下文
        if conversation_history:
            # build conversation history context
            (
                conversation_history_context,
                conversation_history_context_data,
            ) = conversation_history.build_context(
                include_user_turns_only=conversation_history_user_turns_only,
                max_qa_turns=conversation_history_max_turns,
                column_delimiter=column_delimiter,
                max_context_tokens=max_context_tokens,
                context_name="用户会话记录",
                recency_bias=False,  # 最近会话的权重，打开后会话越近权重越大
            )
            if conversation_history_context.strip() != "":
                final_context.append(conversation_history_context)
                final_context_data = conversation_history_context_data
                max_context_tokens = max_context_tokens - num_tokens(
                    conversation_history_context, self.token_encoder
                )

        ## 2.2 构建组织内容上下文 *注：不使用组织内容，只构建不添加！
        # community_tokens = max(int(max_context_tokens * community_prop), 0)
        # community_context, community_context_data = self._build_community_context(
        #     selected_entities=selected_entities,
        #     max_context_tokens=community_tokens,
        #     use_community_summary=use_community_summary,
        #     column_delimiter=column_delimiter,
        #     include_community_rank=include_community_rank,
        #     min_community_rank=min_community_rank,
        #     return_candidate_context=return_candidate_context,
        #     context_name=community_context_name,
        # )
        # if community_context.strip() != "":
        #     final_context.append(community_context)
        #     final_context_data = {**final_context_data, **community_context_data}

        ## 2.2 构建局部检索上下文信息
        local_prop = 1 - text_unit_prop
        max_local_tokens = max(int(max_context_tokens * local_prop), 0)
        local_context, local_context_data = self._build_local_context(
            selected_entities=selected_entities,
            max_context_tokens=max_local_tokens,
            include_entity_rank=include_entity_rank,
            include_relationship_weight=include_relationship_weight,
            top_k_relationships=top_k_relationships,
            relationship_ranking_attribute=relationship_ranking_attribute,
        )
        if local_context.strip() != "":
            final_context.append(str(local_context))
            final_context_data.update(local_context_data)

            ### 2.2.1 返回本地搜索的信息
            yield LocalContextBuildEvent(
                content=local_context,
                source="知识检索助手"
            )

        ## 2.3 原始数据内容
        text_unit_tokens = max(int(max_context_tokens * text_unit_prop), 0)
        text_unit_context, text_unit_context_data = self._build_text_unit_context(
            selected_entities=selected_entities,
            max_context_tokens=text_unit_tokens
        )
        if text_unit_context.strip() != "":
            final_context.append(text_unit_context)
            final_context_data.update(text_unit_context_data)

            ### 2.2.1 返回本地搜索的信息
            yield SourceContextBuildEvent(
                content=text_unit_context,
                source="知识检索助手"
            )

        # STEP 3. 构建最终上下文返回
        yield ContextBuilderResult(
            context_chunks="\n\n".join(final_context),  ## 使用换行符拼接所有上下文信息,这个参数是最终被大模型消费的！
            context_records=final_context_data,
        )

    def _build_community_context(
            self,
            selected_entities: list[Entity],
            max_context_tokens: int = 4000,
            use_community_summary: bool = False,
            column_delimiter: str = "|",
            include_community_rank: bool = False,
            min_community_rank: int = 0,
            return_candidate_context: bool = False,
            context_name: str = "Reports",
    ) -> tuple[str, dict[str, pd.DataFrame]]:
        """Add community data to the context window until it hits the max_context_tokens limit.

        Args:
            context_name (str): 
        """
        if len(selected_entities) == 0 or len(self.community_reports) == 0:
            return "", {context_name.lower(): pd.DataFrame()}

        community_matches = {}
        for entity in selected_entities:
            # increase count of the community that this entity belongs to
            if entity.community_ids:
                for community_id in entity.community_ids:
                    community_matches[community_id] = (
                            community_matches.get(community_id, 0) + 1
                    )

        # sort communities by number of matched entities and rank
        selected_communities = [
            self.community_reports[community_id]
            for community_id in community_matches
            if community_id in self.community_reports
        ]
        for community in selected_communities:
            if community.attributes is None:
                community.attributes = {}
            community.attributes["matches"] = community_matches[community.community_id]
        selected_communities.sort(
            key=lambda x: (x.attributes["matches"], x.rank),  # type: ignore
            reverse=True,  # type: ignore
        )
        for community in selected_communities:
            del community.attributes["matches"]  # type: ignore

        context_text, context_data = build_community_context(
            community_reports=selected_communities,
            token_encoder=self.token_encoder,
            use_community_summary=use_community_summary,
            column_delimiter=column_delimiter,
            shuffle_data=False,
            include_community_rank=include_community_rank,
            min_community_rank=min_community_rank,
            max_context_tokens=max_context_tokens,
            single_batch=True,
            context_name=context_name,
        )
        if isinstance(context_text, list) and len(context_text) > 0:
            context_text = "\n\n".join(context_text)

        if return_candidate_context:
            candidate_context_data = get_candidate_communities(
                selected_entities=selected_entities,
                community_reports=list(self.community_reports.values()),
                use_community_summary=use_community_summary,
                include_community_rank=include_community_rank,
            )
            context_key = context_name.lower()
            if context_key not in context_data:
                context_data[context_key] = candidate_context_data
                context_data[context_key]["in_context"] = False
            else:
                if (
                        "id" in candidate_context_data.columns
                        and "id" in context_data[context_key].columns
                ):
                    candidate_context_data["in_context"] = candidate_context_data[
                        "id"
                    ].isin(  # cspell:disable-line
                        context_data[context_key]["id"]
                    )
                    context_data[context_key] = candidate_context_data
                else:
                    context_data[context_key]["in_context"] = True
        return str(context_text), context_data

    def _build_text_unit_context(
            self,
            selected_entities: list[Entity],
            max_context_tokens: int = 8000,
    ) -> tuple[str, dict[str, pd.DataFrame]]:
        """
        构建文本单元上下文
        Args:
            selected_entities:
            max_context_tokens:
            column_delimiter:

        Returns:

        """
        if not selected_entities or not self.text_units:
            return "", {"source_table": pd.DataFrame()}
        selected_text_units = []
        text_unit_ids_set = set()

        unit_info_list = []
        relationship_values = list(self.relationships.values())

        for index, entity in enumerate(selected_entities):
            # 筛选实体对应的关系集合
            entity_relationships = [
                rel
                for rel in relationship_values
                if rel.source == entity.title or rel.target == entity.title
            ]

            # 遍历对应的文本源数据
            for text_id in entity.text_unit_ids or []:
                if text_id not in text_unit_ids_set and text_id in self.text_units:
                    selected_unit = deepcopy(self.text_units[text_id])
                    num_relationships = count_relationships(
                        entity_relationships, selected_unit
                    )
                    text_unit_ids_set.add(text_id)
                    # 返回 unit原始数据、在entity列表中的索引、关联的关系数量
                    unit_info_list.append((selected_unit, index, num_relationships))

        # 文本列表排序
        unit_info_list.sort(key=lambda x: (x[1], -x[2]))

        selected_text_units = [unit[0] for unit in unit_info_list]

        source_md_table, source_pd_table = build_text_unit_table_data(
            text_units=selected_text_units,
            token_encoder=self.token_encoder,
            max_context_tokens=max_context_tokens,
            shuffle_data=False,
            title="原始数据单元"
        )
        res_df = {"source_table": source_pd_table}
        return source_md_table, res_df

    def _build_local_context(
            self,
            selected_entities: list[Entity],
            max_context_tokens: int = 8000,
            include_entity_rank: bool = False,
            include_relationship_weight: bool = False,
            top_k_relationships: int = 10,
            relationship_ranking_attribute: str = "rank",
    ) -> tuple[str, dict[str, pd.DataFrame]]:
        """
        构建局部检索上下文
        Args:
            selected_entities: 召回的主体
            max_context_tokens: 最大token限制
            include_entity_rank: 是否包含主体rank
            include_relationship_weight:
            top_k_relationships:
            relationship_ranking_attribute:

        Returns:
            tuple[str, dict[str, pd.DataFrame]]:

        """
        entity_md_table, entity_pd_table = build_entity_table_data(
            selected_entities=selected_entities,
            token_encoder=self.token_encoder,
            max_context_tokens=max_context_tokens,
            include_entity_rank=include_entity_rank,
            title="相关的实体数据表",
        )

        # 计算当前entity占用的token数量
        entity_tokens = num_tokens(entity_md_table, self.token_encoder)

        # build relationship-covariate context
        added_entities = []
        final_local_md_tables = []
        final_local_pd_tables = {}

        # 通过不断增加实体，构建关系上下文
        for entity in selected_entities:
            current_md_tables = []
            current_pd_tables = {}
            added_entities.append(entity)

            # 构建关系上下文
            (
                relationship_md_table,
                relationship_pd_table,
            ) = build_relationship_table_data(
                selected_entities=added_entities,
                relationships=list(self.relationships.values()),
                token_encoder=self.token_encoder,
                max_context_tokens=max_context_tokens,
                top_k_relationships=top_k_relationships,
                include_relationship_weight=include_relationship_weight,
                relationship_ranking_attribute=relationship_ranking_attribute,
                title="实体关系数据表",
            )

            current_md_tables.append(relationship_md_table)
            current_pd_tables["relationships"] = relationship_pd_table
            total_tokens = entity_tokens + num_tokens(
                relationship_md_table, self.token_encoder
            )

            # 循环构建各类的事件上下文
            for covariate in self.covariates:
                covariate_md_table, covariate_pd_table = build_covariates_table_data(
                    selected_entities=added_entities,
                    covariates=self.covariates[covariate],
                    token_encoder=self.token_encoder,
                    max_context_tokens=max_context_tokens,
                    title=f"相关实体的事件数据(类型={covariate})",
                )
                total_tokens += num_tokens(covariate_md_table, self.token_encoder)
                current_md_tables.append(covariate_md_table)
                current_pd_tables[covariate.lower()] = covariate_pd_table

            if total_tokens > max_context_tokens:
                log.info("Reached token limit - reverting to previous context state")
                break

            final_local_md_tables = current_md_tables
            final_local_pd_tables = current_pd_tables

        # attach entity context to final context
        final_md_tables_text = entity_md_table + "\n\n" + "\n\n".join(final_local_md_tables)
        final_local_pd_tables["entities"] = entity_pd_table

        for key in final_local_pd_tables:
            final_local_pd_tables[key]["in_context"] = True
        return final_md_tables_text, final_local_pd_tables
