# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Context Build utility methods."""

import random
from typing import Any, cast

import pandas as pd
import tiktoken

from graphrag.data_model.relationship import Relationship
from graphrag.data_model.text_unit import TextUnit
from graphrag.query.llm.text_utils import num_tokens

from ..utils.build_markdown_table import ColumnHeader, build_markdown_table

"""
Contain util functions to build text unit context for the search's system prompt
"""


def build_text_unit_table_data(
        text_units: list[TextUnit],
        token_encoder: tiktoken.Encoding | None = None,
        shuffle_data: bool = True,
        max_context_tokens: int = 8000,
        title: str = "原始数据内容",
        random_state: int = 86,
) -> tuple[str, pd.DataFrame]:
    """

    Args:
        text_units:
        token_encoder:
        shuffle_data:
        max_context_tokens:
        title:
        random_state:

    Returns:

    """

    if text_units is None or len(text_units) == 0:
        return "", pd.DataFrame()

    # 随机打乱文本单元列表顺序
    if shuffle_data:
        random.seed(random_state)
        random.shuffle(text_units)

    # 构造表格头
    col_header = [
        ColumnHeader("short_id", "ID"),
        ColumnHeader("text", "文本内容")
    ]

    # 动态添加属性列
    if text_units[0].attributes:
        attributes = text_units[0].attributes
        col_header.extend(ColumnHeader(k, k) for k, v in attributes.items())

    header_id_list = [col.id for col in col_header]
    current_tokens = num_tokens(title, token_encoder)
    pd_rows = []
    md_table_rows = []

    for unit in text_units:
        item = {
            "short_id": unit.short_id or "",
            "text": unit.text or ""
        }
        if unit.attributes:
            item.update(unit.attributes)
        pd_row = [str(item.get(header_id, "")) for header_id in header_id_list]

        # 判断token是否超限,超限直接中断
        row_text = ' | '.join(pd_row)
        row_text_tokens = num_tokens(row_text, token_encoder)
        if current_tokens + row_text_tokens > max_context_tokens:
            break
        current_tokens += row_text_tokens
        pd_rows.append(pd_row)
        md_table_rows.append(item)

    pd_data = pd.DataFrame(data=pd_rows, columns=header_id_list)
    md_table = build_markdown_table(col_header, md_table_rows, title)
    return md_table, pd_data


def count_relationships(
        entity_relationships: list[Relationship], text_unit: TextUnit
) -> int:
    """Count the number of relationships of the selected entity that are associated with the text unit."""
    if not text_unit.relationship_ids:
        # Use list comprehension to count relationships where the text_unit.id is in rel.text_unit_ids
        return sum(
            1
            for rel in entity_relationships
            if rel.text_unit_ids and text_unit.id in rel.text_unit_ids
        )

    # Use a set for faster lookups if entity_relationships is large
    entity_relationship_ids = {rel.id for rel in entity_relationships}

    # Count matching relationship ids efficiently
    return sum(
        1 for rel_id in text_unit.relationship_ids if rel_id in entity_relationship_ids
    )
