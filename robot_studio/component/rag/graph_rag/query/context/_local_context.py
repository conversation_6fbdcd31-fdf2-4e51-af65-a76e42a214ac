# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Local Context Builder."""

from collections import defaultdict
from typing import cast, List

import pandas as pd
import tiktoken

from graphrag.data_model.covariate import Covariate
from graphrag.data_model.entity import Entity
from graphrag.data_model.relationship import Relationship
from graphrag.query.input.retrieval.covariates import (
    get_candidate_covariates,
    to_covariate_dataframe,
)
from graphrag.query.input.retrieval.entities import to_entity_dataframe
from graphrag.query.input.retrieval.relationships import (
    get_candidate_relationships,
    get_entities_from_relationships,
    get_in_network_relationships,
    get_out_network_relationships,
    to_relationship_dataframe,
)
from graphrag.query.llm.text_utils import num_tokens

from robot_studio.component.rag.graph_rag.query.utils.build_markdown_table import ColumnHeader, build_markdown_table


def build_entity_context(
        selected_entities: list[Entity],
        token_encoder: tiktoken.Encoding | None = None,
        max_context_tokens: int = 8000,
        include_entity_rank: bool = True,
        rank_description: str = "number of relationships",
        column_delimiter: str = "|",
        context_name="Entities",
) -> tuple[str, pd.DataFrame]:
    """
    构建主体上下文信息
    Args:
        selected_entities:
        token_encoder:
        max_context_tokens:
        include_entity_rank:
        rank_description:
        column_delimiter:
        context_name:

    Returns:

    """
    if not selected_entities:
        return "", pd.DataFrame()

    # 构造表格头
    current_context_text = f"-----{context_name}-----" + "\n"
    header = ["id", "entity", "description"]
    if include_entity_rank:
        header.append(rank_description)
    attribute_cols = (
        list(selected_entities[0].attributes.keys())
        if selected_entities[0].attributes
        else []
    )
    header.extend(attribute_cols)
    current_context_text += column_delimiter.join(header) + "\n"
    current_tokens = num_tokens(current_context_text, token_encoder)

    all_context_records = [header]
    for entity in selected_entities:
        new_context = [
            entity.short_id if entity.short_id else "",
            entity.title,
            entity.description if entity.description else "",
        ]
        if include_entity_rank:
            new_context.append(str(entity.rank))
        for field in attribute_cols:
            field_value = (
                str(entity.attributes.get(field))
                if entity.attributes and entity.attributes.get(field)
                else ""
            )
            new_context.append(field_value)
        new_context_text = column_delimiter.join(new_context) + "\n"
        new_tokens = num_tokens(new_context_text, token_encoder)
        if current_tokens + new_tokens > max_context_tokens:
            break
        current_context_text += new_context_text
        all_context_records.append(new_context)
        current_tokens += new_tokens

    record_df = pd.DataFrame(
        all_context_records[1:],
        columns=all_context_records[0]
    ) if len(all_context_records) > 1 else pd.DataFrame()

    return current_context_text, record_df


def build_entity_table_data(
        selected_entities: list[Entity],
        token_encoder: tiktoken.Encoding | None = None,
        max_context_tokens: int = 8000,
        include_entity_rank: bool = True,
        title: str = "相关的实体数据表",
) -> tuple[str, pd.DataFrame]:
    """
    构建主体上下文信息
    Args:
        title: 构建的表格标题
        selected_entities: 召回的实体列表
        token_encoder: token计算器
        max_context_tokens: 最大token限制
        include_entity_rank: 是否包含主体排名

    Returns:
        tuple[str, pd.DataFrame]: markdown形式的表格, pd形式的表格

    """
    if not selected_entities:
        return "", pd.DataFrame()

    # 构造表格头
    col_header = [
        ColumnHeader("short_id", "ID"),
        ColumnHeader("title", "实体"),
        ColumnHeader("description", "描述")
    ]
    # 添加排名列
    if include_entity_rank:
        col_header.append(ColumnHeader("rank", "排名"))

    # 动态添加属性列
    if selected_entities[0].attributes:
        attributes = selected_entities[0].attributes
        col_header.extend(ColumnHeader(k, k) for k, v in attributes.items())

    header_id_list = [col.id for col in col_header]
    current_tokens = num_tokens(title, token_encoder)
    pd_rows = []
    md_table_rows = []
    for entity in selected_entities:
        item = {
            "short_id": entity.short_id or "",
            "title": entity.title or "",
            "description": entity.description or ""
        }
        if include_entity_rank:
            item.update({
                "rank": str(entity.rank) if entity.rank is not None else ""
            })
        if entity.attributes:
            item.update(entity.attributes)

        pd_row = [str(item.get(header_id, "")) for header_id in header_id_list]

        # 判断token是否超限,超限直接中断
        row_text = ' | '.join(pd_row)
        row_text_tokens = num_tokens(row_text, token_encoder)
        if current_tokens + row_text_tokens > max_context_tokens:
            break
        current_tokens += row_text_tokens

        pd_rows.append(pd_row)
        md_table_rows.append(item)

    pd_data = pd.DataFrame(data=pd_rows, columns=header_id_list)
    md_table = build_markdown_table(col_header, md_table_rows, title)
    return md_table, pd_data


def build_covariates_table_data(
        selected_entities: list[Entity],
        covariates: list[Covariate],
        token_encoder: tiktoken.Encoding | None = None,
        max_context_tokens: int = 8000,
        title: str = "相关实体的事件数据",
) -> tuple[str, pd.DataFrame]:
    """
    构建实体相关事件的数据表
    Args:
        selected_entities: 相关实体
        covariates: 所有事件信息
        token_encoder:
        max_context_tokens:
        title:

    Returns:

    """
    if len(selected_entities) == 0 or len(covariates) == 0:
        return "", pd.DataFrame()

    # 构造表格头
    col_header = [
        ColumnHeader("short_id", "ID"),
        ColumnHeader("subject_id", "事件")
    ]

    # 动态添加属性列
    if covariates[0].attributes:
        attributes = covariates[0].attributes
        col_header.extend(ColumnHeader(k, k) for k, v in attributes.items())

    # 从事件表中筛选回来和实体相关的数据
    selected_covariates: List[Covariate] = []
    for entity in selected_entities:
        selected_covariates.extend([
            cov for cov in covariates if cov.subject_id == entity.title
        ])

    header_id_list = [col.id for col in col_header]
    current_tokens = num_tokens(title, token_encoder)
    pd_rows = []
    md_table_rows = []
    for covariate in selected_covariates:
        item = {
            "short_id": covariate.short_id or "",
            "subject_id": covariate.subject_id or ""
        }
        if covariate.attributes:
            item.update(covariate.attributes)
        pd_row = [str(item.get(header_id, "")) for header_id in header_id_list]

        # 判断token是否超限,超限直接中断
        row_text = ' | '.join(pd_row)
        row_text_tokens = num_tokens(row_text, token_encoder)
        if current_tokens + row_text_tokens > max_context_tokens:
            break
        current_tokens += row_text_tokens

        pd_rows.append(pd_row)
        md_table_rows.append(item)

    pd_data = pd.DataFrame(data=pd_rows, columns=header_id_list)
    md_table = build_markdown_table(col_header, md_table_rows, title)
    return md_table, pd_data


def build_relationship_table_data(
        selected_entities: list[Entity],
        relationships: list[Relationship],
        token_encoder: tiktoken.Encoding | None = None,
        include_relationship_weight: bool = False,
        max_context_tokens: int = 8000,
        top_k_relationships: int = 10,
        relationship_ranking_attribute: str = "rank",
        title: str = "实体关系数据表",
) -> tuple[str, pd.DataFrame]:
    """
    构建实体的关系表
    Args:
        selected_entities:
        relationships:
        token_encoder:
        include_relationship_weight:
        max_context_tokens:
        top_k_relationships:
        relationship_ranking_attribute:
        column_delimiter:
        title:

    Returns:

    """
    # 筛选实体对应的关系
    selected_relationships = _filter_relationships(
        selected_entities=selected_entities,
        relationships=relationships,
        top_k_relationships=top_k_relationships,
        relationship_ranking_attribute=relationship_ranking_attribute,
    )
    if len(selected_entities) == 0 or len(selected_relationships) == 0:
        return "", pd.DataFrame()

    # 构造表格头
    col_header = [
        ColumnHeader("short_id", "关系ID"),
        ColumnHeader("source", "来源实体"),
        ColumnHeader("target", "关联实体"),
        ColumnHeader("description", "关系描述"),
    ]
    # 添加关系权重列
    if include_relationship_weight:
        col_header.append(ColumnHeader("weight", "关系权重"))

    # 动态添加属性列
    if selected_relationships[0].attributes:
        attributes = selected_relationships[0].attributes
        col_header.extend(ColumnHeader(k, k) for k, v in attributes.items())

    header_id_list = [col.id for col in col_header]
    current_tokens = num_tokens(title, token_encoder)
    pd_rows = []
    md_table_rows = []
    for rel in selected_relationships:
        item = {
            "short_id": rel.short_id or "",
            "source": rel.source or "",
            "target": rel.target or "",
            "description": rel.description or ""
        }
        if include_relationship_weight:
            item.update({
                "weight": str(rel.rank) if rel.rank is not None else ""
            })
        if rel.attributes:
            item.update(rel.attributes)

        pd_row = [str(item.get(header_id, "")) for header_id in header_id_list]

        # 判断token是否超限,超限直接中断
        row_text = ' | '.join(pd_row)
        row_text_tokens = num_tokens(row_text, token_encoder)
        if current_tokens + row_text_tokens > max_context_tokens:
            break
        current_tokens += row_text_tokens

        pd_rows.append(pd_row)
        md_table_rows.append(item)

    pd_data = pd.DataFrame(data=pd_rows, columns=header_id_list)
    md_table = build_markdown_table(col_header, md_table_rows, title)
    return md_table, pd_data


def build_covariates_context(
        selected_entities: list[Entity],
        covariates: list[Covariate],
        token_encoder: tiktoken.Encoding | None = None,
        max_context_tokens: int = 8000,
        column_delimiter: str = "|",
        context_name: str = "Covariates",
) -> tuple[str, pd.DataFrame]:
    """Prepare covariate data tables as context data for system prompt."""
    # create an empty list of covariates
    if len(selected_entities) == 0 or len(covariates) == 0:
        return "", pd.DataFrame()

    selected_covariates = list[Covariate]()
    record_df = pd.DataFrame()

    # add context header
    current_context_text = f"-----{context_name}-----" + "\n"

    # add header
    header = ["id", "entity"]
    attributes = covariates[0].attributes or {} if len(covariates) > 0 else {}
    attribute_cols = list(attributes.keys()) if len(covariates) > 0 else []
    header.extend(attribute_cols)
    current_context_text += column_delimiter.join(header) + "\n"
    current_tokens = num_tokens(current_context_text, token_encoder)

    all_context_records = [header]
    for entity in selected_entities:
        selected_covariates.extend([
            cov for cov in covariates if cov.subject_id == entity.title
        ])

    for covariate in selected_covariates:
        new_context = [
            covariate.short_id if covariate.short_id else "",
            covariate.subject_id,
        ]
        for field in attribute_cols:
            field_value = (
                str(covariate.attributes.get(field))
                if covariate.attributes and covariate.attributes.get(field)
                else ""
            )
            new_context.append(field_value)

        new_context_text = column_delimiter.join(new_context) + "\n"
        new_tokens = num_tokens(new_context_text, token_encoder)
        if current_tokens + new_tokens > max_context_tokens:
            break
        current_context_text += new_context_text
        all_context_records.append(new_context)
        current_tokens += new_tokens

        if len(all_context_records) > 1:
            record_df = pd.DataFrame(
                all_context_records[1:], columns=cast("Any", all_context_records[0])
            )
        else:
            record_df = pd.DataFrame()

    return current_context_text, record_df


def build_relationship_context(
        selected_entities: list[Entity],
        relationships: list[Relationship],
        token_encoder: tiktoken.Encoding | None = None,
        include_relationship_weight: bool = False,
        max_context_tokens: int = 8000,
        top_k_relationships: int = 10,
        relationship_ranking_attribute: str = "rank",
        column_delimiter: str = "|",
        context_name: str = "Relationships",
) -> tuple[str, pd.DataFrame]:
    """Prepare relationship data tables as context data for system prompt."""
    selected_relationships = _filter_relationships(
        selected_entities=selected_entities,
        relationships=relationships,
        top_k_relationships=top_k_relationships,
        relationship_ranking_attribute=relationship_ranking_attribute,
    )

    if len(selected_entities) == 0 or len(selected_relationships) == 0:
        return "", pd.DataFrame()

    # add headers
    current_context_text = f"-----{context_name}-----" + "\n"
    header = ["id", "source", "target", "description"]
    if include_relationship_weight:
        header.append("weight")
    attribute_cols = (
        list(selected_relationships[0].attributes.keys())
        if selected_relationships[0].attributes
        else []
    )
    attribute_cols = [col for col in attribute_cols if col not in header]
    header.extend(attribute_cols)

    current_context_text += column_delimiter.join(header) + "\n"
    current_tokens = num_tokens(current_context_text, token_encoder)

    all_context_records = [header]
    for rel in selected_relationships:
        new_context = [
            rel.short_id if rel.short_id else "",
            rel.source,
            rel.target,
            rel.description if rel.description else "",
        ]
        if include_relationship_weight:
            new_context.append(str(rel.weight if rel.weight else ""))
        for field in attribute_cols:
            field_value = (
                str(rel.attributes.get(field))
                if rel.attributes and rel.attributes.get(field)
                else ""
            )
            new_context.append(field_value)
        new_context_text = column_delimiter.join(new_context) + "\n"
        new_tokens = num_tokens(new_context_text, token_encoder)
        if current_tokens + new_tokens > max_context_tokens:
            break
        current_context_text += new_context_text
        all_context_records.append(new_context)
        current_tokens += new_tokens

    if len(all_context_records) > 1:
        record_df = pd.DataFrame(
            all_context_records[1:], columns=cast("Any", all_context_records[0])
        )
    else:
        record_df = pd.DataFrame()

    return current_context_text, record_df


def _filter_relationships(
        selected_entities: list[Entity],
        relationships: list[Relationship],
        top_k_relationships: int = 10,
        relationship_ranking_attribute: str = "rank",
) -> list[Relationship]:
    """Filter and sort relationships based on a set of selected entities and a ranking attribute."""
    # First priority: in-network relationships (i.e. relationships between selected entities)
    in_network_relationships = get_in_network_relationships(
        selected_entities=selected_entities,
        relationships=relationships,
        ranking_attribute=relationship_ranking_attribute,
    )

    # Second priority -  out-of-network relationships
    # (i.e. relationships between selected entities and other entities that are not within the selected entities)
    out_network_relationships = get_out_network_relationships(
        selected_entities=selected_entities,
        relationships=relationships,
        ranking_attribute=relationship_ranking_attribute,
    )
    if len(out_network_relationships) <= 1:
        return in_network_relationships + out_network_relationships

    # within out-of-network relationships, prioritize mutual relationships
    # (i.e. relationships with out-network entities that are shared with multiple selected entities)
    selected_entity_names = [entity.title for entity in selected_entities]
    out_network_source_names = [
        relationship.source
        for relationship in out_network_relationships
        if relationship.source not in selected_entity_names
    ]
    out_network_target_names = [
        relationship.target
        for relationship in out_network_relationships
        if relationship.target not in selected_entity_names
    ]
    out_network_entity_names = list(
        set(out_network_source_names + out_network_target_names)
    )
    out_network_entity_links = defaultdict(int)
    for entity_name in out_network_entity_names:
        targets = [
            relationship.target
            for relationship in out_network_relationships
            if relationship.source == entity_name
        ]
        sources = [
            relationship.source
            for relationship in out_network_relationships
            if relationship.target == entity_name
        ]
        out_network_entity_links[entity_name] = len(set(targets + sources))

    # sort out-network relationships by number of links and rank_attributes
    for rel in out_network_relationships:
        if rel.attributes is None:
            rel.attributes = {}
        rel.attributes["links"] = (
            out_network_entity_links[rel.source]
            if rel.source in out_network_entity_links
            else out_network_entity_links[rel.target]
        )

    # sort by attributes[links] first, then by ranking_attribute
    if relationship_ranking_attribute == "rank":
        out_network_relationships.sort(
            key=lambda x: (x.attributes["links"], x.rank),  # type: ignore
            reverse=True,  # type: ignore
        )
    elif relationship_ranking_attribute == "weight":
        out_network_relationships.sort(
            key=lambda x: (x.attributes["links"], x.weight),  # type: ignore
            reverse=True,  # type: ignore
        )
    else:
        out_network_relationships.sort(
            key=lambda x: (
                x.attributes["links"],  # type: ignore
                x.attributes[relationship_ranking_attribute],  # type: ignore
            ),  # type: ignore
            reverse=True,
        )

    relationship_budget = top_k_relationships * len(selected_entities)
    return in_network_relationships + out_network_relationships[:relationship_budget]


def get_candidate_context(
        selected_entities: list[Entity],
        entities: list[Entity],
        relationships: list[Relationship],
        covariates: dict[str, list[Covariate]],
        include_entity_rank: bool = True,
        entity_rank_description: str = "number of relationships",
        include_relationship_weight: bool = False,
) -> dict[str, pd.DataFrame]:
    """Prepare entity, relationship, and covariate data tables as context data for system prompt."""
    candidate_context = {}
    candidate_relationships = get_candidate_relationships(
        selected_entities=selected_entities,
        relationships=relationships,
    )
    candidate_context["relationships"] = to_relationship_dataframe(
        relationships=candidate_relationships,
        include_relationship_weight=include_relationship_weight,
    )
    candidate_entities = get_entities_from_relationships(
        relationships=candidate_relationships, entities=entities
    )
    candidate_context["entities"] = to_entity_dataframe(
        entities=candidate_entities,
        include_entity_rank=include_entity_rank,
        rank_description=entity_rank_description,
    )

    for covariate in covariates:
        candidate_covariates = get_candidate_covariates(
            selected_entities=selected_entities,
            covariates=covariates[covariate],
        )
        candidate_context[covariate.lower()] = to_covariate_dataframe(
            candidate_covariates
        )

    return candidate_context
