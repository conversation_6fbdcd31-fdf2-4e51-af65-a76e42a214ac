# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Base classes for global and local context builders."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import AsyncGenerator

import pandas as pd
from autogen_agentchat.messages import BaseAgentEvent
from graphrag.query.context_builder.conversation_history import (
    ConversationHistory,
)


@dataclass
class ContextBuilderResult:
    """A class to hold the results of the build_context."""

    context_chunks: str | list[str]
    context_records: dict[str, pd.DataFrame]
    llm_calls: int = 0
    prompt_tokens: int = 0
    output_tokens: int = 0


class LocalContextBuilder(ABC):
    """重写构建器基类"""

    @abstractmethod
    async def build_context(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,
            **kwargs,
    ) -> ContextBuilderResult:
        """Build the context for the local search mode."""

    @abstractmethod
    async def build_streaming_context(
            self,
            query: str,
            conversation_history: ConversationHistory | None = None,
            **kwargs,
    ) -> AsyncGenerator[BaseAgentEvent | ContextBuilderResult, None]:
        """Build the context for the local search mode.

        Args:
            query:
            conversation_history:
            **kwargs:

        Returns:
            AsyncGenerator[BaseAgentEvent | ContextBuilderResult, None]: 
        """
