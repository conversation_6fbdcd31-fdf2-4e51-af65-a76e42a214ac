import os
from pathlib import Path
from typing import AsyncGenerator

import pandas as pd
from autogen_agentchat.messages import BaseAgentEvent
from graphrag.config.embeddings import entity_description_embedding
from graphrag.logger.print_progress import PrintProgressLogger
from graphrag.utils.api import get_embedding_store, load_search_prompt
from graphrag.utils.cli import redact

from robot_studio.common.config_service import get_config, get_configs_by_tag

from ..engine import LocalSearchEngine, SearchResult
from ..utils.config_resolve import resolve_output_files
from ..utils.indexer_adapters import read_indexer_entities, read_indexer_covariates, read_indexer_text_units, \
    read_indexer_relationships
from ..utils.load_config import load_config

logger = PrintProgressLogger("")


class LocalSearch:

    def __init__(self):

        # 项目目录从环境变量中获取
        pythonpath = os.environ.get('PYTHONPATH')
        self.rag_root_path = pythonpath  # rag的项目目录

        # 配置文件地址
        self.rag_config_path = pythonpath + '/robot_studio/component/rag/graph_rag/settings.yaml'

    async def stream_search(self, query: str) -> AsyncGenerator[BaseAgentEvent | SearchResult, None]:
        """
        搜索执行方法入口
        Args:
            query: 查询请求

        Returns:
            AsyncGenerator[BaseAgentEvent | SearchResult, None]: 生成检索过程事件和最终检索结果

        """

        # Step 1. 加载配置文件为GraphRAG的配置模型
        root_dir = Path(self.rag_root_path)
        config_path = Path(self.rag_config_path)
        if not root_dir.exists():
            raise ValueError(f"rag的根目录不存在={self.rag_root_path}")
        root = root_dir.resolve()

        # 从configuration配置表加载GRAPHRAG相关配置
        try:
            # 获取GRAPHRAG相关的所有配置
            graphrag_configs = get_configs_by_tag("GRAPHRAG")
            
            # 将配置加载到环境变量中
            for key, value in graphrag_configs.items():
                if value:  # 只设置非空值
                    if key in ["GRAPHRAG_API_KEY", "GRAPHRAG_API_BASE", "GRAPHRAG_EMBEDDING_BATCH_SIZE"]:
                        os.environ[key] = value
                        logger.info(f"已从配置表加载环境变量: {key}")
                    os.environ[key] = value

                    logger.info(f"已从配置表加载环境变量: {key}")
            
        except Exception as e:
            # 如果配置服务失败，继续执行（回退到现有的环境变量）
            logger.error(f"GRAPHRAG配置加载失败: {str(e)}")
            pass

        config = load_config(root, config_path, None)

        # Step 2. 从setting配置文件中读取原始数据
        dataframe_dict = await resolve_output_files(
            config=config,
            output_list=[
                # 现在读取实体、关系和原始数据，不消费组织实体和报告
                "text_units",
                "relationships",
                "entities",
            ],
            optional_list=[
                "covariates",
            ],
        )
        final_communities: pd.DataFrame = pd.DataFrame()
        final_community_reports: pd.DataFrame = pd.DataFrame()
        final_text_units: pd.DataFrame = dataframe_dict["text_units"]
        final_relationships: pd.DataFrame = dataframe_dict["relationships"]
        final_entities: pd.DataFrame = dataframe_dict["entities"]
        final_covariates: pd.DataFrame | None = dataframe_dict["covariates"]

        # Step 3. 构建局部搜索引擎参数
        vector_store_args = {}
        for index, store in config.vector_store.items():
            vector_store_args[index] = store.model_dump()
        msg = f"向量存储参数: {redact(vector_store_args)}"
        print(msg)

        ## Step 3.1 获取实体描述的向量存储,用于实体召回
        description_embedding_store = get_embedding_store(
            config_args=vector_store_args,
            embedding_name=entity_description_embedding,
        )

        ## Step 3.2 全量读取实体、关系等数据
        entities = read_indexer_entities(final_entities, final_communities, 2)
        covariates = read_indexer_covariates(final_covariates) if final_covariates is not None else []
        text_units = read_indexer_text_units(final_text_units)
        relationships = read_indexer_relationships(final_relationships)

        ## Step 3.3 prompt获取
        prompt = load_search_prompt(config.root_dir, config.local_search.prompt)

        ## Step 3.4 实例化搜索引擎并执行检索
        search_engine = LocalSearchEngine.get_local_search_engine(
            config=config,
            reports=[],
            text_units=text_units,
            entities=entities,
            relationships=relationships,
            covariates={"claims": covariates},
            description_embedding_store=description_embedding_store,
            response_type="Multiple Paragraphs",
            system_prompt=prompt,
            callbacks=[]
        )
        async for response in search_engine.stream_search(query=query):
            yield response
