from typing import Literal

from autogen_agentchat.messages import BaseAgentEvent


class EntityRetrievalEvent(BaseAgentEvent):
    """实体召回事件"""

    content: str
    """实体检索内容"""

    type: Literal["EntityRetrievalEvent"] = "EntityRetrievalEvent"

    def to_text(self) -> str:
        return self.content


class LocalContextBuildEvent(BaseAgentEvent):
    """关系上下文构建事件"""

    content: str
    """关系上下文构建事件"""

    type: Literal["LocalContextBuildEvent"] = "LocalContextBuildEvent"

    def to_text(self) -> str:
        return self.content


class SourceContextBuildEvent(BaseAgentEvent):
    """原始内容上下文构建事件"""

    content: str
    """原始内容上下文构建事件"""

    type: Literal["SourceContextBuildEvent"] = "SourceContextBuildEvent"

    def to_text(self) -> str:
        return self.content
