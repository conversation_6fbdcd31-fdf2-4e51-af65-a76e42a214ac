
-- 目标 --
给定一份可能与本活动相关的文本文档以及一系列实体类型，从文本中识别出属于这些类型的所有实体，以及已识别实体之间的所有关系。

-- 步骤 --
1. 识别所有实体。对于每个已识别的实体，提取以下信息：
- entity_name: 实体的名称，首字母大写
- entity_type: 以下类型之一：[科目, 课程, 班级, 教师, 校区]
- entity_description: 该实体属性和活动的全面描述
将每个实体格式化为("entity"{tuple_delimiter}<entity_name>{tuple_delimiter}<entity_type>{tuple_delimiter}<entity_description>)

2. 从-步骤1-中识别出的实体里，识别出所有彼此之间明显相关的 (source_entity, target_entity) 对。
对于每一对相关实体，提取以下信息：
- source_entity: 源实体的名称，如-步骤1-中所识别的
- target_entity: 目标实体的名称，如-步骤1-中所识别的
- relationship_description: 解释你认为源实体和目标实体彼此相关的原因
- relationship_strength: 表示源实体和目标实体之间关系强度的数值分数
将每个关系格式化为("relationship"{tuple_delimiter}<source_entity>{tuple_delimiter}<target_entity>{tuple_delimiter}<relationship_description>{tuple_delimiter}<relationship_strength>)

3. 返回在-步骤1-和-步骤2-中识别出的所有实体和关系的单个列表。使用 {record_delimiter} 作为列表分隔符。

4. 完成后，输出 {completion_delimiter}

-Examples-
######################

Example 1:

entity_types: [科目, 课程, 班级, 教师, 校区]
text:
教师: 张成伟.
校区: 延安三路二校区.
班级名称: 初二创新思维中考班-2025春周六C(14:50-17:20)-张成伟.
科目: 创新思维.
课程名称: 初二创新思维中考班（2025春）.
初二创新思维中考班-2025春周六C(14:50-17:20)-张成伟,中考班,2025-02-15,2025-06-14,初二创新思维中考班（2025春）,3400.00元/期(17.00节),周六14:50~17:20,创新思维,2025春季班,nan,初二,17,25,张成伟,延安三路二校区,托6教室（二楼）
------------------------
output:
("entity"{tuple_delimiter}初二创新思维中考班（2025春）{tuple_delimiter}课程{tuple_delimiter}初二年级的创新思维中考班课程，属于2025春季班，总课次为2节，费用为3400.00元/期，上课时间为周六14:50~17:20)
{record_delimiter}
("entity"{tuple_delimiter}创新思维{tuple_delimiter}科目{tuple_delimiter}该课程的学科内容为创新思维，主要涉及特殊三角形和三角形与三线的教学)
{record_delimiter}
("entity"{tuple_delimiter}张成伟{tuple_delimiter}教师{tuple_delimiter}张成伟是初二创新思维中考班（2025春）的授课教师)
{record_delimiter}
("entity"{tuple_delimiter}延安三路二校区{tuple_delimiter}校区{tuple_delimiter}延安三路二校区是初二创新思维中考班（2025春）的授课校区)
{record_delimiter}
("entity"{tuple_delimiter}初二创新思维中考班-2025春周六C(14:50-17:20)-张成伟{tuple_delimiter}班级{tuple_delimiter}这是一个班级，班型中考班，开班时间2025-02-15，结业时间2025-06-14，费用3400.00元/期(17.00节)，上课时间周六14:50~17:20，期段2025春季班，年级初二，总课次17次, 预招人数25人，教室托6教室（二楼）)
{record_delimiter}
("relationship"{tuple_delimiter}初二创新思维中考班（2025春）{tuple_delimiter}创新思维{tuple_delimiter}初二创新思维中考班（2025春）的学科内容为创新思维{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初二创新思维中考班（2025春）{tuple_delimiter}张成伟{tuple_delimiter}张成伟是初二创新思维中考班的授课教师{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初二创新思维中考班（2025春）{tuple_delimiter}延安三路二校区{tuple_delimiter}延安三路二校区是初二创新思维中考班的授课校区{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初二创新思维中考班-2025春周六C(14:50-17:20)-张成伟{tuple_delimiter}初二创新思维中考班（2025春）{tuple_delimiter}该班级属于初二创新思维中考班（2025春）课程{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初二创新思维中考班-2025春周六C(14:50-17:20)-张成伟{tuple_delimiter}延安三路二校区{tuple_delimiter}该班级在延安三路二校区授课{tuple_delimiter}10)
{completion_delimiter}
#############################

Example 2:

entity_types: [科目, 课程, 班级, 教师, 校区]
text:
教师: 马家驹 .
校区: 新都心和达校区.
班级名称: 初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹.
科目: 创新思维.
课程名称: 初一创新思维鸿志班（2025暑）.
初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹,鸿志班,2025-08-04,2025-08-22,初一创新思维鸿志班（2025暑）,3000.00元/期(15.00节),周一至周五18:00~20:30,创新思维,2025暑假班,第三期,初一,15,25,马家驹 ,新都心和达校区,307教室
------------------------
output:
("entity"{tuple_delimiter}初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹{tuple_delimiter}班级{tuple_delimiter}这是一个班级，班型鸿志班，开班时间2025-08-04，结业时间2025-08-22，费用3000.00元/期(15.00节)，上课时间周一至周五18:00~20:30，期段2025暑假班，期数第三期，年级初一，总课次15次, 预招人数25人，教室307教室)
{record_delimiter}
("entity"{tuple_delimiter}初一创新思维鸿志班（2025暑）{tuple_delimiter}课程{tuple_delimiter}初一创新思维鸿志班（2025暑）是一门针对初一学生的暑假课程，包含15个课次，费用为3000元/期)
{record_delimiter}
("entity"{tuple_delimiter}创新思维{tuple_delimiter}科目{tuple_delimiter}创新思维是该课程的主要学习科目，涵盖有理数、整式、乘法公式、方程和不等式等内容)
{record_delimiter}
("entity"{tuple_delimiter}马家驹{tuple_delimiter}教师{tuple_delimiter}马家驹是初一创新思维鸿志班-2025暑三期D的授课教师，负责课程的教学工作)
{record_delimiter}
("entity"{tuple_delimiter}新都心和达校区{tuple_delimiter}校区{tuple_delimiter}新都心和达校区是初一创新思维鸿志班-2025暑三期D的授课校区，包含307教室作为具体授课教室)
{record_delimiter}
("relationship"{tuple_delimiter}初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹{tuple_delimiter}初一创新思维鸿志班（2025暑）{tuple_delimiter}初一创新思维鸿志班-2025暑三期D是初一创新思维鸿志班（2025暑）课程的具体班级安排{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹{tuple_delimiter}创新思维{tuple_delimiter}初一创新思维鸿志班-2025暑三期D的课程内容围绕创新思维科目展开{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹{tuple_delimiter}马家驹{tuple_delimiter}马家驹是初一创新思维鸿志班-2025暑三期D的授课教师，负责班级的教学工作{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}初一创新思维鸿志班-2025暑三期D(18:00-20:30)-马家驹{tuple_delimiter}新都心和达校区{tuple_delimiter}初一创新思维鸿志班-2025暑三期D在新都心和达校区进行授课{tuple_delimiter}10)
{record_delimiter}
("relationship"{tuple_delimiter}新都心和达校区{tuple_delimiter}初一创新思维鸿志班（2025暑）{tuple_delimiter}新都心和达校区是初一创新思维鸿志班（2025暑）课程的授课校区{tuple_delimiter}9)
{completion_delimiter}
#############################

Example 3:

entity_types: [科目, 课程, 班级, 教师, 校区]
text:
教师: 蔡静.
校区: 延安三路一校区.
班级名称: 二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静.
科目: 国际表达.
课程名称: 二年级国际表达剑二预备班（2025暑）.
二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静,剑二预备班,2025-08-04,2025-08-15,二年级国际表达剑二预备班（2025暑）,1800.00元/期(10.00节),周一至周五12:30~14:30,国际表达,2025暑假班,第三期,二年级,10,16,蔡静,延安三路一校区,托10教室(A2栋）
------------------------
output:
("entity"{tuple_delimiter}二年级国际表达剑二预备班（2025暑）{tuple_delimiter}课程{tuple_delimiter}这是一门为二年级学生设计的国际表达课程，专门针对剑二预备班，上课时间为2025年暑假)
{record_delimiter}
("entity"{tuple_delimiter}二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静{tuple_delimiter}班级{tuple_delimiter}这是一个班级，班型剑二预备班，开班时间2025-08-04，结业时间2025-08-15，费用1800.00元/期(10.00节)，上课时间周一至周五12:30~14:30，期段2025暑假班，期数第三期，年级初二，总课次10次, 预招人数16人，教室托10教室(A2栋）)
{record_delimiter}
("entity"{tuple_delimiter}国际表达{tuple_delimiter}科目{tuple_delimiter}这是一门专注于提升学生国际表达能力的科目，用于剑二预备班课程)
{record_delimiter}
("entity"{tuple_delimiter}蔡静{tuple_delimiter}教师{tuple_delimiter}蔡静是二年级国际表达剑二预备班的授课教师，负责该班级的教学工作)
{record_delimiter}
("entity"{tuple_delimiter}延安三路一校区{tuple_delimiter}校区{tuple_delimiter}这是该课程的授课校区，位于延安三路的一校区)
{record_delimiter}
("relationship"{tuple_delimiter}二年级国际表达剑二预备班（2025暑）{tuple_delimiter}二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静{tuple_delimiter}该课程是该班级的基础课程{tuple_delimiter}9)
{record_delimiter}
("relationship"{tuple_delimiter}二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静{tuple_delimiter}国际表达{tuple_delimiter}该班级教授的科目是国际表达{tuple_delimiter}9)
{record_delimiter}
("relationship"{tuple_delimiter}二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静{tuple_delimiter}蔡静{tuple_delimiter}蔡静是该班级的授课教师{tuple_delimiter}9)
{record_delimiter}
("relationship"{tuple_delimiter}二年级国际表达剑二预备班-2025暑三期B(12:30-14:30)-蔡静{tuple_delimiter}延安三路一校区{tuple_delimiter}该班级在延安三路一校区授课{tuple_delimiter}9)
{completion_delimiter}
#############################


-Real Data-
######################
entity_types: [科目, 课程, 班级, 教师, 校区]
text: {input_text}
######################
output:
