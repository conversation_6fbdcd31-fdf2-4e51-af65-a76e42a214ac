#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GraphRAG 索引主程序

按顺序执行以下步骤：
1. 生成配置文件 (settings_generator.py)
2. 生成提示词 (_prompt_tune.py)
3. 执行索引操作 (_index.py)
"""

import asyncio
import logging

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root

# 设置日志
logger = logging.getLogger(__name__)

# 导入各模块的主要功能
from robot_studio.component.rag.graph_rag.index.generator.settings.settings_generator import generate_settings
from robot_studio.component.rag.graph_rag.index.auto_prompt._prompt_tune import async_main as prompt_tune_async_main
from robot_studio.component.rag.graph_rag.index._index import run_index_for_patterns



async def main():
    """
    主函数，按顺序执行各个步骤
    """
    try:
        # 查找资源根目录
        try:
            resource_root = find_resource_root()
            logger.info(f"使用资源根目录: {resource_root}")
        except FileNotFoundError as e:
            logger.warning(f"{e}")
            # 如果找不到资源根目录，使用默认方式
            raise
        #
        # 步骤 1: 生成配置文件
        logger.info("步骤 1: 开始生成配置文件")
        generate_settings(resource_root=resource_root)
        logger.info("步骤 1: 配置文件生成完成")

        # # 步骤 2: 生成提示词
        # logger.info("步骤 2: 开始生成提示词")
        # await prompt_tune_async_main(resource_root=resource_root)
        # logger.info("步骤 2: 提示词生成完成")

        # 步骤 3: 执行索引操作
        logger.info("步骤 3: 开始执行索引操作")
        await run_index_for_patterns(resource_root=resource_root)
        logger.info("步骤 3: 索引操作完成")

        logger.info("所有步骤已完成")
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())