"""
读取graph_data/input/processed/merged_教师明细.csv
添加教师介绍和相关资源

教师介绍: -
相关资源: 拼接oss上的教师介绍目录,拼接规则2025_sq/teacher_intro/ 负责科目转义 + _ + 教师名称拼音首字母 + _intro.jpeg, 例如2025_sq/teacher_intro/english_cj_intro.jpeg
"""

import logging
import os
import pandas as pd
from pathlib import Path
from typing import Optional
from pypinyin import pinyin, Style

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root
from robot_studio.database.oss.oss_client import OssClient
from robot_studio.database.oss.config import load_oss_credentials

# 设置日志
logger = logging.getLogger(__name__)

# 文件名常量
TEACHER_DETAIL_CSV = "merged_教师明细.csv"

def get_oss_client() -> Optional[OssClient]:
    """
    获取OSS客户端

    Returns
    -------
    Optional[OssClient]
        OSS客户端实例，如果创建失败则返回None
    """
    try:
        # 尝试从配置文件加载凭证
        load_oss_credentials()

        # 从配置文件加载OSS配置
        from robot_studio.database.oss.config import load_config
        config = load_config()
        bucket = config.get('bucket', "mindshake-yitong")
        region = config.get('region', "cn-qingdao")
        endpoint = config.get('endpoint')

        # 创建OSS客户端
        return OssClient(bucket=bucket, region=region, endpoint=endpoint)
    except Exception as e:
        logger.error(f"创建OSS客户端失败: {e}")
        return None

def translate_subject(subject: str) -> str:
    """
    将中文科目名称转义为英文

    Parameters
    ----------
    subject : str
        中文科目名称

    Returns
    -------
    str
        英文科目名称
    """
    # 科目名称映射字典
    subject_map = {
        "国际表达": "english",
        "创新思维": "math",
        "物质理论": "physics",
        "科学探究": "chemistry",
        "人文素养": "Chinese",
    }

    return subject_map.get(subject, "general")

def get_name_initials(name: str) -> str:
    """
    获取教师名称的拼音首字母

    Parameters
    ----------
    name : str
        教师名称

    Returns
    -------
    str
        教师名称的拼音首字母
    """
    # 移除名称中的空格
    name = name.replace(" ", "")

    # 如果名称不包含中文字符，直接返回前两个字符
    if not any('\u4e00' <= char <= '\u9fff' for char in name):
        return name[:2].lower() if len(name) >= 2 else name.lower()

    # 使用pypinyin获取中文拼音首字母
    try:
        # 获取每个汉字的拼音首字母
        py_list = pinyin(name, style=Style.FIRST_LETTER)
        # 将首字母列表合并为字符串
        initials = ''.join([item[0] for item in py_list])
        # 返回小写形式
        return initials.lower()
    except Exception as e:
        logging.error(f"获取拼音首字母失败: {e}")
        # 如果转换失败，返回原名的前两个字符
        return name[:2].lower() if len(name) >= 2 else name.lower()

def generate_resource_url(teacher_name: str, subject: str, oss_client: OssClient) -> str:
    """
    生成教师资源URL

    Parameters
    ----------
    teacher_name : str
        教师名称
    subject : str
        负责科目
    oss_client : OssClient
        OSS客户端实例

    Returns
    -------
    str
        资源URL
    """
    # 如果有多个科目，只取第一个
    if ',' in subject:
        subject = subject.split(',')[0].strip()

    # 获取教师名称拼音首字母
    name_initials = get_name_initials(teacher_name)

    # 转义科目名称为英文
    subject_en = translate_subject(subject)

    # 构建OSS对象键 - 使用英文科目名称和教师名称拼音首字母
    key = f"2025_sq/teacher_intro/{subject_en}_{name_initials}_intro.jpeg"

    try:
        # 检查对象是否存在
        if oss_client.object_exists(key):
            # 生成预签名URL
            # url = oss_client.generate_url(key, expires=3600*24*7)  # 7天有效期
            # return url
            return key
        else:
            logger.warning(f"OSS对象不存在: {key}")
            return ""
    except Exception as e:
        logger.error(f"生成资源URL失败: {e}")
        return ""

def add_teacher_intro(input_file: Path, output_file: Path) -> None:
    """
    添加教师介绍和相关资源

    Parameters
    ----------
    input_file : Path
        输入文件路径
    output_file : Path
        输出文件路径
    """
    try:
        # 读取CSV文件
        logger.info(f"读取教师明细CSV: {input_file}")
        df = pd.read_csv(input_file)

        # 获取OSS客户端
        oss_client = get_oss_client()
        if not oss_client:
            logger.warning("无法获取OSS客户端，将不生成资源URL")

        # 添加教师介绍和相关资源
        logger.info("开始添加教师介绍和相关资源")
        for i, row in df.iterrows():
            teacher_name = row['教师名称']
            subject = row['负责科目']

            # 生成资源URL
            if oss_client:
                resource_url = generate_resource_url(teacher_name, subject, oss_client)
                df.at[i, '相关资源'] = resource_url

            # 教师介绍暂时为空
            df.at[i, '介绍'] = ""

        # 保存更新后的CSV文件
        logger.info(f"保存更新后的CSV文件: {output_file}")
        df.to_csv(output_file, index=False)

        logger.info("教师介绍和相关资源添加完成")
    except Exception as e:
        logger.error(f"添加教师介绍和相关资源失败: {e}")
        raise

def main():
    """
    主函数
    """
    try:
        # 获取资源根目录
        resource_root = find_resource_root()

        # 定义输入和输出目录
        processed_dir = resource_root / "graph_data" / "input" / "processed"

        # 检查目录是否存在
        if not processed_dir.exists():
            raise FileNotFoundError(f"目录不存在: {processed_dir}")

        # 定义输入和输出文件路径
        input_file = processed_dir / TEACHER_DETAIL_CSV
        output_file = input_file  # 覆盖原文件

        # 检查输入文件是否存在
        if not input_file.exists():
            raise FileNotFoundError(f"文件不存在: {input_file}")

        # 添加教师介绍和相关资源
        add_teacher_intro(input_file, output_file)

    except Exception as e:
        logger.error(f"处理教师介绍和相关资源时出错: {e}")
        raise

if __name__ == "__main__":
    main()
