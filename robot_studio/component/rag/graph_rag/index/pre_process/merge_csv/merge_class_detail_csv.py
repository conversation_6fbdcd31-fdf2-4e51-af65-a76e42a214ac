"""
将教学ERP软件中导出的csv整合成宽表csv
input文件目录为graph_data/input
输出的merged文件目录为graph_data/input/processed

输入的csv:

- csv1
    - 文件名称:班级信息.csv
    - 表头字段:班级名称,校区名称,课程名称,收费标准,任课老师,助教,班主任 默认教室,开班日期,当前进度,排课结束日期,计划结业日期,上课时间,最近上课时间,年级,科目,类型,班型,预招人数,当前人数,入班率 计划课次,计费单位,已排课次,已上课次,上课进度（已上）,上课进度（已排）,结业状态,实际结业时间,备注

- csv2
    - 文件名称:课程管理表.csv
    - 表头字段:课程名称,单价,单位,年级,科目,年份,期段,类型,班型,一对一,按期收费,动态课消,已停用,授权

- csv3
    - 文件名称:排课记录.csv
    - 表头字段:班级名称,课程名称,课程所属期段,课程所属年级,课程所属科目,课程所属类型,课程所属班型,所属校区,班主任,任课老师,助教,在职类型,上课教室,章节内容,上课内容,上课时间,上课时长,状态,备注,实到人数,应到人数,计费人数,上课照片

输出的csv:
- 表之间的连接关系:
    csv1 join csv2 on [课程名称]
    csv1 join csv3 on [班级名称]
- 文件名称: merged_班级明细.csv
- 表头字段:
    班级名称（唯一主体）: csv1-班级名称
    班型 : csv1-类型
    开班时间 : csv1-开班日期
    结业时间 : csv1-计划结业日期
    课程名称 : csv1-课程名称
    费用 : csv1-收费标准
    上课时间 : csv1-上课时间
    科目 : 从csv1-科目中提取, 并且去除冗余前缀, 如'03-国际表达'提取成'国际表达'
    期段 : 从csv2-期段中提取, 并且去除冗余前缀, 如'02-春季班'提取成'春季班', 并且前置拼接csv2-年份, 如"2025春季班"
    期数: 从班级名称中提取, 如果班级名称中含有期数的信息这提取,否则为空, 例如五年级六年级国际表达PET预备班-2025暑二期B(12:00-14:30)-张齐, 则期数为二期
    年级 : 从csv1-年级中提取, 并且去除冗余前缀, 如'01-一年级'提取成'一年级'
    排课计划 : 对csv3的全量数据 group by [班级名称] , [章节内容] , [上课内容] , 并通过default_chat_model做summary, 产出如下json格式
        ''' 总览：总计排课10节
            排课时间：
            1. 第1节正方形拆一拆：2025-07-21 12:00~14:30[Monday]
            2. 第2节加减巧算进阶：2025-07-22 12:00~14:30[Tuesday]
            3. 第3节迷宫连线：2025-07-23 12:00~14:30[Wednesday] '''
    总课次 : csv1-已排课次
    预招人数 : csv1-预招人数
    教师 : csv1-任课老师
    校区 : 从csv1-校区名称中提取, 并且去除冗余后缀, 如'浮山后校区-小低学部'提取成'浮山后校区'
    教室 : 从csv1-默认教室中提取,并且去除冗余前缀, 如'浮山后校区-105教室'提取成'105教室'
"""

import logging
import os
import re
from pathlib import Path
from typing import Tuple

import pandas as pd
import datetime

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root, load_config

# 设置日志
logger = logging.getLogger(__name__)

# 文件名常量
CLASS_INFO_CSV = "班级信息.csv"
COURSE_MANAGEMENT_CSV = "课程管理表.csv"
SCHEDULE_RECORD_CSV = "排课记录.csv"
OUTPUT_CSV = "merged_班级明细.csv"

# 星期几的中文映射
WEEKDAY_MAP = {
    "Monday": "星期一",
    "Tuesday": "星期二",
    "Wednesday": "星期三",
    "Thursday": "星期四",
    "Friday": "星期五",
    "Saturday": "星期六",
    "Sunday": "星期日"
}

def read_csv_files(input_dir: Path) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    读取三个CSV文件

    Parameters
    ----------
    input_dir : Path
        输入目录路径

    Returns
    -------
    Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]
        三个CSV文件的DataFrame
    """
    try:
        # 读取班级信息CSV
        class_info_path = input_dir / CLASS_INFO_CSV
        logger.info(f"读取班级信息CSV: {class_info_path}")
        class_info_df = pd.read_csv(class_info_path)

        # 读取课程管理表CSV
        course_management_path = input_dir / COURSE_MANAGEMENT_CSV
        logger.info(f"读取课程管理表CSV: {course_management_path}")
        course_management_df = pd.read_csv(course_management_path)

        # 读取排课记录CSV
        schedule_record_path = input_dir / SCHEDULE_RECORD_CSV
        logger.info(f"读取排课记录CSV: {schedule_record_path}")
        schedule_record_df = pd.read_csv(schedule_record_path)

        return class_info_df, course_management_df, schedule_record_df
    except Exception as e:
        logger.error(f"读取CSV文件失败: {e}")
        raise

def validate_dataframes(class_info_df: pd.DataFrame, course_management_df: pd.DataFrame, schedule_record_df: pd.DataFrame) -> None:
    """
    验证DataFrame是否包含所需的列

    Parameters
    ----------
    class_info_df : pd.DataFrame
        班级信息DataFrame
    course_management_df : pd.DataFrame
        课程管理表DataFrame
    schedule_record_df : pd.DataFrame
        排课记录DataFrame

    Raises
    ------
    ValueError
        如果缺少所需的列
    """
    # 验证班级信息DataFrame
    required_columns_class = ["班级名称", "校区名称", "课程名称", "收费标准", "任课老师", "默认教室",
                             "开班日期", "计划结业日期", "上课时间", "年级", "科目", "类型",
                             "预招人数", "计划课次"]
    missing_columns = [col for col in required_columns_class if col not in class_info_df.columns]
    if missing_columns:
        raise ValueError(f"班级信息CSV缺少以下列: {missing_columns}")

    # 验证课程管理表DataFrame
    required_columns_course = ["课程名称", "期段"]
    missing_columns = [col for col in required_columns_course if col not in course_management_df.columns]
    if missing_columns:
        raise ValueError(f"课程管理表CSV缺少以下列: {missing_columns}")

    # 验证排课记录DataFrame
    required_columns_schedule = ["班级名称", "章节内容", "上课内容", "上课时间"]
    missing_columns = [col for col in required_columns_schedule if col not in schedule_record_df.columns]
    if missing_columns:
        raise ValueError(f"排课记录CSV缺少以下列: {missing_columns}")

def generate_schedule_summary(schedule_df: pd.DataFrame, class_name: str) -> str:
    """
    为指定班级生成排课计划摘要，使用规则处理而非大模型

    Parameters
    ----------
    schedule_df : pd.DataFrame
        排课记录DataFrame
    class_name : str
        班级名称

    Returns
    -------
    str
        排课计划摘要
    """
    # 筛选指定班级的排课记录
    class_schedule = schedule_df[schedule_df["班级名称"] == class_name]

    if class_schedule.empty:
        return "暂无排课计划"

    # 计算总课次
    total_lessons = len(class_schedule)

    # 按时间排序
    if "上课时间" in class_schedule.columns:
        class_schedule = class_schedule.sort_values(by="上课时间")

    # 生成摘要开头
    summary = f"### 总览：总计排课{total_lessons}节\n- 排课时间：\n"

    # 如果没有课程数据，返回简单消息
    if total_lessons == 0:
        return summary + "  - 暂无排课计划"

    # 生成每节课的详细信息
    lesson_details = []
    for i, lesson in enumerate(class_schedule.itertuples()):
        try:
            # 获取原始上课时间字符串
            lesson_time_str = getattr(lesson, "上课时间", "")

            # 检查是否已经包含星期几信息
            if "[" in lesson_time_str and "]" in lesson_time_str:
                # 已经包含星期几信息，直接使用
                formatted_time = lesson_time_str
                # 尝试从方括号中提取星期几
                weekday_match = re.search(r'\[(.*?)\]', lesson_time_str)
                weekday_str = WEEKDAY_MAP.get(weekday_match.group(1) if weekday_match else "", "")
                formatted_time = f"{lesson_time_str.replace(weekday_match.group(), f'[{weekday_str}]')}"
            else:
                # 尝试将上课时间转换为日期格式
                # 首先检查并清理时间格式，移除可能的时间范围和星期几信息
                clean_time_str = re.sub(r'~.*$', '', lesson_time_str).strip()

                # 转换为日期时间
                lesson_time = pd.to_datetime(clean_time_str)

                # 获取星期几
                weekday = lesson_time.weekday()
                weekday_str = WEEKDAY_MAP.get(weekday, "")

                # 格式化日期和时间
                formatted_time = f"{lesson_time.strftime('%Y-%m-%d %H:%M')}~[{weekday_str}]"

            # 获取章节内容，如果不存在则使用空字符串
            chapter_content = getattr(lesson, "上课内容", "")
            chapter_no = getattr(lesson, "章节内容", "")
            if pd.isna(chapter_content):
                chapter_content = ""

            # 生成详细信息
            detail = f"  - {chapter_no}, {chapter_content}, {formatted_time}"
            lesson_details.append(detail)
        except Exception as e:
            # 如果处理失败，使用原始数据
            logger.warning(f"处理课程信息失败: {e}")
            chapter_content = getattr(lesson, "章节内容", "")
            lesson_time = getattr(lesson, "上课时间", "")
            detail = f"{i+1}. 第{i+1}节{chapter_content}：{lesson_time}"
            lesson_details.append(detail)

    # 将所有课程详细信息添加到摘要中
    summary += "\n".join(lesson_details)

    return summary

def remove_prefix(value):
    """
    移除字符串前缀，如 '03-国际表达' 变为 '国际表达'

    Parameters
    ----------
    value : str or any
        需要处理的值

    Returns
    -------
    str
        处理后的字符串
    """
    if pd.notna(value):
        return re.sub(r'^\d+\-', '', str(value))
    return ""

def remove_campus_suffix(campus_name):
    """
    从csv1-校区名称中提取, 并且去除冗余后缀, 如'浮山后校区-小低学部'提取成'浮山后校区'

    Parameters
    ----------
    campus_name : str or any
        校区名称

    Returns
    -------
    str
        处理后的校区名称
    """
    if pd.isna(campus_name) or not isinstance(campus_name, str):
        return ""

    # 移除后缀，如'-小低学部', '-初中部', '-高中部'等
    return re.sub(r'-[^-]+部$', '', campus_name)

def remove_classroom_prefix(classroom):
    """
    从csv1-默认教室中提取, 并且去除冗余前缀, 如'浮山后校区-105教室'提取成'105教室'

    Parameters
    ----------
    classroom : str or any
        教室名称

    Returns
    -------
    str
        处理后的教室名称
    """
    if pd.isna(classroom) or not isinstance(classroom, str):
        return ""

    # 如果包含校区名称作为前缀，则移除该前缀
    # 匹配模式：校区名称-教室名称，例如：浮山后校区-105教室
    match = re.search(r'^.*?-(.*?)$', classroom)
    if match:
        return match.group(1)

    return classroom

def process_period_with_year(row):
    """
    处理期段，去除冗余前缀并前置拼接年份
    如 '02-春季班' 和 年份 '2025' 处理成 '2025春季班'

    Parameters
    ----------
    row : pandas.Series
        包含期段和年份的数据行

    Returns
    -------
    str
        处理后的期段字符串
    """
    period = row["期段"]
    year = row["年份"]

    # 先去除期段的前缀
    if pd.notna(period):
        period = re.sub(r'^\d+\-', '', str(period))
    else:
        period = ""

    # 如果年份存在，则前置拼接，并确保年份是整数格式
    if pd.notna(year) and period:
        # 将年份转换为整数格式，去除小数点
        try:
            year_int = int(float(year))
            return f"{year_int}{period}"
        except (ValueError, TypeError):
            # 如果转换失败，则使用原始年份
            return f"{year}{period}"

    return period

def extract_period(class_name):
    """
    从班级名称中提取期数信息

    Parameters
    ----------
    class_name : str
        班级名称

    Returns
    -------
    str
        提取的期数，如果没有找到则返回空字符串
    """
    # 匹配如 "暑二期"、"春三期" 等带季节的格式
    season_period_match = re.search(r'[春夏秋冬暑][零一二三四五六七八九十]期', class_name)
    if season_period_match:
        # 只提取期数部分，例如从"暑二期"提取"二期"
        return '第' + season_period_match.group(0)[1:]

    # 匹配如 "一期"、"二期"、"三期" 等格式
    period_match = re.search(r'[零一二三四五六七八九十]期', class_name)
    if period_match:
        return '第' + period_match.group(0)

    # 匹配如 "1期"、"2期" 等数字格式
    digit_period_match = re.search(r'\d+期', class_name)
    if digit_period_match:
        return '第' + digit_period_match.group(0)

    # 匹配带年份的期数，如 "2025暑二期"
    year_season_period_match = re.search(r'\d{4}[春夏秋冬暑][零一二三四五六七八九十]期', class_name)
    if year_season_period_match:
        # 只提取期数部分，例如从"2025暑二期"提取"二期"
        return '第' + year_season_period_match.group(0)[-2:]

    # 如果以上模式都不匹配，返回空字符串
    return ""

def filter_expired_classes(df: pd.DataFrame) -> pd.DataFrame:
    """
    剔除结业时间过期的班级

    Parameters
    ----------
    df : pd.DataFrame
        包含班级信息的DataFrame

    Returns
    -------
    pd.DataFrame
        剔除过期班级后的DataFrame
    """
    # 获取当前日期
    current_date = pd.Timestamp.now().normalize()
    logger.info(f"当前日期: {current_date}")

    # 将结业时间转换为日期格式
    df['计划结业日期'] = pd.to_datetime(df['计划结业日期'], errors='coerce')

    # 记录原始班级数量
    original_count = len(df)
    logger.info(f"原始班级数量: {original_count}")

    # 剔除结业时间过期的班级
    filtered_df = df[df['计划结业日期'].isna() | (df['计划结业日期'] >= current_date)]

    # 记录剔除后的班级数量
    filtered_count = len(filtered_df)
    expired_count = original_count - filtered_count
    logger.info(f"剔除结业时间过期的班级数量: {expired_count}")
    logger.info(f"剔除后的班级数量: {filtered_count}")

    return filtered_df

def merge_csv_files(class_info_df: pd.DataFrame, course_management_df: pd.DataFrame,
                   schedule_record_df: pd.DataFrame) -> pd.DataFrame:
    """
    合并三个CSV文件

    Parameters
    ----------
    class_info_df : pd.DataFrame
        班级信息DataFrame
    course_management_df : pd.DataFrame
        课程管理表DataFrame
    schedule_record_df : pd.DataFrame
        排课记录DataFrame

    Returns
    -------
    pd.DataFrame
        合并后的DataFrame
    """
    # 剔除结业时间过期的班级
    logger.info("开始剔除结业时间过期的班级")
    class_info_df = filter_expired_classes(class_info_df)

    # 合并班级信息和课程管理表（基于课程名称）
    merged_df = pd.merge(class_info_df, course_management_df[["课程名称", "期段", "年份"]],
                         on="课程名称", how="left")

    # 创建输出DataFrame
    output_columns = [
        "班级名称", "班型", "开班时间", "结业时间", "课程名称", "费用",
        "上课时间", "科目", "期段", "期数", "年级",
        "排课计划", "总课次", "预招人数",
        "教师", "校区", "教室"
    ]

    output_df = pd.DataFrame(columns=output_columns)

    # 填充输出DataFrame
    output_df["班级名称"] = merged_df["班级名称"]
    output_df["班型"] = merged_df["类型"]
    output_df["开班时间"] = merged_df["开班日期"]
    output_df["结业时间"] = merged_df["计划结业日期"]
    output_df["课程名称"] = merged_df["课程名称"]
    output_df["费用"] = merged_df["收费标准"]
    output_df["上课时间"] = merged_df["上课时间"]
    # 处理科目，去除冗余前缀，如'03-国际表达'提取成'国际表达'
    output_df["科目"] = merged_df["科目"].apply(remove_prefix)

    # 处理期段，去除冗余前缀并前置拼接年份，如'02-春季班'和年份'2025'提取成'2025春季班'
    output_df["期段"] = merged_df.apply(process_period_with_year, axis=1)

    # 处理年级，去除冗余前缀，如'01-一年级'提取成'一年级'
    output_df["年级"] = merged_df["年级"].apply(remove_prefix)
    output_df["总课次"] = merged_df["已排课次"]
    output_df["预招人数"] = merged_df["预招人数"]
    output_df["教师"] = merged_df["任课老师"]
    # 处理校区名称，从csv1-校区名称中提取, 并且去除冗余后缀, 如'浮山后校区-小低学部'提取成'浮山后校区'
    output_df["校区"] = merged_df["校区名称"].apply(remove_campus_suffix)
    # 处理教室名称，从csv1-默认教室中提取, 并且去除冗余前缀, 如'浮山后校区-105教室'提取成'105教室'
    output_df["教室"] = merged_df["默认教室"].apply(remove_classroom_prefix)

    # 为每个班级生成排课计划摘要
    logger.info("开始生成排课计划摘要")
    for i, class_name in enumerate(output_df["班级名称"]):
        logger.info(f"正在为班级 {class_name} 生成排课计划摘要 ({i+1}/{len(output_df)})")
        summary = generate_schedule_summary(schedule_record_df, class_name)
        output_df.at[i, "排课计划"] = summary

    # 从班级名称中提取期数信息
    logger.info("开始提取班级期数信息")
    output_df["期数"] = output_df["班级名称"].apply(extract_period)

    return output_df

def main():
    """
    主函数
    """
    try:
        # 获取资源根目录
        resource_root = find_resource_root()

        # 定义输入和输出目录
        input_dir = resource_root / "graph_data" / "input"
        output_dir = input_dir / "processed"

        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)

        logger.info(f"输入目录: {input_dir}")
        logger.info(f"输出目录: {output_dir}")

        # 读取CSV文件
        class_info_df, course_management_df, schedule_record_df = read_csv_files(input_dir)

        # 验证DataFrame
        validate_dataframes(class_info_df, course_management_df, schedule_record_df)

        # 合并CSV文件
        merged_df = merge_csv_files(class_info_df, course_management_df, schedule_record_df)

        # 保存合并后的CSV文件
        output_path = output_dir / OUTPUT_CSV
        merged_df.to_csv(output_path, index=False)

        logger.info(f"合并后的CSV文件已保存至: {output_path}")

    except Exception as e:
        logger.error(f"处理CSV文件时出错: {e}")
        raise

if __name__ == "__main__":
    main()

