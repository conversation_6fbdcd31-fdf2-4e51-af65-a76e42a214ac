"""将教学ERP软件中导出的csv整合成宽表csv
input文件目录为graph_data/input
输出的merged文件目录为graph_data/input/processed

输入的csv:

- csv1(剔除过期的计划结业日期)
    - 文件名称:班级信息.csv
    - 表头字段:班级名称,校区名称,课程名称,收费标准,任课老师,助教,班主任 默认教室,开班日期,当前进度,排课结束日期,计划结业日期,上课时间,最近上课时间,年级,科目,类型,班型,预招人数,当前人数,入班率 计划课次,计费单位,已排课次,已上课次,上课进度（已上）,上课进度（已排）,结业状态,实际结业时间,备注

输出的csv:
- 表之间的连接关系:无
- 文件名称: merged_校区明细.csv
- 表头字段:
    校区名称（唯一主体）: 对csv1的全量数据 group by [校区名称] , 去除冗余后缀, 如'浮山后校区-小低学部'提取成'浮山后校区'
    校区位置: -
    校区停车说明: -
    相关资源: -
"""

import logging
import os
import re
from pathlib import Path
from typing import Dict, List, Set

import pandas as pd

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root

# 设置日志
logger = logging.getLogger(__name__)

# 文件名常量
CLASS_INFO_CSV = "班级信息.csv"
OUTPUT_CSV = "merged_校区明细.csv"


def read_csv_file(input_dir: Path) -> pd.DataFrame:
    """
    读取班级信息CSV文件

    Parameters
    ----------
    input_dir : Path
        输入目录路径

    Returns
    -------
    pd.DataFrame
        班级信息DataFrame
    """
    try:
        # 读取班级信息CSV
        class_info_path = input_dir / CLASS_INFO_CSV
        logger.info(f"读取班级信息CSV: {class_info_path}")
        # 明确指定第一行为表头
        class_info_df = pd.read_csv(class_info_path, header=0)

        return class_info_df
    except Exception as e:
        logger.error(f"读取CSV文件失败: {e}")
        raise


def validate_dataframe(class_info_df: pd.DataFrame) -> None:
    """
    验证DataFrame是否包含所需的列

    Parameters
    ----------
    class_info_df : pd.DataFrame
        班级信息DataFrame

    Raises
    ------
    ValueError
        如果缺少所需的列
    """
    # 验证班级信息DataFrame
    required_columns = ["校区名称", "计划结业日期"]
    missing_columns = [col for col in required_columns if col not in class_info_df.columns]
    if missing_columns:
        raise ValueError(f"班级信息CSV缺少以下列: {missing_columns}")


def filter_expired_classes(df: pd.DataFrame) -> pd.DataFrame:
    """
    剔除结业时间过期的班级

    Parameters
    ----------
    df : pd.DataFrame
        包含班级信息的DataFrame

    Returns
    -------
    pd.DataFrame
        剔除过期班级后的DataFrame
    """
    # 获取当前日期
    current_date = pd.Timestamp.now().normalize()
    logger.info(f"当前日期: {current_date}")

    # 将结业时间转换为日期格式
    df['计划结业日期'] = pd.to_datetime(df['计划结业日期'], errors='coerce')

    # 记录原始班级数量
    original_count = len(df)
    logger.info(f"原始班级数量: {original_count}")

    # 剔除结业时间过期的班级
    filtered_df = df[df['计划结业日期'].isna() | (df['计划结业日期'] >= current_date)]

    # 记录剔除后的班级数量
    filtered_count = len(filtered_df)
    expired_count = original_count - filtered_count
    logger.info(f"剔除结业时间过期的班级数量: {expired_count}")
    logger.info(f"剔除后的班级数量: {filtered_count}")

    return filtered_df


def remove_campus_suffix(campus_name: str) -> str:
    """
    移除校区名称中的冗余后缀，如'浮山后校区-小低学部'提取成'浮山后校区'

    Parameters
    ----------
    campus_name : str
        校区名称

    Returns
    -------
    str
        处理后的校区名称
    """
    if pd.isna(campus_name) or not isinstance(campus_name, str):
        return ""

    # 移除后缀，如'-小低学部', '-初中部', '-高中部'等
    return re.sub(r'-[^-]+学部$', '', campus_name)


def process_campus_details(class_info_df: pd.DataFrame) -> pd.DataFrame:
    """
    处理校区明细数据

    Parameters
    ----------
    class_info_df : pd.DataFrame
        班级信息DataFrame

    Returns
    -------
    pd.DataFrame
        处理后的校区明细DataFrame
    """
    # 处理校区名称，去除冗余后缀
    class_info_df['处理后校区'] = class_info_df['校区名称'].apply(remove_campus_suffix)

    # 获取所有校区列表（去重）
    campuses = class_info_df['处理后校区'].dropna().unique()
    logger.info(f"共有 {len(campuses)} 个校区")

    # 创建输出DataFrame
    output_columns = ["校区名称", "校区位置", "校区停车说明", "相关资源"]
    output_df = pd.DataFrame(columns=output_columns)

    # 填充校区名称
    output_df["校区名称"] = campuses

    # 其他字段暂时留空
    output_df["校区位置"] = ""
    output_df["校区停车说明"] = ""
    output_df["相关资源"] = ""

    return output_df


def main():
    """
    主函数
    """
    try:
        # 获取资源根目录
        resource_root = find_resource_root()

        # 定义输入和输出目录
        input_dir = resource_root / "graph_data" / "input"
        output_dir = input_dir / "processed"

        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)

        logger.info(f"输入目录: {input_dir}")
        logger.info(f"输出目录: {output_dir}")

        # 读取CSV文件
        class_info_df = read_csv_file(input_dir)

        # 验证DataFrame
        validate_dataframe(class_info_df)

        # 剔除结业时间过期的班级
        logger.info("开始剔除结业时间过期的班级")
        class_info_df = filter_expired_classes(class_info_df)

        # 处理校区明细数据
        logger.info("开始处理校区明细数据")
        campus_details_df = process_campus_details(class_info_df)

        # 保存处理后的CSV文件
        output_path = output_dir / OUTPUT_CSV
        # 明确指定写入表头，并设置编码为UTF-8
        campus_details_df.to_csv(output_path, index=False, header=True)

        logger.info(f"校区明细CSV已保存至: {output_path}")
        logger.info(f"共处理 {len(campus_details_df)} 个校区")

    except Exception as e:
        logger.error(f"处理校区明细数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()
