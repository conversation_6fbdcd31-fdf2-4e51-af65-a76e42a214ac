import pandas as pd
import os
from pathlib import Path

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root

project_root = find_resource_root()

# 修改要处理的文件名
FILE_NAME = "merged_班级明细.csv"

# 打开文件
try:
    input_dir = project_root / "graph_data/input/processed"

    # Check if the directory exists
    if not input_dir.exists():
        raise FileNotFoundError(f"Directory not found: {input_dir}")

except Exception as e:
    print(f"Second approach failed: {e}")
    # Raise exception if both approaches fail
    raise FileNotFoundError(f"Could not find graph_data/input directory using any method") from e


# Input and output paths
input_file = input_dir / FILE_NAME
output_dir = input_dir

print(f"Using input directory: {input_dir}")

# Create output directory if it doesn't exist
os.makedirs(output_dir, exist_ok=True)

# Read the CSV file
df = pd.read_csv(input_file)


# 新增text列, 合并所有列到text中, 通过,连接, 但排除'排课计划'列
df['text'] = df.apply(lambda row: ','.join(str(value) for col, value in row.items() if col != '排课计划'), axis=1)


# 新增title列, 指定列的主键, 后续更新数据以该主键作为最小更新粒度
# df['title'] = df.apply(lambda row: f"{row['班级名称']}-{row['章节内容']}-{row['上课内容']}", axis=1)
# 如果只有一列作为主键,这使用该行执行
df['title'] = df['班级名称']

# Save the processed data
output_file = output_dir / ("processed_" + FILE_NAME)
df.to_csv(output_file, index=False)

print(f"Processed data saved to {output_file}")
print(f"Sample of processed data:")
print(df[['title', 'text']].head(2))
