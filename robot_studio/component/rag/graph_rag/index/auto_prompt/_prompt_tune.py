import logging
import asyncio
import logging
import re
from pathlib import Path
from typing import List, Dict, Any, <PERSON>ple

import pandas as pd
from graphrag.callbacks.noop_workflow_callbacks import NoopWorkflowCallbacks
from graphrag.language_model.manager import ModelManager

from robot_studio.component.rag.graph_rag.index.generator import generate_persona, generate_entity_relationship_examples, \
    create_entity_summarization_prompt, create_extract_graph_prompt
from robot_studio.component.rag.graph_rag.index.utils.load_config import load_config_file, load_config, \
    load_config_path

# Configure logging
logger = logging.getLogger(__name__)

def sample_input_data(resource_root: Path, settings: Dict[str, Any], sample_size: int = 3) -> List[Dict[str, Any]]:
    """Sample rows from the input data specified in settings.yaml.

    Args:
        resource_root: 根目录
        settings: The settings dictionary from settings.yaml
        sample_size: Number of rows to sample

    Returns:
        List of sampled data rows
    """
    try:
        # Get input settings
        input_settings = settings.get("input", {})
        if not input_settings:
            logger.warning("No input settings found in settings.yaml")
            return []

        # Get input file path pattern
        base_dir = input_settings.get("base_dir", "input")
        file_pattern = input_settings.get("file_pattern", r".*\.csv")
        file_type = input_settings.get("file_type", "csv")
        base_path = resource_root / base_dir

        # Check if the input directory exists
        if not base_path.exists():
            logger.warning(f"Input directory {base_path} does not exist")
            return []

        # Find files matching the pattern
        pattern = re.compile(file_pattern)
        matching_files = [f for f in base_path.glob(f"**/*.{file_type}") if pattern.match(f.name)]

        if not matching_files:
            logger.warning(f"No files matching pattern {file_pattern} found in {base_dir}")
            return []

        # Select the first matching file
        input_file = matching_files[0]
        logger.info(f"Using input file: {input_file}")

        # Read the data based on file type
        if file_type.lower() == "csv":
            df = pd.read_csv(input_file, encoding=input_settings.get("file_encoding", "utf-8"))
        elif file_type.lower() == "json":
            df = pd.read_json(input_file, encoding=input_settings.get("file_encoding", "utf-8"))
        else:
            logger.warning(f"Unsupported file type: {file_type}")
            return []

        # Sample rows and convert to text format
        if len(df) <= sample_size:
            sampled_df = df
        else:
            sampled_df = df.sample(sample_size)

        # Convert each row to a text format
        sampled_data = []
        for _, row in sampled_df.iterrows():
            text_data = "\n".join([f"{col}: {val}" for col, val in row.items() if pd.notna(val) and col != 'text'])
            if 'text' in row and pd.notna(row['text']):
                text_data += "\n" + str(row['text'])
            sampled_data.append(text_data)

        return sampled_data
    except Exception as e:
        logger.error(f"Error sampling input data: {e}")
        return []


async def auto_tune_prompts(resource_root: Path = None) -> Tuple[bool, str, List[str]]:
    """Generate prompts by filling in templates with examples from input data.

    Args:
        resource_root: root directory of the project

    Returns:
        Tuple of (success, message, list of generated prompt files)
    """
    try:
        # Load settings from the config file
        settings = load_config_file(resource_root)

        # Get entity types from settings
        entity_types = settings.get("extract_graph", {}).get("entity_types", [])

        # Sample input data
        input_data_samples = sample_input_data(resource_root, settings, 3)
        if not input_data_samples:
            logger.warning("No input data samples available. Using generic examples.")

        # 读取配置对象
        config = load_config(resource_root)
        default_llm_settings = config.get_language_model_config("default_chat_model")
        # 根据抽取的sample大小动态设置模型重试次数
        if default_llm_settings.max_retries < -1:
            default_llm_settings.max_retries = min(
                len(input_data_samples), 10
            )
            logger.warning(f"max_retries not set, using default value: {default_llm_settings.max_retries}")

        llm = ModelManager().register_chat(
            name="prompt_tuning",
            model_type=default_llm_settings.type,
            config=default_llm_settings,
            callbacks=NoopWorkflowCallbacks(), # 空回调
            cache=None,
        )

        # domain默认设置,后续视场景抽出
        domain = "教育培训"
        # prompt人设
        persona = await generate_persona(llm, domain)

        logger.info("开始生成实体和关系的prompt")
        try:
            logger.info(f"使用的实体类型: {entity_types}")
            logger.info(f"输入数据样本数量: {len(input_data_samples)}")
            examples = await generate_entity_relationship_examples(
                llm,
                persona=persona,
                entity_types=entity_types,
                docs=input_data_samples,
            )
            logger.info("实体和关系的prompt生成完成")
        except Exception as e:
            logger.error(f"生成实体和关系的prompt时出错: {e}")
            raise

        logger.info("开始生成实体抽取prompt")
        try:
            extract_graph_prompt = create_extract_graph_prompt(
                entity_types=entity_types,
                docs=input_data_samples,
                examples=examples,
                language="Chinese",
                encoding_model=default_llm_settings.encoding_model,
                max_token_count=1000,
                output_path=(resource_root / config.extract_graph.prompt).parent,
                min_examples_required=3,
            )
            logger.info("实体抽取prompt生成完成")
        except Exception as e:
            logger.error(f"生成实体抽取prompt时出错: {e}")
            raise

        logger.info("开始生成实体总结prompt")
        try:
            entity_summarization_prompt = create_entity_summarization_prompt(
                persona=persona,
                output_path=(resource_root /config.summarize_descriptions.prompt).parent,
            )
            logger.info("实体总结prompt生成完成")
        except Exception as e:
            logger.error(f"生成实体总结prompt时出错: {e}")
            raise

        logger.info(f"完成生成prompt人设, persona=${persona}")
        logger.info(f"完成生成实体和关系的prompt, examples=${examples}")
        logger.info(f"完成生成实体抽取prompt, extract_graph_prompt=${extract_graph_prompt}")
        logger.info(f"完成生成实体总结prompt, entity_summarization_prompt=${entity_summarization_prompt}")

        generate_prompts = [
            extract_graph_prompt,
            entity_summarization_prompt,
        ]
        return True, f"Successfully generated prompts", generate_prompts
    except Exception as e:
        logger.error(f"Error during prompt tuning: {e}")
        return False, f"Failed to auto-tune prompts: {str(e)}", []


async def async_main(resource_root: Path = None):
    """Async main function to run the prompt tuning process based on settings.yaml.

    Args:
        resource_root: 资源根目录，如果提供则使用此目录查找配置文件
    """
    try:
        # Get the current execution directory
        settings_path = load_config_path(resource_root)
        logger.info(f"Using settings path: {settings_path}")

        if not settings_path.exists():
            logger.error(f"Settings file not found at {settings_path}")
            return

        logger.info(f"Starting extract_graph prompt tuning")
        # Call the simplified function
        success, message, generated_prompts = await auto_tune_prompts(
            resource_root=resource_root
        )

        if success:
            logger.info(message)
            logger.info(f"Generated prompts: {generated_prompts}")

            # Print instructions for updating settings
            print("\nPrompt tuning completed successfully!")
            print("\nTo use the generated prompt, update your settings.yaml with:")
            print("\nextract_graph:")
            print("  prompt: \"prompts/extract_graph.txt\"")
        else:
            logger.error(f"Prompt tuning failed: {message}")
            print(f"\nPrompt tuning failed: {message}")
    except Exception as e:
        logger.error(f"Error in main function: {e}")
        print(f"Error: {e}")


def main():
    """Main function that runs the async main function using asyncio."""
    try:
        # Run the async main function using asyncio.run
        asyncio.run(async_main())
    except Exception as e:
        logger.error(f"Error in main function: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
