# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Entity summarization prompt generation module."""

from pathlib import Path

from graphrag.prompt_tune.template.entity_summarization import (
    ENTITY_SUMMARIZATION_PROMPT,
)

_ENTITY_SUMMARIZATION_FILENAME = "summarize_descriptions.txt"

_ENTITY_SUMMARIZATION_PROMPT = """
{persona}
请运用你的专业知识，为以下提供的数据生成一个全面的总结。
给定一个或多个实体，以及一系列描述，这些都与同一实体或实体组相关。
请将所有这些内容合并成一个全面的描述。确保包含从所有描述中收集的信息。
如果提供的描述有矛盾，请解决这些矛盾并提供一个单一的、连贯的总结。
确保使用第三人称写作，并包含实体名称以便我们有完整的上下文。
将最终描述的长度限制在{{max_length}}个词。

尽可能用相关文本中的信息来丰富内容，这一点非常重要。

如果无法回答，或描述为空，只传达文本中提供的信息。

#######
-Data-
Entities: {{entity_name}}
Description List: {{description_list}}
#######
Output:
"""


def create_entity_summarization_prompt(
    persona: str,
    output_path: Path | None = None,
) -> str:
    """
    Create a prompt for entity summarization.

    Parameters
    ----------
    - persona (str): The persona to use for the entity summarization prompt
    - language (str): The language to use for the entity summarization prompt
    - output_path (Path | None): The path to write the prompt to. Default is None.
    """
    prompt = _ENTITY_SUMMARIZATION_PROMPT.format(persona=persona)

    if output_path:
        output_path.mkdir(parents=True, exist_ok=True)

        output_path = output_path / _ENTITY_SUMMARIZATION_FILENAME
        # Write file to output path
        with output_path.open("wb") as file:
            file.write(prompt.encode(encoding="utf-8", errors="strict"))

    return prompt
