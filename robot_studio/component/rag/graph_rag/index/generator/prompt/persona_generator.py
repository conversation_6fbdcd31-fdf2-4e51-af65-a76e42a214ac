async def generate_persona(llm, domain) -> str:
    """生成prompt人设

    Parameters:
        - llm: 模型实例
        - domain: 领域

    Returns:
        - persona: 人设prompt文本
    """
    _GENERATE_PERSONA_PROMPT = """
    你是一个智能助手，帮助人类分析文本文档中的信息。
    给定特定类型的任务和示例文本，请通过生成3到4句话来描述一个能够帮助解决问题的专家。
    请使用以下类似格式：
    你是一位专业的{{role}}，你擅长{{relevant skills}}。你善于帮助人们处理{{specific task}}。

    任务：识别并分析在{domain}领域内特定群体的关系结构和组织形态。
    角色描述："""

    persona_prompt = _GENERATE_PERSONA_PROMPT.format(domain=domain)

    response = await llm.achat(persona_prompt)

    return str(response.output.content)