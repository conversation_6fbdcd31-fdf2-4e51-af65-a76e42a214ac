# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""Entity Extraction prompt generator module."""

from pathlib import Path

import graphrag.config.defaults as defs
from graphrag.index.utils.tokens import num_tokens_from_string

_EXTRACT_GRAPH_FILENAME = "extract_graph.txt"

_GRAPH_EXTRACTION_PROMPT = """
-- 目标 --
给定一份可能与本活动相关的文本文档以及一系列实体类型，从文本中识别出属于这些类型的所有实体，以及已识别实体之间的所有关系。

-- 步骤 --
1. 识别所有实体。对于每个已识别的实体，提取以下信息：
- entity_name: 实体的名称，首字母大写
- entity_type: 以下类型之一：[{entity_types}]
- entity_description: 该实体属性和活动的全面描述
将每个实体格式化为("entity"{{tuple_delimiter}}<entity_name>{{tuple_delimiter}}<entity_type>{{tuple_delimiter}}<entity_description>)

2. 从-步骤1-中识别出的实体里，识别出所有彼此之间明显相关的 (source_entity, target_entity) 对。
对于每一对相关实体，提取以下信息：
- source_entity: 源实体的名称，如-步骤1-中所识别的
- target_entity: 目标实体的名称，如-步骤1-中所识别的
- relationship_description: 解释你认为源实体和目标实体彼此相关的原因
- relationship_strength: 表示源实体和目标实体之间关系强度的数值分数
将每个关系格式化为("relationship"{{tuple_delimiter}}<source_entity>{{tuple_delimiter}}<target_entity>{{tuple_delimiter}}<relationship_description>{{tuple_delimiter}}<relationship_strength>)

3. 返回在-步骤1-和-步骤2-中识别出的所有实体和关系的单个列表。使用 {{record_delimiter}} 作为列表分隔符。

4. 完成后，输出 {{completion_delimiter}}

-Examples-
######################
{examples}

-Real Data-
######################
entity_types: [{entity_types}]
text: {{input_text}}
######################
output:
"""

_EXAMPLE_EXTRACTION_TEMPLATE = """
Example {n}:

entity_types: [{entity_types}]
text:
{input_text}
------------------------
output:
{output}
#############################
"""


def create_extract_graph_prompt(
    entity_types: str | list[str] | None,
    docs: list[str],
    examples: list[str],
    language: str,
    max_token_count: int,
    encoding_model: str = defs.ENCODING_MODEL,
    output_path: Path | None = None,
    min_examples_required: int = 2,
) -> str:
    """
    Create a prompt for entity extraction.

    Parameters
    ----------
    - entity_types (str | list[str]): The entity types to extract
    - docs (list[str]): The list of documents to extract entities from
    - examples (list[str]): The list of examples to use for entity extraction
    - language (str): The language of the inputs and outputs
    - encoding_model (str): The name of the model to use for token counting
    - max_token_count (int): The maximum number of tokens to use for the prompt
    - json_mode (bool): Whether to use JSON mode for the prompt. Default is False
    - output_path (Path | None): The path to write the prompt to. Default is None.
        - min_examples_required (int): The minimum number of examples required. Default is 2.

    Returns
    -------
    - str: The entity extraction prompt
    """
    prompt = (
        _GRAPH_EXTRACTION_PROMPT
    )
    if isinstance(entity_types, list):
        entity_types = ", ".join(map(str, entity_types))

    tokens_left = (
        max_token_count
        - num_tokens_from_string(prompt, encoding_name=encoding_model)
        - num_tokens_from_string(entity_types, encoding_name=encoding_model)
        if entity_types
        else 0
    )

    examples_prompt = ""

    # Iterate over examples, while we have tokens left or examples left
    for i, output in enumerate(examples):
        input = docs[i]
        example_formatted = (
            _EXAMPLE_EXTRACTION_TEMPLATE.format(
                n=i + 1, input_text=input, entity_types=entity_types, output=output
            )
        )

        example_tokens = num_tokens_from_string(
            example_formatted, encoding_name=encoding_model
        )

        # Ensure at least three examples are included
        if i >= min_examples_required and example_tokens > tokens_left:
            break

        examples_prompt += example_formatted
        tokens_left -= example_tokens

    prompt = (
        prompt.format(
            entity_types=entity_types, examples=examples_prompt, language=language
        )
        if entity_types
        else prompt.format(examples=examples_prompt, language=language)
    )

    if output_path:
        output_path.mkdir(parents=True, exist_ok=True)

        output_path = output_path / _EXTRACT_GRAPH_FILENAME
        # Write file to output path
        with output_path.open("wb") as file:
            file.write(prompt.encode(encoding="utf-8", errors="strict"))

    return prompt
