# 1. settings_generator.py
# 2. _prompt_tune.py
# 3. _index.py

"""Settings generator module."""
import logging
import re
from pathlib import Path
from typing import Optional, Dict, Any, List

from robot_studio.component.rag.graph_rag.index.utils.load_config import load_config_path, find_resource_root

# 设置日志
logger = logging.getLogger(__name__)

# 变量文件路径
INDEX_DIR = find_resource_root()
VARIABLES_DIR = INDEX_DIR / "robot_studio" / "component" / "rag" / "graph_rag" / "index" / "generator" / "variables"
META_DATA_VAR = VARIABLES_DIR / ".meta_data_list_var"
CHUNK_GROUP_VAR = VARIABLES_DIR / ".chunk_group_column_list_var"
ENTITY_TYPE_VAR = VARIABLES_DIR / ".entity_type_list_var"
FILE_PATTERN_VAR = VARIABLES_DIR / ".file_pattern_regular_var"
TEMPLATE_VAR = INDEX_DIR / "robot_studio" / "component" / "rag" / "graph_rag" / "index" / "generator" / "settings" / "settings.yaml.template"

def extract_suffix_from_pattern(pattern: str) -> Optional[str]:
    """从文件模式中提取后缀

    Args:
        pattern: 文件模式字符串，例如 ".*\\班级信息.csv"

    Returns:
        提取的后缀，例如 "班级信息"，如果无法提取则返回None
    """
    # 从模式中提取文件名部分
    match = re.search(r'([^\\]+)\.csv', pattern)
    if match:
        return match.group(1)
    return None


def read_variable_content(var_file: Path) -> str:
    """读取变量文件内容

    Args:
        var_file: 变量文件路径

    Returns:
        文件内容字符串
    """
    try:
        with open(var_file, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取变量文件 {var_file} 时出错: {e}")
        return ""


def extract_variable_by_suffix(content: str, suffix: str) -> Optional[str]:
    """根据后缀从变量内容中提取对应的变量值

    Args:
        content: 变量文件内容
        suffix: 文件后缀

    Returns:
        匹配的变量值字符串，如果未找到则返回None
    """
    # 查找形如 XXX_后缀 = [...] 的模式
    pattern = rf'(\w+)_{suffix}\s*=\s*(\[[\s\S]*?\])'
    match = re.search(pattern, content)
    if match:
        return match.group(2)
    return None


def load_file_pattern(config_data: Dict[str, Any], file_pattern: str) -> None:
    """替换配置中的文件模式

    Args:
        config_data: 配置数据字典
        file_pattern: 文件模式字符串
    """
    logger.info(f"替换文件模式: {file_pattern}")
    config_data['input']['file_pattern'] = file_pattern


def load_meta_data(config_data: Dict[str, Any], file_pattern: str) -> None:
    """根据文件模式替换元数据列表

    Args:
        config_data: 配置数据字典
        file_pattern: 文件模式字符串
    """
    suffix = extract_suffix_from_pattern(file_pattern)
    if not suffix:
        logger.warning(f"无法从文件模式 {file_pattern} 中提取后缀")
        return

    meta_data_content = read_variable_content(META_DATA_VAR)
    meta_data_value = extract_variable_by_suffix(meta_data_content, suffix)

    if meta_data_value:
        try:
            meta_data_list = eval(meta_data_value)
            logger.info(f"替换元数据列表: {meta_data_list}")
            config_data['input']['metadata'] = meta_data_list
        except Exception as e:
            logger.error(f"处理元数据值时出错: {e}")
    else:
        logger.warning(f"未找到后缀 {suffix} 对应的元数据列表")


def load_chunk_group_column_list(config_data: Dict[str, Any], file_pattern: str) -> None:
    """根据文件模式替换分组列表

    Args:
        config_data: 配置数据字典
        file_pattern: 文件模式字符串
    """
    suffix = extract_suffix_from_pattern(file_pattern)
    if not suffix:
        logger.warning(f"无法从文件模式 {file_pattern} 中提取后缀")
        return

    chunk_group_content = read_variable_content(CHUNK_GROUP_VAR)
    chunk_group_value = extract_variable_by_suffix(chunk_group_content, suffix)

    if chunk_group_value:
        try:
            chunk_group_list = eval(chunk_group_value)
            logger.info(f"替换分组列表: {chunk_group_list}")
            config_data['chunks']['group_by_columns'] = chunk_group_list
        except Exception as e:
            logger.error(f"处理分组列表值时出错: {e}")
    else:
        logger.warning(f"未找到后缀 {suffix} 对应的分组列表")


def load_entity_type_list(config_data: Dict[str, Any], file_pattern: str) -> None:
    """根据文件模式替换实体类型列表

    Args:
        config_data: 配置数据字典
        file_pattern: 文件模式字符串
    """
    suffix = extract_suffix_from_pattern(file_pattern)
    if not suffix:
        logger.warning(f"无法从文件模式 {file_pattern} 中提取后缀")
        return

    entity_type_content = read_variable_content(ENTITY_TYPE_VAR)
    entity_type_value = extract_variable_by_suffix(entity_type_content, suffix)

    if entity_type_value:
        try:
            entity_type_list = eval(entity_type_value)
            logger.info(f"替换实体类型列表: {entity_type_list}")
            config_data['extract_graph']['entity_types'] = entity_type_list
        except Exception as e:
            logger.error(f"处理实体类型列表值时出错: {e}")
    else:
        logger.warning(f"未找到后缀 {suffix} 对应的实体类型列表")

def read_file_patterns(resource_root: Path = None) -> List[str]:
    """读取文件模式列表

    Args:
        resource_root: 资源根目录，如果提供则使用此目录查找文件模式

    Returns:
        文件模式列表
    """
    try:
        # 如果提供了资源根目录，尝试在其下查找文件模式变量文件
        file_pattern_var_path = FILE_PATTERN_VAR

        # 检查文件模式变量文件是否存在
        if not file_pattern_var_path.exists():
            logger.error(f"文件模式变量文件不存在: {file_pattern_var_path}")
            return []

        # 读取文件模式变量文件
        with open(file_pattern_var_path, 'r', encoding='utf-8') as f:
            patterns = [line.strip() for line in f if line.strip()]
            # 移除引号
            patterns = [pattern.strip('"\'\'') for pattern in patterns]
            logger.info(f"读取到 {len(patterns)} 个文件模式: {patterns}")
            return patterns
    except Exception as e:
        logger.error(f"读取文件模式时出错: {e}")
        return []

def generate_settings_for_file_pattern(resource_root: Path, file_pattern: str) -> None:
    """根据文件模式生成配置文件

    Args:
        resource_root: 根目录
        file_pattern: 文件模式字符串
    """
    try:
        # 获取配置模板路径
        template_path = TEMPLATE_VAR

        # 确定output输出路径
        output_path = load_config_path(resource_root)

        # 检查模板文件是否存在
        if not template_path.exists():
            logger.error(f"配置模板文件不存在: {template_path}")
            raise FileNotFoundError(f"配置模板文件不存在: {template_path}")

        # 读取模板文件
        with open(template_path, 'r', encoding='utf-8') as f:
            config_template = f.read()

        # 提取文件后缀
        suffix = extract_suffix_from_pattern(file_pattern)
        if not suffix:
            logger.warning(f"无法从文件模式 {file_pattern} 中提取后缀，将使用原始文件模式")

        # 读取变量文件内容
        meta_data_content = read_variable_content(META_DATA_VAR)
        chunk_group_content = read_variable_content(CHUNK_GROUP_VAR)
        entity_type_content = read_variable_content(ENTITY_TYPE_VAR)

        # 提取对应的变量值
        meta_data_value = None
        chunk_group_value = None
        entity_type_value = None

        if suffix:
            meta_data_value = extract_variable_by_suffix(meta_data_content, suffix)
            chunk_group_value = extract_variable_by_suffix(chunk_group_content, suffix)
            entity_type_value = extract_variable_by_suffix(entity_type_content, suffix)

        # 替换模板中的占位符
        config_text = config_template

        # 替换文件模式
        config_text = config_text.replace("${FILE_PATTERN_REGULAR}", file_pattern)

        # 替换元数据列表
        if meta_data_value:
            config_text = config_text.replace("${META_DATA_LIST}", meta_data_value)
        else:
            logger.warning(f"未找到后缀 {suffix} 对应的元数据列表，将保留原始占位符")

        # 替换分组列表
        if chunk_group_value:
            config_text = config_text.replace("${CHUNK_GROUP_COLUMN_LIST}", chunk_group_value)
        else:
            logger.warning(f"未找到后缀 {suffix} 对应的分组列表，将保留原始占位符")

        # 替换实体类型列表
        if entity_type_value:
            config_text = config_text.replace("${ENTITY_TYPE_LIST}", entity_type_value)
        else:
            logger.warning(f"未找到后缀 {suffix} 对应的实体类型列表，将保留原始占位符")

        # 写入配置文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(config_text)

        logger.info(f"已生成配置文件: {output_path}")
    except Exception as e:
        logger.error(f"生成配置文件时出错: {e}")
        raise


def generate_settings(resource_root: Path = None) -> None:
    """根据文件模式列表生成配置文件

    如果有多个文件模式，将使用第一个文件模式生成配置文件

    Args:
        resource_root: 资源根目录，如果提供则使用此目录查找文件模式
    """
    try:
        # 读取文件模式列表
        file_patterns = read_file_patterns(resource_root)
        if not file_patterns:
            logger.error("未找到文件模式列表")
            raise ValueError("未找到文件模式列表")

        # 使用第一个文件模式生成配置文件
        file_pattern = file_patterns[0]
        logger.info(f"使用文件模式 {file_pattern} 生成配置文件")
        generate_settings_for_file_pattern(resource_root=resource_root, file_pattern=file_pattern)

        # 如果有多个文件模式，提示用户
        if len(file_patterns) > 1:
            logger.info(f"注意：共有 {len(file_patterns)} 个文件模式，但只使用了第一个生成配置文件")
            logger.info(f"其他文件模式: {file_patterns[1:]}")
    except Exception as e:
        logger.error(f"生成配置文件时出错: {e}")
        raise
