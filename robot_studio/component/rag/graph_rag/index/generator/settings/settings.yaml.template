### This config file contains required core defaults that must be set, along with a handful of common optional settings.
### For a full list of available settings, see https://microsoft.github.io/graphrag/config/yaml/

### LLM settings ###
## There are a number of settings to tune the threading and token limits for LLM calls - check the docs.

models:
  default_chat_model:
    type: openai_chat # or azure_openai_chat
    api_base: ${GRAPHRAG_API_BASE}
    api_key: ${GRAPHRAG_API_KEY}
    model: qwen-plus-latest
    encoding_model: cl100k_base
    encoding_name: cl100k_base
    model_supports_json: true
    concurrent_requests: 50
    async_mode: threaded
    retry_strategy: native
    max_retries: -1
    max_retry_wait: 5
    tokens_per_minute: 0     # 禁用速率限制
    requests_per_minute: 0   # 禁用速率限制
  default_embedding_model:
    type: openai_embedding # or azure_openai_embedding
    api_base: ${GRAPHRAG_API_BASE}
    api_key: ${GRAPHRAG_API_KEY}
    model: text-embedding-v3
    encoding_model: cl100k_base
    encoding_name: cl100k_base
    model_supports_json: true
    concurrent_requests: 10
    async_mode: threaded
    retry_strategy: native
    max_retries: -1 # 动态retry
    tokens_per_minute: 0     # 禁用速率限制
    requests_per_minute: 0   # 禁用速率限制

vector_store:
  default_vector_store:
    type: lancedb
    db_uri: graph_data/output/lancedb
    container_name: default
    overwrite: True

embed_text:
  model_id: default_embedding_model
  vector_store_id: default_vector_store
  batch_size:  10 # qwen embedding v3 support maximum batch size is 10
  target: selected
  names:
    - "text_unit.text"
    - "document.text"
    - "entity.title"
    - "entity.description"
    - "relationship.description"
    # 移除以下嵌入
    # - "community.title"
    # - "community.summary"
    # - "community.full_content"

### Input settings ###

input:
  type: file # or blob
  file_type: csv # [csv, text, json]
  base_dir: "graph_data/input/processed"
  file_encoding: utf-8
  file_pattern: ${FILE_PATTERN_REGULAR}
  metadata: ${META_DATA_LIST}
  text_column: "text"
  title_column: "title"

chunks:
  size: 800        # 减小chunk大小，因为每条课程记录不会太长
  overlap: 50      # 减小重叠，避免重复信息
  group_by_columns: ${CHUNK_GROUP_COLUMN_LIST}  # 按班级分组，保持相关信息在一起
  prepend_metadata: true  # 每个chunk开头添加元数据
  chunk_size_includes_metadata: false # metadata不计入chunk大小限制


### Output settings ###
## If blob storage is specified in the following four sections,
## connection_string and container_name must be provided

cache:
  type: file # [file, blob, cosmosdb]
  base_dir: "graph_data/cache"

reporting:
  type: file # [file, blob, cosmosdb]
  base_dir: "graph_data/logs"

output:
  type: file # [file, blob, cosmosdb]
  base_dir: "graph_data/output"

### Workflow settings ###

workflows: # 不执行create_communities create_community_reports
  - create_base_text_units
  - create_final_documents
  - extract_graph
  - finalize_graph
  # - create_communities
  - create_final_text_units
  # - create_community_reports
  - generate_text_embeddings

extract_graph:
  model_id: default_chat_model
  prompt: "robot_studio/component/rag/graph_rag/prompts/index/extract_graph.txt"
  max_gleanings: 1
  entity_types: ${ENTITY_TYPE_LIST}

summarize_descriptions:
  model_id: default_chat_model
  prompt: "robot_studio/component/rag/graph_rag/prompts/index/summarize_descriptions.txt"
  max_length: 500

extract_graph_nlp:
  enabled: false

extract_claims:
  enabled: false    # 禁用相关的 claims 提取
  model_id: default_chat_model
  prompt: "prompts/extract_claims.txt"
  description: "Any claims or facts that could be relevant to information discovery."
  max_gleanings: 1

community_reports:
  enabled: false

cluster_graph:
  max_cluster_size: 10

embed_graph:
  enabled: false # if true, will generate node2vec embeddings for nodes

umap:
  enabled: false # if true, will generate UMAP embeddings for nodes (embed_graph must also be enabled)

snapshots:
  graphml: true
  embeddings: false

### Query settings ###
## The prompt locations are required here, but each search method has a number of optional knobs that can be tuned.
## See the config docs: https://microsoft.github.io/graphrag/config/yaml/#query

local_search:
  chat_model_id: default_chat_model
  embedding_model_id: default_embedding_model
  prompt: "robot_studio/component/rag/graph_rag/prompts/query/local_search_system_prompt.txt"
  top_k_entities: 5
  top_k_relationships: 3

global_search:
  chat_model_id: default_chat_model
  map_prompt: "robot_studio/component/rag/graph_rag/prompts/query/global_search_map_system_prompt.txt"
  reduce_prompt: "robot_studio/component/rag/graph_rag/prompts/query/global_search_reduce_system_prompt.txt"
  knowledge_prompt: "robot_studio/component/rag/graph_rag/prompts/query/global_search_knowledge_system_prompt.txt"

drift_search:
  chat_model_id: default_chat_model
  embedding_model_id: default_embedding_model
  prompt: "robot_studio/component/rag/graph_rag/prompts/query/drift_search_system_prompt.txt"
  reduce_prompt: "robot_studio/component/rag/graph_rag/prompts/query/drift_search_reduce_prompt.txt"

basic_search:
  chat_model_id: default_chat_model
  embedding_model_id: default_embedding_model
  prompt: "robot_studio/component/rag/graph_rag/prompts/query/basic_search_system_prompt.txt"
