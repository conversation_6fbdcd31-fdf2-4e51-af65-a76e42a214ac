import json
import logging
from pathlib import Path
from typing import Dict, Any

import yaml
from graphrag.config.load_config import create_graphrag_config, _parse_env_variables, _load_dotenv
from graphrag.config.models.graph_rag_config import GraphRagConfig

logger = logging.getLogger(__name__)

def load_config(resource_root: Path) -> GraphRagConfig:
    """加载GraphRAG配置文本并做env环境变量替换

    Args:
        resource_root: 资源根目录，如果提供则使用此目录作为root_dir

    Returns:
        配置数据字典
    """
    # 使用资源根目录作为root_dir
    logger.info(f"load_config使用root_dir: {resource_root}")

    config_file = load_config_path(resource_root)

    if not config_file.exists():
        logger.error(f"Config file not found at: {config_file}")
        raise FileNotFoundError(f"Config file not found at: {config_file}")

    logger.info(f"Found config file at: {config_file}")

    # 读取YAML配置文件
    with open(config_file, 'r') as f:
        config_data = yaml.safe_load(f)

    # 加载环境变量
    _load_dotenv(config_file)

    # 替换环境变量
    models_config_text = json.dumps(config_data["models"])
    models_config_text = _parse_env_variables(models_config_text)
    config_data["models"].update(json.loads(models_config_text))

    # 创建GraphRagConfig实例
    return create_graphrag_config(config_data, root_dir=str(resource_root))

def load_config_file(resource_root: Path) -> Dict[str, Any]:
    """Load settings from a YAML file.

    Args:
        resource_root: Root Path to the settings YAML file

    Returns:
        Dictionary containing the settings
    """

    logger.info(f"load_config_file使用root_dir: {resource_root}")

    config_file = resource_root / "robot_studio" / "component" / "rag" / "graph_rag" / "settings.yaml"
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Error loading settings from {config_file}: {e}")
        raise

def load_config_path(resource_root: Path) -> Path:
    """Load settings from a YAML file.

    Args:
        resource_root: Root Path to the settings YAML file

    Returns:
        Dictionary containing the settings
    """

    logger.info(f"load_config_path使用root_dir: {resource_root}")

    return resource_root / "robot_studio" / "component" / "rag" / "graph_rag" / "settings.yaml"

def replace_config_variable(resource_root: Path, file_pattern: str, env_replace: bool = False) -> None:
    """替换配置中的变量

    Args:
        resource_root: 根目录
        file_pattern: 文件模式字符串
        env_replace: 是否替换环境变量
    """
    # Import the functions here to avoid circular imports
    from robot_studio.component.rag.graph_rag.index.generator.settings.settings_generator import (
        load_file_pattern, load_meta_data, load_chunk_group_column_list, load_entity_type_list
    )
    
    # 读取配置文件
    config_file = load_config_path(resource_root)
    if not config_file.exists():
        logger.error(f"Config file not found at: {config_file}")
        raise FileNotFoundError(f"Config file not found at: {config_file}")

    with open(config_file, 'r') as f:
        config_data = yaml.safe_load(f)

    # 替换文件模式
    load_file_pattern(config_data, file_pattern)
    # 替换元数据列表
    load_meta_data(config_data, file_pattern)
    # 替换分组列表
    load_chunk_group_column_list(config_data, file_pattern)
    # 替换实体类型列表
    load_entity_type_list(config_data, file_pattern)

    if env_replace:
        # 替换环境变量
        models_config_text = json.dumps(config_data["models"])
        models_config_text = _parse_env_variables(models_config_text)
        config_data["models"].update(json.loads(models_config_text))

    # 写回配置文件
    with open(config_file, 'w') as f:
        yaml.dump(config_data, f, allow_unicode=True)

def find_resource_root(start_path: Path = None) -> Path:
    """
    递归向上查找，直到找到上级目录为mindshake且下级目录为robot_studio目录

    Parameters
    ----------
    start_path : Path
        开始查找的路径

    Returns
    -------
    Path
        找到的资源根目录

    Raises
    ------
    FileNotFoundError
        如果找不到指定目录
    """
    if start_path is None:
        start_path = Path(__file__).parent
    current = start_path.resolve()

    # 防止无限循环，设置最大递归深度
    max_depth = 10
    depth = 0

    while depth < max_depth:
        # 检查当前目录是否上级目录为mindshake且下级目录为robot_studio
        if (current.parent / "mindshake").exists() and (current / "robot_studio").exists():
            logger.info(f"找到资源根目录: {current}")
            return current

        # 检查是否已经到达文件系统根目录
        if current == current.parent:
            break

        # 向上一级目录
        current = current.parent
        depth += 1

    # 如果找不到，抛出异常
    raise FileNotFoundError(f"无法找到包含graph_data或robot_studio目录的资源根目录，从{start_path}开始查找")
