import asyncio
import logging
from pathlib import Path

from graphrag.api.index import build_index
from graphrag.config.enums import IndexingMethod
from graphrag.logger.factory import LoggerFactory, LoggerType

from robot_studio.component.rag.graph_rag.index.generator.settings.settings_generator import read_file_patterns
from robot_studio.component.rag.graph_rag.index.utils.load_config import load_config, replace_config_variable

# 设置日志
logger = logging.getLogger(__name__)

async def run_indexing(file_pattern=None, resource_root=None):
    """执行索引操作

    Args:
        file_pattern: 文件模式字符串，如果提供则会先替换配置文件中的文件模式
        resource_root: 资源根目录，如果提供则使用此目录作为root_dir
    """
    # 如果提供了文件模式，先替换配置文件中的文件模式
    if file_pattern:
        replace_config_variable(resource_root=resource_root, file_pattern=file_pattern)

    # 加载配置，并传入资源根目录
    config = load_config(resource_root)

    logger.info(f"Successfully loaded config. Model: {config.get_language_model_config('default_chat_model').model}")


    # 创建进度记录器
    progress_logger = LoggerFactory.create_logger(LoggerType.RICH)

    # 调用build_index函数
    logger.info("Starting indexing process...")
    try:
        results = await build_index(
            config=config,
            method=IndexingMethod.Standard,  # 可以根据需要选择Standard或Fast
            is_update_run=False,  # 这是一个新的索引，而不是更新
            memory_profile=False,  # 不启用内存分析
            callbacks=None,  # 使用默认回调
            progress_logger=progress_logger  # 使用Rich进度记录器
        )

        # 处理结果
        logger.info(f"Indexing completed with {len(results)} workflow results")
        has_extract_graph_error = False

        for i, result in enumerate(results):
            logger.info(f"Workflow {i+1}: {result.workflow}")
            if result.errors and len(result.errors) > 0:
                logger.error(f"Errors: {result.errors}")
                if result.workflow == "extract_graph":
                    has_extract_graph_error = True
            else:
                logger.info("No errors")

        if has_extract_graph_error:
            logger.warning("\n" + "=" * 80)
            logger.warning("extract_graph 工作流失败，但这不会影响基本的查询功能")
            logger.warning("您仍然可以使用 GraphRAG 的查询功能，只是无法使用图形功能")
            logger.warning("=" * 80 + "\n")

        logger.info("索引操作完成")
        return results
    except Exception as e:
        logger.error(f"Error during indexing: {e}")
        raise

async def run_index_for_patterns(resource_root: Path = None):
    """只为第一个文件模式运行索引过程

    Args:
        resource_root: 资源根目录，如果提供则使用此目录查找文件模式
    """
    # 读取文件模式
    patterns = read_file_patterns(resource_root)
    if not patterns:
        logger.error("没有找到文件模式")
        return

    # 只处理第一个文件模式
    pattern = patterns[0]
    logger.info(f"只处理第一个文件模式: {pattern}")
    try:
        # 运行索引过程，并传入资源根目录
        logger.info(f"开始为文件模式 {pattern} 执行索引")
        await run_indexing(file_pattern=pattern, resource_root=resource_root)
        logger.info(f"文件模式 {pattern} 的索引过程完成")

        # 如果有多个文件模式，提示用户
        if len(patterns) > 1:
            logger.info(f"注意：共有 {len(patterns)} 个文件模式，但只处理了第一个")
            logger.info(f"其他文件模式: {patterns[1:]}")
    except Exception as e:
        logger.error(f"执行索引过程时出错: {e}")

    logger.info("索引过程完成")


if __name__ == '__main__':
    # Get the absolute path to the project root
    current_dir = Path(__file__).parent

    # Construct the path to the config directory
    config_dir = current_dir.parent

    # Check if the directory exists
    if not config_dir.exists():
        logger.warning(f"Config directory not found at: {config_dir}")
        # Try to find the config directory relative to the current script
        project_root = current_dir.parent.parent.parent.parent  # Go up to the project root
        config_dir = project_root / "graph_data"
        logger.info(f"Trying alternative path: {config_dir}")

    logger.info(f"Looking for config in: {config_dir}")

    # 执行索引操作
    try:
        # 使用asyncio运行异步函数
        # 运用file_pattern匹配下的索引任务
        asyncio.run(run_index_for_patterns(config_dir))

        # 运行单个索引（不替换变量）
        #asyncio.run(run_indexing(config_dir))
        logger.info("索引操作完成")
    except Exception as e:
        logger.error(f"Error during indexing: {e}")
        # 打印异常堆栈
        import traceback
        logger.error(traceback.format_exc())

        # 提供备选方案
        logger.info("您也可以使用命令行工具执行索引操作：")
        logger.info(f"graphrag index --root {config_dir} --method standard --verbose --logger rich --cache")
