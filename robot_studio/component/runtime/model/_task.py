import copy
from typing import List

from pydantic import BaseModel, Field

from robot_studio.component.chat_chunk.model import ChatChunk


class RuntimeContext(BaseModel):
    """任务运行时上下文"""
    session_id: str | None = Field(default=None, description="The id of the session")
    """会话ID"""

    task_id: str | None = Field(default=None, description="The id of the task")
    """归属的任务ID"""

    parent_span_id: str = Field(default='-', description="The id of the parent span")
    """调用链路节点ID，如果没有调用方默认为-"""

    span_id: str | None = Field(default=None, description="The id of the span")
    """当前节点ID，需要自动生成"""

    runtime_params: dict | None = Field(default=None, description="runtime  params")
    """运行时参数"""

    def deep_copy(self) -> "RuntimeContext":
        """创建当前RuntimeContext的深拷贝"""
        return copy.deepcopy(self)


class ComponentTask(BaseModel):
    """组件顶层任务模型"""

    session_id: str | None = Field(default=None, description="The id of the session")
    """关联会话ID"""

    task_id: str | None = Field(default=None, description="The id of the task")
    """任务ID"""

    status: str | None = Field(default=None, description="The status of the task")
    """任务的执行状态"""

    user_message_id: str | None = Field(default=None, description="The id of the user message")
    """关联的用户消息ID"""

    pending_chunk_id: str | None = Field(default=None, description="The id of the current chunk")
    """任务当前挂起的chunk_id"""

    with_products: bool | None = Field(default=None, description="Have Gen products")
    """任务是否产出了AIGC产物"""

    reply_messages: List[ChatChunk] = Field(default_factory=list, description="任务的消息")
