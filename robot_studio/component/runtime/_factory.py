import importlib
from random import random
from typing import TypeVar, Type, Dict, Any

from autogen_core import is_component_class, ComponentModel
from autogen_core.models import ChatCompletionClient

from robot_studio.component.iteration import IterationManager
from robot_studio.component.manage import ComponentManager
from robot_studio.component.manage.model import ComponentBase, Component, ComponentStatus, ComponentType
from robot_studio.component.template.base import ComponentRunner, BaseComponentAgent, BaseComponentTool

ExpectedType = TypeVar("ExpectedType")


class ComponentFactory:
    """组件工厂"""

    def __init__(self):
        self._component_manager: ComponentManager = ComponentManager()
        self._iteration_manager: IterationManager = IterationManager()
        self._component_types: Dict[str, Type] = {ComponentType.AGENT.value: BaseComponentAgent,
                                                  ComponentType.TOOL.value: BaseComponentTool,
                                                  ComponentType.MODEL.value: ChatCompletionClient}

    def load_instance(self, component_base: ComponentBase | Component) -> Any:

        # 如果组件版本非空，可以查询到指定的组件配置
        component_config = None
        if isinstance(component_base, ComponentBase):
            component_config = self.load_component_config(component_base)
        else:
            component_config = component_base

        # 转换为Autogen的ComponentModel模型
        loaded_model = self.covert_runtime_autogen_config(component_config)

        # 根据provider类，加载具体的类实例
        output = loaded_model.provider.rsplit(".", maxsplit=1)
        if len(output) != 2:
            raise ValueError("Invalid")

        module_path, class_name = output
        module = importlib.import_module(module_path)
        component_class = module.__getattribute__(class_name)

        # 强制要求继承Autogen的组件协议
        if not is_component_class(component_class):
            raise TypeError("Invalid component class")

        # 强制要求组件定义Schema配置类
        if not hasattr(component_class, "component_config_schema"):
            raise AttributeError("component_config_schema not defined")

        # 强制要求定义组件类型
        if not hasattr(component_class, "component_type"):
            raise AttributeError("component_type not defined")

        # 配置模型转换
        schema = component_class.component_config_schema  # type: ignore
        validated_config = schema.model_validate(loaded_model.config)

        # 如果继承了ComponentConfig，重写了_from_config的方法
        instance = component_class._from_config(config=validated_config)  # type: ignore

        # 如果是继承了组件运行器，初始化组件配置信息，Agent、Team、Tool、Workflow都会继承Runner类
        if issubclass(component_class, ComponentRunner):
            instance.component_config = component_config
        return instance

    def load_component_config(self, component_base: ComponentBase) -> Component:
        # 如果组件版本非空，可以查询到指定的组件配置
        component_config: Component | None = None
        if component_base.version:  # 如果已经指定了版本
            component_config = self._component_manager.query_component_by_version(component_base.component_id,
                                                                                  component_base.version)
        else:  # 如果没有指定版本号，走线上包，需要进行切流操作
            component_config = self._router_online_version(component_base.component_id)
        assert component_config, "没有匹配到对应的组件配置！"
        return component_config

    def _process_config_params(self, config_params, config_kv, component_config: Component) -> dict:
        """
        处理配置参数，将模板参数与实际配置值进行匹配和类型转换
        
        Args:
            config_params: 模板组件的配置参数定义
            config_kv: 实际配置的参数值
            component_config: 组件配置对象
            
        Returns:
            dict: 处理后的最终配置参数
        """
        final_config_params = {}

        # 构建config_kv的键值映射
        config_kv_dict = {}
        if config_kv:
            for kv_param in config_kv:
                if kv_param.name and kv_param.value is not None:
                    config_kv_dict[kv_param.name] = kv_param.value
        config_params = config_params or {}
        for param in config_params:
            if not param.name:
                continue

            param_name = param.name
            param_value = None

            # 从config_kv中获取对应的值
            if param_name in config_kv_dict:
                param_value = config_kv_dict[param_name]
            elif param.default_value is not None:
                param_value = param.default_value
            elif param.is_required:
                raise ValueError(f"Required parameter '{param_name}' is missing and has no default value")
            else:
                continue  # 非必填参数且无默认值时跳过

            # 根据参数类型进行转换
            if param.type == "string":
                final_config_params[param_name] = str(param_value)
            elif param.type == "number":
                final_config_params[param_name] = float(param_value) if isinstance(param_value,
                                                                                   str) and '.' in param_value else int(
                    param_value)
            elif param.type == "boolean":
                if isinstance(param_value, bool):
                    final_config_params[param_name] = param_value
                elif isinstance(param_value, str):
                    final_config_params[param_name] = param_value.lower() in ('true', '1', 'yes', 'on')
                else:
                    final_config_params[param_name] = bool(param_value)
            elif param.type == "component":
                # 对于component类型，需要加载ComponentBase类型的实例
                _new_component_base: ComponentBase | None = None
                if isinstance(param_value, ComponentBase):
                    _new_component_base = param_value
                elif isinstance(param_value, dict):
                    _new_component_base = ComponentBase.model_validate(param_value)
                else:
                    raise TypeError(
                        f"Parameter '{param_name}' of type 'component' must be ComponentBase instance or dict")
                # 路由到组件的实际版本
                final_config_params[param_name] = self.load_depend_component_base(_new_component_base.component_id,
                                                                                  component_config.status,
                                                                                  component_config.iterate_id)
            elif param.type == "json_object":
                if isinstance(param_value, dict):
                    final_config_params[param_name] = param_value
                elif isinstance(param_value, str):
                    import json
                    final_config_params[param_name] = json.loads(param_value)
                else:
                    final_config_params[param_name] = param_value
            elif param.type == "string_array":
                if isinstance(param_value, list):
                    final_config_params[param_name] = [str(item) for item in param_value]
                elif isinstance(param_value, str):
                    import json
                    final_config_params[param_name] = json.loads(param_value)
                else:
                    final_config_params[param_name] = [str(param_value)]
            elif param.type == "component_array":
                if isinstance(param_value, list):
                    component_array = []
                    for item in param_value:
                        _new_component_base: ComponentBase | None = None
                        if isinstance(item, ComponentBase):
                            _new_component_base = item
                        elif isinstance(item, dict):
                            _new_component_base = ComponentBase.model_validate(item)
                        else:
                            raise TypeError(
                                f"Items in component_array '{param_name}' must be ComponentBase instances or dicts")
                        # 组件版本路由
                        component_array.append(
                            self.load_depend_component_base(_new_component_base.component_id, component_config.status,
                                                            component_config.iterate_id))
                    final_config_params[param_name] = component_array
                else:
                    raise TypeError(f"Parameter '{param_name}' of type 'component_array' must be a list")
            else:
                # 默认情况下直接赋值
                final_config_params[param_name] = param_value

        return final_config_params

    def covert_runtime_autogen_config(self, component_config: Component) -> ComponentModel:
        """
        把组件模型转为Autogen框架的ComponentModel配置模型
        Args:
            component_config:

        Returns:

        """
        # CASE1: 依赖是具体的代码路径，直接构造ComponentModel
        if not component_config.depend_is_component():
            final_config_params = self._process_config_params(
                component_config.config_params,  # 不依赖模版组件，配置参数和运行时参数一致
                component_config.config_params,
                component_config
            )
            return ComponentModel(
                provider=component_config.depend_provider,
                description=component_config.desc,
                config=final_config_params
            )

        # CASE2:  依赖是具体的模版组件，1. 需要加载正确的模版组件，2. 根据模版的参数配置构建真实的Autogen参数配置
        depend_template_cmp_id = component_config.depend_provider
        template_cmp = self.load_depend_component(depend_template_cmp_id, component_config.status,
                                                  component_config.iterate_id)

        final_config_params = self._process_config_params(
            template_cmp.config_params,
            component_config.config_params,
            component_config
        )

        return ComponentModel(
            provider=template_cmp.depend_provider,
            description=component_config.desc,
            config=final_config_params
        )

    def load_depend_component(self, depend_component_id: str, parent_cmp_status: str,
                              iteration_id) -> Component:

        # 父级组件为已发布态，当前组件按照切留版本进行
        component_config = None
        if parent_cmp_status == ComponentStatus.RELEASE.value:  # 父级组件已发布，需要进行切流
            component_config = self._router_online_version(depend_component_id)

        # 父级组件在Beta态，当前组件优先取同迭代的Beta态组件，否则走线上版本
        if parent_cmp_status == ComponentStatus.BETA.value or parent_cmp_status == ComponentStatus.DEVELOPING.value:
            # 优先取迭代关联的组件
            component_config = self._component_manager.query_component_by_iteration(iteration_id,
                                                                                    depend_component_id)
            # 迭代中没有模版组件变更，取线上正式版本组件
            if not component_config:
                component_config = self._component_manager.query_release_component_latest_version(depend_component_id)

        assert component_config, f"开发中的组件加载不到模版！"
        return component_config

    def load_depend_component_base(self, depend_component_id: str, parent_cmp_status: str,
                                   iteration_id) -> ComponentBase:
        """
        加载依赖的组件的Base信息
        Args:
            depend_component_id:
            parent_cmp_status:
            iteration_id:

        Returns:

        """

        component_config = self.load_depend_component(depend_component_id, parent_cmp_status, iteration_id)
        return ComponentBase(component_id=component_config.component_id, version=component_config.version)

    def _router_online_version(self, component_id: str) -> Component | None:
        """
        线上执行切流
        Args:
            component_id:

        Returns:

        """
        latest_version_config = self._component_manager.query_component_latest_version(component_id)
        assert latest_version_config, f"线上版本不存在！"
        # 如果线上最新版是正式包，使用该版本
        if latest_version_config.status == ComponentStatus.RELEASE.value:
            return latest_version_config

        # 如果最新版是Beta包，按照Beta比例切留，
        if latest_version_config.status == ComponentStatus.BETA.value:
            iteration_manager = IterationManager()
            iteration = self._iteration_manager.query_iteration_detail(latest_version_config.iterate_id)
            random_float = random()
            ## 如果当前Beta版本为初始版本 或者 命中切流逻辑，走Beta包
            if random_float <= iteration.beta_ratio or latest_version_config.version == 1:
                return latest_version_config
            else:
                return self._component_manager.query_component_by_version(
                    latest_version_config.component_id,
                    latest_version_config.version - 1)

        return None
