import re

from robot_studio.database.oss.oss_client import OssClient


def replace_image_placeholders(
        text: str,
        expires: int = 3600 * 24 * 1,  # 默认7天有效期
) -> str:
    """
    替换文本中的图片占位符为实际的OSS预签名URL

    占位符格式: [path/to/image.jpg]

    参数:
        text: 包含图片占位符的文本
        bucket: OSS存储桶名称，如果为None则使用默认配置
        region: OSS区域，默认为cn-qingdao
        endpoint: 自定义endpoint，如果为None则使用默认endpoint
        expires: URL过期时间（秒），默认为一周

    返回:
        替换后的文本
    """
    # 创建OSS客户端
    from robot_studio.database.oss.config import load_config
    config = load_config()
    bucket = config.get('bucket', "mindshake-yitong")
    region = config.get('region', "cn-qingdao")
    endpoint = config.get('endpoint')
    oss_client = OssClient(bucket=bucket, region=region, endpoint=endpoint)

    # 定义正则表达式匹配图片占位符 [path/to/image.jpg]
    pattern = r'\[([^\[\]]+\.(jpeg|jpg|png|gif|webp))\]'

    def replace_match(match):
        image_path = "2025_sq/" + match.group(1)
        try:
            # 生成预签名URL
            url, expiration = oss_client.generate_url(key=image_path, method="GET", expires=expires)
            return f"![]({url})"
        except Exception as e:
            print(f"生成图片URL失败: {image_path}, 错误: {e}")
            # 保留原始占位符
            return match.group(0)

    # 替换所有匹配项
    return re.sub(pattern, replace_match, text)


if __name__ == "__main__":
    # 测试示例
    test_text = """# 暑假班详细介绍

## 上课时间
暑假总共三期课，内容是一样的，任选一期即可。每期总共12次课，一天一节，一节课时长2.5小时，上6休1
排课表图片： [course_schedule/math_g8_summer_schedule.jpeg]

## 费用
200元/节

## 培优体系
- 内容大纲图片: [course_syllabus/math_初二培优班_summer_syllabus.jpeg]

## 竞赛体系
- 内容大纲图片: [course_syllabus/math_初二竞赛班_summer_syllabus.jpeg]
"""

    result = replace_image_placeholders(test_text)
    print(result)
