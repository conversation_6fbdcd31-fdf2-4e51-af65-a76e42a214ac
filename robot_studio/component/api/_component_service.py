import logging
from typing import List

from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import ErrorCode, BaseResult
from robot_studio.component.api.request import ComponentRequest
from robot_studio.component.api.result import ComponentVO
from robot_studio.component.api.result import CreateComponentRes, UpdateComponentRes
from robot_studio.component.manage import ComponentManager
from robot_studio.component.manage.model import ComponentStatus

logger = logging.getLogger(__name__)


class ComponentService:
    """组件配置服务，提供组件的创建和修改功能"""

    def __init__(self):
        self._component_manager = ComponentManager()

    @api_handler
    def create_new_component(self, request: ComponentRequest) -> CreateComponentRes:
        """
        创建新组件
        Args:
            request: 组件创建请求
        Returns:
            CreateComponentRes: 创建结果
        """
        try:
            # 转换为Component模型
            component = request.to_model()
            assert component.iterate_id is not None, "新组件创建的迭代ID为空！"

            # 设置默认状态为开发态，初始化版本为1
            component.status = ComponentStatus.DEVELOPING.value
            component.version = 1

            # 创建组件
            created_component = self._component_manager.create_component_version(component)

            # 转换为视图对象
            component_vo = ComponentVO.from_model(created_component)

            return CreateComponentRes.success_result(data=component_vo)

        except AssertionError as e:
            logger.error(f"创建组件失败: {e}")
            return CreateComponentRes.param_error_result(str(e))
        except Exception as e:
            logger.error(f"创建组件失败: {e}")
            return CreateComponentRes.server_error_result(f"创建组件失败: {str(e)}")

    @api_handler
    def update_component(self, request: ComponentRequest) -> BaseResult:
        """
        更新组件信息
        Args:
            request: 组件更新请求
        Returns:
            BaseResult: 更新结果
        """
        try:
            # 参数验证
            if request.id is None:
                return BaseResult.param_error_result("组件ID不能为空")

            # 转换为Component模型
            component = request.to_model()

            # 更新组件
            success = self._component_manager.update_component(component)

            if not success:
                return BaseResult.not_found_error_result("组件不存在或更新失败")

            return BaseResult.success_result()

        except Exception as e:
            logger.error(f"更新组件失败: {e}")
            return BaseResult.server_error_result(f"更新组件失败: {str(e)}")

    @api_handler
    def batch_delete_component(self, ids: List[int]) -> BaseResult:
        """
        批量删除组件
        Args:
            ids: 要删除的组件ID列表（数据库主键ID）
        Returns:
            BaseResult: 删除结果
        """
        try:
            # 参数验证
            if not ids:
                return BaseResult.param_error_result("组件ID列表不能为空")

            # 批量删除组件
            success = self._component_manager.batch_delete_component(ids)

            if success:
                return BaseResult.success_result()
            else:
                return BaseResult.server_error_result("批量删除组件失败")

        except Exception as e:
            logger.error(f"批量删除组件失败: {e}")
            return BaseResult.server_error_result(f"批量删除组件失败: {str(e)}")
