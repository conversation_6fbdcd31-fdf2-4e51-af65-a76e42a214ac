from typing import List, Optional

from pydantic import BaseModel, Field

from robot_studio.common.base_result import BaseResult
from robot_studio.component.iteration.model import Iteration
from robot_studio.component.manage.model import Component
from ._component_res import ComponentVO


class IterationVO(BaseModel):
    """迭代视图对象"""

    iterate_id: Optional[str] = Field(default=None, description="迭代ID，ITE开头")
    cid: Optional[str] = Field(default=None, description="关联的企业ID")
    gmt_create: Optional[str] = Field(default=None, description="迭代创建时间")
    gmt_modified: Optional[str] = Field(default=None, description="迭代更新时间")
    name: Optional[str] = Field(default=None, description="迭代名称")
    desc: Optional[str] = Field(default=None, description="迭代信息")
    tags: Optional[List[str]] = Field(default=None, description="迭代标签")
    owner: Optional[str] = Field(default=None, description="迭代创建者")
    members: Optional[List[str]] = Field(default=None, description="迭代成员信息")
    release_ip_list: Optional[List[str]] = Field(default=None, description="发布的机器IP")
    status: Optional[str] = Field(default=None, description="迭代状态")
    code_release: Optional[bool] = Field(default=None, description="是否涉及代码发布")
    image_id: Optional[str] = Field(default=None, description="迭代发布的目标镜像ID")
    rollback_image_id: Optional[str] = Field(default=None, description="回滚的目标镜像ID")
    related_components: Optional[List[ComponentVO]] = Field(default=None, description="关联的组件列表")

    @classmethod
    def from_model(cls, iteration: Iteration, related_components: Optional[List[Component]] = None) -> 'IterationVO':
        """
        从 ComponentIteration 模型创建 IterationVO
        Args:
            iteration: ComponentIteration 模型对象
            related_components: 关联的组件列表
            
        Returns:
            IterationVO: 迭代视图对象
        """
        # 转换关联的组件
        component_vos = []
        if related_components:
            component_vos = [ComponentVO.from_model(component) for component in related_components]

        return cls(
            iterate_id=iteration.iterate_id,
            cid=iteration.cid,
            gmt_create=str(iteration.gmt_create) if iteration.gmt_create else None,
            gmt_modified=str(iteration.gmt_modified) if iteration.gmt_modified else None,
            name=iteration.name,
            desc=iteration.desc,
            tags=iteration.tags,
            owner=iteration.owner,
            members=iteration.members,
            release_ip_list=iteration.release_ip_list,
            status=iteration.status,
            code_release=iteration.code_release,
            image_id=iteration.image_id,
            rollback_image_id=iteration.rollback_image_id,
            related_components=component_vos
        )


class QueryIterationRes(BaseResult):
    """查询迭代响应"""
    data: Optional[List[IterationVO]] = Field(default=None, description="迭代列表")


class IterationDetailRes(BaseResult):
    """查询迭代详情响应"""
    data: Optional[IterationVO] = Field(default=None, description="迭代详情")
