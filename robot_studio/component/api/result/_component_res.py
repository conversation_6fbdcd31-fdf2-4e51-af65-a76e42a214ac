from typing import Optional, List

from pydantic import BaseModel, Field

from robot_studio.common.base_result import BaseResult
from robot_studio.component.manage.model import Component, ConfigParam, RuntimeParam


class ComponentVO(BaseModel):
    """组件视图对象"""

    # 组件基础信息
    id: Optional[int] = Field(default=None, description="数据库表主键ID")
    gmt_create: Optional[str] = Field(default=None, description="创建时间")
    gmt_modified: Optional[str] = Field(default=None, description="修改时间")
    component_id: Optional[str] = Field(default=None, description="组件ID，CMP开头")
    code: Optional[str] = Field(default=None, description="组件编码")
    name: Optional[str] = Field(default=None, description="组件名称")
    desc: Optional[str] = Field(default=None, description="组件描述")
    type: Optional[str] = Field(default=None, description="组件类型")
    template_type: Optional[str] = Field(default=None, description="模版类型，组件类型是Template时，需要确认具体的模版类型")
    provider_type: Optional[str] = Field(default=None, description="依赖类型，模版OR类路径")
    scene: Optional[str] = Field(default=None, description="组件应用场景")
    tags: Optional[List[str]] = Field(default=None, description="组件标签")
    cid: Optional[str] = Field(default=None, description="关联的企业ID")

    # 组件版本信息
    version: Optional[int] = Field(default=None, description="组件版本号")
    version_desc: Optional[str] = Field(default=None, description="当前版本描述")
    version_author: Optional[str] = Field(default=None, description="当前版本作者")
    status: Optional[str] = Field(default=None, description="当前版本状态")
    iterate_id: Optional[str] = Field(default=None, description="关联的迭代ID")
    code_change: Optional[bool] = Field(default=False, description="是否涉及代码变更")

    # 组件配置信息
    depend_provider: Optional[str] = Field(default=None, description="依赖模版或者代码类路径")
    config_params: Optional[List[ConfigParam]] = Field(default=None, description="模版配置参数")
    runtime_params: Optional[List[RuntimeParam]] = Field(default=None, description="运行时参数")

    @classmethod
    def from_model(cls, component: Component) -> 'ComponentVO':
        """
        从 Component 模型创建 ComponentVO
        Args:
            component: Component 模型对象
            
        Returns:
            ComponentVO: 组件视图对象
        """
        # 将dict转换为Config
        return cls(
            id=component.id,
            gmt_create=str(component.gmt_create) if component.gmt_create else None,
            gmt_modified=str(component.gmt_modified) if component.gmt_modified else None,
            component_id=component.component_id,
            code=component.code,
            name=component.name,
            desc=component.desc,
            type=component.type.value if component.type else None,
            template_type=component.template_type.value if component.template_type else None,
            provider_type=component.provider_type,
            scene=component.scene,
            tags=component.tags,
            depend_provider=component.depend_provider,
            config_params=component.config_params,
            runtime_params=component.runtime_params,
            version=component.version,
            version_desc=component.version_desc,
            version_author=component.version_author,
            status=component.status,
            iterate_id=component.iterate_id,
            cid=component.cid,
            code_change=component.code_change,
        )


class CreateComponentRes(BaseResult):
    """创建组件响应结果"""
    data: Optional[ComponentVO] = Field(default=None, description="创建的组件信息")


class UpdateComponentRes(BaseResult):
    """更新组件响应结果"""
    data: Optional[ComponentVO] = Field(default=None, description="更新后的组件信息")
