from typing import List, Optional

from pydantic import Field

from robot_studio.common.base_request import BaseRequest
from robot_studio.component.iteration.model import Iteration


class IterationReq(BaseRequest):
    """迭代请求模型"""

    iterate_id: Optional[str] = Field(default=None, description="迭代ID，创建时不需要，更新时需要")
    name: Optional[str] = Field(default=None, description="迭代名称")
    desc: Optional[str] = Field(default=None, description="迭代描述")
    owner: Optional[str] = Field(default=None, description="迭代创建者")
    members: Optional[List[str]] = Field(default=None, description="迭代成员UID列表")
    tags: Optional[List[str]] = Field(default=None, description="迭代标签")
    status: Optional[str] = Field(default=None, description="迭代状态")
    code_release: Optional[bool] = Field(default=False, description="迭代是否涉及代码发布")
    image_id: Optional[str] = Field(default=None, description="迭代代码发布的镜像ID")
    rollback_image_id: Optional[str] = Field(default=None, description="发布回滚的目标镜像ID")

    def to_model(self) -> Iteration:
        """转换为 ComponentIteration 模型"""
        return Iteration(
            iterate_id=self.iterate_id,
            cid=self.cid,
            name=self.name,
            desc=self.desc,
            owner=self.owner,
            members=self.members,
            tags=self.tags,
            status=self.status,
            image_id=self.image_id,
            rollback_image_id=self.rollback_image_id
        )


class RelateComponentReq(BaseRequest):
    """关联组件请求"""
    iterate_id: Optional[str] = Field(..., description="关联的组件ID")
    """迭代ID"""

    related_component_uuids: Optional[List[str]] = Field(..., description="迭代需要关联的组件ID列表")
    """需要升版本的组件列表"""
