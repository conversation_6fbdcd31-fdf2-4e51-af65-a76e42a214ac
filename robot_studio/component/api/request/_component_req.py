from typing import Optional, List

from pydantic import Field

from robot_studio.common.base_request import BaseRequest
from robot_studio.component.manage.model import Component, RuntimeParam, ConfigParam, ComponentType


class ComponentRequest(BaseRequest):
    """组件请求模型，用于创建和修改组件"""

    # 组件的基础信息
    id: Optional[int] = Field(default=None, description="组件ID")
    component_id: Optional[str] = Field(
        default=None, description="组件ID，CMP开头，创建时不需要，更新时需要"
    )
    code: Optional[str] = Field(default=None, description="组件编码，英文缩写")
    name: Optional[str] = Field(default=None, description="组件中文名称")
    type: Optional[str] = Field(default=None, description="组件类型")
    template_type: Optional[str] = Field(default=None, description="模版类型，组件类型是Template时，需要确认具体的模版类型")
    provider_type: Optional[str] = Field(default=None, description="依赖类型，模版OR类路径")
    desc: Optional[str] = Field(default=None, description="组件描述")
    scene: Optional[str] = Field(default=None, description="组件应用场景")
    tags: Optional[List[str]] = Field(default=None, description="组件标签")

    # 组件的配置信息
    depend_provider: Optional[str] = Field(default=None, description="依赖的模版组件或者代码路径")
    config_params: Optional[List[ConfigParam]] = Field(default=None, description="模版配置参数")
    runtime_params: Optional[List[RuntimeParam]] = Field(default=None, description="运行时参数")

    # 组件的版本信息
    version_desc: Optional[str] = Field(default=None, description="版本描述")
    version_author: Optional[str] = Field(default=None, description="版本作者ID")
    iterate_id: Optional[str] = Field(default=None, description="关联的迭代ID")
    code_change: Optional[bool] = Field(default=False, description="是否涉及代码变更")

    def to_model(self) -> Component:
        """转换为 Component 模型"""
        return Component(
            id=self.id,
            component_id=self.component_id,
            code=self.code,
            name=self.name,
            type=ComponentType.from_value(self.type) if self.type else None,
            template_type=ComponentType.from_value(self.template_type) if self.template_type else None,
            provider_type=self.provider_type,
            desc=self.desc,
            scene=self.scene,
            tags=self.tags,
            depend_provider=self.depend_provider,
            config_params=self.config_params,
            runtime_params=self.runtime_params,
            version_desc=self.version_desc,
            version_author=self.version_author,
            iterate_id=self.iterate_id,
            code_change=self.code_change if self.code_change is not None else False,
            cid=self.cid,
        )


class BatchDeleteComponentReq(BaseRequest):
    """批量删除组件请求模型"""

    ids: List[int] = Field(..., description="要删除的组件ID列表（数据库主键ID）")
