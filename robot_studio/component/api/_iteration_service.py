import logging

from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import BaseResult, ErrorCode
from robot_studio.component.api.request import IterationReq, RelateComponentReq
from robot_studio.component.api.result import (
    IterationVO,
    QueryIterationRes,
    IterationDetailRes,
)
from robot_studio.component.iteration import IterationManager
from robot_studio.component.manage import ComponentManager

logger = logging.getLogger(__name__)


class IterationService:
    """迭代服务，提供迭代相关的API接口"""

    def __init__(self):
        self._iteration_manager = IterationManager()
        self._component_manager = ComponentManager()

    @api_handler
    def create_iteration(self, request: IterationReq) -> BaseResult:
        """
        创建迭代
        Args:
            request: 迭代请求参数

        Returns:
            BaseResult: 创建结果
        """
        try:
            # 参数验证
            if not request.name:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="迭代名称不能为空",
                )

            if not request.cid:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="企业ID不能为空",
                )

            # 转换为模型
            iteration_model = request.to_model()

            # 创建迭代
            created_iteration = self._iteration_manager.create_iteration(
                iteration_model
            )

            return BaseResult.success_result(data=created_iteration)

        except Exception as e:
            logger.error(f"创建迭代失败: {e}")
            return BaseResult(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"创建迭代失败: {str(e)}",
            )

    @api_handler
    def update_iteration(self, request: IterationReq) -> BaseResult:
        """
        修改迭代信息
        Args:
            request: 迭代请求参数

        Returns:
            BaseResult: 修改结果
        """
        try:
            # 参数验证
            if not request.iterate_id:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="迭代ID不能为空",
                )

            # 检查迭代是否存在
            existing_iteration = self._iteration_manager.query_iteration_detail(
                request.iterate_id
            )
            if not existing_iteration:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.NOT_FOUND.code,
                    error_msg=f"迭代不存在: {request.iterate_id}",
                )

            # 转换为模型
            iteration_model = request.to_model()

            # 更新迭代
            success = self._iteration_manager.update_iteration(iteration_model)

            if success:
                # 获取更新后的迭代信息
                updated_iteration = self._iteration_manager.query_iteration_detail(
                    request.iterate_id
                )
                return BaseResult.success_result(data=updated_iteration)
            else:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.SERVER_ERROR.code,
                    error_msg="更新迭代失败",
                )

        except Exception as e:
            logger.error(f"更新迭代失败: {e}")
            return BaseResult(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"更新迭代失败: {str(e)}",
            )

    @api_handler
    def delete_iteration(self, iterate_id: str) -> BaseResult:
        """
        删除迭代
        Args:
            iterate_id: 迭代ID
        Returns:
            BaseResult: 删除结果
        """
        try:
            if not iterate_id:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="迭代ID不能为空",
                )
            # 检查迭代是否存在
            existing_iteration = self._iteration_manager.query_iteration_detail(
                iterate_id
            )
            if not existing_iteration:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.NOT_FOUND.code,
                    error_msg=f"迭代不存在: {iterate_id}",
                )
            # 删除迭代
            success = self._iteration_manager.delete_iteration(iterate_id)
            if success:
                return BaseResult.success_result()
            else:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.SERVER_ERROR.code,
                    error_msg="删除迭代失败",
                )
        except Exception as e:
            logger.error(f"删除迭代失败: {e}")
            return BaseResult(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"删除迭代失败: {str(e)}",
            )

    @api_handler
    def query_iteration_detail(self, iterate_id: str) -> IterationDetailRes:
        """
        查询迭代详情
        Args:
            iterate_id: 迭代ID

        Returns:
            IterationDetailRes: 查询结果
        """
        try:
            if not iterate_id:
                return IterationDetailRes(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="迭代ID不能为空",
                )

            # 查询迭代详情
            iteration = self._iteration_manager.query_iteration_detail(iterate_id)
            if not iteration:
                return IterationDetailRes(
                    success=False,
                    error_code=ErrorCode.NOT_FOUND.code,
                    error_msg=f"迭代不存在: {iterate_id}",
                )

            # 查询关联的组件
            if iteration.iterate_id:
                related_components = self._component_manager.query_iteration_components(iteration.iterate_id)
            else:
                related_components = []

            # 使用 from_model 类方法转换
            iteration_vo = IterationVO.from_model(iteration, related_components)

            return IterationDetailRes.success_result(data=iteration_vo)

        except Exception as e:
            logger.error(f"查询迭代详情失败: {e}")
            return IterationDetailRes(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"查询迭代详情失败: {str(e)}",
            )

    @api_handler
    def query_cid_iterations(self, cid: str) -> QueryIterationRes:
        """
        查询企业下的迭代列表
        Args:
            cid: 企业ID

        Returns:
            QueryIterationRes: 查询结果
        """
        try:
            if not cid:
                return QueryIterationRes(
                    success=False,
                    error_code=ErrorCode.PARAM_ERROR.code,
                    error_msg="企业ID不能为空",
                )

            # 查询迭代列表
            iterations = self._iteration_manager.query_iterations_by_cid(cid)

            # 转换为 VO 对象
            iteration_vos = []
            for iteration in iterations:
                # 查询关联的组件
                if iteration.iterate_id:
                    related_components = self._component_manager.query_iteration_components(iteration.iterate_id)
                else:
                    related_components = []

                # 使用 from_model 类方法转换
                iteration_vo = IterationVO.from_model(iteration, related_components)
                iteration_vos.append(iteration_vo)

            return QueryIterationRes(
                success=True,
                error_code=ErrorCode.SUCCESS.code,
                error_msg=ErrorCode.SUCCESS.message,
                data=iteration_vos,
            )

        except Exception as e:
            logger.error(f"查询迭代列表失败: {e}")
            return QueryIterationRes(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"查询迭代列表失败: {str(e)}",
            )

    @api_handler
    def iteration_related_components(self, req: RelateComponentReq) -> BaseResult:
        """
        迭代关联新组件
        Args:
            req:
            
        Returns:
            BaseResult: 关联结果
        """
        try:

            # 检查迭代是否存在
            existing_iteration = self._iteration_manager.query_iteration_detail(req.iterate_id)
            if not existing_iteration:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.NOT_FOUND.code,
                    error_msg=f"迭代不存在: {req.iterate_id}",
                )

            # 调用迭代管理器的批量关联组件方法
            success_component_ids = self._iteration_manager.batch_associate_components(
                req.related_component_uuids, req.iterate_id, req.uid
            )

            if success_component_ids:
                return BaseResult(
                    success=True,
                    error_code=ErrorCode.SUCCESS.code,
                    error_msg=ErrorCode.SUCCESS.message,
                    data={
                        "iterate_id": req.iterate_id,
                        "success_component_ids": success_component_ids,
                        "total_requested": len(req.related_component_uuids),
                        "total_success": len(success_component_ids)
                    }
                )
            else:
                return BaseResult(
                    success=False,
                    error_code=ErrorCode.SERVER_ERROR.code,
                    error_msg="没有成功关联任何组件",
                )

        except Exception as e:
            logger.error(f"迭代关联组件失败: {e}")
            return BaseResult(
                success=False,
                error_code=ErrorCode.SERVER_ERROR.code,
                error_msg=f"迭代关联组件失败: {str(e)}",
            )
