import logging
from typing import List, Optional

from robot_studio.component.iteration.model import Iteration, IterationStatus
from robot_studio.component.manage import ComponentManager
from robot_studio.component.manage.model import Component, ComponentStatus
from robot_studio.database.mysql.repository import IterationRepository

logger = logging.getLogger(__name__)


class IterationManager:
    """迭代管理器，提供迭代的CRUD操作"""

    def __init__(self):
        self._iteration_repository = IterationRepository()
        self._component_manager = ComponentManager()

    def create_iteration(self, iteration_model: Iteration) -> Iteration:
        """
        创建组件迭代
        Args:
            iteration_model: 迭代模型
            
        Returns:
            Iteration: 创建的具体迭代模型
        """
        try:
            # 使用to_do方法转换为IterationDO
            iteration_do = iteration_model.to_do()

            # 创建迭代
            created_iteration = self._iteration_repository.create_iteration(iteration_do)

            # 使用from_do方法转换回ComponentIteration
            return Iteration.from_do(created_iteration)
        except Exception as e:
            logger.error(f"创建迭代失败: {e}")
            raise

    def update_iteration(self, iteration_model: Iteration) -> bool:
        """
        更新迭代
        Args:
            iteration_model: 迭代模型
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not iteration_model.iterate_id:
                logger.error("更新迭代失败：缺少迭代ID")
                return False

            # 准备更新数据
            update_data = {}
            if iteration_model.name is not None:
                update_data['name'] = iteration_model.name
            if iteration_model.desc is not None:
                update_data['desc'] = iteration_model.desc
            if iteration_model.members is not None:
                update_data['members'] = iteration_model.members

            if iteration_model.dev_ip_list is not None:
                update_data['dev_ip_list'] = iteration_model.dev_ip_list
            if iteration_model.release_ip_list is not None:
                update_data['release_ip_list'] = iteration_model.release_ip_list
            if iteration_model.status is not None:
                update_data['status'] = iteration_model.status
            if iteration_model.image_id is not None:
                update_data['image_id'] = iteration_model.image_id
            if iteration_model.rollback_image_id is not None:
                update_data['rollback_image_id'] = iteration_model.rollback_image_id

            # 更新迭代
            updated_iteration = self._iteration_repository.update_iteration(
                iteration_model.iterate_id, update_data
            )

            return updated_iteration is not None
        except Exception as e:
            logger.error(f"更新迭代失败: {e}")
            return False

    def query_iterations_by_cid(self, cid: str) -> List[Iteration]:
        """
        查询迭代，获取企业CID下所有的未被删除的迭代
        Args:
            cid: 企业CID
            
        Returns:
            List[Iteration]: 迭代列表
        """
        try:
            iterations = self._iteration_repository.get_iterations_by_cid(cid)

            # 使用from_do方法转换
            return [Iteration.from_do(iteration) for iteration in iterations]
        except Exception as e:
            logger.error(f"查询迭代失败: {e}")
            return []

    def query_iteration_detail(self, iterate_id: str) -> Optional[Iteration]:
        """
        查询迭代详情
        Args:
            iterate_id: 迭代ID
            
        Returns:
            Optional[Iteration]: 具体的迭代模型，不存在则返回None
        """
        try:
            iteration = self._iteration_repository.get_iteration_by_id(iterate_id)

            if not iteration:
                return None

            # 使用from_do方法转换
            return Iteration.from_do(iteration)
        except Exception as e:
            logger.error(f"查询迭代详情失败: {e}")
            return None

    def delete_iteration(self, iterate_id: str) -> bool:
        """
        删除迭代（软删除）
        Args:
            iterate_id: 迭代ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 1. 获取迭代详情，检查状态
            iteration_detail = self.query_iteration_detail(iterate_id)
            if not iteration_detail:
                logger.error(f"删除迭代失败：迭代不存在，iterate_id={iterate_id}")
                return False

            # 2. 检查迭代状态，只有开发中状态才能删除
            if iteration_detail.status != IterationStatus.DEVELOPING.value:
                logger.error(
                    f"删除迭代失败：迭代状态不是开发中，iterate_id={iterate_id}, status={iteration_detail.status}")
                return False

            # 3. 获取该迭代关联的所有组件ID
            associated_components = self._component_manager.query_iteration_components(iterate_id)
            component_uuids = [comp.id for comp in associated_components if comp.component_id]

            # 4. 批量删除关联的组件
            if component_uuids:
                deleted_component_ids = self._component_manager.batch_delete_component(component_uuids)
                logger.info(
                    f"删除迭代时同步删除组件：iterate_id={iterate_id}, 关联组件UUID={component_uuids}, 成功删除={deleted_component_ids}")

            # 5. 删除迭代本身
            return self._iteration_repository.delete_iteration(iterate_id)
        except Exception as e:
            logger.error(f"删除迭代失败: {e}")
            return False

    def batch_associate_components(self, component_ids: List[str], iterate_id: str, uid: str | None) -> List[str]:
        """
        批量关联组件到当前迭代
        Args:
            uid: 操作人
            component_ids: 新增的组件ID列表
            iterate_id: 迭代ID
            
        Returns:
            List[str]: 成功关联的组件ID列表
        """
        try:
            if not iterate_id:
                logger.error("批量关联组件失败：缺少迭代ID")
                return []

            # 验证迭代是否存在
            iteration = self.query_iteration_detail(iterate_id)
            if not iteration:
                logger.error(f"批量关联组件失败：迭代不存在，iterate_id={iterate_id}")
                return []

            success_component_ids = []

            for component_id in component_ids:
                try:
                    # 获取组件的最新版本
                    latest_component = self._component_manager.query_component_latest_version(component_id)
                    if not latest_component:
                        logger.warning(f"组件最新版本不存在，跳过：component_id={component_id}")
                        continue

                    # 检查组件状态和迭代关联
                    if latest_component.status != ComponentStatus.DEVELOPING.value:
                        # 非正式版本：检查是否已经在其他迭代中，如果在其他迭代中直接跳过
                        if latest_component.iterate_id != iterate_id:
                            logger.warning(
                                f"组件已在其他迭代中，跳过：component_id={component_id}, current_iterate_id={latest_component.iterate_id}")
                            continue
                        else:
                            success_component_ids.append(component_id)

                    elif latest_component.status == ComponentStatus.PRODUCTION.value:
                        # 正式态：创建新版本
                        new_component = Component(
                            code=latest_component.code,
                            name=latest_component.name,
                            type=latest_component.type,
                            desc=latest_component.desc,
                            scene=latest_component.scene,
                            tags=latest_component.tags,
                            cid=latest_component.cid,
                            depend_provider=latest_component.depend_provider,
                            config_params=latest_component.config_params,
                            runtime_params=latest_component.runtime_params,
                            version=(latest_component.component_version or 0) + 1,  # 新版本默认+1
                            version_author=uid or latest_component.version_author,
                            status=ComponentStatus.DEVELOPING.value,  # 新版本默认为开发状态
                            iterate_id=iterate_id,
                        )

                        created_component = self._component_manager.create_component_version(new_component)
                        success_component_ids.append(created_component.component_id)

                    else:
                        logger.warning(
                            f"组件状态不支持，跳过：component_id={component_id}, status={latest_component.status}")
                        continue

                except Exception as e:
                    logger.error(f"处理组件失败：component_id={component_id}, error={e}")
                    continue

            logger.info(f"批量关联组件完成：成功关联 {len(success_component_ids)} 个组件")
            return success_component_ids

        except Exception as e:
            logger.error(f"批量关联组件失败: {e}")
            return []
