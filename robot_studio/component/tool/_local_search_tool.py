import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.graph_rag.query.api import LocalSearch
from robot_studio.component.rag.graph_rag.query.engine import SearchResult


class GraphRagArgs(BaseModel):
    query: str = Field(..., description="具体的查询问题，如三年级的英语课程")


class GraphRagReturn(BaseModel):
    answer: str = Field(..., description="问题的查询结果")


class GraphRagTool(BaseTool[GraphRagArgs, GraphRagReturn]):
    """
    GraphRage工具，根据查询问题
    """

    def __init__(
            self,
            description: str | None = "信息检索工具，能够查询课程明细、教师明细、班级明细相关信息",
    ):
        super().__init__(
            args_type=GraphRagArgs,
            return_type=GraphRagReturn,
            name="search_tool",
            description=description,
        )
        # Use the adapter
        self._search_engine = LocalSearch()

    @cl.step(name='知识图谱检索', type="tool")
    async def run(self, args: GraphRagArgs, cancellation_token: CancellationToken) -> GraphRagReturn:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = None
        async for res in self._search_engine.stream_search(args.query):
            if isinstance(res, SearchResult):
                search_result = res
        assert search_result is not None, "查询结果为空！"
        assert isinstance(search_result.response, str), "Expected response to be a string"
        current_step.output = search_result.model_dump()
        return GraphRagReturn(answer=search_result.response)
