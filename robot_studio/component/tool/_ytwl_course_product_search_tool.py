import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.aliyun_retrival import bailian_engine
from robot_studio.component.utils.replace_oss_url import replace_image_placeholders


class CourseProductSearchArgs(BaseModel):
    query: str = Field(...,
                       description="查询具体课程暑假班和秋季班信息的关键词组合，可以是 科目、年级、班型的组合。"
                                   "如：3年级创新思维、初一国际表达早鸟班，科目、年级最好询问用户确认清楚，保证检索结果准确")


class CourseProductSearchRes(BaseModel):
    answer: str = Field(..., description="具体课程暑假班和秋季班信息的查询结果")


class CourseProductSearchTool(BaseTool[CourseProductSearchArgs, CourseProductSearchRes]):
    """
    壹同未来-各科目课程体系检索工具
    """

    def __init__(
            self,
            top_k: int = 2,
            min_score: float = 0.5,
            description: str | None = "2025年各科目年级的暑假班、秋季班课程信息查询工具，包含暑假班和秋季班的具体班型、上课时间、课程大纲截图等内容总览",
    ):
        super().__init__(
            args_type=CourseProductSearchArgs,
            return_type=CourseProductSearchRes,
            name="course_product_search_tool",
            description=description,
        )
        self._search_engine = bailian_engine
        self._top_k = top_k
        self._min_score = min_score
        self._knowledge_index_id = 'o5blzw28q8'

    @cl.step(name='2025年暑秋课程知识检索', type="tool")
    async def run(self, args: CourseProductSearchArgs, cancellation_token: CancellationToken) -> CourseProductSearchRes:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = await self._search_engine.run_retrival_async(knowledge_index_id=self._knowledge_index_id,
                                                                     top_k=self._top_k, min_score=self._min_score,
                                                                     query=args.query)
        current_step.output = search_result.model_dump()
        if search_result.success:
            re = search_result.json_str_for_llm()
            md_with_image = replace_image_placeholders(re)
            return CourseProductSearchRes(answer=f"查询结果如下(JSON格式):{md_with_image}")
        return CourseProductSearchRes(answer="未查询到相关知识！")


if __name__ == '__main__':
    async def main():
        query = CourseProductSearchArgs(query='初三英语暑期课程')
        tool = CourseProductSearchTool()
        res = await tool.run(query, CancellationToken())
        print(res)


    import asyncio

    asyncio.run(main())
