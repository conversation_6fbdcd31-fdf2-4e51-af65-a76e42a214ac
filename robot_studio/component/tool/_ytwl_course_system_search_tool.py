import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.aliyun_retrival import bailian_engine


class CourseSystemSearchArgs(BaseModel):
    query: str = Field(...,
                       description="查询课程体系介绍的关键词组合，可以是 科目、年级、班型的组合。"
                                   "如：3年级创新思维、初一国际表达早鸟班，科目、年级最好询问用户确认清楚，保证检索结果准确")


class CourseSystemSearchRes(BaseModel):
    answer: str = Field(..., description="科目课程体系知识的查询结果")


class CourseSystemSearchTool(BaseTool[CourseSystemSearchArgs, CourseSystemSearchRes]):
    """
    壹同未来-各科目课程体系检索工具
    """

    def __init__(
            self,
            top_k: int = 5,
            min_score: float = 0.5,
            description: str | None = "具体科目的课程体系检索工具，课程体系包含了班型介绍、教材内容介绍、报名方式等，回答用户关于班型区别、教材相关知识使用该工具",
    ):
        super().__init__(
            args_type=CourseSystemSearchArgs,
            return_type=CourseSystemSearchRes,
            name="course_system_search_tool",
            description=description,
        )
        self._search_engine = bailian_engine
        self._top_k = top_k
        self._min_score = min_score
        self._knowledge_index_id = 'ilvxc4lmel'

    @cl.step(name='课程体系知识检索', type="tool")
    async def run(self, args: CourseSystemSearchArgs, cancellation_token: CancellationToken) -> CourseSystemSearchRes:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = await self._search_engine.run_retrival_async(knowledge_index_id=self._knowledge_index_id,
                                                                     top_k=self._top_k, min_score=self._min_score,
                                                                     query=args.query)
        current_step.output = search_result.model_dump()
        if search_result.success:
            return CourseSystemSearchRes(answer=f"查询结果如下(JSON格式):{search_result.json_str_for_llm()}")
        return CourseSystemSearchRes(answer="未查询到相关知识！")
