import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.aliyun_retrival import bailian_engine
from robot_studio.component.utils.replace_oss_url import replace_image_placeholders


class SchoolSearchArgs(BaseModel):
    query: str = Field(...,
                       description="查询校区信息的关键词组合，必须是校区名或者家长位置的关键词组合。"
                                   "如：浮山后校区、市南区、延安路等等，家长的位置最好询问用户确认清楚，保证检索结果准确。"
                                   "注意：一定不要构建课程、年级等不相关的查询条件！"
                       )


class SchoolSearchRes(BaseModel):
    answer: str = Field(..., description="具体校区的信息查询结果")


class SchoolSearchTool(BaseTool[SchoolSearchArgs, SchoolSearchRes]):
    """
    壹同未来-各科目课程体系检索工具
    """

    def __init__(
            self,
            top_k: int = 5,
            min_score: float = 0.2,
            description: str | None = "校区的信息查询工具，包含了校区位置和指引海报，请一定要传入具体的校区名称或者询问家长得到的位置！",
    ):
        super().__init__(
            args_type=SchoolSearchArgs,
            return_type=SchoolSearchRes,
            name="school_search_tool",
            description=description,
        )
        self._search_engine = bailian_engine
        self._top_k = top_k
        self._min_score = min_score
        self._knowledge_index_id = '248tpx21ga'

    @cl.step(name='校区信息检索', type="tool")
    async def run(self, args: SchoolSearchArgs, cancellation_token: CancellationToken) -> SchoolSearchRes:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = await self._search_engine.run_retrival_async(knowledge_index_id=self._knowledge_index_id,
                                                                     top_k=self._top_k, min_score=self._min_score,
                                                                     query=args.query)
        current_step.output = search_result.model_dump()
        if search_result.success:
            re = search_result.json_str_for_llm()
            md_with_image = replace_image_placeholders(re)
            return SchoolSearchRes(answer=f"查询结果如下(JSON格式):{md_with_image}")
        return SchoolSearchRes(answer="未查询到相关知识！")
