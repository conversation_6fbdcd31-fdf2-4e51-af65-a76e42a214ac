import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.aliyun_retrival import bailian_engine


class ClassInfoSearchArgs(BaseModel):
    query: str = Field(...,
                       description="查询班级信息的关键词组合，可以是 年级、科目、班型、期段（如暑假班）、期数（具体哪一期课程）、教师的组合。"
                                   "如：3年级-创新思维-勤思班-2025年暑假班，注意：年级、科目、班型、暑假/秋季班最好询问用户确认清楚，保证检索结果准确")


class ClassInfoSearchRes(BaseModel):
    answer: str = Field(..., description="具体班级信息的查询结果")


class ClassInfoSearchTool(BaseTool[ClassInfoSearchArgs, ClassInfoSearchRes]):
    """
    壹同未来-各科目课程体系检索工具
    """

    def __init__(
            self,
            top_k: int = 5,
            min_score: float = 0.5,
            description: str | None = "具体课程对应的开课班级明细信息查询工具，包含班级授课教师、上课时间、校区、费用和排课计划等",
    ):
        super().__init__(
            args_type=ClassInfoSearchArgs,
            return_type=ClassInfoSearchRes,
            name="class_info_search_tool",
            description=description,
        )
        self._search_engine = bailian_engine
        self._top_k = top_k
        self._min_score = min_score
        self._knowledge_index_id = 'szbqngb1es'

    @cl.step(name='具体开课班级信息检索', type="tool")
    async def run(self, args: ClassInfoSearchArgs, cancellation_token: CancellationToken) -> ClassInfoSearchRes:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = await self._search_engine.run_retrival_async(knowledge_index_id=self._knowledge_index_id,
                                                                     top_k=self._top_k, min_score=self._min_score,
                                                                     query=args.query)
        current_step.output = search_result.model_dump()
        if search_result.success:
            return ClassInfoSearchRes(answer=f"查询结果如下(JSON格式):{search_result.json_str_for_llm()}")
        return ClassInfoSearchRes(answer="未查询到相关知识！")


if __name__ == '__main__':
    async def main():
        query = ClassInfoSearchArgs(query='3年级-创新思维-勤思班-2025年暑假班')
        tool = ClassInfoSearchTool()
        res = await tool.run(query, CancellationToken())
        print(res)


    import asyncio

    asyncio.run(main())
