import chainlit as cl
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, Field

from robot_studio.component.rag.aliyun_retrival import bailian_engine
from robot_studio.component.utils.replace_oss_url import replace_image_placeholders


class TeacherSearchArgs(BaseModel):
    query: str = Field(...,
                       description="查询教师信息的关键词组合，可以是 老师名字、科目、年级等关键词组合。"
                                   "如：3年级国际表达老师、郭明明")


class TeacherSearchRes(BaseModel):
    answer: str = Field(..., description="具体老师的信息查询结果")


class TeacherSearchTool(BaseTool[TeacherSearchArgs, TeacherSearchRes]):
    """
    壹同未来-各科目课程体系检索工具
    """

    def __init__(
            self,
            top_k: int = 5,
            min_score: float = 0.5,
            description: str | None = "老师的信息查询工具，包含了老师的宣传海报",
    ):
        super().__init__(
            args_type=TeacherSearchArgs,
            return_type=TeacherSearchRes,
            name="teacher_search_tool",
            description=description,
        )
        self._search_engine = bailian_engine
        self._top_k = top_k
        self._min_score = min_score
        self._knowledge_index_id = '0kxetrc2r9'

    @cl.step(name='教师信息检索', type="tool")
    async def run(self, args: TeacherSearchArgs, cancellation_token: CancellationToken) -> TeacherSearchRes:
        current_step = cl.context.current_step
        current_step.input = args.model_dump()
        search_result = await self._search_engine.run_retrival_async(knowledge_index_id=self._knowledge_index_id,
                                                                     top_k=self._top_k, min_score=self._min_score,
                                                                     query=args.query)
        current_step.output = search_result.model_dump()
        if search_result.success:
            re = search_result.json_str_for_llm()
            md_with_image = replace_image_placeholders(re)
            return TeacherSearchRes(answer=f"查询结果如下(JSON格式):{md_with_image}")
        return TeacherSearchRes(answer="未查询到相关知识！")
