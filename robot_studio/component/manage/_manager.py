import logging
from typing import List, Optional

from robot_studio.component.manage.model import Component
from robot_studio.database.mysql.repository import ComponentRepository

logger = logging.getLogger(__name__)


class ComponentManager:
    """组件管理器，提供组件的CURD功能"""

    def __init__(self):
        self._component_repository = ComponentRepository()

    def query_iteration_components(self, iterate_id: str) -> List[Component]:
        """
        根据迭代ID查询关联的组件列表
        Args:
            iterate_id: 迭代ID

        Returns:
            List[Component]: 组件列表
        """
        try:
            components = self._component_repository.get_components_by_iteration_id(
                iterate_id
            )
            return [Component.from_do(component) for component in components]
        except Exception as e:
            logger.error(f"查询迭代组件失败: {e}")
            return []

    def query_component_all_version(self, component_id: str) -> List[Component]:
        """
        根据组件ID查询组件的所有版本
        Args:
            component_id: 组件ID

        Returns:
            List[Component]: 组件的所有版本列表，不存在则返回空列表
        """
        try:
            components = self._component_repository.get_component_by_id(component_id)
            return [Component.from_do(component) for component in components]
        except Exception as e:
            logger.error(f"查询组件失败: {e}")
            return []

    def query_component_latest_version(self, component_id: str) -> Optional[Component]:
        """
        根据组件ID查询最新版本的组件（组件状态为Beta或者Release状态）
        Args:
            component_id: 组件ID

        Returns:
            Optional[Component]: 最新版本的组件信息，不存在则返回None
        """
        try:
            component = self._component_repository.get_latest_online_component_by_id(
                component_id
            )
            if not component:
                return None
            return Component.from_do(component)
        except Exception as e:
            logger.error(f"查询最新组件失败: {e}")
            return None

    def query_component_by_version(
            self, component_id: str, version: int
    ) -> Optional[Component]:
        """
        根据组件ID和版本号查询指定版本的组件
        Args:
            component_id: 组件ID
            version: 版本号

        Returns:
            Optional[Component]: 指定版本的组件信息，不存在则返回None
        """
        try:
            component = self._component_repository.get_component_by_id_and_version(
                component_id, version
            )
            if not component:
                return None
            return Component.from_do(component)
        except Exception as e:
            logger.error(f"查询指定版本组件失败: {e}")
            return None

    def query_component_by_iteration(self, iterate_id: str, component_id: str) -> Optional[Component]:
        """
        根据迭代ID和组件ID查询特定组件
        Args:
            iterate_id: 迭代ID
            component_id: 组件ID

        Returns:
            Optional[Component]: 组件信息，不存在则返回None
        """
        try:
            component = self._component_repository.get_component_by_iteration_and_id(
                iterate_id, component_id
            )
            if not component:
                return None
            return Component.from_do(component)
        except Exception as e:
            logger.error(f"查询迭代组件失败: {e}")
            return None

    def query_release_component_latest_version(self, component_id: str) -> Optional[Component]:
        """
        根据组件ID查询最新的发布版本组件（状态为Release）
        Args:
            component_id: 组件ID

        Returns:
            Optional[Component]: 最新的发布版本组件信息，不存在则返回None
        """
        try:
            component = self._component_repository.get_latest_release_component_by_id(
                component_id
            )
            if not component:
                return None
            return Component.from_do(component)
        except Exception as e:
            logger.error(f"查询最新发布版本组件失败: {e}")
            return None

    def query_component_by_uuid(self, id: int) -> Component | None:
        """
        根据数据库主键ID获取组件信息
        Args:
            id: 数据库表主键ID

        Returns:
            Component | None: 组件信息，不存在则返回None
        """
        try:
            component_do = self._component_repository.get_component_by_db_id(id)
            if not component_do:
                return None

            return Component.from_do(component_do)
        except Exception as e:
            logger.error(f"获取组件信息失败: {e}")
            return None

    def create_component_version(self, component: Component) -> Component:
        """
        创建组件的新版本
        Args:
            component: 组件模型

        Returns:
            Component: 创建后的组件模型
        """
        try:
            # 使用to_do方法转换为ComponentDO
            component_do = component.to_do()

            # 创建组件
            created_component = self._component_repository.create_component(
                component_do
            )

            # 使用from_do方法转换回Component
            return Component.from_do(created_component)
        except Exception as e:
            logger.error(f"创建组件失败: {e}")
            raise

    def update_component(self, update_component: Component) -> bool:
        """
        更新组件信息（排除关键字段）
        Args:
            update_component: 组件模型，包含要更新的字段

        Returns:
            bool: 更新是否成功

        注意：以下字段不允许更新：
        - component_id: 组件ID
        - status: 状态
        - component_version: 版本号
        - iterate_id: 关联的迭代
        - cid: 企业ID
        - provider: 类型
        """
        try:
            component = update_component.to_do()
            if not component.id:
                logger.error("更新组件失败：数据库主键ID为空")
                return False

            # 更新可修改的基础信息
            update_data = {}
            if component.code is not None:
                update_data["code"] = component.code
            if component.name is not None:
                update_data["name"] = component.name
            if component.desc is not None:
                update_data["desc"] = component.desc
            if component.scene is not None:
                update_data["scene"] = component.scene
            if component.tags is not None:
                update_data["tags"] = component.tags

            # 更新可修改的版本信息
            if component.version_desc is not None:
                update_data["version_desc"] = component.version_desc
            if component.version_author is not None:
                update_data["version_author"] = component.version_author
            update_data["code_change"] = component.code_change

            # 更新schema参数配置
            if component.depend_provider is not None:
                update_data["depend_provider"] = component.depend_provider
            if component.template_type is not None:
                update_data["template_type"] = component.template_type
            if component.provider_type is not None:
                update_data["provider_type"] = component.provider_type
            if component.config_params is not None:
                update_data["config_params"] = component.config_params
            if component.runtime_params is not None:
                update_data["runtime_params"] = component.runtime_params

            # 更新组件
            updated_component = self._component_repository.update_component_by_id(
                component.id, update_data
            )
            return updated_component is not None
        except Exception as e:
            logger.error(f"更新组件信息失败: {e}")
            return False

    def delete_component_by_uuid(self, id: int) -> bool:
        """
        根据数据库主键ID删除组件（软删除）
        Args:
            id: 数据库表主键ID

        Returns:
            bool: 删除是否成功
        """
        try:
            return self._component_repository.delete_component_by_id(id)
        except Exception as e:
            logger.error(f"删除组件失败: {e}")
            return False

    def batch_delete_component(self, ids: List[int]) -> bool:
        """
        根据组件Uuid批量删除组件版本
        Args:
            ids: 要删除的组件ID列表（数据库主键ID）

        Returns:
            bool: 批量删除是否成功
        """
        try:
            success_count = 0
            total_count = len(ids)

            for uuid in ids:
                if self.delete_component_by_uuid(uuid):
                    success_count += 1
                else:
                    logger.warning(f"删除组件失败，id={uuid}")

            # 如果所有组件都删除成功，返回True
            if success_count == total_count:
                logger.info(f"批量删除组件成功，共删除{success_count}个组件")
                return True
            else:
                logger.warning(f"批量删除组件部分失败，成功{success_count}/{total_count}个")
                return False

        except Exception as e:
            logger.error(f"批量删除组件异常: {e}")
            return False
