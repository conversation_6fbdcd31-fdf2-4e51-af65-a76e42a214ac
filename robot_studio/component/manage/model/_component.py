from datetime import datetime
from enum import Enum
from typing import Any, Literal

from autogen_core import ComponentModel
from pydantic import BaseModel, Field

from robot_studio.database.mysql.table_schema import ComponentDO
from robot_studio.utils.uuid import DataType


class ComponentStatus(Enum):
    """组件状态枚举"""

    DEVELOPING = "dev"
    """开发态"""

    BETA = "beta"
    """验证态"""

    RELEASE = "release"
    """正式态"""


class ProviderType(Enum):
    """依赖提供者类型枚举"""

    CODE = "class_path"
    """代码类路径"""

    TEMPLATE = "template_component"
    """模版组件"""


class ComponentType(Enum):
    """组件类型枚举"""

    MODEL = "model"
    """模型组件"""

    CONTEXT = "context"
    """上下文组件"""

    AGENT = "agent"
    """代理组件"""

    TOOL = "tool"
    """工具组件"""

    TERMINATION = "termination"
    """终止组件"""

    TEMPLATE = "template"
    """模板组件"""

    PROMPT = "prompt"
    """提示词组件"""

    TEAM = "team"
    """团队组件"""

    WORKFLOW = "workflow"
    """工作流组件"""

    @classmethod
    def from_value(cls, value: str) -> "ComponentType":
        """
        根据 value 值匹配并返回对应的枚举实例
        
        Args:
            value: 枚举的 value 值
            
        Returns:
            ComponentType: 匹配的枚举实例
            
        Raises:
            ValueError: 当找不到匹配的枚举时抛出异常
        """
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f"未找到匹配的 ComponentType: {value}")


class Prompt(BaseModel):
    """prompt末班"""

    content: str | None = None
    """具体prompt模版内容"""


class ConfigParam(BaseModel):
    name: str | None = None
    """参数名，英文"""

    desc: str | None = None
    """参数描述"""

    type: (
            Literal[
                "string",
                "number",
                "boolean",
                "component",
                "json_object",
                "string_array",
                "component_array",
            ]
            | None
    ) = None
    """参数类型"""

    component_type: str | None = None
    """如果type为组件或者组件列表，对应的组件类型，为空代表所有组件适用"""

    is_required: bool | None = None
    """是否必填"""

    default_value: Any | None = None
    """默认值"""

    value: Any | None = None
    """参数值"""


class RuntimeParam(ConfigParam):
    """运行时参数"""
    model_extract: bool | None = None
    """是否需要模型解析"""


class Config(BaseModel):
    """模板配置"""

    params: list[ConfigParam] | None = None
    """参数列表"""


class ComponentBase(BaseModel):
    """组件的基础信息"""
    component_id: str = Field(default=None, description="The id of the component")
    """组件ID"""

    version: int | None = Field(default=None, description="The version of the component")
    """组件版本"""


class Component(BaseModel):
    """继承Autogen的组件化配置"""

    # 基础信息
    id: int | None = None
    """数据库表主键ID"""

    gmt_create: datetime | None = None
    """组件创建时间"""

    gmt_modified: datetime | None = None
    """组件修改时间"""

    component_id: str | None = None
    """组件UUID，CMP开头"""

    code: str | None = None
    """组件编码,英文缩写"""

    name: str | None = None
    """组件中文名称"""

    desc: str | None = None
    """组件详细描述"""

    type: ComponentType | None = None
    """组件类型"""

    template_type: ComponentType | None = None
    """模版类型，组件类型是Template时，需要确认具体的模版类型"""

    provider_type: str | None = None
    """依赖类型，模版OR类路径"""

    scene: str | None = None
    """组件应用场景"""

    tags: list[str] | None = None
    """组件标签"""

    cid: str | None = None
    """组件归属的企业"""

    # 版本信息，初始为1
    version: int | None = None
    """版本号"""

    version_desc: str | None = None
    """当前版本描述"""

    version_author: str | None = None
    """当前版本作者"""

    status: str | None = None
    """当前版本状态"""

    iterate_id: str | None = None
    """版本关联的迭代ID"""

    code_change: bool = False
    """版本是否涉及代码升级，模版组件涉及"""

    # 静态schema配置
    depend_provider: str | None = None
    """依赖，如果可以是模版，也可以是具体代码路径"""

    config_params: list[ConfigParam] | None = None
    """组件模版实例化参数配置"""

    # 运行时参数配置
    runtime_params: list[RuntimeParam] | None = None
    """组件实例运行时结构化入参配置"""

    @classmethod
    def from_do(cls, component_do) -> "Component":
        """
        从数据库对象转换为模型对象
        Args:
            component_do: 数据库组件对象

        Returns:
            Component: 组件模型对象
        """
        return cls(
            id=component_do.id,
            gmt_create=component_do.gmt_create,
            gmt_modified=component_do.gmt_modified,
            component_id=component_do.component_id,
            code=component_do.code,
            name=component_do.name,
            desc=component_do.desc,
            type=ComponentType.from_value(component_do.type),
            template_type=ComponentType.from_value(component_do.template_type) if component_do.template_type else None,
            provider_type=component_do.provider_type,
            scene=component_do.scene,
            tags=component_do.tags,
            cid=component_do.cid,
            version=component_do.version,
            version_desc=component_do.version_desc,
            version_author=component_do.version_author,
            status=component_do.status,
            iterate_id=component_do.iterate_id,
            code_change=component_do.code_change,
            depend_provider=component_do.depend_provider,
            config_params=[ConfigParam.model_validate(item) for item in
                           component_do.config_params] if component_do.config_params else [],
            runtime_params=[RuntimeParam.model_validate(item) for item in
                            component_do.runtime_params] if component_do.runtime_params else [],
        )

    def to_do(self):
        """
        转换为数据库对象
        Returns:
            数据库组件对象
        """
        return ComponentDO(

            # 基础信息属性
            id=self.id,
            component_id=self.component_id,
            code=self.code,
            name=self.name,
            desc=self.desc,
            type=self.type.value if self.type else None,
            template_type=self.template_type.value if self.template_type else None,
            provider_type=self.provider_type,
            scene=self.scene,
            tags=self.tags,
            cid=self.cid,

            # 版本相关属性
            version=self.version,
            version_desc=self.version_desc,
            version_author=self.version_author,
            status=self.status,
            iterate_id=self.iterate_id,
            code_change=self.code_change,

            # schema配置
            depend_provider=self.depend_provider,
            config_params=[item.model_dump() for item in self.config_params] if self.config_params else [],
            runtime_params=[item.model_dump() for item in self.runtime_params] if self.runtime_params else [],
        )

    def to_autogen_component(self) -> ComponentModel:
        ...

    def depend_is_component(self) -> bool:
        return self.depend_provider.lower().startswith(DataType.COMPONENT.value.lower())
