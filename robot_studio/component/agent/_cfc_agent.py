import asyncio
import json
import logging
from typing import Any, AsyncGenerator, <PERSON>waitable, Callable, Dict, List, Mapping, Optional, Sequence, Tuple, Union, cast

from pydantic import BaseModel, Field
from autogen_agentchat.agents import BaseChatAgent
from autogen_core import CancellationToken, Component, ComponentModel, FunctionCall
from autogen_core.model_context import ChatCompletionContext, UnboundedChatCompletionContext
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    CreateResult,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    LLMMessage,
    SystemMessage
)
from autogen_core.tools import BaseTool, FunctionTool
from autogen_agentchat.base import Response
from autogen_agentchat.messages import (
    BaseAgentEvent,
    BaseChatMessage,
    ModelClientStreamingChunkEvent,
    TextMessage,
    ThoughtEvent,
    ToolCallExecutionEvent,
    ToolCallRequestEvent,
    ToolCallSummaryMessage
)
from autogen_agentchat.state import AssistantAgentState


class CostumeFunctionCallAgentConfig(BaseModel):
    """Configuration for CostumeFunctionCallAgent."""
    name: str = Field(default="costume_function_call_agent", description="Name of the component")
    model_client: Any = Field(description="Model client for the component")
    tools: List[Any] | None = Field(default=None, description="List of tools available to the component")
    model_context: Optional[Any] = Field(default=None, description="The event context for storing and retrieving messages")
    description: str = Field(default="A custom component that can use function calls in AutoGen.", description="Description of the component")
    system_message: Optional[str] = Field(default="You are a helpful assistant that can use functions.", description="System message for the component")
    model_client_stream: bool = Field(default=False, description="Whether to stream event client responses")
    reflect_on_tool_use: bool = Field(default=True, description="Whether to reflect on tool use")
    tool_call_summary_format: str = Field(default="{result}", description="Format for tool call summaries")
    metadata: Dict[str, str] = Field(default_factory=dict, description="Metadata for the component")

    model_config = {
        "arbitrary_types_allowed": True
    }


class CostumeFunctionCallAgent(BaseChatAgent, Component[CostumeFunctionCallAgentConfig]):
    """A custom component that can use function calls in AutoGen.

    This component extends BaseChatAgent and implements function calling capabilities.
    It can process function calls from LLM responses and execute them.

    Args:
        name (str): The name of the component.
        model_client (ChatCompletionClient): The event client to use for inference.
        tools (List[BaseTool[Any, Any] | Callable[..., Any] | Callable[..., Awaitable[Any]]] | None, optional): The tools to register with the component.
        model_context (ChatCompletionContext | None, optional): The event context for storing and retrieving messages.
        description (str, optional): The description of the component.
        system_message (str | None, optional): The system message for the event.
        model_client_stream (bool, optional): If True, the event client will be used in streaming mode.
        reflect_on_tool_use (bool, optional): If True, the component will make another event inference using the tool call and result.
        tool_call_summary_format (str, optional): The format string used to create the content for a ToolCallSummaryMessage response.
        metadata (Dict[str, str] | None, optional): Optional metadata for tracking.
    """

    component_config_schema = CostumeFunctionCallAgentConfig
    component_provider_override = "robot_studio.robot.CostumeFunctionCallAgent"

    def __init__(
        self,
        name: str,
        model_client: ChatCompletionClient,
        *,
        tools: List[BaseTool[Any, Any] | Callable[..., Any] | Callable[..., Awaitable[Any]]] | None = None,
        model_context: ChatCompletionContext | None = None,
        description: str = "A custom component that can use function calls in AutoGen.",
        system_message: str | None = "You are a helpful assistant that can use functions.",
        model_client_stream: bool = False,
        reflect_on_tool_use: bool = True,
        tool_call_summary_format: str = "{result}",
        metadata: Dict[str, str] | None = None,
    ):
        """Initialize the CostumeFunctionCallAgent."""
        super().__init__(name=name, description=description)
        self._metadata = metadata or {}

        self._model_client = model_client
        self._model_client_stream = model_client_stream

        self._system_messages: List[SystemMessage] = []
        if system_message is not None:
            self._system_messages = [SystemMessage(content=system_message)]

        self._tools: List[BaseTool[Any, Any]] = []
        if tools is not None:
            if model_client.model_info["function_calling"] is False:
                raise ValueError("The event does not support function calling.")
            for tool in tools:
                if isinstance(tool, BaseTool):
                    self._tools.append(tool)
                elif callable(tool):
                    if hasattr(tool, "__doc__") and tool.__doc__ is not None:
                        tool_description = tool.__doc__
                    else:
                        tool_description = ""
                    self._tools.append(FunctionTool(tool, description=tool_description))
                else:
                    raise ValueError(f"Unsupported tool type: {type(tool)}")

        # Check if tool names are unique
        tool_names = [tool.name for tool in self._tools]
        if len(tool_names) != len(set(tool_names)):
            raise ValueError(f"Tool names must be unique: {tool_names}")

        if model_context is not None:
            self._model_context = model_context
        else:
            self._model_context = UnboundedChatCompletionContext()

        self._reflect_on_tool_use = reflect_on_tool_use
        self._tool_call_summary_format = tool_call_summary_format

    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        """Return the types of messages this component can produce."""
        message_types: List[type[BaseChatMessage]] = []
        if self._tools:
            message_types.append(ToolCallSummaryMessage)
        message_types.append(TextMessage)
        return tuple(message_types)

    @property
    def model_context(self) -> ChatCompletionContext:
        """Return the event context used by the component."""
        return self._model_context

    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:
        """Process the incoming messages and return a response.

        Args:
            messages: The messages to process
            cancellation_token: Token for cancelling the operation

        Returns:
            Response: The component's response
        """
        async for message in self.on_messages_stream(messages, cancellation_token):
            if isinstance(message, Response):
                return message
        raise AssertionError("The stream should have returned the final result.")

    async def on_messages_stream(
        self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        """Process the incoming messages and yield events/responses as they happen.

        Args:
            messages: The messages to process
            cancellation_token: Token for cancelling the operation

        Yields:
            Events and responses during processing
        """
        # Gather all relevant state
        agent_name = self.name
        model_context = self._model_context
        system_messages = self._system_messages
        tools = self._tools
        model_client = self._model_client
        model_client_stream = self._model_client_stream
        reflect_on_tool_use = self._reflect_on_tool_use
        tool_call_summary_format = self._tool_call_summary_format

        # STEP 1: Add new messages to the event context
        for msg in messages:
            await model_context.add_message(msg.to_model_message())

        # STEP 2: Run the first inference
        model_result = None
        async for inference_output in self._call_llm(
            model_client=model_client,
            model_client_stream=model_client_stream,
            system_messages=system_messages,
            model_context=model_context,
            tools=tools,
            agent_name=agent_name,
            cancellation_token=cancellation_token,
        ):
            if isinstance(inference_output, CreateResult):
                model_result = inference_output
            else:
                # Streaming chunk event
                yield inference_output

        assert model_result is not None, "No event result was produced."

        # If the event produced a thought, yield it as an event
        inner_messages: List[BaseAgentEvent | BaseChatMessage] = []
        if model_result.thought:
            thought_event = ThoughtEvent(content=model_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # Add the assistant message to the event context
        await model_context.add_message(
            AssistantMessage(
                content=model_result.content,
                source=agent_name,
                thought=getattr(model_result, "thought", None),
            )
        )

        # STEP 3: Process the event output
        async for output_event in self._process_model_result(
            model_result=model_result,
            inner_messages=inner_messages,
            cancellation_token=cancellation_token,
            agent_name=agent_name,
            system_messages=system_messages,
            model_context=model_context,
            tools=tools,
            model_client=model_client,
            model_client_stream=model_client_stream,
            reflect_on_tool_use=reflect_on_tool_use,
            tool_call_summary_format=tool_call_summary_format,
        ):
            yield output_event

    @classmethod
    async def _call_llm(
        cls,
        model_client: ChatCompletionClient,
        model_client_stream: bool,
        system_messages: List[SystemMessage],
        model_context: ChatCompletionContext,
        tools: List[BaseTool[Any, Any]],
        agent_name: str,
        cancellation_token: CancellationToken,
    ) -> AsyncGenerator[Union[CreateResult, ModelClientStreamingChunkEvent], None]:
        """Perform a event inference and yield either streaming chunk events or the final CreateResult."""
        all_messages = await model_context.get_messages()
        llm_messages = system_messages + all_messages

        if model_client_stream:
            model_result: Optional[CreateResult] = None
            async for chunk in model_client.create_stream(
                llm_messages,
                tools=tools,
                cancellation_token=cancellation_token,
            ):
                if isinstance(chunk, CreateResult):
                    model_result = chunk
                elif isinstance(chunk, str):
                    yield ModelClientStreamingChunkEvent(content=chunk, source=agent_name)
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")
            if model_result is None:
                raise RuntimeError("No final event result in streaming mode.")
            yield model_result
        else:
            model_result = await model_client.create(
                llm_messages,
                tools=tools,
                cancellation_token=cancellation_token,
            )
            yield model_result

    @classmethod
    async def _process_model_result(
        cls,
        model_result: CreateResult,
        inner_messages: List[BaseAgentEvent | BaseChatMessage],
        cancellation_token: CancellationToken,
        agent_name: str,
        system_messages: List[SystemMessage],
        model_context: ChatCompletionContext,
        tools: List[BaseTool[Any, Any]],
        model_client: ChatCompletionClient,
        model_client_stream: bool,
        reflect_on_tool_use: bool,
        tool_call_summary_format: str,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        """Handle final or partial responses from model_result, including tool calls and reflection if needed."""

        # If direct text response (string)
        if isinstance(model_result.content, str):
            yield Response(
                chat_message=TextMessage(
                    content=model_result.content,
                    source=agent_name,
                    models_usage=model_result.usage,
                ),
                inner_messages=inner_messages,
            )
            return

        # Otherwise, we have function calls
        assert isinstance(model_result.content, list) and all(
            isinstance(item, FunctionCall) for item in model_result.content
        )

        # STEP 4A: Yield ToolCallRequestEvent
        tool_call_msg = ToolCallRequestEvent(
            content=model_result.content,
            source=agent_name,
            models_usage=model_result.usage,
        )
        inner_messages.append(tool_call_msg)
        yield tool_call_msg

        # STEP 4B: Execute tool calls
        executed_calls_and_results = await asyncio.gather(
            *[
                cls._execute_tool_call(
                    tool_call=call,
                    tools=tools,
                    agent_name=agent_name,
                    cancellation_token=cancellation_token,
                )
                for call in model_result.content
            ]
        )
        exec_results = [result for _, result in executed_calls_and_results]

        # Yield ToolCallExecutionEvent
        tool_call_result_msg = ToolCallExecutionEvent(
            content=exec_results,
            source=agent_name,
        )
        await model_context.add_message(FunctionExecutionResultMessage(content=exec_results))
        inner_messages.append(tool_call_result_msg)
        yield tool_call_result_msg

        # STEP 4C: Reflect or summarize tool results
        if reflect_on_tool_use:
            async for reflection_response in cls._reflect_on_tool_use_flow(
                system_messages=system_messages,
                model_client=model_client,
                model_client_stream=model_client_stream,
                model_context=model_context,
                agent_name=agent_name,
                inner_messages=inner_messages,
            ):
                yield reflection_response
        else:
            yield cls._summarize_tool_use(
                executed_calls_and_results=executed_calls_and_results,
                inner_messages=inner_messages,
                tool_call_summary_format=tool_call_summary_format,
                agent_name=agent_name,
            )

    @classmethod
    async def _reflect_on_tool_use_flow(
        cls,
        system_messages: List[SystemMessage],
        model_client: ChatCompletionClient,
        model_client_stream: bool,
        model_context: ChatCompletionContext,
        agent_name: str,
        inner_messages: List[BaseAgentEvent | BaseChatMessage],
    ) -> AsyncGenerator[Response | ModelClientStreamingChunkEvent | ThoughtEvent, None]:
        """If reflect_on_tool_use=True, we do another inference based on tool results
        and yield the final text response (or streaming chunks)."""
        all_messages = system_messages + await model_context.get_messages()

        reflection_result: Optional[CreateResult] = None

        if model_client_stream:
            async for chunk in model_client.create_stream(all_messages):
                if isinstance(chunk, CreateResult):
                    reflection_result = chunk
                elif isinstance(chunk, str):
                    yield ModelClientStreamingChunkEvent(content=chunk, source=agent_name)
                else:
                    raise RuntimeError(f"Invalid chunk type: {type(chunk)}")
        else:
            reflection_result = await model_client.create(all_messages)

        if not reflection_result or not isinstance(reflection_result.content, str):
            raise RuntimeError("Reflect on tool use produced no valid text response.")

        # If the reflection produced a thought, yield it
        if reflection_result.thought:
            thought_event = ThoughtEvent(content=reflection_result.thought, source=agent_name)
            yield thought_event
            inner_messages.append(thought_event)

        # Add to context (including thought if present)
        await model_context.add_message(
            AssistantMessage(
                content=reflection_result.content,
                source=agent_name,
                thought=getattr(reflection_result, "thought", None),
            )
        )

        yield Response(
            chat_message=TextMessage(
                content=reflection_result.content,
                source=agent_name,
                models_usage=reflection_result.usage,
            ),
            inner_messages=inner_messages,
        )

    @staticmethod
    def _summarize_tool_use(
        executed_calls_and_results: List[Tuple[FunctionCall, FunctionExecutionResult]],
        inner_messages: List[BaseAgentEvent | BaseChatMessage],
        tool_call_summary_format: str,
        agent_name: str,
    ) -> Response:
        """If reflect_on_tool_use=False, create a summary message of all tool calls."""
        tool_call_summaries: List[str] = []
        for tool_call, tool_call_result in executed_calls_and_results:
            tool_call_summaries.append(
                tool_call_summary_format.format(
                    tool_name=tool_call.name,
                    arguments=tool_call.arguments,
                    result=tool_call_result.content,
                )
            )
        tool_call_summary = "\n".join(tool_call_summaries)
        return Response(
            chat_message=ToolCallSummaryMessage(
                content=tool_call_summary,
                source=agent_name,
            ),
            inner_messages=inner_messages,
        )

    @staticmethod
    async def _execute_tool_call(
        tool_call: FunctionCall,
        tools: List[BaseTool[Any, Any]],
        agent_name: str,
        cancellation_token: CancellationToken,
    ) -> Tuple[FunctionCall, FunctionExecutionResult]:
        """Execute a single tool call and return the result."""
        try:
            if not tools:
                raise ValueError("No tools are available.")
            tool = next((t for t in tools if t.name == tool_call.name), None)
            if tool is None:
                raise ValueError(f"The tool '{tool_call.name}' is not available.")
            arguments: Dict[str, Any] = json.loads(tool_call.arguments) if tool_call.arguments else {}
            result = await tool.run_json(arguments, cancellation_token)
            result_as_str = tool.return_value_as_string(result)
            return (
                tool_call,
                FunctionExecutionResult(
                    content=result_as_str,
                    call_id=tool_call.id,
                    is_error=False,
                    name=tool_call.name,
                ),
            )
        except Exception as e:
            return (
                tool_call,
                FunctionExecutionResult(
                    content=f"Error: {e}",
                    call_id=tool_call.id,
                    is_error=True,
                    name=tool_call.name,
                ),
            )

    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        """Reset the component to its initialization state."""
        await self._model_context.clear()

    async def save_state(self) -> Mapping[str, Any]:
        """Save the current state of the component."""
        model_context_state = await self._model_context.save_state()
        return AssistantAgentState(llm_context=model_context_state).model_dump()

    async def load_state(self, state: Mapping[str, Any]) -> None:
        """Load the state of the component."""
        assistant_agent_state = AssistantAgentState.model_validate(state)
        # Load the event context state.
        await self._model_context.load_state(assistant_agent_state.llm_context)

    def _to_config(self) -> CostumeFunctionCallAgentConfig:
        """Convert the component to a declarative config."""
        return CostumeFunctionCallAgentConfig(
            name=self.name,
            model_client=self._model_client,
            tools=self._tools,
            model_context=self._model_context,
            description=self.description,
            system_message=self._system_messages[0].content if self._system_messages else None,
            model_client_stream=self._model_client_stream,
            reflect_on_tool_use=self._reflect_on_tool_use,
            tool_call_summary_format=self._tool_call_summary_format,
            metadata=self._metadata,
        )
