from robot_studio.auth.model import User
from robot_studio.database.mysql.repository import UserRepository


class UserManager:
    """用户信息管理器
    提供服务如下：
    1. 新增客户信息[注册]
    2. 根据客户手机号查询客户
    """

    def __init__(self):
        self._user_repo = UserRepository()
    
    def query_user_by_telephone(self, telephone: str) -> User:
        """
        获取具体用户信息
        Args:
            telephone: 用户手机号

        Returns:

        """
        _usr = self._user_repo.get_user_by_telephone(telephone=telephone)
        assert _usr is not None, "用户信息不存在！telephone={telephone}"
        return User.from_do(_usr)

    def query_user_by_uid(self, uid: str) -> User | None:
        """
        根据用户UID获取用户信息
        Args:
            uid: 用户UID

        Returns:
            User: 用户信息，不存在返回None
        """
        _usr = self._user_repo.get_user_by_uid(uid=uid)
        if _usr is None:
            return None
        return User.from_do(_usr)
    
    def create_user(self, user: User) -> User:
        """
        用户注册
        Args:
            user: 用户信息

        Returns:

        """
        _usr = self._user_repo.create_user(user.to_do())
        return User.from_do(_usr)
    
    def update_last_login(self, telephone: str) -> User | None:
        """
        更新用户最后登录时间
        Args:
            telephone: 用户手机号

        Returns:
            User | None: 更新后的用户信息，失败返回None
        """
        _usr = self._user_repo.update_last_login(telephone=telephone)
        if _usr is None:
            return None
        return User.from_do(_usr)

    def delete_user(self, uid: str) -> bool:
        """
        根据用户UID删除用户信息
        Args:
            uid: 用户UID

        Returns:
            bool: 删除结果
        """
        return self._user_repo.delete_user(uid=uid)
