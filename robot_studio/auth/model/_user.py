from datetime import datetime

from pydantic import BaseModel

from robot_studio.database.mysql.table_schema import UserDO


class User(BaseModel):
    """用户模型"""
    cid: str | None = None
    """公司CID"""

    email: str | None = None
    """用户邮箱"""

    last_login: datetime = None
    """用户最后一次登录日期"""

    password_hash: str | None = None
    """用户密码"""

    role: str | None = None
    """用户角色"""

    telephone: str | None = None
    """用户手机号"""

    username: str | None = None
    """用户名"""

    uid: str | None = None
    """用户UID"""

    @classmethod
    def from_do(cls, user: UserDO):
        """
        数据库DO模型转为Model实例
        Args:
            user: 数据库用户模型

        Returns:
            用户模型（User）
        """
        instance = cls()
        instance.cid = user.cid
        instance.email = user.email
        instance.last_login = user.last_login
        instance.password_hash = user.password_hash
        instance.role = user.role
        instance.telephone = user.telephone
        instance.username = user.username
        instance.uid = user.uid
        return instance
    
    def to_do(self) -> UserDO:
        """
        Model实例转为DO模型
        Returns:
            UserDO: 转换的DO模型

        """
        return UserDO(cid=self.cid, 
                      email=self.email, 
                      last_login=self.last_login, 
                      password_hash=self.password_hash, 
                      role=self.role,
                      telephone=self.telephone,
                      username=self.username)
