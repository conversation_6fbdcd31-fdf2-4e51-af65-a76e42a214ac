import logging

from robot_studio.auth.api.result.company_res import (
    CompanyListRes,
    CompanyModel,
    CompanyRes,
)
from robot_studio.company import CompanyManager
from robot_studio.company.model import Company
from robot_studio.common.api_handler import api_handler
from robot_studio.auth.api.request.user_req import (
    CreateUserReq,
    LoginReq,
    DeleteUserReq,
)
from robot_studio.auth.api.result.user_res import UserRes
from robot_studio.auth.core import UserManager
from robot_studio.utils.jwt_util import gen_token
from robot_studio.utils.password_util import verify_password

logger = logging.getLogger(__name__)


class UserService:
    def __init__(self):
        self._user_manager = UserManager()
        self._company_manager = CompanyManager()

    @api_handler
    def sign_up(self, req: CreateUserReq) -> UserRes:
        """用户注册
        Args:
            req: {
                "telephone": "13855177418", # 用户手机号
                "username": "测试用户2", # 用户名
                "cid": "CID_0001", # 企业ID
                "password": "123456!@#", # 密码, 未加密
            }
        Returns:
            res: {
                "cid": "CID_0001", # 企业ID
                "token": "sdwfdcv..." # JWT库生成的token
                "success": true, # 注册结果
                "error_msg": "异常原因报错", # 错误码信息
                "error_code": "错误码" # 错误码
            }
        """
        user = self._user_manager.create_user(req.to_model())
        payload_dict = {
            "telephone": user.telephone,
            "cid": user.cid,
            "username": user.username,
            "password": user.password_hash
        }
        token = gen_token(payload=payload_dict)
        return UserRes(cid=user.cid, token=token)

    @api_handler
    def login(self, req: LoginReq) -> UserRes:
        """用户登录
        Args:
            req: {
                telephone: 用户手机号
                password: 用户密码（未加密）
            }
        Returns:
            res: {
                "cid": "CID_0001", # 企业ID
                "token": "sdwfdcv..." # JWT库生成的token
                "success": true, # 登录结果
                "error_msg": "异常原因报错", # 错误码信息
                "error_code": "错误码" # 错误码
            }
        """
        telephone = req.telephone
        password = req.password
        try:
            # 查询手机号对应的用户
            user = self._user_manager.query_user_by_telephone(telephone=telephone)
            if not user:
                # 用户不存在
                return UserRes(
                    cid=None,
                    token=None,
                    success=False,
                    error_msg=f"用户不存在！！！",
                )
            elif not verify_password(password, user.password_hash):
                # 密码不匹配
                return UserRes(
                    cid=None,
                    token=None,
                    success=False,
                    error_msg=f"账号与密码不匹配！！！",
                )
            else:
                # 更新用户最后登录时间
                update_res = self._user_manager.update_last_login(telephone=telephone)
                if update_res is None:
                    return UserRes(
                        cid=None,
                        token=None,
                        success=False,
                        error_msg=f"用户最后登录时间更新失败！！！"
                    )
                print("=======HERE=======")
                payload_dict = {
                    "telephone": user.telephone,
                    "cid": user.cid,
                    "username": user.username,
                    "password": user.password_hash,
                    "uid": user.uid
                }
                token = gen_token(payload=payload_dict)
                return UserRes(
                    cid=user.cid,
                    token=token,
                    username=user.username,
                    uid=user.uid,
                    success=True
                )
        except Exception as e:
            logger.error(f"用户登录失败: {e}")
            # 返回失败结果
            return UserRes(
                cid=None,
                token=None,
                username=None,
                success=False,
                error_msg=f"用户登录失败: {str(e)}",
                error_code="DB_ERROR"
            )

    @api_handler
    def get_company(self) -> CompanyListRes:
        # 获取公司列表
        companies: list[Company] = self._company_manager.query_all_companies()
        logger.info(f"查询到公司数量: {len(companies)}")

        if not companies:
            return CompanyListRes(data=[], success=False, error_msg="未查询到公司数据")

        # 将 List[Company] 转换为 List[CompanyRes]
        company_res_list = [
            CompanyModel(
                cid=company.cid,
                name=company.name,
                abbr=company.abbr,
                logo=company.logo,
                industry=company.industry,
                company_type=(
                    company.company_type.value if company.company_type else None
                ),
            )
            for company in companies
        ]

        # 返回封装后的响应对象
        return CompanyListRes(data=company_res_list)

    @api_handler
    def get_company_by_cid(self, cid: str) -> CompanyRes:
        company = self._company_manager.query_company(cid=cid)
        if not company:
            return CompanyRes(data=None, success=False, error_msg="未查询到公司数据")

        company_res = CompanyRes(
            data=CompanyModel(
                cid=company.cid,
                name=company.name,
                abbr=company.abbr,
                logo=company.logo,
                industry=company.industry,
                company_type=(
                    company.company_type.value if company.company_type else None
                ),
            )
        )

        return company_res

    @api_handler
    def get_full_company_models(self, cid: str) -> CompanyListRes:
        """
        获取三个企业模型
        Args:
            cid: 公司CID

        Returns:
            CompanyListRes: 包含三个企业模型的列表
        """
        try:
            current_company, industry_virtual, common_virtual = (
                self._company_manager.query_full_company_models(cid)
            )

            companies = []

            # 添加当前企业
            if current_company:
                companies.append(
                    CompanyModel(
                        cid=current_company.cid,
                        name=current_company.name,
                        abbr=current_company.abbr,
                        logo=current_company.logo,
                        industry=current_company.industry,
                        company_type=(
                            current_company.company_type.value
                            if current_company.company_type
                            else None
                        ),
                    )
                )

            # 添加行业虚拟企业
            if industry_virtual:
                companies.append(
                    CompanyModel(
                        cid=industry_virtual.cid,
                        name=industry_virtual.name,
                        abbr=industry_virtual.abbr,
                        logo=industry_virtual.logo,
                        industry=industry_virtual.industry,
                        company_type=(
                            industry_virtual.company_type.value
                            if industry_virtual.company_type
                            else None
                        ),
                    )
                )

            # 添加通用虚拟企业
            if common_virtual:
                companies.append(
                    CompanyModel(
                        cid=common_virtual.cid,
                        name=common_virtual.name,
                        abbr=common_virtual.abbr,
                        logo=common_virtual.logo,
                        industry=common_virtual.industry,
                        company_type=(
                            common_virtual.company_type.value
                            if common_virtual.company_type
                            else None
                        ),
                    )
                )

            return CompanyListRes(data=companies)

        except Exception as e:
            logger.error(f"获取三个企业模型失败: {e}")
            return CompanyListRes(
                data=[], success=False, error_msg=f"获取企业模型失败: {str(e)}"
            )

    @api_handler
    def delete_user(self, req: DeleteUserReq) -> UserRes:
        """删除用户信息
        Args:
            req: {
                "uid": "UID_0001", # 用户UID
            }
        Returns:
            res: {
                "success": true, # 删除结果
                "error_msg": "异常原因报错", # 错误码信息
                "error_code": "错误码" # 错误码
            }
        """
        try:
            # 先检查用户是否存在
            user = self._user_manager.query_user_by_uid(uid=req.uid)
            if not user:
                return UserRes(
                    cid=None,
                    token=None,
                    success=False,
                    error_msg=f"用户不存在: {req.uid}",
                    error_code="USER_NOT_FOUND"
                )
            
            # 删除用户
            delete_result = self._user_manager.delete_user(uid=req.uid)
            if delete_result:
                return UserRes(cid=None, token=None, success=True)
            else:
                return UserRes(
                    cid=None,
                    token=None,
                    success=False,
                    error_msg=f"删除用户失败: {req.uid}",
                    error_code="DELETE_FAILED"
                )
                
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return UserRes(
                cid=None,
                token=None,
                success=False,
                error_msg=f"删除用户失败: {str(e)}",
                error_code="DB_ERROR"
            )
