from pydantic import Field, BaseModel
from robot_studio.common.base_result import BaseResult


class CompanyModel(BaseModel):
    cid: str | None = Field(default=None, description="公司cid")
    name: str | None = Field(default=None, description="公司名称")
    abbr: str | None = Field(default=None, description="公司缩写")
    logo: str | None = Field(default=None, description="公司logo")
    industry: str | None = Field(default=None, description="公司归属行业")
    company_type: str | None = Field(default=None, description="公司类型")

class CompanyListRes(BaseResult):
    data: list[CompanyModel] | None = Field(default=None, description="具体的公司列表")


class CompanyRes(BaseResult):
    data: CompanyModel | None = Field(default=None, description="具体公司信息")