from pydantic import BaseModel, Field

from robot_studio.auth.model import User
from robot_studio.utils.password_util import hash_password


class CreateUserReq(BaseModel):
    """创建用户请求"""
    telephone: str = Field(..., description="用户手机号")

    email: str | None = Field(default=None, description="用户邮箱")

    username: str = Field(..., description="用户名")

    cid: str | None = Field(default=None, description="用户所在企业ID")

    password: str = Field(..., description="用户密码")

    def to_model(self) -> User:
        # 密码加密
        return User(telephone=self.telephone,
                    email=self.email,
                    username=self.username, 
                    cid=self.cid, 
                    password_hash=hash_password(self.password))


class LoginReq(BaseModel):
    """用户登录请求"""
    telephone: str = Field(..., description="用户手机号")

    password: str = Field(..., description="用户密码")


class DeleteUserReq(BaseModel):
    """删除用户请求"""
    uid: str = Field(..., description="用户UID")
