from typing import Optional
import chainlit as cl

# 用户认证配置
# 可以根据需要修改用户名和密码
# TODO 设置账密数据模块 @kehan
USERS = {
    "admin": "admin123",  # 管理员账号
    "teacher": "teacher123",  # 教师账号
    "parent": "parent123"  # 家长账号
}

# 认证回调函数
@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[cl.User]:
    """验证用户名和密码
    
    Args:
        username: 用户名
        password: 密码
        
    Returns:
        cl.User: 认证成功返回用户对象，失败返回None
    """
    if username in USERS and USERS[username] == password:
        # 根据不同用户类型设置不同的角色
        if username == "admin":
            role = "admin"
        elif username == "teacher":
            role = "teacher"
        else:
            role = "parent"
            
        return cl.User(
            identifier=username,
            metadata={"role": role, "provider": "credentials"}
        )
    return None
