import logging
import os
from typing import Dict, Any, List

from robot_studio.database.mysql.db_engine import get_common_session
from robot_studio.database.mysql.repository._configuration_repository import ConfigurationRepository

logger = logging.getLogger(__name__)


class ConfigurationService:
    """配置服务类，提供统一的配置获取接口"""
    
    def __init__(self):
        self._cache = {}
        self._cache_enabled = True
    
    def get_config(self, key: str, env: str = None, cid: str = None, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            env: 环境标识，默认从环境变量获取
            cid: 企业ID，默认从环境变量获取
            default: 默认值
            
        Returns:
            配置值
        """
        # 确定环境和企业ID
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        # 检查缓存
        cache_key = f"{key}:{env}:{cid}"
        if self._cache_enabled and cache_key in self._cache:
            return self._cache[cache_key]
        
        # 从数据库获取配置
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                value = repo.get_config_value(key, env, cid, default)
                
                # 缓存结果
                if self._cache_enabled:
                    self._cache[cache_key] = value
                
                return value
        except Exception as e:
            logger.error(f"Failed to get config {key}: {str(e)}")
            return default
    
    def get_all_configs(self, env: str = None, cid: str = None) -> Dict[str, str]:
        """
        获取所有配置项
        
        Args:
            env: 环境标识
            cid: 企业ID
            
        Returns:
            配置字典
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                return repo.get_all_configs(env, cid)
        except Exception as e:
            logger.error(f"Failed to get all configs: {str(e)}")
            return {}
    
    def set_config(self, key: str, value: str, env: str = None, cid: str = None, 
                  tag: str = None, description: str = None, is_encrypted: bool = False) -> bool:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
            env: 环境标识
            cid: 企业ID
            tag: 配置标签
            description: 配置描述
            is_encrypted: 是否加密
            
        Returns:
            是否成功
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                
                # 检查是否已存在
                existing_config = repo.get_config(key, env, cid)
                if existing_config:
                    # 更新现有配置
                    success = repo.update_config(key, value, env, cid)
                    # 如果提供了tag，也更新tag
                    if tag is not None:
                        repo.update_config_tag(key, tag, env, cid)
                else:
                    # 创建新配置
                    repo.create_config(key, value, env, cid, tag, description, is_encrypted)
                    success = True
                
                # 清除缓存
                if success:
                    self.clear_cache()
                
                return success
        except Exception as e:
            logger.error(f"Failed to set config {key}: {str(e)}")
            return False
    
    def delete_config(self, key: str, env: str = None, cid: str = None) -> bool:
        """
        删除配置项
        
        Args:
            key: 配置键
            env: 环境标识
            cid: 企业ID
            
        Returns:
            是否成功
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                success = repo.delete_config(key, env, cid)
                
                # 清除缓存
                if success:
                    self.clear_cache()
                
                return success
        except Exception as e:
            logger.error(f"Failed to delete config {key}: {str(e)}")
            return False
    
    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()
    
    def disable_cache(self):
        """禁用缓存"""
        self._cache_enabled = False
        self._cache.clear()
    
    def enable_cache(self):
        """启用缓存"""
        self._cache_enabled = True
    
    def get_configs_by_tag(self, tag: str, env: str = None, cid: str = None) -> Dict[str, str]:
        """
        根据标签获取配置
        
        Args:
            tag: 配置标签
            env: 环境标识
            cid: 企业ID
            
        Returns:
            配置字典
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                return repo.get_configs_by_tag_dict(tag, env, cid)
        except Exception as e:
            logger.error(f"Failed to get configs by tag {tag}: {str(e)}")
            return {}
    
    def get_all_tags(self, env: str = None, cid: str = None) -> List[str]:
        """
        获取所有标签
        
        Args:
            env: 环境标识
            cid: 企业ID
            
        Returns:
            标签列表
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                return repo.get_all_tags(env, cid)
        except Exception as e:
            logger.error(f"Failed to get all tags: {str(e)}")
            return []
    
    def update_config_tag(self, key: str, tag: str, env: str = None, cid: str = None) -> bool:
        """
        更新配置项的标签
        
        Args:
            key: 配置键
            tag: 新标签
            env: 环境标识
            cid: 企业ID
            
        Returns:
            是否成功
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                success = repo.update_config_tag(key, tag, env, cid)
                
                # 清除缓存
                if success:
                    self.clear_cache()
                
                return success
        except Exception as e:
            logger.error(f"Failed to update tag for config {key}: {str(e)}")
            return False
    
    def load_to_env(self, env: str = None, cid: str = None):
        """
        将配置加载到环境变量中
        
        Args:
            env: 环境标识
            cid: 企业ID
        """
        configs = self.get_all_configs(env, cid)
        for key, value in configs.items():
            os.environ[key] = value
        
        logger.info(f"Loaded {len(configs)} configurations to environment variables")
    
    def load_tag_to_env(self, tag: str, env: str = None, cid: str = None):
        """
        将指定标签的配置加载到环境变量中
        
        Args:
            tag: 配置标签
            env: 环境标识
            cid: 企业ID
        """
        configs = self.get_configs_by_tag(tag, env, cid)
        for key, value in configs.items():
            os.environ[key] = value
        
        logger.info(f"Loaded {len(configs)} configurations with tag '{tag}' to environment variables")
    
    def encrypt_existing_config(self, key: str, env: str = None, cid: str = None) -> bool:
        """
        加密现有的明文配置
        
        Args:
            key: 配置键
            env: 环境标识
            cid: 企业ID
            
        Returns:
            是否成功
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                success = repo.encrypt_existing_config(key, env, cid)
                
                # 清除缓存
                if success:
                    self.clear_cache()
                
                return success
        except Exception as e:
            logger.error(f"Failed to encrypt existing config {key}: {str(e)}")
            return False
    
    def batch_encrypt_configs(self, config_keys: List[str], env: str = None, cid: str = None) -> Dict[str, bool]:
        """
        批量加密配置
        
        Args:
            config_keys: 要加密的配置键列表
            env: 环境标识
            cid: 企业ID
            
        Returns:
            加密结果字典
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            with next(get_common_session()) as session:
                repo = ConfigurationRepository(session)
                results = repo.batch_encrypt_configs(config_keys, env, cid)
                
                # 清除缓存
                self.clear_cache()
                
                return results
        except Exception as e:
            logger.error(f"Failed to batch encrypt configs: {str(e)}")
            return {key: False for key in config_keys}
    
    def get_encryption_status(self, env: str = None, cid: str = None) -> Dict[str, bool]:
        """
        获取配置的加密状态
        
        Args:
            env: 环境标识
            cid: 企业ID
            
        Returns:
            配置加密状态字典 {key: is_encrypted}
        """
        if env is None:
            env = os.getenv("ENV", "common")
        if cid is None:
            cid = os.getenv("CID", "common")
        
        try:
            from robot_studio.database.mysql.table_schema._configuration import ConfigurationDO
            from sqlmodel import select
            
            with next(get_common_session()) as session:
                # 获取所有配置的加密状态
                stmt = select(ConfigurationDO.key, ConfigurationDO.is_encrypted).where(
                    ConfigurationDO.env == env,
                    ConfigurationDO.cid == cid,
                    ConfigurationDO.is_active == True
                )
                results = session.exec(stmt).all()
                
                return {key: is_encrypted for key, is_encrypted in results}
        except Exception as e:
            logger.error(f"Failed to get encryption status: {str(e)}")
            return {}


# 全局配置服务实例
config_service = ConfigurationService()


def get_config(key: str, env: str = None, cid: str = None, default: Any = None) -> Any:
    """
    获取配置的便捷函数
    
    Args:
        key: 配置键
        env: 环境标识
        cid: 企业ID
        default: 默认值
        
    Returns:
        配置值
    """
    return config_service.get_config(key, env, cid, default)


def get_all_configs(env: str = None, cid: str = None) -> Dict[str, str]:
    """
    获取所有配置的便捷函数
    
    Args:
        env: 环境标识
        cid: 企业ID
        
    Returns:
        配置字典
    """
    return config_service.get_all_configs(env, cid)


def load_configs_to_env(env: str = None, cid: str = None):
    """
    将配置加载到环境变量的便捷函数
    
    Args:
        env: 环境标识
        cid: 企业ID
    """
    config_service.load_to_env(env, cid)


def get_configs_by_tag(tag: str, env: str = None, cid: str = None) -> Dict[str, str]:
    """
    根据标签获取配置的便捷函数
    
    Args:
        tag: 配置标签
        env: 环境标识
        cid: 企业ID
        
    Returns:
        配置字典
    """
    return config_service.get_configs_by_tag(tag, env, cid)


def get_all_tags(env: str = None, cid: str = None) -> List[str]:
    """
    获取所有标签的便捷函数
    
    Args:
        env: 环境标识
        cid: 企业ID
        
    Returns:
        标签列表
    """
    return config_service.get_all_tags(env, cid)


def load_tag_to_env(tag: str, env: str = None, cid: str = None):
    """
    将指定标签的配置加载到环境变量的便捷函数
    
    Args:
        tag: 配置标签
        env: 环境标识
        cid: 企业ID
    """
    config_service.load_tag_to_env(tag, env, cid)