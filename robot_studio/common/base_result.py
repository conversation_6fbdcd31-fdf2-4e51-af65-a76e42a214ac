from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class ErrorCode(Enum):
    """错误码枚举，包含错误码和错误信息"""
    SUCCESS = ("200", "成功")

    # 通用错误码 10xxx
    UNKNOWN_ERROR = ("10000", "未知异常！")
    PARAM_ERROR = ("10001", "参数传递错误！")
    AUTH_ERROR = ("10002", "认证失败！")
    PERMISSION_ERROR = ("10003", "权限不足！")
    NOT_FOUND = ("10004", "资源不存在！")
    SERVER_ERROR = ("10005", "服务器内部错误！")
    INVALID_TOKEN  = ("10006", "无效的令牌！")
    TOKEN_EXPIRED = ("10007", "令牌已过期！")

    # 数据模块 20xxx
    DATA_NOT_FOUND = ("20001", "数据不存在！")
    DATA_INVALID = ("20002", "数据无效！")

    def __init__(self, code: str, message: str):
        self.code = code
        self.message = message


class BaseResult(BaseModel):
    success: bool = Field(default=True, description="请求是否成功")
    error_code: str | None = Field(default=None, description="错误码")
    error_msg: str | None = Field(default=None, description="错误信息")
    data: Any | None = Field(default=None, description="请求数据体")

    @classmethod
    def success_result(cls, data: Any = None):
        """
        构建成功结果
        Args:
            data: 返回的数据，默认为None
            
        Returns:
            成功结果对象
        """
        return cls(
            success=True,
            error_code=ErrorCode.SUCCESS.code,
            error_msg=ErrorCode.SUCCESS.message,
            data=data
        )

    @classmethod
    def error_result(cls, error_code: str, error_msg: str, data: Any = None):
        """
        构建失败结果
        Args:
            error_code: 错误码
            error_msg: 错误信息
            data: 返回的数据，默认为None
        Returns:
            失败结果对象
        """
        return cls(
            success=False,
            error_code=error_code,
            error_msg=error_msg,
            data=data
        )

    @classmethod
    def param_error_result(cls, error_msg: str = None, data: Any = None):
        """
        构建参数错误结果
        Args:
            error_msg: 错误信息，默认为None（使用默认错误信息）
            data: 返回的数据，默认为None
        Returns:
            参数错误结果对象
        """
        return cls.error_result(
            error_code=ErrorCode.PARAM_ERROR.code,
            error_msg=error_msg if error_msg is not None else ErrorCode.PARAM_ERROR.message,
            data=data
        )

    @classmethod
    def server_error_result(cls, error_msg: str = None, data: Any = None):
        """
        构建服务器错误结果
        Args:
            error_msg: 错误信息，默认为None（使用默认错误信息）
            data: 返回的数据，默认为None
        Returns:
            服务器错误结果对象
        """
        return cls.error_result(
            error_code=ErrorCode.SERVER_ERROR.code,
            error_msg=error_msg if error_msg is not None else ErrorCode.SERVER_ERROR.message,
            data=data
        )

    @classmethod
    def not_found_error_result(cls, error_msg: str = None, data: Any = None):
        """
        构建数据不存在错误结果
        Args:
            error_msg: 错误信息，默认为None（使用默认错误信息）
            data: 返回的数据，默认为None
        Returns:
            数据不存在错误结果对象
        """
        return cls.error_result(
            error_code=ErrorCode.DATA_NOT_FOUND.code,
            error_msg=error_msg if error_msg is not None else ErrorCode.DATA_NOT_FOUND.message,
            data=data
        )
