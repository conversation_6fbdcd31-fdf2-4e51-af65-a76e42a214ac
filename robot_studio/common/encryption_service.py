"""
Configuration encryption service
"""
import base64
import logging

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


class ConfigurationEncryption:
    """配置加密服务"""
    
    def create_fernet_cipher(self, master_key: str) -> Fernet:
        """使用指定密钥创建Fernet加密器"""
        if not master_key:
            raise ValueError("Master key is required for encryption/decryption operations")
        
        # 使用PBKDF2从主密钥派生加密密钥
        salt = b'mindshake_config_salt'  # 固定盐值，确保同一主密钥生成相同的加密密钥
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(master_key.encode('utf-8')))
        return Fe<PERSON><PERSON>(key)
    
    def encrypt(self, plaintext: str, master_key: str) -> str:
        """
        加密明文
        
        Args:
            plaintext: 明文字符串
            master_key: 主密钥（必须提供）
            
        Returns:
            加密后的密文（Base64编码）
        """
        if not plaintext:
            return plaintext
        
        if not master_key:
            raise ValueError("Master key is required for encryption")
        
        try:
            fernet = self.create_fernet_cipher(master_key)
            logger.info("Encrypting data using provided master key")
            
            # 加密并转换为base64字符串
            encrypted_bytes = fernet.encrypt(plaintext.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encrypt data: {str(e)}")
            raise
    
    def decrypt(self, ciphertext: str, master_key: str) -> str:
        """
        解密密文
        
        Args:
            ciphertext: 密文字符串（Base64编码）
            master_key: 主密钥（必须提供）
            
        Returns:
            解密后的明文
        """
        if not ciphertext:
            return ciphertext
        
        if not master_key:
            raise ValueError("Master key is required for decryption")
        
        try:
            fernet = self.create_fernet_cipher(master_key)
            logger.info("Decrypting data using provided master key")
            
            # 从base64解码并解密
            encrypted_bytes = base64.urlsafe_b64decode(ciphertext.encode('utf-8'))
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to decrypt data: {str(e)}")
            # 如果解密失败，可能是因为数据未加密或密钥错误
            # 返回原数据以保持向后兼容性
            logger.warning("Decryption failed, returning original data for backward compatibility")
            return ciphertext
    
    def get_encryption_info(self) -> dict:
        """
        获取加密信息
        
        Returns:
            加密配置信息
        """
        return {
            "algorithm": "Fernet (AES 128)",
            "key_derivation": "PBKDF2-SHA256",
            "iterations": 100000,
            "salt": "mindshake_config_salt"
        }

# 全局加密服务实例
encryption_service = ConfigurationEncryption()


def encrypt_config_value(value: str, master_key: str) -> str:
    """
    加密配置值的便捷函数
    
    Args:
        value: 要加密的配置值
        master_key: 主密钥（必须提供）
        
    Returns:
        加密后的值
    """
    return encryption_service.encrypt(value, master_key)


def decrypt_config_value(encrypted_value: str, master_key: str) -> str:
    """
    解密配置值的便捷函数
    
    Args:
        encrypted_value: 加密的配置值
        master_key: 主密钥（必须提供）
        
    Returns:
        解密后的值
    """
    return encryption_service.decrypt(encrypted_value, master_key)


def create_encryption_key() -> str:
    """
    生成新的加密密钥
    
    Returns:
        Base64编码的随机密钥
    """
    import os
    return base64.urlsafe_b64encode(os.urandom(32)).decode('utf-8')


if __name__ == "__main__":
    # 测试加密功能
    test_data = "test_password_123"
    test_master_key = "test_master_key_for_encryption"
    
    print("=== 配置加密服务测试 ===")
    print(f"原始数据: {test_data}")
    print(f"测试密钥: {test_master_key}")
    
    # 加密
    encrypted = encrypt_config_value(test_data, test_master_key)
    print(f"加密后: {encrypted}")
    
    # 解密
    decrypted = decrypt_config_value(encrypted, test_master_key)
    print(f"解密后: {decrypted}")
    
    # 验证
    print(f"加密解密成功: {test_data == decrypted}")
    
    # 加密信息
    info = encryption_service.get_encryption_info()
    print(f"加密信息: {info}")
    
    # 生成新密钥示例
    new_key = create_encryption_key()
    print(f"新生成的密钥: {new_key}")
    
    # 测试没有密钥的情况
    try:
        encrypt_config_value(test_data, "")
    except ValueError as e:
        print(f"预期错误 - 空密钥: {e}")