import functools
import logging

from robot_studio.common.base_result import BaseResult, ErrorCode

logger = logging.getLogger(__name__)


def api_handler(func):
    """api操作切面类"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"api处理发生异常！: {str(e)}")
            return BaseResult(error_code=ErrorCode.UNKNOWN_ERROR.code,
                              error_msg=f"{ErrorCode.UNKNOWN_ERROR.message} 具体异常信息:{str(e)}")

    return wrapper
