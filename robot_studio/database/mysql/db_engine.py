import logging
import os

from dotenv import load_dotenv, find_dotenv
from sqlmodel import create_engine, Session

logger = logging.getLogger(__name__)


def database_url(db_name):
    load_dotenv(find_dotenv(), override=True)
    db_url_prod = os.getenv('DB_URL_PROD') or 'rm-bp1069j22f20cfu32.mysql.rds.aliyuncs.com'
    db_url_dev = os.getenv('DB_URL_DEV') or 'rm-bp1r8a1y8w872i170oo.mysql.rds.aliyuncs.com'
    env_tag = os.getenv('ENV_TAG') or 'DEV'
    db_url = db_url_prod if env_tag == 'PROD' or (env_tag == 'TEST' and db_name == 'mindshake_common') else db_url_dev

    # 修复端口号处理，确保是有效的整数
    db_port_env = os.getenv('DB_PORT')
    if db_port_env and db_port_env.lower() != 'none' and db_port_env.isdigit():
        db_port = int(db_port_env)
    else:
        db_port = 3306  # 默认MySQL端口

    db_user = os.getenv('DB_USER') or 'mindshake_1'
    db_password = os.getenv('DB_PASSWORD') or '_PH7VunKJiDffQ_4NwkWBc4q'
    # 添加字符集参数支持emoji等特殊字符
    return f"mysql+pymysql://{db_user}:{db_password}@{db_url}:{db_port}/{db_name}?charset=utf8mb4"

# Default engine for mindshake database
engine = create_engine(database_url("mindshake"), echo=False, pool_pre_ping=True)

# Common database engine for mindshake_common database
# mindshake_common存储环境共享的系统配置信息和组件配置信息
common_engine = create_engine(database_url("mindshake_common"), echo=False, pool_pre_ping=True)


def db_handler(func):
    """数据库操作异常处理装饰器"""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database operation failed: {str(e)}")
            # 可以根据需要处理不同类型的异常
            # 例如: SQLAlchemyError, IntegrityError等
            raise

    return wrapper


def get_session():
    with Session(engine) as session:
        try:
            yield session
        finally:
            session.close()


def get_common_session():
    with Session(common_engine) as session:
        try:
            yield session
        finally:
            session.close()


if __name__ == '__main__':
    conn_session = get_session()
    print(type(conn_session))
