from datetime import datetime
from typing import Optional

from sqlmodel import SQLModel, Field


class ConfigurationDO(SQLModel, table=True):
    """配置信息表"""
    __tablename__ = 'configuration'
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                   description="修改时间")
    conf_key: str = Field(nullable=False, max_length=100, description="配置键名")
    conf_value: str = Field(nullable=False, max_length=1000, description="配置值")
    env: str = Field(nullable=False, max_length=20, default="common", description="生效环境(dev/test/prod/common)")
    cid: str = Field(nullable=False, max_length=20, default="common", description="企业ID，通用配置填common")
    tag: str = Field(nullable=True, max_length=50, description="配置标签，用于分组管理")
    description: str = Field(nullable=True, max_length=200, description="配置描述")
    is_encrypted: bool = Field(nullable=False, default=False, description="是否加密存储")
    is_active: bool = Field(nullable=False, default=True, description="是否激活")
    
    class Config:
        indexes = [
            ("conf_key", "env", "cid"),  # 复合索引用于快速查询
            ("env", "cid"),
            ("tag",),  # tag索引用于按标签查询
            ("is_active",)
        ]