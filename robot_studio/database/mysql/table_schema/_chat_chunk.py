from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Text
from sqlmodel import SQLModel, Field


class ChatChunkDO(SQLModel, table=True):
    """聊天块表 - 简化的聊天块管理"""
    __tablename__ = 'chat_chunk'

    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    chunk_id: Optional[str] = Field(default=None, max_length=64, description="聊天块唯一标识")
    session_id: Optional[str] = Field(default=None, max_length=64, description="会话ID")
    task_id: Optional[str] = Field(default=None, max_length=64, description="组件任务ID")
    span_id: Optional[str] = Field(default=None, max_length=64, description="会话块归属的逻辑单元ID")
    parent_span_id: Optional[str] = Field(default=None, max_length=64, description="调用当前逻辑单元ID")
    role_type: Optional[str] = Field(default=None, max_length=32, description="会话角色类型: user,assistant")
    role_entity: Optional[str] = Field(default=None, sa_type=Text, description="会话角色实体JSON")
    chunk_type: Optional[str] = Field(default=None, max_length=32, description="聊天块类型: Event,Message")
    chunk_sub_type: Optional[str] = Field(default=None, max_length=64, description="会话块子类型")
    chunk_status: Optional[str] = Field(default=None, max_length=32, description="会话块状态")
    content: Optional[str] = Field(default=None, sa_type=Text, description="聊天块内容")
    runtime_params: Optional[str] = Field(default=None, sa_type=Text, description="运行时参数JSON")
    gmt_create: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    gmt_modified: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="修改时间"
    )


# 角色类型枚举常量
class RoleType:
    USER = "user"           # 用户
    ASSISTANT = "assistant" # AI助手


# 聊天块类型枚举常量
class ChunkType:
    EVENT = "Event"         # 事件
    MESSAGE = "Message"     # 消息
