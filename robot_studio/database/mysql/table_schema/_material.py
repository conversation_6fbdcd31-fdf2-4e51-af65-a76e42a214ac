from datetime import datetime
from typing import Optional

from sqlmodel import SQLModel, Field


class MaterialDO(SQLModel, table=True):
    """素材表"""
    __tablename__ = 'material'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False,
                                 default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False,
                                   default_factory=lambda: datetime.now(),
                                   description="修改时间")
    mid: str = Field(nullable=False, max_length=20, description="素材UID")
    name: str = Field(nullable=False, max_length=30, description="素材名称")
    type: str = Field(nullable=False, max_length=10, description="素材类型")
    group_id: str = Field(nullable=False, max_length=20, description="关联素材组id")
    source: str = Field(nullable=False, max_length=20, description="素材来源")
    is_del: bool = Field(nullable=False, default=False, description="是否删除")
    content_digest: str = Field(nullable=True, default=None, max_length=200, description="素材内容简介")
    tags: str = Field(nullable=True, max_length=200, description="自定义标签")
    valid_date: datetime = Field(nullable=True, description="生效时间")
    invalid_date: datetime = Field(nullable=True, description="过期时间")
    status: str = Field(nullable=True, description="状态")
    version: str = Field(nullable=True, description="版本")
    material_content: str = Field(nullable=True, description="素材内容")
    oss_url: str = Field(nullable=True, max_length=1024, description="oss url")
    oss_path: str = Field(nullable=True, max_length=1024, description="oss path")
    oss_expiration: datetime = Field(nullable=True, max_length=1024, description="oss path")
    create_uid: str = Field(nullable=True, max_length=20, description="用户ID")
    create_user: str = Field(nullable=True, max_length=64, description="用户名")
    longtime_valid: bool = Field(default=False, description="是否长期有效，默认不是")
    oss_preview_url: str = Field(nullable=True, max_length=1024, description="视频素材的预览图")

