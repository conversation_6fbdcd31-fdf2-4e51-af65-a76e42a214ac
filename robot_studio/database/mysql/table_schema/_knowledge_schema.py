from datetime import datetime
from typing import Optional, List, Dict, Any

from sqlalchemy import Column
from sqlmodel import SQLModel, Field

from robot_studio.database.mysql.table_schema._json_col import JSONText


class KnowledgeSchemaDO(SQLModel, table=True):
    """知识Schema表"""
    __tablename__ = 'knowledge_schema'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                   description="修改时间")
    schema_id: str = Field(nullable=False, unique=True, max_length=20, description="知识结构ID")
    type: str = Field(nullable=False, max_length=20, description="知识结构类型")
    name: str = Field(nullable=False, max_length=256, description="知识结构名称")
    desc: str = Field(nullable=True, default=None, max_length=512, description="知识结构描述")
    config: List[Dict[str, Any]] = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                         description="知识结构配置", alias="schema")
    cid: str = Field(nullable=False, max_length=20, description="企业ID")
