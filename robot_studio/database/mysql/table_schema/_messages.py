from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Text
from sqlmodel import SQLModel, Field


class MessagesDO(SQLModel, table=True):
    """对话消息表"""
    __tablename__ = 'messages'
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    message_id: Optional[str] = Field(default=None, max_length=64, description="消息唯一标识")
    session_id: Optional[str] = Field(default=None, max_length=64, description="会话ID")
    parent_message_id: Optional[str] = Field(default=None, max_length=64, description="父消息ID")
    contents: Optional[str] = Field(default=None, sa_type=Text, description="消息内容")
    token_count: Optional[int] = Field(default=None, description="Token数量")
    model_info: Optional[str] = Field(default=None, max_length=64, description="使用的模型信息")
    usage_info: Optional[str] = Field(default=None, sa_type=Text, description="使用统计信息")
    message_metadata: Optional[str] = Field(default=None, sa_type=Text, description="消息元数据")
    artifact_count: Optional[int] = Field(default=0, description="关联产物数量")
    status: Optional[int] = Field(default=1, description="消息状态:1-正常,2-已编辑,3-已删除")
    is_deleted: Optional[int] = Field(default=0, description="是否删除:0-否,1-是")
    gmt_create: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    gmt_modified: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="修改时间"
    )


# 消息状态枚举常量
class MessageStatus:
    NORMAL = 1      # 正常
    EDITED = 2      # 已编辑
    DELETED = 3     # 已删除


# 删除状态枚举常量
class DeleteStatus:
    NOT_DELETED = 0  # 未删除
    DELETED = 1      # 已删除