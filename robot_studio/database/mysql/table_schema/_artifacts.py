from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Text
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class ArtifactsDO(SQLModel, table=True):
    """内容产物表"""
    __tablename__ = 'artifacts'
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    artifact_id: Optional[str] = Field(default=None, max_length=64, description="产物唯一标识")
    session_id: Optional[str] = Field(default=None, max_length=64, description="会话ID")
    message_id: Optional[str] = Field(default=None, max_length=64, description="消息ID")
    type: Optional[str] = Field(default=None, max_length=32, description="产物类型: image,code,chart,file,doc等")
    title: Optional[str] = Field(default=None, max_length=200, description="产物标题")
    description: Optional[str] = Field(default=None, sa_type=Text, description="产物描述")
    content: Optional[str] = Field(default=None, sa_type=Text, description="产物原始内容")
    file_path: Optional[str] = Field(default=None, max_length=500, description="文件存储路径")
    file_url: Optional[str] = Field(default=None, max_length=500, description="文件访问URL")
    file_size: Optional[int] = Field(default=None, description="文件大小(字节)")
    mime_type: Optional[str] = Field(default=None, max_length=500, description="MIME类型")
    file_hash: Optional[str] = Field(default=None, max_length=64, description="文件哈希值")
    thumbnail_path: Optional[str] = Field(default=None, max_length=500, description="缩略图路径")
    tags: Optional[str] = Field(default=None, sa_type=Text, description="标签列表")
    status: Optional[int] = Field(default=None, description="状态:1-待处理,2-处理中,3-已完成,4-失败,5-过期")
    download_count: Optional[int] = Field(default=None, description="下载次数")
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")
    is_deleted: Optional[bool] = Field(default=False, description="是否删除:0-否,1-是")
    gmt_create: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    gmt_modified: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="修改时间"
    )
    artifact_metadata: Optional[str] = Field(default=None, sa_type=Text, description="产物元数据")