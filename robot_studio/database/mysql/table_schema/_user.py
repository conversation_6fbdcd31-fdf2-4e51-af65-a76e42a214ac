from datetime import datetime
from typing import Optional

from sqlmodel import SQLModel, Field


class UserDO(SQLModel, table=True):
    """用户信息表"""
    __tablename__ = 'user_auth'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    cid: str = Field(nullable=False, max_length=20, description="公司CID")
    uid: str = Field(nullable=False, max_length=20, description="用户UID")
    email: str = Field(nullable=True, max_digits=50, description="用户邮箱")
    gmt_create: datetime = Field(nullable=False, 
                                 default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, 
                                   default_factory=lambda: datetime.now(),
                                   description="修改时间")
    last_login: datetime = Field(nullable=True, description="用户最后登录时间")
    password_hash: str = Field(nullable=False, description="密码（hash加密）")
    role: str = Field(nullable=True, description="用户角色")
    telephone: str = Field(nullable=False, description="用户手机号")
    username: str = Field(nullable=False, description="用户名")
