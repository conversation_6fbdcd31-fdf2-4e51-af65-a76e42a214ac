from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class ResourceGroupDO(SQLModel, table=True):
    """资源分组数据库模型"""
    __tablename__ = "resource_group"
    """资源分组基础模型"""

    id: Optional[int] = Field(default=None, primary_key=True, description="主键")

    group_id: Optional[str] = Field(nullable=True, unique=True, index=True)
    """分组ID"""

    gmt_create: datetime = Field(default_factory=datetime.now)
    """创建时间"""

    gmt_modified: datetime = Field(default_factory=datetime.now)
    """更新时间"""

    group_name: Optional[str] = Field(nullable=True, max_length=128)
    """分组名称"""

    group_desc: Optional[str] = Field(nullable=True, max_length=512)
    """分组描述"""

    group_type: str = Field(nullable=False, max_length=32)
    """分组类型，GroupType枚举"""

    sub_group_type: Optional[str] = Field(nullable=True, max_length=32)
    """分组类型，SubGroupType"""

    cid: Optional[str] = Field(nullable=True, max_length=64)
    """关联的企业ID"""

    rel_resource_id: Optional[str] = Field(nullable=True, max_length=64)
    """关联的资源ID, 仅素材组有此字段"""

    rel_resource_type: Optional[str] = Field(nullable=True, max_length=32)
    """关联的资源类型，Knowledge/Material"""

    create_user: Optional[str] = Field(nullable=True, max_length=64)
    """创建人"""

    create_uid: Optional[str] = Field(nullable=True, max_length=64, alias="uid")
    """创建人的用户ID"""

    tags: Optional[List[str]] = Field(sa_column=Column(JSONText(), nullable=True, default=None))
    """标签数组"""

    is_deleted: bool = Field(default=False)
    """是否已删除（软删除标记）"""