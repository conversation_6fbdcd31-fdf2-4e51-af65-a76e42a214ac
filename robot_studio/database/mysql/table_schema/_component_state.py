from datetime import datetime
from typing import Optional

from sqlalchemy import Column
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class ComponentStateDO(SQLModel, table=True):
    """公司信息表"""

    __tablename__ = "component_state"
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(
        nullable=False, default_factory=lambda: datetime.now(), description="创建时间"
    )
    gmt_modified: datetime = Field(
        nullable=False, default_factory=lambda: datetime.now(), description="修改时间"
    )
    session_id: str = Field(nullable=False, max_length=128, description="会话ID")
    component_id: str = Field(nullable=False, max_length=64, description="组件ID")
    version: int = Field(nullable=False, description="组件版本")
    state: dict | None = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                               description="组件实例化参数")
    is_del: bool = Field(nullable=False, default=False, description="是否删除")
