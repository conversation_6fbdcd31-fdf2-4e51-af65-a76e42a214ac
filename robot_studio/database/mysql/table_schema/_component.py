from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class ComponentDO(SQLModel, table=True):
    """组件信息"""
    __tablename__ = 'component'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                   description="修改时间")
    component_id: str | None = Field(nullable=True, max_length=64, description="组件ID")

    code: str | None = Field(nullable=True, max_length=64, description="组件编码")
    name: str | None = Field(nullable=True, max_length=128, description="组件名称")
    desc: str | None = Field(nullable=True, max_length=1024, description="组件描述")
    type: str | None = Field(nullable=True, default=None, max_length=64, description="组件类型")
    scene: str | None = Field(nullable=True, max_length=64, description="应用场景")
    tags: List[str] | None = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                   description="组件标签")
    cid: str | None = Field(nullable=True, max_length=64, description="组件关联的企业ID")
    depend_provider: str | None = Field(nullable=True, max_length=512, description="组件包路径")
    config_params: list | None = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                       description="组件实例化参数")
    runtime_params: list | None = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                        description="运行时参数")
    version: int | None = Field(nullable=True, description="版本号")
    version_desc: str | None = Field(nullable=True, max_length=512, description="版本描述")
    version_author: str | None = Field(nullable=True, max_length=32, description="版本作者")
    status: str | None = Field(nullable=True, max_length=32, description="组件当前版本状态")
    iterate_id: str | None = Field(nullable=True, max_length=64, description="当前组件版本关联的研发迭代ID")
    code_change: bool | None = Field(nullable=False, default=False, description="当前版本代码是否变更")
    is_del: bool | None = Field(nullable=True, default=False, description="当前版本是否删除")
    template_type: str | None = Field(nullable=True, max_length=64, description="模版类型")
    provider_type: str | None = Field(nullable=True, max_length=64, description="依赖类型，模版OR类路径")
