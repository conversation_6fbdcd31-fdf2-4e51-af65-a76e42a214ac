from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Text
from sqlmodel import SQLModel, Field

from ._json_col import JSONText


class KnowledgeDO(SQLModel, table=True):
    """知识信息表"""
    __tablename__ = 'knowledge'
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                 description="创建时间")
    gmt_modified: datetime = Field(nullable=False, default_factory=lambda: datetime.now(),
                                   description="修改时间")
    knowledge_id: str = Field(nullable=False, max_length=20, description="知识ID")
    name: str = Field(nullable=False, max_length=256, description="知识名称")
    desc: str = Field(nullable=True, default=None, max_length=512, description="知识描述")
    source: str = Field(nullable=False, max_length=64, description="知识来源")
    schema_id: str = Field(nullable=False, max_length=20, description="知识结构ID")
    valid_date: datetime = Field(nullable=True, default=None, description="知识生效日期")
    invalid_date: datetime = Field(nullable=True, default=None, description="知识失效日期")
    status: str = Field(nullable=False, max_length=20, description="知识状态")
    version: Optional[int] = Field(nullable=True, default=0, description="知识版本号")
    group_id: str = Field(nullable=False, max_length=20, description="归属的知识组ID")
    rel_material_group_id: str = Field(nullable=True, default=None, max_length=20, description="关联的素材组ID")
    content_uuid: str = Field(nullable=True, default=None, max_length=128, description="知识内容的UUID")
    content_structure: list = Field(sa_column=Column(JSONText(), nullable=True, default=None),
                                    description="结构化知识内容")
    content_md: str = Field(nullable=True, default=None, description="非结构化知识内容")
    content_graph: list = Field(sa_column=Column(JSONText(), nullable=True, default=None), description="图知识数据")
    bailian_data_id: str = Field(nullable=True, default=None, max_length=128, description="阿里云百炼知识库ID")
    bailian_index_id: str = Field(nullable=True, default=None, max_length=128, description="阿里云百炼向量库ID")
    is_del: bool = Field(nullable=False, default=False, description="是否删除")
    create_uid: str = Field(nullable=True, default=None, max_length=64, description="创建人uid")
    content_type: str = Field(nullable=True, default=None, max_length=64, description="内容类型")
    content_document: str = Field(nullable=True, default=None, sa_type=Text, description="知识内容文本")
    longtime_valid: bool = Field(default=False, description="是否长期有效，默认不是")
    tags: Optional[List[str]] = Field(sa_column=Column(JSONText(), nullable=True, default=None))
