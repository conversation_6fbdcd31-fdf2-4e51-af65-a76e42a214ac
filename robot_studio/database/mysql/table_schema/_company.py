from datetime import datetime
from typing import Optional

from sqlmodel import SQLModel, Field


class CompanyDO(SQLModel, table=True):
    """公司信息表"""

    __tablename__ = "company"
    id: Optional[int] = Field(default=None, primary_key=True, description="主键")
    gmt_create: datetime = Field(
        nullable=False, default_factory=lambda: datetime.now(), description="创建时间"
    )
    gmt_modified: datetime = Field(
        nullable=False, default_factory=lambda: datetime.now(), description="修改时间"
    )
    cid: str | None = Field(nullable=False, max_length=20, description="公司UID")
    name: str | None = Field(nullable=False, max_length=30, description="公司名称")
    abbr: str | None = Field(nullable=False, max_length=20, description="公司缩写")
    logo: str | None = Field(
        nullable=True, default=None, max_length=200, description="公司LogoUrl"
    )
    industry: str | None = Field(nullable=False, max_length=30, description="归属行业")
    company_type: str | None = Field(
        nullable=True, default=None, max_length=20, description="公司类型"
    )
    is_del: bool = Field(nullable=False, default=False, description="是否删除")
