from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Text
from sqlmodel import SQLModel, Field



class ComponentSessionDO(SQLModel, table=True):
    """组件会话表"""
    __tablename__ = 'component_session'
    
    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")
    session_id: Optional[str] = Field(default=None, max_length=64, description="会话唯一标识")
    user_id: Optional[str] = Field(default=None, max_length=64, description="用户ID")
    title: Optional[str] = Field(default=None, max_length=200, description="会话标题")
    status: Optional[int] = Field(default=None, description="会话状态:1-活跃,2-归档,3-删除")
    component_config: Optional[str] = Field(default=None, sa_type=Text, description="组件执行配置")
    message_count: Optional[int] = Field(default=0, description="消息数量")
    artifact_count: Optional[int] = Field(default=0, description="产物数量")
    last_message_time: Optional[datetime] = Field(default=None, description="最后消息时间")
    is_deleted: Optional[bool] = Field(default=False, description="是否删除:0-否,1-是")
    gmt_create: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    gmt_modified: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(),
        description="修改时间"
    )
    session_metadata: Optional[str] = Field(default=None, sa_type=Text, description="会话元数据")
