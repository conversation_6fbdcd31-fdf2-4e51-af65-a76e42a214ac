from datetime import datetime
from typing import Optional, Sequence

from sqlmodel import Session, select, and_, func, desc

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema._messages import MessagesDO, MessageStatus, DeleteStatus
from robot_studio.utils.uuid import build_uuid, DataType


class MessagesRepository:
    """对话消息数据仓库类"""
    
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_message(self, message: MessagesDO) -> MessagesDO:
        """
        创建消息记录
        Args:
            message: 消息信息

        Returns:
            MessagesDO: 创建的消息记录
        """
        # 生成消息ID
        if message.message_id is None:
            message.message_id = build_uuid(DataType.MESSAGE)
            
        with Session(self._engine) as session:
            session.add(message)
            session.commit()
            session.refresh(message)
        return message

    @db_handler
    def get_message_by_id(self, message_id: str) -> Optional[MessagesDO]:
        """
        根据消息ID查询消息信息
        Args:
            message_id: 消息ID

        Returns:
            Optional[MessagesDO]: 消息信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.message_id == message_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_message_by_db_id(self, db_id: int) -> Optional[MessagesDO]:
        """
        根据数据库ID查询消息信息
        Args:
            db_id: 数据库主键ID

        Returns:
            Optional[MessagesDO]: 消息信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.id == db_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_messages_by_session_id(self, session_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[MessagesDO]:
        """
        根据会话ID查询消息列表
        Args:
            session_id: 会话ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[MessagesDO]: 消息列表
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.session_id == session_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED
            ).order_by(MessagesDO.gmt_create.asc())
            
            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_messages_by_parent_id(self, parent_message_id: str) -> Sequence[MessagesDO]:
        """
        根据父消息ID查询子消息列表
        Args:
            parent_message_id: 父消息ID

        Returns:
            Sequence[MessagesDO]: 子消息列表
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.parent_message_id == parent_message_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED
            ).order_by(MessagesDO.gmt_create.asc())
            
            return session.exec(statement).all()

    @db_handler
    def search_messages(self, session_id: Optional[str] = None, keyword: Optional[str] = None, 
                       model_info: Optional[str] = None, status: Optional[int] = None,
                       start_time: Optional[datetime] = None, end_time: Optional[datetime] = None,
                       limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[MessagesDO]:
        """
        搜索消息
        Args:
            session_id: 会话ID
            keyword: 内容关键词
            model_info: 模型信息
            status: 消息状态
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[MessagesDO]: 消息列表
        """
        with Session(self._engine) as session:
            conditions = [
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED
            ]
            
            if session_id:
                conditions.append(MessagesDO.session_id == session_id)
            if keyword:
                conditions.append(MessagesDO.contents.contains(keyword))
            if model_info:
                conditions.append(MessagesDO.model_info == model_info)
            if status is not None:
                conditions.append(MessagesDO.status == status)
            if start_time:
                conditions.append(MessagesDO.gmt_create >= start_time)
            if end_time:
                conditions.append(MessagesDO.gmt_create <= end_time)
            
            statement = select(MessagesDO).where(and_(*conditions)).order_by(desc(MessagesDO.gmt_create))
            
            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def update_message(self, db_id: int, update_data: dict) -> Optional[MessagesDO]:
        """
        更新消息信息
        Args:
            db_id: 数据库主键ID
            update_data: 更新数据字典

        Returns:
            Optional[MessagesDO]: 更新后的消息信息
        """
        message = self.get_message_by_db_id(db_id)
        if not message:
            return None
            
        # 更新消息属性
        for key, value in update_data.items():
            if hasattr(message, key):
                setattr(message, key, value)
                
        message.gmt_modified = datetime.now()
        
        with Session(self._engine) as session:
            session.add(message)
            session.commit()
            session.refresh(message)
        return message

    @db_handler
    def update_message_status(self, message_id: str, status: int) -> bool:
        """
        更新消息状态
        Args:
            message_id: 消息ID
            status: 新状态

        Returns:
            bool: 更新是否成功
        """
        message = self.get_message_by_id(message_id)
        if not message:
            return False
            
        return self.update_message(message.id, {"status": status}) is not None

    @db_handler
    def update_artifact_count(self, message_id: str, count: int) -> bool:
        """
        更新消息关联的产物数量
        Args:
            message_id: 消息ID
            count: 产物数量

        Returns:
            bool: 更新是否成功
        """
        message = self.get_message_by_id(message_id)
        if not message:
            return False
            
        return self.update_message(message.id, {"artifact_count": count}) is not None

    @db_handler
    def increment_artifact_count(self, message_id: str, increment: int = 1) -> bool:
        """
        增加产物数量
        Args:
            message_id: 消息ID
            increment: 增加数量，默认为1

        Returns:
            bool: 更新是否成功
        """
        message = self.get_message_by_id(message_id)
        if not message:
            return False
            
        new_count = (message.artifact_count or 0) + increment
        return self.update_message(message.id, {"artifact_count": new_count}) is not None

    @db_handler
    def delete_message(self, message_id: str) -> bool:
        """
        软删除消息
        Args:
            message_id: 消息ID

        Returns:
            bool: 删除是否成功
        """
        message = self.get_message_by_id(message_id)
        if not message:
            return True  # 已经不存在，视为删除成功
            
        update_data = {
            "is_deleted": DeleteStatus.DELETED,
            "status": MessageStatus.DELETED
        }
        return self.update_message(message.id, update_data) is not None

    @db_handler
    def get_session_message_count(self, session_id: str) -> int:
        """
        获取会话的消息数量
        Args:
            session_id: 会话ID

        Returns:
            int: 消息数量
        """
        with Session(self._engine) as session:
            statement = select(func.count(MessagesDO.id)).where(
                MessagesDO.session_id == session_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED
            )
            return session.exec(statement).one()

    @db_handler
    def get_session_latest_message(self, session_id: str) -> Optional[MessagesDO]:
        """
        获取会话的最新消息
        Args:
            session_id: 会话ID

        Returns:
            Optional[MessagesDO]: 最新消息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.session_id == session_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED
            ).order_by(desc(MessagesDO.gmt_create)).limit(1)
            
            return session.exec(statement).one_or_none()

    @db_handler
    def get_messages_by_model(self, model_info: str, limit: Optional[int] = None) -> Sequence[MessagesDO]:
        """
        根据模型信息查询消息列表
        Args:
            model_info: 模型信息
            limit: 限制返回数量

        Returns:
            Sequence[MessagesDO]: 消息列表
        """
        with Session(self._engine) as session:
            statement = select(MessagesDO).where(
                MessagesDO.model_info == model_info,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED
            ).order_by(desc(MessagesDO.gmt_create))
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_messages_with_artifacts(self, session_id: Optional[str] = None, limit: Optional[int] = None) -> Sequence[MessagesDO]:
        """
        获取有产物的消息列表
        Args:
            session_id: 会话ID，可选
            limit: 限制返回数量

        Returns:
            Sequence[MessagesDO]: 有产物的消息列表
        """
        with Session(self._engine) as session:
            conditions = [
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED,
                MessagesDO.artifact_count > 0
            ]
            
            if session_id:
                conditions.append(MessagesDO.session_id == session_id)
            
            statement = select(MessagesDO).where(and_(*conditions)).order_by(desc(MessagesDO.gmt_create))
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_session_token_stats(self, session_id: str) -> dict:
        """
        获取会话的token统计信息
        Args:
            session_id: 会话ID

        Returns:
            dict: token统计信息
        """
        with Session(self._engine) as session:
            statement = select(
                func.sum(MessagesDO.token_count).label('total_tokens'),
                func.count(MessagesDO.id).label('message_count'),
                func.avg(MessagesDO.token_count).label('avg_tokens')
            ).where(
                MessagesDO.session_id == session_id,
                MessagesDO.is_deleted == DeleteStatus.NOT_DELETED,
                MessagesDO.status != MessageStatus.DELETED,
                MessagesDO.token_count.is_not(None)
            )
            
            result = session.exec(statement).one()
            
            return {
                'total_tokens': result.total_tokens or 0,
                'message_count': result.message_count or 0,
                'avg_tokens': float(result.avg_tokens or 0)
            }