import logging
from datetime import datetime
from typing import Sequence, Dict, Any

from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine
from robot_studio.database.mysql.table_schema import ResourceGroupDO
from robot_studio.utils.uuid import build_uuid, DataType

logger = logging.getLogger(__name__)


class ResourceGroupRepository:
    """资源分组仓库，提供对资源分组的CRUD操作"""

    def __init__(self):
        self._engine = engine

    def create_group(self, group: ResourceGroupDO) -> ResourceGroupDO:
        """
        创建资源分组
        Args:
            group: 资源分组信息

        Returns:
            ResourceGroup: 创建后的资源分组信息
        """
        # 生成分组ID
        if not group.group_id:
            group.group_id = build_uuid(DataType.GROUP)

        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(group)
            session.commit()
            session.refresh(group)
        return group

    def get_group_by_id(self, group_id: str) -> ResourceGroupDO | None:
        """
        根据分组ID查询资源分组
        Args:
            group_id: 分组ID

        Returns:
            ResourceGroupDO | None: 资源分组信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ResourceGroupDO).where(
                ResourceGroupDO.group_id == group_id,
                ResourceGroupDO.is_deleted == False
            )
            return session.exec(statement).one_or_none()

    def get_group_by_id_ignore_deleted(self, group_id: str) -> ResourceGroupDO | None:
        """
        根据分组ID查询资源分组，忽略删除状态
        Args:
            group_id: 分组ID

        Returns:
            ResourceGroupDO | None: 资源分组信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ResourceGroupDO).where(
                ResourceGroupDO.group_id == group_id
            )
            return session.exec(statement).one_or_none()

    def get_groups_by_cid(self, cid: str) -> Sequence[ResourceGroupDO]:
        """
        根据企业ID查询所有资源分组
        Args:
            cid: 企业ID

        Returns:
            Sequence[ResourceGroupDO]: 资源分组列表
        """
        with Session(self._engine) as session:
            statement = select(ResourceGroupDO).where(
                ResourceGroupDO.cid == cid,
                ResourceGroupDO.is_deleted == False
            )
            return session.exec(statement).all()

    def get_all_groups_by_type(self, group_type, cid) -> Sequence[ResourceGroupDO]:
        """
        获取所有素材或知识组信息
        group_type 组类别（素材 | 知识）
        cid 企业ID

        Returns:
            Sequence[ResourceGroupDO]: 组信息列表
        """
        with Session(self._engine) as session:
            statement = select(ResourceGroupDO).where(ResourceGroupDO.group_type == group_type,
                                                      ResourceGroupDO.cid == cid,
                                                      ResourceGroupDO.is_deleted == False)
            return session.exec(statement).all()

    def get_groups_by_sub_group_type(self,
                                     cid,
                                     group_type,
                                     sub_group_type) -> Sequence[ResourceGroupDO]:
        """
        获取指定企业和类型的全部素材组信息
        cid 企业ID
        group_type 组类别（素材 | 知识）
        sub_group_type 子类别 | 投放平台

        Returns:
            Sequence[ResourceGroupDO]: 组信息列表
        """
        with Session(self._engine) as session:
            statement = select(ResourceGroupDO).where(ResourceGroupDO.cid == cid,
                                                      ResourceGroupDO.group_type == group_type,
                                                      ResourceGroupDO.sub_group_type == sub_group_type,
                                                      ResourceGroupDO.is_deleted == False)
            return session.exec(statement).all()

    def update_group(self, group_id: str, update_data: Dict[str, Any]) -> ResourceGroupDO | None:
        """
        更新资源分组
        Args:
            group_id: 分组ID
            update_data: 更新数据字典

        Returns:
            ResourceGroupDO | None: 更新后的资源分组信息，失败则返回None
        """
        group = self.get_group_by_id(group_id)
        if not group:
            logger.error(f"待更新的资源分组不存在，group_id={group_id}")
            return None

        try:
            # 更新资源分组属性
            for key, value in update_data.items():
                if hasattr(group, key):
                    setattr(group, key, value)

            # 更新修改时间
            group.gmt_modified = datetime.now()

            # 保存到数据库
            with Session(self._engine) as session:
                session.add(group)
                session.commit()
                session.refresh(group)
            return group
        except Exception as e:
            logger.error(f"更新资源分组失败，group_id={group_id}: {e}")
            return None

    def delete_group(self, group_id: str) -> bool:
        """
        删除资源分组（软删除）
        Args:
            group_id: 分组ID

        Returns:
            bool: 删除结果
        """
        try:
            group = self.get_group_by_id(group_id)
            if not group:
                logger.warning(f"待删除的资源分组不存在，group_id={group_id}")
                return False

            # 软删除
            update_res = self.update_group(group_id, {"is_deleted": True})
            return update_res is not None
        except Exception as e:
            logger.error(f"软删除资源分组失败，group_id={group_id}: {e}")
            return False
