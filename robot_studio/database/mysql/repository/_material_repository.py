import logging
from datetime import datetime
from typing import Sequence

from sqlmodel import Session
from sqlmodel import select, func

from robot_studio.database.mysql.db_engine import engine
from robot_studio.database.mysql.table_schema import MaterialDO
from robot_studio.utils.uuid import build_uuid, DataType

logger = logging.getLogger(__name__)


class MaterialRepository:
    def __init__(self):
        self._engine = engine

    def create_material(self, material: MaterialDO) -> MaterialDO:
        """
        创建素材信息
        Args:
            material: 素材实例信息

        Returns:

        """
        # 生成公司ID
        material.mid = build_uuid(DataType.MATERIAL)
        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(material)
            session.commit()
            session.refresh(material)
        return material

    def get_material_by_mid(self, mid: str) -> MaterialDO | None:
        """
        根据MID查询素材信息
        Args:
            mid:素材ID

        Returns:
            MaterialDO: 素材实例信息

        """
        with Session(self._engine) as session:
            statement = select(MaterialDO).where(MaterialDO.mid == mid, MaterialDO.is_del.is_(False))
            return session.exec(statement).one_or_none()

    def get_material_by_group_id(self, group_id: str) -> Sequence[MaterialDO]:
        """
        获取同一分组下的素材信息
        group_id: 素材组id

        Returns:
            Sequence[MaterialDO]: 同一分组下的素材信息

        """
        with Session(self._engine) as session:
            statement = select(MaterialDO).where(MaterialDO.group_id == group_id, MaterialDO.is_del.is_(False))
            return session.exec(statement).all()

    def get_material_valid_by_group_id(self, group_id: str) -> Sequence[MaterialDO]:
        """
        获取固定分组下仍在有效期的素材信息
        group_id: 素材组id

        Returns:
            Sequence[MaterialDO]: 同一分组下仍在有效期的素材信息
        """
        with Session(self._engine) as session:
            now_time = datetime.now()
            statement = select(MaterialDO).where(MaterialDO.group_id == group_id,
                                                 MaterialDO.invalid_date > now_time,
                                                 MaterialDO.is_del.is_(False),
                                                 )
            return session.exec(statement).all()

    def count_valid_materials_by_group(self, group_id: str) -> int:
        """
        获取固定分组下仍在有效期的素材数量
        group_id: 素材组id

        Returns:
            当前分组下仍在有效期的素材数量
        """
        with Session(self._engine) as session:
            statement = select(func.count(MaterialDO.mid)).where(
                MaterialDO.group_id == group_id,
                MaterialDO.is_del.is_(False)
            )
            return session.exec(statement).one_or_none()

    def update_material(self, mid: str, update_data: dict) -> MaterialDO | None:
        """
        更新公司数据
        Args:
            mid: 素材MID
            update_data:更新数据，字典类型

        Returns:
            MaterialDO | None: 更新后的素材信息

        """
        mat = self.get_material_by_mid(mid)
        assert mat is not None, f"待更新的素材不存在，mid={mid}"
        try:
            # 更新素材属性
            for key, value in update_data.items():
                if hasattr(mat, key):
                    setattr(mat, key, value)
            mat.gmt_modified = datetime.now()
            with Session(self._engine) as session:
                session.add(mat)
                session.commit()
                session.refresh(mat)
            return mat
        except Exception as e:
            logger.error(f"更新素材数据失败，mid={mid}: {e}")
            return None

    def delete_material(self, mid: str) -> bool:
        """
        删除数据，软删除
        Args:
            mid: 待删除的素材MID

        Returns:
            bool: 删除结果

        """
        try:
            mat = self.get_material_by_mid(mid)
            if not mat:
                return True
            if mat.is_del:
                return True
            update_res = self.update_material(mid, {"is_del": True})
            return update_res is not None
        except Exception as e:
            logger.error(f"软删除公司数据失败，cid={mid}: {e}")
            return False

    def get_online_media_materials_by_cid(self, cid: str) -> Sequence[MaterialDO]:
        """
        查询指定企业下所有在线状态的图片和视频素材
        Args:
            cid: 企业ID

        Returns:
            Sequence[MaterialDO]: 在线状态的图片和视频素材列表
        """
        with Session(self._engine) as session:
            from robot_studio.database.mysql.table_schema import ResourceGroupDO
            
            # 直接JOIN查询，无需子查询
            statement = (
                select(MaterialDO)
                .join(ResourceGroupDO, MaterialDO.group_id == ResourceGroupDO.group_id)
                .where(
                    ResourceGroupDO.cid == cid,
                    ResourceGroupDO.is_deleted == False,
                    MaterialDO.status == "ONLINE",
                    MaterialDO.is_del == False
                )
            )
            
            return session.exec(statement).all()
