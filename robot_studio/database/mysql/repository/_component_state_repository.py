import logging
from datetime import datetime

from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema import ComponentStateDO

logger = logging.getLogger(__name__)


class ComponentStateRepository:
    """组件状态仓库，提供对组件状态的CRUD操作"""

    def __init__(self):
        self._engine = engine

    @db_handler
    def create_state(self, component_state: ComponentStateDO) -> ComponentStateDO:
        """
        纯粹创建组件状态
        Args:
            component_state: 组件状态信息

        Returns:
            ComponentStateDO: 创建后的组件状态信息
        """
        with Session(self._engine) as session:
            session.add(component_state)
            session.commit()
            session.refresh(component_state)
        return component_state

    @db_handler
    def create_or_update_state(self, component_state: ComponentStateDO) -> ComponentStateDO:
        """
        创建组件状态，如果已存在则更新
        Args:
            component_state: 组件状态信息

        Returns:
            ComponentStateDO: 创建或更新后的组件状态信息
        """
        # 先查询是否已存在
        existing_state = self.query_state(
            component_state.session_id,
            component_state.component_id,
            component_state.version
        )

        if existing_state:
            # 如果存在，则更新state字段
            return self.update_state(
                component_state.session_id,
                component_state.component_id,
                component_state.version,
                component_state.state
            )
        else:
            return self.create_state(component_state)

    @db_handler
    def query_state(self, session_id: str, component_id: str, version: int) -> ComponentStateDO | None:
        """
        根据session_id, component_id, version查询组件状态
        Args:
            session_id: 会话ID
            component_id: 组件ID
            version: 版本号

        Returns:
            ComponentStateDO | None: 组件状态信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentStateDO).where(
                ComponentStateDO.session_id == session_id,
                ComponentStateDO.component_id == component_id,
                ComponentStateDO.version == version,
                ComponentStateDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def update_state(
            self,
            session_id: str,
            component_id: str,
            version: int,
            state: dict | None
    ) -> ComponentStateDO | None:
        """
        根据session_id, component_id, version更新组件状态
        Args:
            session_id: 会话ID
            component_id: 组件ID
            version: 版本号
            state: 组件状态数据

        Returns:
            ComponentStateDO | None: 更新后的组件状态信息，失败则返回None
        """
        component_state = self.query_state(session_id, component_id, version)
        if not component_state:
            logger.error(
                f"待更新的组件状态不存在，session_id={session_id}, component_id={component_id}, version={version}")
            return None

        try:
            # 更新state字段
            component_state.state = state

            # 更新修改时间
            component_state.gmt_modified = datetime.now()

            # 保存到数据库
            with Session(self._engine) as session:
                session.add(component_state)
                session.commit()
                session.refresh(component_state)
            return component_state
        except Exception as e:
            logger.error(
                f"更新组件状态失败，session_id={session_id}, component_id={component_id}, version={version}: {e}")
            return None

    @db_handler
    def delete_state(self, session_id: str, component_id: str, version: int) -> bool:
        """
        根据session_id, component_id, version软删除组件状态
        Args:
            session_id: 会话ID
            component_id: 组件ID
            version: 版本号

        Returns:
            bool: 删除结果
        """
        try:
            component_state = self.query_state(session_id, component_id, version)
            if not component_state:
                logger.warning(
                    f"待删除的组件状态不存在，session_id={session_id}, component_id={component_id}, version={version}")
                return False

            # 软删除
            component_state.is_del = True
            component_state.gmt_modified = datetime.now()

            with Session(self._engine) as session:
                session.add(component_state)
                session.commit()
            return True
        except Exception as e:
            logger.error(
                f"软删除组件状态失败，session_id={session_id}, component_id={component_id}, version={version}: {e}")
            return False
