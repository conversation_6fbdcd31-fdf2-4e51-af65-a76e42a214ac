from datetime import datetime
from typing import Optional, Sequence

from sqlmodel import Session, select, and_, or_

from robot_studio.component.session.model import SessionStatus
from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema._component_session import ComponentSessionDO
from robot_studio.utils.uuid import build_uuid, DataType


class ComponentSessionRepository:
    """组件会话数据仓库类"""
    
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_session(self, session: ComponentSessionDO) -> ComponentSessionDO:
        """
        创建会话记录
        Args:
            session: 会话信息

        Returns:
            ComponentSessionDO: 创建的会话记录
        """
        # 生成会话ID
        if session.session_id is None:
            session.session_id = build_uuid(DataType.SESSION)
            
        with Session(self._engine) as session_db:
            session_db.add(session)
            session_db.commit()
            session_db.refresh(session)
        return session

    @db_handler
    def get_session_by_id(self, session_id: str) -> Optional[ComponentSessionDO]:
        """
        根据会话ID查询会话信息
        Args:
            session_id: 会话ID

        Returns:
            Optional[ComponentSessionDO]: 会话信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.session_id == session_id,
                ComponentSessionDO.is_deleted == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_session_by_db_id(self, db_id: int) -> Optional[ComponentSessionDO]:
        """
        根据数据库ID查询会话信息
        Args:
            db_id: 数据库主键ID

        Returns:
            Optional[ComponentSessionDO]: 会话信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.id == db_id,
                ComponentSessionDO.is_deleted == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_sessions_by_user_id(self, user_id: str, limit: Optional[int] = None) -> Sequence[ComponentSessionDO]:
        """
        根据用户ID查询会话列表
        Args:
            user_id: 用户ID
            limit: 限制返回数量

        Returns:
            Sequence[ComponentSessionDO]: 会话列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.user_id == user_id,
                ComponentSessionDO.is_deleted == False
            ).order_by(ComponentSessionDO.last_message_time.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_active_sessions_by_user_id(self, user_id: str) -> Sequence[ComponentSessionDO]:
        """
        根据用户ID查询活跃会话列表
        Args:
            user_id: 用户ID

        Returns:
            Sequence[ComponentSessionDO]: 活跃会话列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.user_id == user_id,
                ComponentSessionDO.status == SessionStatus.ACTIVE,
                ComponentSessionDO.is_deleted == False
            ).order_by(ComponentSessionDO.last_message_time.desc())
            
            return session.exec(statement).all()

    @db_handler
    def get_sessions_by_status(self, status: int, limit: Optional[int] = None) -> Sequence[ComponentSessionDO]:
        """
        根据状态查询会话列表
        Args:
            status: 会话状态
            limit: 限制返回数量

        Returns:
            Sequence[ComponentSessionDO]: 会话列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.status == status,
                ComponentSessionDO.is_deleted == False
            ).order_by(ComponentSessionDO.gmt_create.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def update_session(self, db_id: int, update_data: dict) -> Optional[ComponentSessionDO]:
        """
        更新会话信息
        Args:
            db_id: 数据库主键ID
            update_data: 更新数据字典

        Returns:
            Optional[ComponentSessionDO]: 更新后的会话信息
        """
        session_obj = self.get_session_by_db_id(db_id)
        if not session_obj:
            return None
            
        # 更新会话属性
        for key, value in update_data.items():
            if hasattr(session_obj, key):
                setattr(session_obj, key, value)
                
        session_obj.gmt_modified = datetime.now()
        
        with Session(self._engine) as session:
            session.add(session_obj)
            session.commit()
            session.refresh(session_obj)
        return session_obj

    @db_handler
    def update_session_status(self, session_id: str, status: int) -> bool:
        """
        更新会话状态
        Args:
            session_id: 会话ID
            status: 新状态

        Returns:
            bool: 更新是否成功
        """
        session_obj = self.get_session_by_id(session_id)
        if not session_obj:
            return False
            
        return self.update_session(session_obj.id, {"status": status}) is not None

    @db_handler
    def update_last_message_time(self, session_id: str, message_time: Optional[datetime] = None) -> bool:
        """
        更新最后消息时间
        Args:
            session_id: 会话ID
            message_time: 消息时间，默认为当前时间

        Returns:
            bool: 更新是否成功
        """
        if message_time is None:
            message_time = datetime.now()
            
        session_obj = self.get_session_by_id(session_id)
        if not session_obj:
            return False
            
        return self.update_session(session_obj.id, {"last_message_time": message_time}) is not None

    @db_handler
    def increment_message_count(self, session_id: str, increment: int = 1) -> bool:
        """
        增加消息数量
        Args:
            session_id: 会话ID
            increment: 增加数量，默认为1

        Returns:
            bool: 更新是否成功
        """
        session_obj = self.get_session_by_id(session_id)
        if not session_obj:
            return False
            
        new_count = (session_obj.message_count or 0) + increment
        update_data = {
            "message_count": new_count,
            "last_message_time": datetime.now()
        }
        return self.update_session(session_obj.id, update_data) is not None

    @db_handler
    def increment_artifact_count(self, session_id: str, increment: int = 1) -> bool:
        """
        增加产物数量
        Args:
            session_id: 会话ID
            increment: 增加数量，默认为1

        Returns:
            bool: 更新是否成功
        """
        session_obj = self.get_session_by_id(session_id)
        if not session_obj:
            return False
            
        new_count = (session_obj.artifact_count or 0) + increment
        return self.update_session(session_obj.id, {"artifact_count": new_count}) is not None

    @db_handler
    def archive_session(self, session_id: str) -> bool:
        """
        归档会话
        Args:
            session_id: 会话ID

        Returns:
            bool: 归档是否成功
        """
        return self.update_session_status(session_id, SessionStatus.ARCHIVED)

    @db_handler
    def delete_session(self, session_id: str) -> bool:
        """
        软删除会话
        Args:
            session_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        session_obj = self.get_session_by_id(session_id)
        if not session_obj:
            return True  # 已经不存在，视为删除成功
            
        update_data = {
            "is_deleted": True,
            "status": SessionStatus.DELETED
        }
        return self.update_session(session_obj.id, update_data) is not None

    @db_handler
    def get_recent_sessions(self, days: int = 7, limit: Optional[int] = None) -> Sequence[ComponentSessionDO]:
        """
        获取最近几天的会话
        Args:
            days: 天数，默认7天
            limit: 限制返回数量

        Returns:
            Sequence[ComponentSessionDO]: 会话列表
        """
        from datetime import timedelta
        start_date = datetime.now() - timedelta(days=days)
        
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.gmt_create >= start_date,
                ComponentSessionDO.is_deleted == False
            ).order_by(ComponentSessionDO.gmt_create.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def count_sessions_by_user(self, user_id: str) -> int:
        """
        统计用户的会话数量
        Args:
            user_id: 用户ID

        Returns:
            int: 会话数量
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.user_id == user_id,
                ComponentSessionDO.is_deleted == False
            )
            return len(session.exec(statement).all())

    @db_handler
    def search_sessions_by_title(self, title_keyword: str, limit: Optional[int] = None) -> Sequence[ComponentSessionDO]:
        """
        根据标题关键词搜索会话
        Args:
            title_keyword: 标题关键词
            limit: 限制返回数量

        Returns:
            Sequence[ComponentSessionDO]: 会话列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentSessionDO).where(
                ComponentSessionDO.title.contains(title_keyword),
                ComponentSessionDO.is_deleted == False
            ).order_by(ComponentSessionDO.last_message_time.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()