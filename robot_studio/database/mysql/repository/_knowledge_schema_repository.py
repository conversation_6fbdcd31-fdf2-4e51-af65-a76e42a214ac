from datetime import datetime
from typing import List
from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema import KnowledgeSchemaDO
from robot_studio.utils.uuid import build_uuid, DataType


class KnowledgeSchemaRepository:
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_schema(self, schema: KnowledgeSchemaDO) -> KnowledgeSchemaDO:
        """
        创建知识Schema
        Args:
            schema: 知识Schema信息

        Returns:
            KnowledgeSchemaDO: 创建后的知识Schema
        """
        # 生成Schema ID
        if schema.schema_id is None:
            schema.schema_id = build_uuid(DataType.KNOWLEDGE_SCHEMA)
        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(schema)
            session.commit()
            session.refresh(schema)
        return schema

    @db_handler
    def get_schemas_by_cid(self, cid: str) -> List[KnowledgeSchemaDO]:
        """
        根据企业ID查询知识Schema
        Args:
            cid: 企业ID

        Returns:
            list[KnowledgeSchemaDO]: 知识Schema列表
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeSchemaDO).where(
                KnowledgeSchemaDO.cid == cid
            )
            results = session.exec(statement).all()
            return results

    @db_handler
    def get_schemas_by_sid(self, sid: str) -> KnowledgeSchemaDO | None:
        """
        根据schema_id查询schema内容
        Args:
            sid:

        Returns:

        """
        with Session(self._engine) as session:
            statement = select(KnowledgeSchemaDO).where(
                KnowledgeSchemaDO.schema_id == sid
            )
            result = session.exec(statement).one_or_none()
            return result

    @db_handler
    def delete_schema_by_sid(self, sid: str) -> bool:
        """
        根据schema_id物理删除schema
        Args:
            sid: schema_id

        Returns:
            bool: 删除结果
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeSchemaDO).where(
                KnowledgeSchemaDO.schema_id == sid
            )
            schema = session.exec(statement).one_or_none()
            if schema:
                session.delete(schema)
                session.commit()
                return True
            return False

    @db_handler
    def update_schema_by_sid(self, sid: str, update_schema: KnowledgeSchemaDO) -> KnowledgeSchemaDO | None:
        """
        根据schema_id更新schema信息
        Args:
            sid: schema_id
            update_schema: 更新的schema信息

        Returns:
            KnowledgeSchemaDO | None: 更新后的schema信息，如果不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeSchemaDO).where(
                KnowledgeSchemaDO.schema_id == sid
            )
            existing_schema = session.exec(statement).one_or_none()
            if existing_schema:
                # 更新字段
                existing_schema.type = update_schema.type
                existing_schema.name = update_schema.name
                existing_schema.desc = update_schema.desc
                existing_schema.config = update_schema.config
                existing_schema.gmt_modified = datetime.now()
                
                session.add(existing_schema)
                session.commit()
                session.refresh(existing_schema)
                return existing_schema
            return None
