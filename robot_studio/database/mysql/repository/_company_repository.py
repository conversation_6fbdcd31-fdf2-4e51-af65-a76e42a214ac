import logging
from datetime import datetime
from typing import Sequence

from sqlmodel import Session
from sqlmodel import select

from robot_studio.database.mysql.db_engine import engine
from robot_studio.database.mysql.table_schema import CompanyDO
from robot_studio.utils.uuid import build_uuid, DataType

logger = logging.getLogger(__name__)


class CompanyRepository:
    def __init__(self):
        self._engine = engine

    def create_company(self, company: CompanyDO) -> CompanyDO:
        """
        创建公司信息
        Args:
            company: 公司信息

        Returns:

        """
        # 生成公司ID
        company.cid = build_uuid(DataType.COMPANY)
        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(company)
            session.commit()
            session.refresh(company)
        return company

    def get_company_by_cid(self, cid: str) -> CompanyDO | None:
        """
        根据CID查询公司信息
        Args:
            cid:公司ID

        Returns:
            CompanyDO: 公司信息

        """
        with Session(self._engine) as session:
            statement = select(CompanyDO).where(CompanyDO.cid == cid, CompanyDO.is_del == False)
            return session.exec(statement).one_or_none()

    def get_all_companies(self) -> Sequence[CompanyDO]:
        """
        获取所有公司信息
        Returns:
            Sequence[CompanyDO]: 所有公司类型

        """

        with Session(self._engine) as session:
            statement = select(CompanyDO).where(CompanyDO.is_del == False)
            return session.exec(statement).all()

    def get_virtual_company_by_industry(self, industry: str) -> CompanyDO | None:
        """
        根据行业获取虚拟企业模型
        Args:
            industry: 行业名称

        Returns:
            CompanyDO | None: 虚拟企业模型

        """
        with Session(self._engine) as session:
            statement = select(CompanyDO).where(
                CompanyDO.industry == industry,
                CompanyDO.company_type == "VIRTUAL_INDUSTRY",
                CompanyDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    def get_common_virtual_company(self) -> CompanyDO | None:
        """
        获取通用虚拟企业模型
        Returns:
            CompanyDO | None: 通用虚拟企业模型

        """
        with Session(self._engine) as session:
            statement = select(CompanyDO).where(
                CompanyDO.company_type == "VIRTUAL_COMMON",
                CompanyDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    def update_company(self, company_id: str, update_data: dict) -> CompanyDO | None:
        """
        更新公司数据
        Args:
            company_id: 公司CID
            update_data:更新数据字典

        Returns:
            CompanyDO | None: 更新后的公司信息

        """
        cmp = self.get_company_by_cid(company_id)
        assert cmp is not None, f"待更新的公司不存在，company_id={company_id}"
        try:
            # 更新公司属性
            for key, value in update_data.items():
                if hasattr(cmp, key):
                    setattr(cmp, key, value)
            cmp.gmt_modified = datetime.now()
            with Session(self._engine) as session:
                session.add(cmp)
                session.commit()
                session.refresh(cmp)
            return cmp
        except Exception as e:
            logger.error(f"更新公司数据失败，company_id={company_id}: {e}")
            return None

    def delete_company(self, cid: str) -> bool:
        """
        删除数据，软删除
        Args:
            cid: 待删除的公司CID

        Returns:
            bool: 删除结果

        """
        try:
            cmp = self.get_company_by_cid(cid)
            if not cmp:
                return True
            if cmp.is_del:
                return True
            update_res = self.update_company(cid, {"is_del": True})
            return update_res is not None
        except Exception as e:
            logger.error(f"软删除公司数据失败，cid={cid}: {e}")
            return False
