from datetime import datetime
from itertools import groupby
from operator import attrgetter
from typing import Sequence

from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema import KnowledgeDO
from robot_studio.utils.uuid import build_uuid, DataType


class KnowledgeRepository:
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_knowledge(self, knowledge: KnowledgeDO) -> KnowledgeDO:
        """
        创建知识信息
        Args:
            knowledge: 知识信息

        Returns:

        """
        # 生成知识ID
        if knowledge.knowledge_id is None:
            knowledge.knowledge_id = build_uuid(DataType.KNOWLEDGE)
        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(knowledge)
            session.commit()
            session.refresh(knowledge)
        return knowledge

    @db_handler
    def get_max_version_knowledge_by_kid(self, knowledge_id: str) -> KnowledgeDO | None:
        """
        根据知识ID查询知识信息
        Args:
            knowledge_id: 知识ID

        Returns:
            KnowledgeDO | None: 知识信息，不存在则返回None,返回最新版本的知识
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.knowledge_id == knowledge_id,
                KnowledgeDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return None

            # 返回version字段最大的记录，处理version可能为空的情况
            return max(results, key=lambda x: x.version if x.version is not None else -1)

    @db_handler
    def get_online_knowledge_by_kid(self, knowledge_id: str) -> KnowledgeDO | None:
        """
        根据知识ID查询线上版本的知识信息
        Args:
            knowledge_id: 知识ID

        Returns:
            KnowledgeDO | None: 线上版本的知识信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.knowledge_id == knowledge_id,
                KnowledgeDO.status == 'ONLINE',
                KnowledgeDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return None

            # 返回version字段最大的记录，处理version可能为空的情况
            return max(results, key=lambda x: x.version if x.version is not None else -1)

    @db_handler
    def get_invalid_knowledge_need_clean(self, source: str) -> Sequence[KnowledgeDO] | None:
        """
        获取百炼ID非空、不在有效期的消息
        Args:
            source: 知识数据来源

        Returns:
            KnowledgeDO | None: 知识信息，不存在则返回None
        """
        now = datetime.now()
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.source == source,
                KnowledgeDO.bailian_index_id.is_not(None),
                KnowledgeDO.status == 'ONLINE',
                KnowledgeDO.is_del == False,
                (
                    # 未生效的知识和已过期的知识
                        (KnowledgeDO.valid_date.is_not(None) & (KnowledgeDO.valid_date > now)) | (
                        KnowledgeDO.invalid_date.is_not(None) & (KnowledgeDO.invalid_date < now))
                )

            )
            results = session.exec(statement).all()
            return results

    @db_handler
    def get_valid_knowledge_need_sync(self, source: str) -> Sequence[KnowledgeDO] | None:
        """
        获取百炼ID为空、有效期内的知识
        Args:
            source: 知识数据来源

        Returns:
            KnowledgeDO | None: 知识信息，不存在则返回None
        """
        now = datetime.now()
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.source == source,
                KnowledgeDO.bailian_index_id.is_(None),
                KnowledgeDO.status == 'ONLINE',
                KnowledgeDO.is_del == False,
                KnowledgeDO.valid_date.is_not(None),
                KnowledgeDO.valid_date < now,
                (KnowledgeDO.invalid_date.is_(None) | (KnowledgeDO.invalid_date > now)),  # 未过期（失效日期为空或大于当前时间）
            )
            results = session.exec(statement).all()
            return results

    @db_handler
    def get_knowledge_by_kid_version(self, knowledge_id: str, version: int) -> KnowledgeDO | None:
        """
        根据知识ID查询知识信息
        Args:
            version:
            knowledge_id: 知识ID

        Returns:
            KnowledgeDO | None: 知识信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.knowledge_id == knowledge_id,
                KnowledgeDO.is_del == False,
                KnowledgeDO.version == version
            )
            results = session.exec(statement).one_or_none()
            return results

    def get_knowledge_by_id(self, uid: int) -> KnowledgeDO | None:
        """
        根据知识唯一ID查询知识信息
        Args:
            uid:

        Returns:
            KnowledgeDO | None: 知识信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.id == uid,
                KnowledgeDO.is_del == False
            )
            results = session.exec(statement).one_or_none()
            return results

    @db_handler
    def get_knowledge_list_by_gid(self, group_id: str) -> Sequence[KnowledgeDO]:
        """
        根据知识组ID查询知识信息
        Args:
            group_id: 知识组ID

        Returns:
            Sequence[KnowledgeDO]: 知识信息列表
        """
        with Session(self._engine) as session:
            statement = select(KnowledgeDO).where(
                KnowledgeDO.group_id == group_id,
                KnowledgeDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return []

            # 使用字典推导式，按knowledge_id分组并保留version最大的项
            return [
                max(group, key=lambda x: x.version if x.version is not None else -1)
                for _, group in groupby(sorted(results, key=attrgetter('knowledge_id')),
                                        key=attrgetter('knowledge_id'))
            ]

    def update_knowledge(self, uid: int, update_data: dict) -> KnowledgeDO:
        """
        更新知识信息
        Args:
            uid:
            update_data:

        Returns:
            KnowledgeDO: 知识信息
        """
        exist_knowledge = self.get_knowledge_by_id(uid)
        assert exist_knowledge, f"待更新的知识不存在，知识表ID={uid}"
        # 更新知识属性
        for key, value in update_data.items():
            if hasattr(exist_knowledge, key):
                setattr(exist_knowledge, key, value)
        exist_knowledge.gmt_modified = datetime.now()
        with Session(self._engine) as session:
            session.add(exist_knowledge)
            session.commit()
            session.refresh(exist_knowledge)
        return exist_knowledge

    @db_handler
    def del_knowledge(self, uid: int) -> bool:
        """
        删除知识的某个版本
        Args:
            uid: 数据库表ID

        Returns:
            None
        """
        knowledge = self.get_knowledge_by_id(uid)
        if not knowledge:
            return True
        if knowledge.is_del:
            return True
        update_res = self.update_knowledge(uid, {"is_del": True})
        return update_res is not None

    @db_handler
    def get_basic_knowledge_list_by_gid(self, group_id: str):
        """
        根据知识组ID查询知识基础信息（不查content_structure等大字段）
        Args:
            group_id: 知识组ID
        Returns:
            精简知识信息列表
        """
        from itertools import groupby
        from operator import attrgetter
        base_columns = [
            KnowledgeDO.id, KnowledgeDO.knowledge_id, KnowledgeDO.name, KnowledgeDO.desc, KnowledgeDO.source,
            KnowledgeDO.schema_id, KnowledgeDO.valid_date, KnowledgeDO.invalid_date, KnowledgeDO.status,
            KnowledgeDO.version, KnowledgeDO.group_id, KnowledgeDO.rel_material_group_id, KnowledgeDO.content_uuid,
            KnowledgeDO.bailian_data_id, KnowledgeDO.bailian_index_id, KnowledgeDO.gmt_create, KnowledgeDO.gmt_modified,
            KnowledgeDO.create_uid, KnowledgeDO.content_type, KnowledgeDO.longtime_valid, KnowledgeDO.tags
        ]
        with Session(self._engine) as session:
            statement = select(*base_columns).where(
                KnowledgeDO.group_id == group_id,
                KnowledgeDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return []
            grouped = groupby(sorted(results, key=attrgetter('knowledge_id')), key=attrgetter('knowledge_id'))
            latest = [max(list(group), key=lambda x: x.version if x.version is not None else -1) for _, group in grouped]
            latest = sorted(latest, key=lambda d: d.gmt_create, reverse=True)
            return latest

    @db_handler
    def get_online_knowledge_list_by_cid(self, cid: str):
        """
        根据企业ID查询所有在线状态的知识基础信息（不查content_structure等大字段）
        Args:
            cid: 企业ID
        Returns:
            在线知识基础信息列表
        """
        from itertools import groupby
        from operator import attrgetter
        from robot_studio.database.mysql.table_schema import ResourceGroupDO
        
        base_columns = [
            KnowledgeDO.id, KnowledgeDO.knowledge_id, KnowledgeDO.name, KnowledgeDO.desc, KnowledgeDO.source,
            KnowledgeDO.schema_id, KnowledgeDO.valid_date, KnowledgeDO.invalid_date, KnowledgeDO.status,
            KnowledgeDO.version, KnowledgeDO.group_id, KnowledgeDO.rel_material_group_id, KnowledgeDO.content_uuid,
            KnowledgeDO.bailian_data_id, KnowledgeDO.bailian_index_id, KnowledgeDO.gmt_create, KnowledgeDO.gmt_modified,
            KnowledgeDO.create_uid, KnowledgeDO.content_type, KnowledgeDO.longtime_valid, KnowledgeDO.tags
        ]
        
        with Session(self._engine) as session:
            statement = select(*base_columns).join(
                ResourceGroupDO, KnowledgeDO.group_id == ResourceGroupDO.group_id
            ).where(
                ResourceGroupDO.cid == cid,
                KnowledgeDO.status == "ONLINE",
                KnowledgeDO.is_del == False,
                ResourceGroupDO.is_deleted == False
            )
            results = session.exec(statement).all()
            if not results:
                return []
            
            # 按knowledge_id分组，获取每组中版本号最大的记录
            grouped = groupby(sorted(results, key=attrgetter('knowledge_id')), key=attrgetter('knowledge_id'))
            latest = [max(list(group), key=lambda x: x.version if x.version is not None else -1) for _, group in grouped]
            latest = sorted(latest, key=lambda d: d.gmt_create, reverse=True)
            return latest
