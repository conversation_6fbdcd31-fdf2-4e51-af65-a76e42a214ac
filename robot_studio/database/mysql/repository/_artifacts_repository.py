from datetime import datetime
from typing import Optional, Sequence

from sqlmodel import Session, select, and_, or_

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema._artifacts import ArtifactsDO
from robot_studio.utils.uuid import build_uuid, DataType


class ArtifactsRepository:
    """内容产物数据仓库类"""
    
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_artifact(self, artifact: ArtifactsDO) -> ArtifactsDO:
        """
        创建产物记录
        Args:
            artifact: 产物信息

        Returns:
            ArtifactsDO: 创建的产物记录
        """
        # 生成产物ID
        if artifact.artifact_id is None:
            artifact.artifact_id = build_uuid(DataType.ARTIFACT)
            
        with Session(self._engine) as session:
            session.add(artifact)
            session.commit()
            session.refresh(artifact)
        return artifact

    @db_handler
    def get_artifact_by_id(self, artifact_id: str) -> Optional[ArtifactsDO]:
        """
        根据产物ID查询产物信息
        Args:
            artifact_id: 产物ID

        Returns:
            Optional[ArtifactsDO]: 产物信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.artifact_id == artifact_id,
                ArtifactsDO.is_deleted == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_artifact_by_db_id(self, db_id: int) -> Optional[ArtifactsDO]:
        """
        根据数据库ID查询产物信息
        Args:
            db_id: 数据库主键ID

        Returns:
            Optional[ArtifactsDO]: 产物信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.id == db_id,
                ArtifactsDO.is_deleted == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_artifacts_by_session_id(self, session_id: str) -> Sequence[ArtifactsDO]:
        """
        根据会话ID查询产物列表
        Args:
            session_id: 会话ID

        Returns:
            Sequence[ArtifactsDO]: 产物列表
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.session_id == session_id,
                ArtifactsDO.is_deleted == False
            ).order_by(ArtifactsDO.gmt_create.desc())
            return session.exec(statement).all()

    @db_handler
    def get_artifacts_by_message_id(self, message_id: str) -> Sequence[ArtifactsDO]:
        """
        根据消息ID查询产物列表
        Args:
            message_id: 消息ID

        Returns:
            Sequence[ArtifactsDO]: 产物列表
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.message_id == message_id,
                ArtifactsDO.is_deleted == False
            ).order_by(ArtifactsDO.gmt_create.desc())
            return session.exec(statement).all()

    @db_handler
    def get_artifacts_by_type(self, artifact_type: str, limit: Optional[int] = None) -> Sequence[ArtifactsDO]:
        """
        根据产物类型查询产物列表
        Args:
            artifact_type: 产物类型
            limit: 限制返回数量

        Returns:
            Sequence[ArtifactsDO]: 产物列表
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.type == artifact_type,
                ArtifactsDO.is_deleted == False
            ).order_by(ArtifactsDO.gmt_create.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_expired_artifacts(self) -> Sequence[ArtifactsDO]:
        """
        查询已过期的产物
        Returns:
            Sequence[ArtifactsDO]: 过期产物列表
        """
        now = datetime.now()
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.expires_at.is_not(None),
                ArtifactsDO.expires_at < now,
                ArtifactsDO.is_deleted == False
            )
            return session.exec(statement).all()

    @db_handler
    def update_artifact(self, db_id: int, update_data: dict) -> Optional[ArtifactsDO]:
        """
        更新产物信息
        Args:
            db_id: 数据库主键ID
            update_data: 更新数据字典

        Returns:
            Optional[ArtifactsDO]: 更新后的产物信息
        """
        artifact = self.get_artifact_by_db_id(db_id)
        if not artifact:
            return None
            
        # 更新产物属性
        for key, value in update_data.items():
            if hasattr(artifact, key):
                setattr(artifact, key, value)
                
        artifact.gmt_modified = datetime.now()
        
        with Session(self._engine) as session:
            session.add(artifact)
            session.commit()
            session.refresh(artifact)
        return artifact

    @db_handler
    def update_artifact_status(self, artifact_id: str, status: int) -> bool:
        """
        更新产物状态
        Args:
            artifact_id: 产物ID
            status: 新状态

        Returns:
            bool: 更新是否成功
        """
        artifact = self.get_artifact_by_id(artifact_id)
        if not artifact:
            return False
            
        return self.update_artifact(artifact.id, {"status": status}) is not None

    @db_handler
    def delete_artifact(self, artifact_id: str) -> bool:
        """
        软删除产物
        Args:
            artifact_id: 产物ID

        Returns:
            bool: 删除是否成功
        """
        artifact = self.get_artifact_by_id(artifact_id)
        if not artifact:
            return True  # 已经不存在，视为删除成功
            
        return self.update_artifact(artifact.id, {"is_deleted": True}) is not None

    @db_handler
    def increment_download_count(self, artifact_id: str) -> bool:
        """
        增加下载次数
        Args:
            artifact_id: 产物ID

        Returns:
            bool: 更新是否成功
        """
        artifact = self.get_artifact_by_id(artifact_id)
        if not artifact:
            return False
            
        new_count = (artifact.download_count or 0) + 1
        return self.update_artifact(artifact.id, {"download_count": new_count}) is not None

    @db_handler
    def get_artifacts_by_status(self, status: int, limit: Optional[int] = None) -> Sequence[ArtifactsDO]:
        """
        根据状态查询产物列表
        Args:
            status: 产物状态
            limit: 限制返回数量

        Returns:
            Sequence[ArtifactsDO]: 产物列表
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.status == status,
                ArtifactsDO.is_deleted == False
            ).order_by(ArtifactsDO.gmt_create.desc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def count_artifacts_by_session(self, session_id: str) -> int:
        """
        统计会话的产物数量
        Args:
            session_id: 会话ID

        Returns:
            int: 产物数量
        """
        with Session(self._engine) as session:
            statement = select(ArtifactsDO).where(
                ArtifactsDO.session_id == session_id,
                ArtifactsDO.is_deleted == False
            )
            return len(session.exec(statement).all())