import logging
from datetime import datetime
from typing import Sequence, Dict, Any, Optional

from sqlmodel import Session, select

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema import ComponentDO
from robot_studio.utils.uuid import build_uuid, DataType

logger = logging.getLogger(__name__)


class ComponentRepository:
    """组件仓库，提供对组件的CRUD操作"""

    def __init__(self):
        self._engine = engine

    @db_handler
    def create_component(self, component: ComponentDO) -> ComponentDO:
        """
        创建组件
        Args:
            component: 组件信息

        Returns:
            ComponentDO: 创建后的组件信息
        """
        # 生成组件ID
        if not component.component_id:
            component.component_id = build_uuid(DataType.COMPONENT)

        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(component)
            session.commit()
            session.refresh(component)
        return component

    @db_handler
    def get_component_by_id(self, component_id: str) -> Sequence[ComponentDO]:
        """
        根据组件ID查询组件的所有版本
        Args:
            component_id: 组件ID

        Returns:
            Sequence[ComponentDO]: 组件的所有版本列表，不存在则返回空列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.component_id == component_id,
                ComponentDO.is_del == False
            )
            return session.exec(statement).all()

    @db_handler
    def get_latest_online_component_by_id(self, component_id: str) -> ComponentDO | None:
        """
        根据组件ID查询最新版本的线上组件（状态为beta或release）
        Args:
            component_id: 组件ID

        Returns:
            ComponentDO | None: 最新版本的线上组件信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.component_id == component_id,
                ComponentDO.status.in_(["beta", "release"]),
                ComponentDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return None

            # 返回version字段最大的记录，处理version可能为空的情况
            return max(results, key=lambda x: x.version if x.version is not None else -1)

    @db_handler
    def get_component_by_id_and_version(self, component_id: str, version: int) -> ComponentDO | None:
        """
        根据组件ID和版本号查询指定版本的组件
        Args:
            component_id: 组件ID
            version: 版本号

        Returns:
            ComponentDO | None: 指定版本的组件信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.component_id == component_id,
                ComponentDO.version == version,
                ComponentDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_components_by_iteration_id(self, iterate_id: str) -> Sequence[ComponentDO]:
        """
        根据迭代ID查询关联的组件列表
        Args:
            iterate_id: 迭代ID

        Returns:
            Sequence[ComponentDO]: 组件列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.iterate_id == iterate_id,
                ComponentDO.is_del == False
            )
            return session.exec(statement).all()

    @db_handler
    def get_latest_release_component_by_id(self, component_id: str) -> ComponentDO | None:
        """
        根据组件ID查询最新版本的发布组件（状态为release）
        Args:
            component_id: 组件ID

        Returns:
            ComponentDO | None: 最新版本的发布组件信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.component_id == component_id,
                ComponentDO.status == "release",
                ComponentDO.is_del == False
            )
            results = session.exec(statement).all()
            if not results:
                return None

            # 返回version字段最大的记录，处理version可能为空的情况
            return max(results, key=lambda x: x.version if x.version is not None else -1)

    @db_handler
    def get_component_by_iteration_and_id(self, iterate_id: str, component_id: str) -> ComponentDO | None:
        """
        根据迭代ID和组件ID查询特定组件
        Args:
            iterate_id: 迭代ID
            component_id: 组件ID

        Returns:
            ComponentDO | None: 组件信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.iterate_id == iterate_id,
                ComponentDO.component_id == component_id,
                ComponentDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def update_component(self, component_id: str, update_data: Dict[str, Any]) -> ComponentDO | None:
        """
        更新组件
        Args:
            component_id: 组件ID
            update_data: 更新数据字典

        Returns:
            ComponentDO | None: 更新后的组件信息，失败则返回None
        """
        components = self.get_component_by_id(component_id)
        if not components:
            logger.error(f"待更新的组件不存在，component_id={component_id}")
            return None

        try:
            # 获取最新版本的组件进行更新
            latest_component = max(components, key=lambda x: x.version if x.version is not None else -1)
            
            # 更新组件属性
            for key, value in update_data.items():
                if hasattr(latest_component, key):
                    setattr(latest_component, key, value)

            # 更新修改时间
            latest_component.gmt_modified = datetime.now()

            # 保存到数据库
            with Session(self._engine) as session:
                session.add(latest_component)
                session.commit()
                session.refresh(latest_component)
            return latest_component
        except Exception as e:
            logger.error(f"更新组件失败，component_id={component_id}: {e}")
            return None

    @db_handler
    def delete_component_by_id(self, id: int) -> bool:
        """
        根据数据库主键ID删除组件（软删除）
        Args:
            id: 数据库表主键ID

        Returns:
            bool: 删除结果
        """
        try:
            component = self.get_component_by_db_id(id)
            if not component:
                logger.warning(f"待删除的组件不存在，id={id}")
                return False

            # 软删除
            update_res = self.update_component_by_id(id, {"is_del": True})
            return update_res is not None
        except Exception as e:
            logger.error(f"软删除组件失败，id={id}: {e}")
            return False

    @db_handler
    def get_component_by_db_id(self, id: int) -> ComponentDO | None:
        """
        根据数据库主键ID查询组件信息
        Args:
            id: 数据库表主键ID

        Returns:
            ComponentDO | None: 组件信息，不存在则返回None
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.id == id,
                ComponentDO.is_del == False
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_components_by_iterate_id(self, iterate_id: str) -> Sequence[ComponentDO]:
        """
        根据迭代ID获取所有关联的组件
        Args:
            iterate_id: 迭代ID

        Returns:
            Sequence[ComponentDO]: 关联的组件列表
        """
        with Session(self._engine) as session:
            statement = select(ComponentDO).where(
                ComponentDO.iterate_id == iterate_id,
                ComponentDO.is_del == False
            )
            return session.exec(statement).all()

    @db_handler
    def update_component_by_id(self, id: int, update_data: Dict[str, Any]) -> ComponentDO | None:
        """
        根据数据库主键ID更新组件
        Args:
            id: 数据库表主键ID
            update_data: 更新数据字典

        Returns:
            ComponentDO | None: 更新后的组件信息，失败则返回None
        """
        component = self.get_component_by_db_id(id)
        if not component:
            logger.error(f"待更新的组件不存在，id={id}")
            return None

        try:
            # 更新组件属性
            for key, value in update_data.items():
                if hasattr(component, key):
                    setattr(component, key, value)

            # 更新修改时间
            component.gmt_modified = datetime.now()

            # 保存到数据库
            with Session(self._engine) as session:
                session.add(component)
                session.commit()
                session.refresh(component)
            return component
        except Exception as e:
            logger.error(f"更新组件失败，id={id}: {e}")
            return None 