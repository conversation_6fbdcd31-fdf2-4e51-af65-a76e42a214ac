import logging
from datetime import datetime

from sqlmodel import Session
from sqlmodel import select

from robot_studio.database.mysql.db_engine import engine
from robot_studio.database.mysql.table_schema import UserDO
from robot_studio.utils.uuid import build_uuid, DataType


logger = logging.getLogger(__name__)


class UserRepository:
    def __init__(self):
        self._engine = engine
    
    def create_user(self, user: UserDO) -> UserDO:
        """
        创建用户信息
        Args:
            user: 待注册用户信息

        Returns:
            UserDO
        """
        # 生成用户ID
        user.uid = build_uuid(DataType.USER)
        # 添加到数据库并提交
        with Session(self._engine) as session:
            session.add(user)
            session.commit()
            session.refresh(user)
        return user

    def get_user_by_telephone(self, telephone: str) -> UserDO | None:
        """
        根据手机号查询用户信息
        Args:
            telephone:用户手机号

        Returns:
            UserDO: 用户信息

        """
        with Session(self._engine) as session:
            statement = select(UserDO).where(UserDO.telephone == telephone)
            return session.exec(statement).one_or_none()

    def get_user_by_uid(self, uid: str) -> UserDO | None:
        """
        根据用户UID查询用户信息
        Args:
            uid: 用户UID

        Returns:
            UserDO: 用户信息

        """
        with Session(self._engine) as session:
            statement = select(UserDO).where(UserDO.uid == uid)
            return session.exec(statement).one_or_none()
    
    def update_last_login(self, telephone: str) -> UserDO | None:
        """
        用户登录成功后, 更新最近登录时间
        Args:
            telephone:用户手机号

        Returns:
            UserDO | None: 更新last_login的用户信息
        """
        _user = self.get_user_by_telephone(telephone=telephone)
        assert _user is not None, f"用户不存在，手机号: {telephone}"
        
        try:
            # 更新用户末次登录时间
            _user.last_login = datetime.now()
            _user.gmt_modified = datetime.now()
            with Session(self._engine) as session:
                session.add(_user)
                session.commit()
                session.refresh(_user)
            return _user
        except Exception as e:
            logger.error(f"更新用户信息失败，telephone={telephone}: {e}")
            return None

    def delete_user(self, uid: str) -> bool:
        """
        根据用户UID删除用户信息
        Args:
            uid: 用户UID

        Returns:
            bool: 删除结果
        """
        try:
            with Session(self._engine) as session:
                # 查询用户是否存在
                statement = select(UserDO).where(UserDO.uid == uid)
                user = session.exec(statement).one_or_none()
                
                if not user:
                    logger.warning(f"用户不存在: {uid}")
                    return False
                
                # 删除用户
                session.delete(user)
                session.commit()
                logger.info(f"用户删除成功: {uid}")
                return True
        except Exception as e:
            logger.error(f"删除用户失败，uid={uid}: {e}")
            return False
