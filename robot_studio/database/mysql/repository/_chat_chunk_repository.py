from datetime import datetime
from typing import Optional, Sequence, Literal

from sqlmodel import Session, select, and_, func, desc

from robot_studio.database.mysql.db_engine import engine, db_handler
from robot_studio.database.mysql.table_schema._chat_chunk import ChatChunkDO, RoleType, ChunkType
from robot_studio.utils.uuid import build_uuid, DataType


class ChatChunkRepository:
    """聊天块数据仓库类"""
    
    def __init__(self):
        self._engine = engine

    @db_handler
    def create_chat_chunk(self, chat_chunk: ChatChunkDO) -> ChatChunkDO:
        """
        创建聊天块记录
        Args:
            chat_chunk: 聊天块信息

        Returns:
            ChatChunkDO: 创建的聊天块记录
        """
        # 生成聊天块ID
        if chat_chunk.chunk_id is None:
            chat_chunk.chunk_id = build_uuid(DataType.MESSAGE)  # 复用MESSAGE前缀
            
        with Session(self._engine) as session:
            session.add(chat_chunk)
            session.commit()
            session.refresh(chat_chunk)
        return chat_chunk

    @db_handler
    def get_chat_chunk_by_id(self, chunk_id: str) -> Optional[ChatChunkDO]:
        """
        根据聊天块ID查询聊天块信息
        Args:
            chunk_id: 聊天块ID

        Returns:
            Optional[ChatChunkDO]: 聊天块信息
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.chunk_id == chunk_id
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_chat_chunk_by_db_id(self, db_id: int) -> Optional[ChatChunkDO]:
        """
        根据数据库主键ID查询聊天块信息
        Args:
            db_id: 数据库主键ID

        Returns:
            Optional[ChatChunkDO]: 聊天块信息
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.id == db_id
            )
            return session.exec(statement).one_or_none()

    @db_handler
    def get_chat_chunks_by_session_id(self, session_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        根据会话ID查询聊天块列表
        Args:
            session_id: 会话ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.session_id == session_id
            ).order_by(ChatChunkDO.gmt_create.asc())
            
            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def get_session_chunk_count(self, session_id: str) -> int:
        """
        获取会话的聊天块数量
        Args:
            session_id: 会话ID

        Returns:
            int: 聊天块数量
        """
        with Session(self._engine) as session:
            statement = select(func.count(ChatChunkDO.id)).where(
                ChatChunkDO.session_id == session_id
            )
            return session.exec(statement).one()

    @db_handler
    def search_chat_chunks(self, session_id: Optional[str] = None, keyword: Optional[str] = None,
                          chunk_type: Optional[Literal['Event', 'Message']] = None, start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None, task_id: Optional[str] = None,
                          span_id: Optional[str] = None, role_type: Optional[Literal['user', 'assistant']] = None,
                          chunk_sub_type: Optional[str] = None, chunk_status: Optional[str] = None,
                          limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        搜索聊天块
        Args:
            session_id: 会话ID
            keyword: 内容关键词
            chunk_type: 聊天块类型
            start_time: 开始时间
            end_time: 结束时间
            task_id: 任务ID
            span_id: 逻辑单元ID
            role_type: 角色类型
            chunk_sub_type: 聊天块子类型
            chunk_status: 聊天块状态
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            conditions = []

            if session_id:
                conditions.append(ChatChunkDO.session_id == session_id)
            if keyword:
                conditions.append(ChatChunkDO.content.contains(keyword))
            if chunk_type:
                conditions.append(ChatChunkDO.chunk_type == chunk_type)
            if start_time:
                conditions.append(ChatChunkDO.gmt_create >= start_time)
            if end_time:
                conditions.append(ChatChunkDO.gmt_create <= end_time)
            if task_id:
                conditions.append(ChatChunkDO.task_id == task_id)
            if span_id:
                conditions.append(ChatChunkDO.span_id == span_id)
            if role_type:
                conditions.append(ChatChunkDO.role_type == role_type)
            if chunk_sub_type:
                conditions.append(ChatChunkDO.chunk_sub_type == chunk_sub_type)
            if chunk_status:
                conditions.append(ChatChunkDO.chunk_status == chunk_status)
            
            if conditions:
                statement = select(ChatChunkDO).where(and_(*conditions)).order_by(desc(ChatChunkDO.gmt_create))
            else:
                statement = select(ChatChunkDO).order_by(desc(ChatChunkDO.gmt_create))
            
            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()

    @db_handler
    def update_chat_chunk(self, db_id: int, update_data: dict) -> Optional[ChatChunkDO]:
        """
        更新聊天块信息
        Args:
            db_id: 数据库主键ID
            update_data: 更新数据字典

        Returns:
            Optional[ChatChunkDO]: 更新后的聊天块信息
        """
        chat_chunk = self.get_chat_chunk_by_db_id(db_id)
        if not chat_chunk:
            return None
            
        # 更新聊天块属性
        for key, value in update_data.items():
            if hasattr(chat_chunk, key):
                setattr(chat_chunk, key, value)
                
        chat_chunk.gmt_modified = datetime.now()
        
        with Session(self._engine) as session:
            session.add(chat_chunk)
            session.commit()
            session.refresh(chat_chunk)
        return chat_chunk

    @db_handler
    def delete_chat_chunk(self, chunk_id: str) -> bool:
        """
        删除聊天块
        Args:
            chunk_id: 聊天块ID

        Returns:
            bool: 删除是否成功
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(ChatChunkDO.chunk_id == chunk_id)
            chat_chunk = session.exec(statement).first()
            if chat_chunk:
                session.delete(chat_chunk)
                session.commit()
                return True
            return False

    @db_handler
    def get_chat_chunks_by_type(self, session_id: str, chunk_type: Literal['Event', 'Message'], limit: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        根据会话ID和聊天块类型查询聊天块列表
        Args:
            session_id: 会话ID
            chunk_type: 聊天块类型
            limit: 限制返回数量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.session_id == session_id,
                ChatChunkDO.chunk_type == chunk_type
            ).order_by(ChatChunkDO.gmt_create.asc())
            
            if limit:
                statement = statement.limit(limit)
                
            return session.exec(statement).all()



    @db_handler
    def get_chat_chunks_by_task_id(self, task_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        根据任务ID查询聊天块列表
        Args:
            task_id: 任务ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.task_id == task_id
            ).order_by(ChatChunkDO.gmt_create.asc())

            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)

            return session.exec(statement).all()

    @db_handler
    def get_chat_chunks_by_span_id(self, span_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        根据逻辑单元ID查询聊天块列表
        Args:
            span_id: 逻辑单元ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.span_id == span_id
            ).order_by(ChatChunkDO.gmt_create.asc())

            if offset:
                statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)

            return session.exec(statement).all()

    @db_handler
    def get_chat_chunks_by_role_type(self, session_id: str, role_type: Literal['user', 'assistant'], limit: Optional[int] = None) -> Sequence[ChatChunkDO]:
        """
        根据会话ID和角色类型查询聊天块列表
        Args:
            session_id: 会话ID
            role_type: 角色类型
            limit: 限制返回数量

        Returns:
            Sequence[ChatChunkDO]: 聊天块列表
        """
        with Session(self._engine) as session:
            statement = select(ChatChunkDO).where(
                ChatChunkDO.session_id == session_id,
                ChatChunkDO.role_type == role_type
            ).order_by(ChatChunkDO.gmt_create.asc())

            if limit:
                statement = statement.limit(limit)

            return session.exec(statement).all()
