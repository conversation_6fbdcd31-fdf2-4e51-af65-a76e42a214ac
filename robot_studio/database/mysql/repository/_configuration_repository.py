import logging
import os
from typing import Optional, List, Dict, Any
from sqlmodel import Session, select
from dotenv import load_dotenv, find_dotenv
from robot_studio.database.mysql.table_schema._configuration import ConfigurationDO
from robot_studio.database.mysql.db_engine import db_handler
from robot_studio.common.encryption_service import encryption_service

logger = logging.getLogger(__name__)


class ConfigurationRepository:
    """配置信息仓库类"""
    
    def __init__(self, session: Session):
        self.session = session
        self._cached_master_key: Optional[str] = None
    
    def _get_master_key(self) -> str:
        """获取主密钥，优先从数据库，然后环境变量，最后默认值"""
        if self._cached_master_key:
            return self._cached_master_key
        
        # 1. 尝试从数据库获取 CONFIG_ENCRYPTION_KEY
        try:
            stmt = select(ConfigurationDO).where(
                ConfigurationDO.conf_key == 'CONFIG_ENCRYPTION_KEY',
                ConfigurationDO.env == 'common',
                ConfigurationDO.cid == 'common',
                ConfigurationDO.is_active == True
            )
            config = self.session.exec(stmt).first()
            if config and config.conf_value:
                self._cached_master_key = config.conf_value
                logger.info("Master key loaded from configuration table")
                return self._cached_master_key
        except Exception as e:
            logger.warning(f"Failed to get master key from database: {str(e)}")
        
        # 2. 回退到环境变量
        load_dotenv(find_dotenv(), override=True)
        master_key = os.getenv('CONFIG_ENCRYPTION_KEY')
        
        if master_key:
            self._cached_master_key = master_key
            logger.info("Master key loaded from environment variable")
            return master_key
        
        # 3. 使用默认密钥（仅用于开发环境）
        master_key = "mindshake_config_encryption_default_key_2025"
        self._cached_master_key = master_key
        logger.warning("Using default encryption key. Please set CONFIG_ENCRYPTION_KEY in configuration table or environment variable for production.")
        
        return master_key
    
    @db_handler
    def get_config(self, key: str, env: str = "common", cid: str = "common") -> Optional[ConfigurationDO]:
        """
        获取配置项
        """
        stmt = select(ConfigurationDO).where(
            ConfigurationDO.conf_key == key,
            ConfigurationDO.env == env,
            ConfigurationDO.cid == cid,
            ConfigurationDO.is_active == True
        )
        return self.session.exec(stmt).first()

    @db_handler
    def get_config_value(self, key: str, env: str = "common", cid: str = "common", default: Any = None) -> Any:
        """
        获取配置值，自动处理解密
        """
        config = self.get_config(key, env, cid)
        if config:
            value = config.conf_value
            # 如果配置标记为加密，则解密
            if config.is_encrypted:
                try:
                    master_key = self._get_master_key()
                    value = encryption_service.decrypt(value, master_key)
                except Exception as e:
                    logger.error(f"Failed to decrypt config {key}: {str(e)}")
                    # 解密失败时返回原值以保持向后兼容性
                    pass
            return value
        return default
    
    @db_handler
    def get_all_configs(self, env: str = "common", cid: str = "common") -> Dict[str, str]:
        """
        获取所有配置项，返回字典格式，自动处理解密
        """
        configs = {}
        
        stmt = select(ConfigurationDO).where(
            ConfigurationDO.env == env,
            ConfigurationDO.cid == cid,
            ConfigurationDO.is_active == True
        )
        common_configs = self.session.exec(stmt).all()
        
        master_key = None  # 缓存主密钥避免重复获取
        for config in common_configs:
            value = config.conf_value
            # 如果配置标记为加密，则解密
            if config.is_encrypted:
                try:
                    if master_key is None:
                        master_key = self._get_master_key()
                    value = encryption_service.decrypt(value, master_key)
                except Exception as e:
                    logger.error(f"Failed to decrypt config {config.conf_key}: {str(e)}")
                    # 解密失败时保持原值
                    pass
            configs[config.conf_key] = value

        return configs
    
    @db_handler
    def create_config(self, key: str, value: str, env: str = "common", cid: str = "common", 
                     tag: str = None, description: str = None, is_encrypted: bool = False) -> ConfigurationDO:
        """
        创建配置项，自动处理加密
        """
        # 如果需要加密，则加密存储
        stored_value = value
        if is_encrypted:
            try:
                master_key = self._get_master_key()
                stored_value = encryption_service.encrypt(value, master_key)
            except Exception as e:
                logger.error(f"Failed to encrypt config {key}: {str(e)}")
                raise
        
        config = ConfigurationDO(
            conf_key=key,
            conf_value=stored_value,
            env=env,
            cid=cid,
            tag=tag,
            description=description,
            is_encrypted=is_encrypted
        )
        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)
        return config
    
    @db_handler
    def update_config(self, key: str, value: str, env: str = "common", cid: str = "common") -> bool:
        """
        更新配置项，自动处理加密
        """
        config = self.get_config(key, env, cid)
        if config:
            # 如果配置标记为加密，则加密新值
            stored_value = value
            if config.is_encrypted:
                try:
                    master_key = self._get_master_key()
                    stored_value = encryption_service.encrypt(value, master_key)
                except Exception as e:
                    logger.error(f"Failed to encrypt updated config {key}: {str(e)}")
                    raise
            
            config.conf_value = stored_value
            self.session.commit()
            return True
        return False
    
    @db_handler
    def delete_config(self, key: str, env: str = "common", cid: str = "common") -> bool:
        """
        删除配置项（软删除）
        """
        config = self.get_config(key, env, cid)
        if config:
            config.is_active = False
            self.session.commit()
            return True
        return False
    
    @db_handler
    def get_configs_by_env(self, env: str) -> List[ConfigurationDO]:
        """
        根据环境获取配置
        """
        stmt = select(ConfigurationDO).where(
            ConfigurationDO.env == env,
            ConfigurationDO.is_active == True
        )
        return list(self.session.exec(stmt).all())
    
    @db_handler
    def get_configs_by_cid(self, cid: str) -> List[ConfigurationDO]:
        """
        根据企业ID获取配置
        """
        stmt = select(ConfigurationDO).where(
            ConfigurationDO.cid == cid,
            ConfigurationDO.is_active == True
        )
        return list(self.session.exec(stmt).all())
    
    @db_handler
    def get_configs_by_tag(self, tag: str, env: str = "common", cid: str = "common") -> List[ConfigurationDO]:
        """
        根据标签获取配置
        """
        stmt = select(ConfigurationDO).where(
            ConfigurationDO.tag == tag,
            ConfigurationDO.env == env,
            ConfigurationDO.cid == cid,
            ConfigurationDO.is_active == True
        )
        return list(self.session.exec(stmt).all())
    
    @db_handler
    def get_configs_by_tag_dict(self, tag: str, env: str = "common", cid: str = "common") -> Dict[str, str]:
        """
        根据标签获取配置，返回字典格式，自动处理解密
        """
        configs = self.get_configs_by_tag(tag, env, cid)
        result = {}
        
        master_key = None  # 缓存主密钥避免重复获取
        for config in configs:
            value = config.conf_value
            # 如果配置标记为加密，则解密
            if config.is_encrypted:
                try:
                    if master_key is None:
                        master_key = self._get_master_key()
                    value = encryption_service.decrypt(value, master_key)
                except Exception as e:
                    logger.error(f"Failed to decrypt config {config.conf_key}: {str(e)}")
                    # 解密失败时保持原值
                    pass
            result[config.conf_key] = value
        
        return result
    
    @db_handler
    def get_all_tags(self, env: str = "common", cid: str = "common") -> List[str]:
        """
        获取所有标签
        """
        stmt = select(ConfigurationDO.tag).where(
            ConfigurationDO.env == env,
            ConfigurationDO.cid == cid,
            ConfigurationDO.is_active == True,
            ConfigurationDO.tag.isnot(None)
        ).distinct()
        return list(self.session.exec(stmt).all())
    
    @db_handler
    def update_config_tag(self, key: str, tag: str, env: str = "common", cid: str = "common") -> bool:
        """
        更新配置项的标签
        """
        config = self.get_config(key, env, cid)
        if config:
            config.tag = tag
            self.session.commit()
            return True
        return False
    
    @db_handler
    def encrypt_existing_config(self, key: str, env: str = "common", cid: str = "common") -> bool:
        """
        加密现有的明文配置
        
        Args:
            key: 配置键
            env: 环境标识
            cid: 企业ID
            
        Returns:
            是否成功
        """
        config = self.get_config(key, env, cid)
        if config and not config.is_encrypted:
            try:
                # 加密当前明文值
                master_key = self._get_master_key()
                encrypted_value = encryption_service.encrypt(config.conf_value, master_key)
                
                # 更新配置
                config.conf_value = encrypted_value
                config.is_encrypted = True
                self.session.commit()
                
                logger.info(f"Successfully encrypted config {key}")
                return True
            except Exception as e:
                logger.error(f"Failed to encrypt existing config {key}: {str(e)}")
                raise
        elif config and config.is_encrypted:
            logger.info(f"Config {key} is already encrypted")
            return True
        else:
            logger.warning(f"Config {key} not found")
            return False
    
    @db_handler
    def batch_encrypt_configs(self, config_keys: List[str], env: str = "common", cid: str = "common") -> Dict[str, bool]:
        """
        批量加密配置
        
        Args:
            config_keys: 要加密的配置键列表
            env: 环境标识
            cid: 企业ID
            
        Returns:
            加密结果字典 {key: success}
        """
        results = {}
        
        for key in config_keys:
            try:
                results[key] = self.encrypt_existing_config(key, env, cid)
            except Exception as e:
                logger.error(f"Failed to encrypt config {key}: {str(e)}")
                results[key] = False
        
        return results