#!/usr/bin/env python
"""
阿里云OSS客户端使用示例

此脚本演示了如何使用OssClient类进行常见的OSS操作。
在运行此脚本前，请确保已设置环境变量：
- OSS_ACCESS_KEY_ID
- OSS_ACCESS_KEY_SECRET

使用方法：
    python example.py
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(project_root))

from robot_studio.database.oss.oss_client import OssClient


def main():
    """主函数，演示OssClient的使用"""

    # 尝试从配置文件加载凭证
    from robot_studio.database.oss.config import load_oss_credentials
    load_oss_credentials()

    # 检查环境变量
    if not os.environ.get("OSS_ACCESS_KEY_ID") or not os.environ.get("OSS_ACCESS_KEY_SECRET"):
        print("请先设置 OSS 凭证，可以通过以下方式：")
        print("1. 设置环境变量：")
        print("   export OSS_ACCESS_KEY_ID='YOUR_ACCESS_KEY_ID'")
        print("   export OSS_ACCESS_KEY_SECRET='YOUR_ACCESS_KEY_SECRET'")
        print("2. 或编辑配置文件：")
        print("   robot_studio/database/oss/oss_config.yaml")
        return

    try:
        # 创建OSS客户端
        # 注意：请替换为您自己的存储桶名称和区域
        from robot_studio.database.oss.config import load_config
        config = load_config()
        bucket = config.get('bucket', "mindshake-yitong")
        region = config.get('region', "cn-qingdao")
        endpoint = config.get('endpoint')

        oss = OssClient(bucket=bucket, region=region, endpoint=endpoint)

        # # 上传文件
        remote_key = "material/IMAGE/e095f8581dcd4dfdb95787befdbcd88c.jpeg"
        # print(f"\n1. 上传文件 {test_file_path} 到 {remote_key}")
        # success = oss.upload_file(test_file_path, remote_key)
        # if success:
        #     print("  上传成功！")
        # else:
        #     print("  上传失败！")
        #     return
        #
        # # 检查文件是否存在
        # print(f"\n2. 检查文件 {remote_key} 是否存在")
        # exists = oss.object_exists(remote_key)
        # print(f"  文件存在: {exists}")

        # 获取文件信息
        print(f"\n3. 获取文件 {remote_key} 的信息")
        info = oss.get_object_info(remote_key)
        if info:
            print(f"  文件大小: {info['content_length']} 字节")
            print(f"  内容类型: {info['content_type']}")
            print(f"  最后修改: {info['last_modified']}")
        else:
            print("  无法获取文件信息")
        #
        # 生成预签名URL
        print(f"\n4. 生成文件 {remote_key} 的预签名URL")
        url = oss.generate_url(remote_key, expires=3600)
        print(f"  预签名URL: {url}")
        print(f"  此URL有效期为1小时，可以直接在浏览器中访问")

        # 列出存储桶中的对象
        print("\n5. 列出存储桶中的对象（前缀为'2025_sq/'）")
        objects = oss.list_objects(prefix="2025_sq/")
        for i, obj in enumerate(objects):
            print(f"  {i+1}. {obj['key']} ({obj['size']} 字节)")

        # # 下载文件
        # download_path = "test_download.txt"
        # print(f"\n6. 下载文件 {remote_key} 到 {download_path}")
        # local_path = oss.download_file(remote_key, download_path)
        # print(f"  下载成功: {local_path}")
        #
        # # 删除远程文件
        # print(f"\n7. 删除远程文件 {remote_key}")
        # deleted = oss.delete_object(remote_key)
        # print(f"  删除{'成功' if deleted else '失败'}")
        #
        # # 再次检查文件是否存在
        # print(f"\n8. 再次检查文件 {remote_key} 是否存在")
        # exists = oss.object_exists(remote_key)
        # print(f"  文件存在: {exists}")

    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
