import os
from typing import Dict, Optional, Any, Union, BinaryIO, List, Tuple

from robot_studio.database.oss.generate_url import generate_presigned_url, create_oss_client
from robot_studio.database.oss.download import download_file
from robot_studio.database.oss.upload import upload_file
from robot_studio.database.oss.config import load_oss_credentials, load_config

"""
阿里云OSS客户端工具

此模块提供了一个统一的接口来操作阿里云OSS，包括：
1. 生成预签名URL
2. 上传文件
3. 下载文件
4. 删除文件
5. 列出存储桶中的文件

使用示例：
    from robot_studio.database.oss.oss_client import OssClient

    # 创建客户端
    oss = OssClient(bucket="mindshake-yitong", region="cn-qingdao")

    # 上传文件
    oss.upload_file("./local/file.jpg", "remote/path/file.jpg")

    # 下载文件
    oss.download_file("remote/path/file.jpg", "./downloads/")

    # 生成预签名URL
    url = oss.generate_url("remote/path/file.jpg")

    # 列出文件
    files = oss.list_objects(prefix="remote/path/")
"""


class OssClient:
    """阿里云OSS客户端类"""

    def __init__(
        self,
        bucket: str | None = None,
        region: str = "cn-qingdao",
        endpoint: Optional[str] = None
    ):
        """
        初始化OSS客户端

        参数:
            bucket: OSS存储桶名称
            region: OSS区域，默认为cn-qingdao
            endpoint: 自定义endpoint，如果为None则使用默认endpoint

        异常:
            EnvironmentError: 如果无法获取凭证
        """
        config = load_config()
        self.bucket = bucket or config["bucket"]
        self.region = region or config["region"]
        self.endpoint = endpoint or config["endpoint"]

        # 尝试从配置文件加载凭证
        load_oss_credentials()

        # 创建OSS客户端
        self.client = create_oss_client(region, endpoint)

    def generate_url(
        self,
        key: str,
        method: str = "GET",
        expires: int = 3600,
        headers: Optional[Dict[str, str]] = None
    ):
        """
        生成预签名URL

        参数:
            key: 对象键名（文件路径）
            method: HTTP方法，支持GET、PUT、DELETE等，默认为GET
            expires: URL过期时间（秒），默认为3600秒（1小时）
            headers: 自定义请求头

        返回:
            预签名URL字符串
        """
        result = generate_presigned_url(
            bucket=self.bucket,
            key=key,
            method=method,
            region=self.region,
            endpoint=self.endpoint,
            expires=expires,
            headers=headers
        )
        return result["url"],result["expiration"]

    def upload_file(
        self,
        local_path: str,
        remote_key: Optional[str] = None,
        content_type: Optional[str] = None,
        expires: int = 3600,
        headers: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        上传文件到OSS

        参数:
            local_path: 本地文件路径
            remote_key: OSS对象键名，如果为None则使用本地文件名
            content_type: 文件内容类型，如'image/jpeg'
            expires: URL过期时间（秒），默认为3600秒
            headers: 自定义请求头

        返回:
            上传是否成功
        """
        # 如果未提供remote_key，使用本地文件名
        if not remote_key:
            remote_key = os.path.basename(local_path)

        return upload_file(
            file_path=local_path,
            bucket=self.bucket,
            key=remote_key,
            region=self.region,
            endpoint=self.endpoint,
            expires=expires,
            content_type=content_type,
            headers=headers
        )

    def download_file(
        self,
        remote_key: str,
        local_path: Optional[str] = None,
        expires: int = 3600,
        headers: Optional[Dict[str, str]] = None
    ) -> str:
        """
        从OSS下载文件

        参数:
            remote_key: OSS对象键名
            local_path: 本地保存路径，如果为None则保存到当前目录
            expires: URL过期时间（秒），默认为3600秒
            headers: 自定义请求头

        返回:
            下载文件的本地路径
        """
        return download_file(
            bucket=self.bucket,
            key=remote_key,
            output_dir=local_path if os.path.isdir(local_path) else os.path.dirname(local_path) if local_path else None,
            output_filename=os.path.basename(local_path) if local_path and not os.path.isdir(local_path) else None,
            region=self.region,
            endpoint=self.endpoint,
            expires=expires,
            headers=headers
        )

    def delete_object(self, key: str) -> bool:
        """
        删除OSS对象

        参数:
            key: 对象键名

        返回:
            删除是否成功
        """
        try:
            import alibabacloud_oss_v2 as oss
            self.client.delete_object(oss.DeleteObjectRequest(bucket=self.bucket, key=key))
            return True
        except Exception as e:
            print(f"删除对象失败: {e}")
            return False

    def list_objects(
        self,
        prefix: Optional[str] = None,
        delimiter: Optional[str] = None,
        max_keys: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        列出存储桶中的对象

        参数:
            prefix: 对象键名前缀
            delimiter: 分隔符，用于分组对象
            max_keys: 最大返回数量

        返回:
            对象列表，每个对象包含key、size、last_modified等信息
        """
        try:
            import alibabacloud_oss_v2 as oss

            # 创建请求
            request = oss.ListObjectsRequest(bucket=self.bucket)
            if prefix:
                request.prefix = prefix
            if delimiter:
                request.delimiter = delimiter
            request.max_keys = max_keys

            # 发送请求
            response = self.client.list_objects(request)

            # 处理结果
            objects = []
            # 检查响应中是否有对象
            if hasattr(response, 'contents'):
                for obj in response.contents:
                    objects.append({
                        "key": obj.key,
                        "size": obj.size,
                        "last_modified": obj.last_modified,
                        "etag": obj.etag,
                        "storage_class": obj.storage_class
                    })

            return objects
        except Exception as e:
            print(f"列出对象失败: {e}")
            return []

    def object_exists(self, key: str) -> bool:
        """
        检查对象是否存在

        参数:
            key: 对象键名

        返回:
            对象是否存在
        """
        try:
            import alibabacloud_oss_v2 as oss
            self.client.head_object(oss.HeadObjectRequest(bucket=self.bucket, key=key))
            return True
        except Exception:
            return False

    def get_object_info(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取对象信息

        参数:
            key: 对象键名

        返回:
            对象信息字典，包含content_type、content_length、last_modified等，如果对象不存在则返回None
        """
        try:
            import alibabacloud_oss_v2 as oss
            response = self.client.head_object(oss.HeadObjectRequest(bucket=self.bucket, key=key))

            return {
                "content_type": response.headers.get("Content-Type"),
                "content_length": int(response.headers.get("Content-Length", 0)),
                "last_modified": response.headers.get("Last-Modified"),
                "etag": response.headers.get("ETag"),
                "headers": dict(response.headers)
            }
        except Exception:
            return None

    def generate_video_preview_url(
        self,
        video_oss_path: str,
        expires: int = 3600,
        snapshot_time: int = 1000,
        width: int = 800,
        height: int = 600,
        image_format: str = "jpg"
    ) -> str:
        """
        生成视频首帧预览URL

        参数:
            video_oss_path: 视频文件在OSS中的路径
            expires: URL过期时间（秒），默认为3600秒（1小时）
            snapshot_time: 截取视频的时间点（毫秒），默认为0（首帧）
            width: 预览图宽度，默认为800
            height: 预览图高度，默认为600
            image_format: 输出图片格式，默认为jpg

        返回:
            视频截图的预签名URL（不是视频文件本身，而是截图图片）

        异常:
            Exception: 如果生成URL失败
        """
        import oss2
        from oss2.credentials import EnvironmentVariableCredentialsProvider

        # 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        auth = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())

        # 填写Bucket名称
        bucket = self.bucket

        # 填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例
        endpoint = self.endpoint

        # 填写阿里云通用Region ID
        region = self.region
        bucket = oss2.Bucket(auth, endpoint, bucket, region=region)

        image_process = f"video/snapshot,t_{str(snapshot_time)},f_{image_format},w_{width},h_{height}"

        # 生成签名URL，带上处理参数
        url = bucket.sign_url('GET', video_oss_path, expires, params={'x-oss-process': image_process}, slash_safe=True)
        return url



if __name__ == '__main__':
    # 创建OSS客户端
    oss_client = OssClient(bucket="mindshake-yitong")

    # 生成视频首帧预览URL
    preview_url = oss_client.generate_video_preview_url("material/VIDEO/测试视频_1751016349220.mp4")

    print(preview_url)