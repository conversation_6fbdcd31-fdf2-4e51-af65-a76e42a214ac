import argparse
import os
import datetime
from typing import Dict, Optional, List, Union, Any
from enum import Enum
import alibabacloud_oss_v2 as oss

from robot_studio.database.oss.config import load_oss_credentials

"""
阿里云OSS对象存储预签名URL生成工具

此模块提供了生成阿里云OSS预签名URL的功能，支持以下操作：
1. 生成GET预签名URL（下载文件）
2. 生成PUT预签名URL（上传文件）
3. 生成DELETE预签名URL（删除文件）
4. 自定义过期时间和请求头

使用示例：
    # 作为命令行工具使用
    python generate_url.py --method GET --bucket mindshake-yitong --key path/to/file.jpg --expires 3600

    # 作为模块导入使用
    from robot_studio.database.oss.generate_url import generate_presigned_url
    url = generate_presigned_url("mindshake-yitong", "path/to/file.jpg", method="GET")
"""


class HttpMethod(str, Enum):
    """HTTP请求方法枚举"""
    GET = "GET"
    PUT = "PUT"
    DELETE = "DELETE"
    POST = "POST"
    HEAD = "HEAD"


def create_oss_client(region: str = "cn-qingdao", endpoint: Optional[str] = None) -> oss.Client:
    """创建OSS客户端

    参数:
        region: OSS区域，默认为cn-qingdao
        endpoint: 自定义endpoint，如果为None则使用默认endpoint

    返回:
        OSS客户端实例

    异常:
        EnvironmentError: 如果无法获取凭证
    """
    # 尝试从配置文件加载凭证
    credentials_loaded = load_oss_credentials()

    # 检查环境变量是否设置
    if not os.environ.get("OSS_ACCESS_KEY_ID") or not os.environ.get("OSS_ACCESS_KEY_SECRET"):
        raise EnvironmentError(
            "无法获取OSS凭证。请设置环境变量或创建配置文件：\n"
            "1. 设置环境变量：\n"
            "   export OSS_ACCESS_KEY_ID='YOUR_ACCESS_KEY_ID'\n"
            "   export OSS_ACCESS_KEY_SECRET='YOUR_ACCESS_KEY_SECRET'\n"
            "2. 或创建配置文件：\n"
            "   ~/.oss_config.yaml"
        )

    # 直接使用环境变量中的凭证创建客户端
    access_key_id = os.environ.get("OSS_ACCESS_KEY_ID")
    access_key_secret = os.environ.get("OSS_ACCESS_KEY_SECRET")

    # 创建凭证提供者
    credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()

    # 使用SDK的默认配置创建配置对象，并设置认证提供者
    cfg = oss.config.load_default()
    cfg.credentials_provider = credentials_provider

    # 设置区域
    cfg.region = region

    # 如果提供了自定义endpoint，则更新配置
    if endpoint:
        cfg.endpoint = endpoint

    # 返回OSS客户端
    return oss.Client(cfg)


def generate_presigned_url(
    bucket: str,
    key: str,
    method: Union[str, HttpMethod] = HttpMethod.GET,
    region: str = "cn-qingdao",
    endpoint: Optional[str] = None,
    expires: int = 3600,
    headers: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """生成OSS对象的预签名URL

    参数:
        bucket: 存储桶名称
        key: 对象键名（文件路径）
        method: HTTP方法，支持GET、PUT、DELETE等，默认为GET
        region: OSS区域，默认为cn-qingdao
        endpoint: 自定义endpoint，如果为None则使用默认endpoint
        expires: URL过期时间（秒），默认为3600秒（1小时）
        headers: 自定义请求头

    返回:
        包含预签名URL信息的字典，包括method、expiration、url和signed_headers
    """
    # 创建OSS客户端
    client = create_oss_client(region, endpoint)

    # 根据HTTP方法创建不同的请求对象
    if isinstance(method, str):
        method = HttpMethod(method.upper())

    request = None
    if method == HttpMethod.GET:
        request = oss.GetObjectRequest(bucket=bucket, key=key)
    elif method == HttpMethod.PUT:
        request = oss.PutObjectRequest(bucket=bucket, key=key)
    elif method == HttpMethod.DELETE:
        request = oss.DeleteObjectRequest(bucket=bucket, key=key)
    elif method == HttpMethod.HEAD:
        request = oss.HeadObjectRequest(bucket=bucket, key=key)
    else:
        raise ValueError(f"不支持的HTTP方法: {method}")

    # 添加自定义请求头
    if headers:
        for header_key, header_value in headers.items():
            request.headers[header_key] = header_value

    # 设置过期时间（从当前时间开始计算）
    expiration = datetime.datetime.now() + datetime.timedelta(seconds=expires)

    # 生成预签名URL
    pre_result = client.presign(request, expiration=expiration)

    # 返回结果
    return {
        "method": pre_result.method,
        "expiration": pre_result.expiration,
        "url": pre_result.url,
        "signed_headers": pre_result.signed_headers
    }


def format_presigned_url_result(result: Dict[str, Any]) -> str:
    """格式化预签名URL结果为可读字符串

    参数:
        result: 预签名URL结果字典

    返回:
        格式化后的字符串
    """
    output = [
        f"方法: {result['method']}",
        f"过期时间: {result['expiration'].strftime('%Y-%m-%dT%H:%M:%S.000Z')}",
        f"URL: {result['url']}"
    ]

    if result['signed_headers']:
        output.append("签名头信息:")
        for key, value in result['signed_headers'].items():
            output.append(f"  {key}: {value}")

    return "\n".join(output)


def main():
    """命令行入口函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="生成阿里云OSS对象的预签名URL")

    # 添加命令行参数
    parser.add_argument('--method', choices=['GET', 'PUT', 'DELETE', 'HEAD'], default='GET',
                        help='HTTP请求方法，默认为GET')
    parser.add_argument('--region', help='存储空间所在的区域', default="cn-qingdao")
    parser.add_argument('--bucket', help='存储空间名称', required=True)
    parser.add_argument('--key', help='对象键名（文件路径）', required=True)
    parser.add_argument('--endpoint', help='自定义endpoint', default=None)
    parser.add_argument('--expires', type=int, help='URL过期时间（秒）', default=3600)
    parser.add_argument('--header', action='append', help='自定义请求头，格式为key=value', default=[])

    # 解析命令行参数
    args = parser.parse_args()

    # 处理自定义请求头
    headers = {}
    for header in args.header:
        if '=' in header:
            key, value = header.split('=', 1)
            headers[key] = value

    # 生成预签名URL
    try:
        result = generate_presigned_url(
            bucket=args.bucket,
            key=args.key,
            method=args.method,
            region=args.region,
            endpoint=args.endpoint,
            expires=args.expires,
            headers=headers if headers else None
        )

        # 打印结果
        print(format_presigned_url_result(result))
    except Exception as e:
        print(f"错误: {e}")


# 当此脚本被直接执行时，调用main函数
if __name__ == "__main__":
    main()