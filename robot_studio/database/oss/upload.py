import argparse
import os
import requests
from pathlib import Path
from typing import Optional, Dict, Any, Union, BinaryIO

from robot_studio.database.oss.generate_url import generate_presigned_url

"""
阿里云OSS对象上传工具

此模块提供了上传文件到阿里云OSS的功能，支持以下操作：
1. 使用预签名URL上传文件
2. 自动生成预签名URL并上传文件
3. 支持自定义上传参数和请求头

使用示例：
    # 作为命令行工具使用
    python upload.py --bucket mindshake-yitong --key path/to/file.jpg --file ./local/file.jpg

    # 使用已有的预签名URL上传
    python upload.py --url "https://bucket.oss-region.aliyuncs.com/object?签名参数" --file ./local/file.jpg

    # 作为模块导入使用
    from robot_studio.database.oss.upload import upload_file
    upload_file(bucket="mindshake-yitong", key="path/to/file.jpg", file_path="./local/file.jpg")
"""


def upload_file_to_url(
    url: str,
    file_path: Optional[str] = None,
    file_obj: Optional[BinaryIO] = None,
    content_type: Optional[str] = None,
    timeout: int = 60,
    headers: Optional[Dict[str, str]] = None
) -> bool:
    """
    使用预签名URL上传文件到OSS

    参数:
        url: 预签名上传URL
        file_path: 本地文件路径（与file_obj互斥）
        file_obj: 文件对象（与file_path互斥）
        content_type: 文件内容类型，如'image/jpeg'
        timeout: 请求超时时间（秒）
        headers: 自定义请求头

    返回:
        上传是否成功

    异常:
        ValueError: 如果URL无效或未提供文件
        IOError: 如果文件无法读取
        requests.RequestException: 如果上传过程中出现网络错误
    """
    if not url:
        raise ValueError("URL不能为空")

    if not file_path and not file_obj:
        raise ValueError("必须提供file_path或file_obj")

    if file_path and file_obj:
        raise ValueError("file_path和file_obj不能同时提供")

    # 准备请求头
    request_headers = {}
    if headers:
        request_headers.update(headers)

    # 如果提供了content_type，添加到请求头
    if content_type:
        request_headers['Content-Type'] = content_type
    elif file_path and not 'Content-Type' in request_headers:
        # 根据文件扩展名猜测内容类型
        import mimetypes
        content_type, _ = mimetypes.guess_type(file_path)
        if content_type:
            request_headers['Content-Type'] = content_type

    try:
        # 直接使用 alibabacloud-oss-v2 库上传文件
        import alibabacloud_oss_v2 as oss
        from robot_studio.database.oss.generate_url import create_oss_client

        # 从 URL 中提取 bucket 和 key
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.strip('/').split('/')

        if len(path_parts) < 2:
            print(f"无法从 URL 中提取 bucket 和 key: {url}")
            return False

        bucket = path_parts[0]
        key = '/'.join(path_parts[1:])

        # 创建 OSS 客户端
        client = create_oss_client()

        # 上传文件
        if file_path:
            file_size = os.path.getsize(file_path)
            print(f"正在上传文件: {file_path} ({file_size} 字节) 到 {bucket}/{key}")

            # 使用 OSS SDK 直接上传文件
            with open(file_path, 'rb') as f:
                # 准备请求参数
                request_params = {
                    'bucket': bucket,
                    'key': key,
                    'body': f
                }

                # 添加内容类型
                if content_type:
                    request_params['content_type'] = content_type

                # 创建请求
                request = oss.PutObjectRequest(**request_params)

                # 执行上传
                client.put_object(request)
                print("上传成功!")
                return True
        else:
            # 使用提供的文件对象
            # 准备请求参数
            request_params = {
                'bucket': bucket,
                'key': key,
                'body': file_obj
            }

            # 添加内容类型
            if content_type:
                request_params['content_type'] = content_type

            # 创建请求
            request = oss.PutObjectRequest(**request_params)

            # 执行上传
            client.put_object(request)
            print("上传成功!")
            return True

    except Exception as e:
        print(f"上传失败: {e}")

        # 尝试使用预签名 URL 方式上传
        try:
            # 上传文件
            if file_path:
                with open(file_path, 'rb') as f:
                    file_size = os.path.getsize(file_path)
                    print(f"尝试使用预签名 URL 上传文件: {file_path} ({file_size} 字节)")

                    response = requests.put(
                        url,
                        data=f,
                        headers=request_headers,
                        timeout=timeout
                    )
            else:
                # 使用提供的文件对象
                response = requests.put(
                    url,
                    data=file_obj,
                    headers=request_headers,
                    timeout=timeout
                )

            # 检查响应
            if response.status_code == 200:
                print("上传成功!")
                return True
            else:
                print(f"上传失败，服务器返回: {response.status_code} {response.text}")
                return False

        except requests.RequestException as e:
            print(f"预签名 URL 上传失败: {e}")
            return False
        except IOError as e:
            print(f"文件读取错误: {e}")
            return False


def upload_file(
    file_path: str,
    bucket: Optional[str] = None,
    key: Optional[str] = None,
    url: Optional[str] = None,
    region: str = "cn-qingdao",
    endpoint: Optional[str] = None,
    expires: int = 3600,
    content_type: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None
) -> bool:
    """
    上传文件到阿里云OSS

    参数:
        file_path: 本地文件路径
        bucket: OSS存储桶名称（如果提供了url则可选）
        key: OSS对象键名/文件路径（如果提供了url则可选）
        url: 预签名URL（如果提供了bucket和key则可选）
        region: OSS区域，默认为cn-qingdao
        endpoint: 自定义endpoint
        expires: URL过期时间（秒），默认为3600秒
        content_type: 文件内容类型，如'image/jpeg'
        headers: 自定义请求头

    返回:
        上传是否成功

    异常:
        ValueError: 如果参数无效
        其他异常: 由upload_file_to_url或generate_presigned_url引发的异常
    """
    # 检查参数
    if not file_path:
        raise ValueError("必须提供file_path")

    # 如果提供了URL，使用预签名URL上传
    if url:
        return upload_file_to_url(
            url=url,
            file_path=file_path,
            content_type=content_type,
            headers=headers
        )

    # 如果未提供bucket或key，则报错
    if not bucket or not key:
        # 如果未提供key但提供了file_path，使用file_path的文件名作为key
        if not key and bucket:
            key = os.path.basename(file_path)
        else:
            raise ValueError("必须提供bucket和key或url")

    try:
        # 直接使用 OSS SDK 上传文件
        import alibabacloud_oss_v2 as oss
        from robot_studio.database.oss.generate_url import create_oss_client

        # 创建 OSS 客户端
        client = create_oss_client(region, endpoint)

        # 上传文件
        file_size = os.path.getsize(file_path)
        print(f"正在上传文件: {file_path} ({file_size} 字节) 到 {bucket}/{key}")

        # 准备请求头
        request_headers = {}
        if headers:
            request_headers.update(headers)

        # 如果提供了content_type，添加到请求头
        if content_type:
            request_headers['Content-Type'] = content_type
        elif not 'Content-Type' in request_headers:
            # 根据文件扩展名猜测内容类型
            import mimetypes
            guessed_content_type, _ = mimetypes.guess_type(file_path)
            if guessed_content_type:
                request_headers['Content-Type'] = guessed_content_type

        # 使用 OSS SDK 直接上传文件
        with open(file_path, 'rb') as f:
            # 准备请求参数
            request_params = {
                'bucket': bucket,
                'key': key,
                'body': f
            }

            # 添加内容类型
            if 'Content-Type' in request_headers:
                request_params['content_type'] = request_headers['Content-Type']

            # 创建请求
            request = oss.PutObjectRequest(**request_params)

            # 执行上传
            client.put_object(request)
            print("上传成功!")
            return True

    except Exception as e:
        print(f"直接上传失败: {e}")

        # 如果直接上传失败，尝试使用预签名 URL 方式
        try:
            # 生成预签名 URL
            result = generate_presigned_url(
                bucket=bucket,
                key=key,
                method="PUT",
                region=region,
                endpoint=endpoint,
                expires=expires,
                headers=headers
            )
            url = result["url"]

            # 使用预签名 URL 上传
            return upload_file_to_url(
                url=url,
                file_path=file_path,
                content_type=content_type,
                headers=headers
            )
        except Exception as e:
            print(f"预签名 URL 上传失败: {e}")
            return False


def main():
    """
    命令行入口函数
    """
    parser = argparse.ArgumentParser(description="上传文件到阿里云OSS")

    # 添加参数组
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--url", help="预签名URL")
    group.add_argument("--bucket", help="OSS存储桶名称")

    # 其他参数
    parser.add_argument("--file", required=True, help="要上传的本地文件路径")
    parser.add_argument("--key", help="OSS对象键名/文件路径（与--bucket一起使用时可选，默认使用文件名）")
    parser.add_argument("--region", default="cn-qingdao", help="OSS区域，默认为cn-qingdao")
    parser.add_argument("--endpoint", help="自定义endpoint")
    parser.add_argument("--expires", type=int, default=3600, help="URL过期时间（秒），默认为3600秒")
    parser.add_argument("--content-type", help="文件内容类型，如'image/jpeg'")
    parser.add_argument("--header", action="append", help="自定义请求头，格式为key=value", default=[])

    args = parser.parse_args()

    # 处理自定义请求头
    headers = {}
    for header in args.header:
        if '=' in header:
            key, value = header.split('=', 1)
            headers[key] = value

    try:
        # 检查文件是否存在
        if not os.path.isfile(args.file):
            raise ValueError(f"文件不存在: {args.file}")

        # 上传文件
        if args.url:
            upload_file_to_url(
                url=args.url,
                file_path=args.file,
                content_type=args.content_type,
                headers=headers if headers else None
            )
        else:
            upload_file(
                file_path=args.file,
                bucket=args.bucket,
                key=args.key,
                region=args.region,
                endpoint=args.endpoint,
                expires=args.expires,
                content_type=args.content_type,
                headers=headers if headers else None
            )
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
