import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

from robot_studio.component.rag.graph_rag.index.utils.load_config import find_resource_root

"""
阿里云OSS配置工具

此模块提供了从配置文件中加载阿里云OSS配置的功能。
"""

def find_config_file() -> Optional[Path]:
    """
    查找OSS配置文件
    
    返回:
        配置文件路径，如果未找到则返回None
    """
    # 当前目录
    project_dir = find_resource_root() / "robot_studio/database/oss" / "oss_config.yaml"
    if project_dir.exists():
        return project_dir

    return None

def load_config() -> Dict[str, Any]:
    """
    加载OSS配置
    
    返回:
        配置字典
    
    异常:
        FileNotFoundError: 如果未找到配置文件
        yaml.YAMLError: 如果配置文件格式不正确
    """
    config_file = find_config_file()
    if not config_file:
        return {}
    
    with open(config_file, 'r') as f:
        try:
            return yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            print(f"配置文件格式错误: {e}")
            return {}

def load_oss_credentials() -> bool:
    """
    从配置文件加载OSS凭证到环境变量
    
    返回:
        是否成功加载凭证
    """
    config = load_config()
    
    # 获取OSS凭证
    access_key_id = config.get('access_key_id')
    access_key_secret = config.get('access_key_secret')
    
    # 如果环境变量中已经有凭证，则不覆盖
    if not os.environ.get('OSS_ACCESS_KEY_ID') and access_key_id:
        os.environ['OSS_ACCESS_KEY_ID'] = access_key_id
    
    if not os.environ.get('OSS_ACCESS_KEY_SECRET') and access_key_secret:
        os.environ['OSS_ACCESS_KEY_SECRET'] = access_key_secret
    
    # 检查是否成功设置环境变量
    return bool(os.environ.get('OSS_ACCESS_KEY_ID') and os.environ.get('OSS_ACCESS_KEY_SECRET'))
