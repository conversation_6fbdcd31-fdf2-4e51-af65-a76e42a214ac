import argparse
import os
import requests
from pathlib import Path
from typing import Optional, Dict, Any, Union

from robot_studio.database.oss.generate_url import generate_presigned_url

"""
阿里云OSS对象下载工具

此模块提供了从阿里云OSS下载文件的功能，支持以下操作：
1. 使用预签名URL下载文件
2. 自动生成预签名URL并下载文件
3. 支持自定义保存路径和下载参数

使用示例：
    # 作为命令行工具使用
    python download.py --bucket mindshake-yitong --key path/to/file.jpg --output ./downloads/

    # 使用已有的预签名URL下载
    python download.py --url "https://bucket.oss-region.aliyuncs.com/object?签名参数" --output ./downloads/

    # 作为模块导入使用
    from robot_studio.database.oss.download import download_file
    download_file(bucket="mindshake-yitong", key="path/to/file.jpg", output_dir="./downloads")
"""


def download_file_from_url(
    url: str,
    output_path: Optional[str] = None,
    chunk_size: int = 8192,
    timeout: int = 60,
    headers: Optional[Dict[str, str]] = None
) -> str:
    """
    从URL下载文件到指定路径

    参数:
        url: 文件下载URL
        output_path: 输出文件路径，如果为None则使用URL中的文件名保存到当前目录
        chunk_size: 下载块大小（字节）
        timeout: 请求超时时间（秒）
        headers: 自定义请求头

    返回:
        下载文件的保存路径

    异常:
        ValueError: 如果URL无效
        IOError: 如果文件无法保存
        requests.RequestException: 如果下载过程中出现网络错误
    """
    if not url:
        raise ValueError("URL不能为空")

    # 如果未指定输出路径，则从URL中提取文件名
    if not output_path:
        # 从URL中提取文件名（去除查询参数）
        file_name = url.split('?')[0].split('/')[-1]
        if not file_name:
            file_name = "downloaded_file"
        output_path = os.path.join(os.getcwd(), file_name)

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    # 下载文件
    try:
        with requests.get(url, stream=True, timeout=timeout, headers=headers) as response:
            response.raise_for_status()  # 如果响应状态码不是200，则引发异常

            # 获取文件大小（如果服务器提供）
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:  # 过滤掉保持连接活跃的空块
                        f.write(chunk)
                        downloaded += len(chunk)

                        # 打印下载进度
                        if total_size > 0:
                            percent = int(100 * downloaded / total_size)
                            print(f"\r下载进度: {percent}% [{downloaded}/{total_size} 字节]", end="")

            if total_size > 0:
                print()  # 换行

            print(f"下载完成: {output_path}")
            return output_path
    except requests.RequestException as e:
        raise requests.RequestException(f"下载失败: {e}")


def download_file(
    bucket: Optional[str] = None,
    key: Optional[str] = None,
    url: Optional[str] = None,
    output_dir: Optional[str] = None,
    output_filename: Optional[str] = None,
    region: str = "cn-qingdao",
    endpoint: Optional[str] = None,
    expires: int = 3600,
    headers: Optional[Dict[str, str]] = None
) -> str:
    """
    从阿里云OSS下载文件

    参数:
        bucket: OSS存储桶名称（如果提供了url则可选）
        key: OSS对象键名/文件路径（如果提供了url则可选）
        url: 预签名URL（如果提供了bucket和key则可选）
        output_dir: 输出目录，默认为当前目录
        output_filename: 输出文件名，默认使用对象键名中的文件名部分
        region: OSS区域，默认为cn-qingdao
        endpoint: 自定义endpoint
        expires: URL过期时间（秒），默认为3600秒
        headers: 自定义请求头

    返回:
        下载文件的保存路径

    异常:
        ValueError: 如果参数无效
        其他异常: 由download_file_from_url或generate_presigned_url引发的异常
    """
    # 检查参数
    if not url and (not bucket or not key):
        raise ValueError("必须提供url或同时提供bucket和key")

    # 如果未提供URL，则生成预签名URL
    if not url:
        result = generate_presigned_url(
            bucket=bucket,
            key=key,
            method="GET",
            region=region,
            endpoint=endpoint,
            expires=expires,
            headers=headers
        )
        url = result["url"]

    # 确定输出文件路径
    if not output_filename and key:
        # 从对象键名中提取文件名
        output_filename = os.path.basename(key)

    if output_dir:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, output_filename) if output_filename else None
    else:
        output_path = output_filename if output_filename else None

    # 下载文件
    return download_file_from_url(url, output_path, headers=headers)


def main():
    """
    命令行入口函数
    """
    parser = argparse.ArgumentParser(description="从阿里云OSS下载文件")

    # 添加参数组
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--url", help="预签名URL")
    group.add_argument("--bucket", help="OSS存储桶名称")

    # 其他参数
    parser.add_argument("--key", help="OSS对象键名/文件路径（与--bucket一起使用时必需）")
    parser.add_argument("--output", help="输出目录或文件路径")
    parser.add_argument("--region", default="cn-qingdao", help="OSS区域，默认为cn-qingdao")
    parser.add_argument("--endpoint", help="自定义endpoint")
    parser.add_argument("--expires", type=int, default=3600, help="URL过期时间（秒），默认为3600秒")
    parser.add_argument("--header", action="append", help="自定义请求头，格式为key=value", default=[])

    args = parser.parse_args()

    # 验证参数
    if args.bucket and not args.key:
        parser.error("使用--bucket时必须提供--key")

    # 处理自定义请求头
    headers = {}
    for header in args.header:
        if '=' in header:
            key, value = header.split('=', 1)
            headers[key] = value

    try:
        # 确定输出路径
        output_path = args.output
        output_dir = None
        output_filename = None

        if output_path:
            # 检查输出路径是目录还是文件
            if os.path.isdir(output_path) or output_path.endswith('/'):
                output_dir = output_path
            else:
                # 分离目录和文件名
                output_dir = os.path.dirname(output_path)
                output_filename = os.path.basename(output_path)

        # 下载文件
        if args.url:
            download_file_from_url(args.url, output_path, headers=headers if headers else None)
        else:
            download_file(
                bucket=args.bucket,
                key=args.key,
                output_dir=output_dir,
                output_filename=output_filename,
                region=args.region,
                endpoint=args.endpoint,
                expires=args.expires,
                headers=headers if headers else None
            )
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()