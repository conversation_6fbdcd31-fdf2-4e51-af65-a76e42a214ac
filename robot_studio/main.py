import os

from chainlit.utils import mount_chainlit
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware
from starlette.staticfiles import StaticFiles

from robot_studio.router.api import router
from robot_studio.utils.logger_config import setup_path_based_logging
from robot_studio.utils.api_logger import APILoggingMiddleware
from robot_studio.utils.otel_config import setup_otel_for_fastapi, instrument_fastapi_only

def create_mindshake_app():
    mindshake = FastAPI(title='MindShake AI', version='1.0')
    
    # 先初始化OpenTelemetry（包含LoggingInstrumentor）
    setup_otel_for_fastapi(
        service_name="robot-studio",
        service_version="1.0.0",
        environment="development",
        enable_console_exporter=True,
        enable_otlp_exporter=False
    )
    # 再初始化日志（LoggingInstrumentor已设置）
    setup_path_based_logging()
    # 先添加API日志中间件
    mindshake.add_middleware(APILoggingMiddleware)
    # 再Instrument FastAPI（确保trace context可用）
    instrument_fastapi_only(mindshake)
    mindshake.include_router(router)
    return mindshake


app = create_mindshake_app()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],  # 支持WebSocket所需的方法
    allow_headers=["*"],  # Allows all headers
)

# 启用chainlit集成的literalai
from robot_studio.common.config_service import get_config
literal_api_key = get_config('LITERAL_API_KEY', default='lsk_6mFmaWR7Q8RU1rVCWM0xwqBNxiUbNk9zLrZAgox4Q')
os.environ["LITERAL_API_KEY"] = literal_api_key
mount_chainlit(app=app, target="robot_studio/chainlit/app_ytwl_assistant.py", path="/chainlit")
