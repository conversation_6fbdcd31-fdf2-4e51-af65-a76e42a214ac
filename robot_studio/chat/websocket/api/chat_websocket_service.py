"""
WebSocket聊天服务层
提供WebSocket聊天的业务逻辑处理
"""

import logging
from datetime import datetime
from typing import AsyncGenerator, Optional, Dict, Any, List, Tuple

from robot_studio.chat.artifacts.api import ArtifactsService
from robot_studio.component.chat_chunk.manager import ChatChunkManager
from robot_studio.component.chat_chunk.model import Chat<PERSON>hunk, RoleEntity
from robot_studio.component.manage.model import ComponentBase
from robot_studio.component.runtime import ComponentExecutor
from robot_studio.component.runtime.model import ComponentTask
from robot_studio.component.session.api import SessionService
from robot_studio.component.session.manager import SessionManager
from robot_studio.component.session.model import ComponentSession
from robot_studio.common.api_handler import api_handler
from robot_studio.utils.uuid import DataType
from robot_studio.utils import uuid

from .request import WebSocketChatRequest, ChatProcessRequest
from .result import WebSocketChatResult, WebSocketChatResponse, <PERSON>ng<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>ult

logger = logging.getLogger(__name__)


class ChatWebSocketService:
    """WebSocket聊天服务，提供聊天相关的业务逻辑处理"""

    def __init__(self):
        self._session_service = SessionService()
        self._artifacts_service = ArtifactsService()
        self._chat_chunk_manager = ChatChunkManager()
        self._session_manager = SessionManager()
        self._component_executor = ComponentExecutor(component=ComponentBase(
            component_id="cmp_9dc834",
            version=1
        ))

    @api_handler
    def create_pong_response(self) -> PongResult:
        """创建心跳响应"""
        return PongResult()

    @api_handler
    def create_ack_response(self, message_id: str) -> AckResult:
        """创建确认响应"""
        return AckResult(id=message_id)

    @api_handler
    def validate_chat_session(self, request: WebSocketChatRequest) -> Tuple[bool, Optional[ComponentSession], Optional[str]]:
        """
        验证聊天会话
        Args:
            request: WebSocket聊天请求

        Returns:
            Tuple[bool, Optional[ComponentSession], Optional[str]]: (是否有效, 会话对象, 错误信息)
        """
        try:
            # 验证会话访问权限
            is_valid, session, error_msg = self._session_service.validate_session_access(
                session_id=request.session_id,
                user_id=request.user_id
            )
            
            if not is_valid:
                return False, None, error_msg
                
            return True, session, None
            
        except Exception as e:
            logger.error(f"验证会话失败: {str(e)}")
            return False, None, f"验证会话失败: {str(e)}"

    @api_handler
    def generate_message_ids(self, request: WebSocketChatRequest) -> Tuple[str, str]:
        """
        生成消息ID
        Args:
            request: WebSocket聊天请求

        Returns:
            Tuple[str, str]: (用户消息ID, 助手消息ID)
        """
        user_message_id = str(uuid.build_uuid(DataType.MESSAGE))
        assistant_message_id = str(uuid.build_uuid(DataType.MESSAGE))
        
        logger.info(f"生成消息ID - 用户: {user_message_id}, 助手: {assistant_message_id}")
        return user_message_id, assistant_message_id

    @api_handler
    async def save_user_message(
        self,
        session: ComponentSession,
        user_content: str,
        user_message_id: str,
        user_id: str,
        user_name: Optional[str] = None
    ) -> bool:
        """
        保存用户消息到数据库
        Args:
            session: 会话对象
            user_content: 用户消息内容
            user_message_id: 用户消息ID
            user_id: 用户ID
            user_name: 用户名称

        Returns:
            bool: 是否保存成功
        """
        try:
            logger.info(f"开始保存用户消息, 消息ID: {user_message_id}, 内容长度: {len(user_content)}")

            # 创建角色实体
            role_entity = RoleEntity(
                user_id=user_id,
                user_name=user_name or "用户"
            )

            # 创建用户ChatChunk对象
            user_message = ChatChunk(
                gmt_create=datetime.now(),
                gmt_modified=datetime.now(),
                session_id=session.session_id,
                role_type="user",
                role_entity=role_entity,
                chunk_id=user_message_id,
                chunk_type="Message",
                chunk_sub_type="user_input",
                chunk_status="success",
                content=user_content,
                runtime_params=None
            )

            # 使用ChatChunkManager保存到数据库
            saved_chunk = self._chat_chunk_manager.create_chat_chunk(user_message)
            
            logger.info(f"用户消息保存成功, 消息ID: {user_message_id}, 数据库ID: {saved_chunk.chunk_id}")
            return True

        except Exception as e:
            logger.error(f"保存用户消息失败, 消息ID: {user_message_id}, 错误: {str(e)}")
            return False

    @api_handler
    async def save_assistant_message(
        self,
        session: ComponentSession,
        full_response: str,
        assistant_message_id: str
    ) -> bool:
        """
        保存助手消息到数据库
        Args:
            session: 会话对象
            full_response: 完整响应内容
            assistant_message_id: 助手消息ID

        Returns:
            bool: 是否保存成功
        """
        try:
            logger.info(f"开始保存助手消息, 消息ID: {assistant_message_id}, 内容长度: {len(full_response)}")

            # 创建ChatChunk对象
            assistant_message = ChatChunk(
                gmt_create=datetime.now(),
                gmt_modified=datetime.now(),
                session_id=session.session_id,
                role_type="assistant",
                role_entity=RoleEntity(
                    user_id="assistant",
                    user_name="Assistant"
                ),
                chunk_id=assistant_message_id,
                chunk_type="Message",
                chunk_sub_type="completion",
                chunk_status="success",
                content=full_response,
                runtime_params=None
            )

            # 使用ChatChunkManager保存到数据库
            saved_chunk = self._chat_chunk_manager.create_chat_chunk(assistant_message)

            logger.info(f"助手消息保存成功, 消息ID: {assistant_message_id}, 数据库ID: {saved_chunk.chunk_id}")
            return True

        except Exception as e:
            logger.error(f"保存助手消息失败, 消息ID: {assistant_message_id}, 错误: {str(e)}")
            return False

    @api_handler
    def get_message_artifacts(self, message_id: str) -> List[Dict[str, Any]]:
        """
        获取消息产物列表
        Args:
            message_id: 消息ID

        Returns:
            List[Dict[str, Any]]: 产物列表
        """
        try:
            artifact_result = self._artifacts_service.get_artifacts_by_message(message_id)
            if artifact_result.success and artifact_result.data:
                artifacts = artifact_result.data.get('artifacts', [])
                logger.info(f"获取消息产物成功, 消息ID:{message_id}, 产物数量: {len(artifacts)}")
                return artifacts
        except Exception as e:
            logger.error(f"获取消息产物失败, 消息ID: {message_id}, 错误: {str(e)}")

        return []

    @api_handler
    async def update_session_info(self, session_id: str) -> bool:
        """
        更新会话信息 - 更新消息数量等统计信息
        Args:
            session_id: 会话ID

        Returns:
            bool: 是否更新成功
        """
        try:
            logger.info(f"开始更新会话信息, 会话ID: {session_id}")

            # 获取会话中的消息数量
            session_chunks = self._chat_chunk_manager.get_chat_chunks_by_session_id(session_id)
            message_count = len([chunk for chunk in session_chunks if chunk.chunk_type == "Message"])

            # 更新会话的最后消息时间
            session_info = self._session_manager.query_session_by_id(session_id)
            if session_info:
                session_info.last_message_time = datetime.now()
                session_info.message_count = message_count
                self._session_manager.update_session(session_info)

            logger.info(f"会话信息更新成功, 会话ID: {session_id}, 消息数量: {message_count}")
            return True

        except Exception as e:
            logger.error(f"更新会话信息失败, 会话ID: {session_id}, 错误: {str(e)}")
            return False

    @api_handler
    async def process_chat_stream(
        self,
        request: ChatProcessRequest,
        session: ComponentSession,
        start_time: datetime
    ) -> AsyncGenerator[WebSocketChatResult, None]:
        """
        处理聊天流式响应
        Args:
            request: 聊天处理请求
            session: 会话对象
            start_time: 开始时间

        Yields:
            WebSocketChatResult: 流式聊天结果 (BaseResult格式)
        """
        try:
            # 获取用户消息
            last_user_content = request.websocket_request.get_last_user_message()
            if not last_user_content:
                yield WebSocketChatResult.error_result(
                    request.assistant_message_id,
                    "未找到用户消息"
                )
                return

            logger.info(f"开始WebSocket流式响应生成, 会话:{session.session_id}, 用户消息:'{last_user_content}'")

            # 创建用户消息对象用于组件执行
            user_message = ChatChunk(
                gmt_create=datetime.now(),
                session_id=session.session_id,
                role_type="user",
                role_entity=RoleEntity(
                    user_id=request.websocket_request.user_id,
                    user_name=request.websocket_request.user_name,
                ),
                content=last_user_content,
                chunk_type="Message",
            )

            # 流式生成响应
            full_response = ""
            chunk_count = 0

            async for chunk in self._component_executor.reply_chunk_stream(user_message):
                if chunk:
                    chunk_count += 1
                    current_time = datetime.now()
                    elapsed_ms = (current_time - start_time).total_seconds() * 1000

                    # 检测返回的chunk类型并相应处理
                    if isinstance(chunk, ComponentTask):
                        # ComponentTask包含预聚合的内容，不需要额外累积
                        logger.info(f"收到ComponentTask, reply_messages数量: {len(chunk.reply_messages) if chunk.reply_messages else 0}")

                        if chunk.reply_messages:
                            # 从reply_messages中提取完整内容
                            for reply_msg in chunk.reply_messages:
                                if hasattr(reply_msg, 'content') and reply_msg.content:
                                    full_response = reply_msg.content  # 使用预聚合的完整内容，不累积
                                    logger.info(f"ComponentTask完整内容长度: {len(full_response)}")
                                    break

                        # ComponentTask通常是最终结果，跳出循环
                        break

                    else:
                        # ChatChunk对象 - 继续流式处理逻辑
                        chunk_content = chunk.content if hasattr(chunk, 'content') else str(chunk)

                        logger.info(f"发送第{chunk_count}个ChatChunk, 耗时:{elapsed_ms:.1f}ms, 内容长度:{len(chunk_content)}, 内容:{chunk_content}")

                        # 生成流式结果 (BaseResult格式)
                        chunk_result = WebSocketChatResult.success_chunk_result(
                            message_id=request.frontend_ai_message_id or request.assistant_message_id,
                            content=chunk_content,
                            chunk_type=chunk.chunk_type,
                            chunk_sub_type=chunk.chunk_sub_type if hasattr(chunk, 'chunk_sub_type') else '流式'
                        )

                        yield chunk_result

            # 保存助手消息
            await self.save_assistant_message(session, full_response, request.assistant_message_id)

            # 获取产物列表
            message_artifacts = self.get_message_artifacts(request.assistant_message_id)

            # 更新会话信息
            await self.update_session_info(session.session_id)

            # 生成完成结果
            end_time = datetime.now()
            total_elapsed_ms = (end_time - start_time).total_seconds() * 1000

            logger.info(f"准备发送完成事件, 总耗时:{total_elapsed_ms:.1f}ms, 总chunk数:{chunk_count}, 响应长度:{len(full_response)}")

            # 计算使用信息
            usage_info = {
                "prompt_tokens": len(last_user_content.split()) if last_user_content else 0,
                "completion_tokens": len(full_response.split()),
                "total_tokens": (len(last_user_content.split()) if last_user_content else 0) + len(full_response.split()),
            }
            #
            # completion_result = WebSocketChatResult.completion_result(
            #     message_id=request.frontend_ai_message_id or request.assistant_message_id,
            #     full_content=full_response,
            #     artifacts=message_artifacts,
            #     usage_info=usage_info
            # )

            # yield completion_result

            logger.info(f"WebSocket流式聊天完成, 会话:{session.session_id}, 总耗时:{total_elapsed_ms:.1f}ms, 总chunk数:{chunk_count}, 响应长度:{len(full_response)}")

        except Exception as e:
            logger.error(e, f"生成WebSocket流式响应失败, 会话:{session.session_id}, 错误:{str(e)}")
            yield WebSocketChatResult.error_result(
                request.assistant_message_id,
                str(e)
            )
