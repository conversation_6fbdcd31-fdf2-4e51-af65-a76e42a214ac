"""
产物管理器
提供产物的CRUD功能，专注于纯粹的数据操作
"""

import logging
from typing import Optional, List
from datetime import datetime

from robot_studio.chat.artifacts.model._artifacts_model import Artifact, ArtifactStatus
from robot_studio.database.mysql.repository._artifacts_repository import ArtifactsRepository
from robot_studio.database.mysql.repository._component_session_repository import ComponentSessionRepository

logger = logging.getLogger(__name__)


class ArtifactsManager:
    """产物管理器，提供产物的CRUD功能"""

    def __init__(self):
        self._artifacts_repository = ArtifactsRepository()
        self._session_repository = ComponentSessionRepository()

    def create_artifact(self, artifact: Artifact) -> Artifact:
        """
        创建产物
        Args:
            artifact: 产物模型

        Returns:
            Artifact: 创建后的产物模型
        """
        try:
            # 使用to_do方法转换为ArtifactsDO
            artifact_do = artifact.to_do()

            # 创建产物
            created_artifact = self._artifacts_repository.create_artifact(artifact_do)

            # 使用from_do方法转换回Artifact
            return Artifact.from_do(created_artifact)
        except Exception as e:
            logger.error(f"创建产物失败: {e}")
            raise

    def query_artifact_by_id(self, artifact_id: str) -> Optional[Artifact]:
        """
        根据产物ID查询产物
        Args:
            artifact_id: 产物ID

        Returns:
            Optional[Artifact]: 产物模型，不存在则返回None
        """
        try:
            artifact_do = self._artifacts_repository.get_artifact_by_id(artifact_id)
            if not artifact_do:
                return None
            return Artifact.from_do(artifact_do)
        except Exception as e:
            logger.error(f"查询产物失败: {e}")
            raise

    def query_artifacts_by_session_id(self, session_id: str) -> List[Artifact]:
        """
        根据会话ID查询产物列表
        Args:
            session_id: 会话ID

        Returns:
            List[Artifact]: 产物列表
        """
        try:
            artifacts_do = self._artifacts_repository.get_artifacts_by_session_id(session_id)
            return [Artifact.from_do(artifact) for artifact in artifacts_do]
        except Exception as e:
            logger.error(f"查询会话产物列表失败: {e}")
            return []

    def query_artifacts_by_type(self, artifact_type: str, limit: Optional[int] = None) -> List[Artifact]:
        """
        根据产物类型查询产物列表
        Args:
            artifact_type: 产物类型
            limit: 限制返回数量

        Returns:
            List[Artifact]: 产物列表
        """
        try:
            artifacts_do = self._artifacts_repository.get_artifacts_by_type(artifact_type, limit)
            return [Artifact.from_do(artifact) for artifact in artifacts_do]
        except Exception as e:
            logger.error(f"查询类型产物列表失败: {e}")
            return []

    def query_artifacts_by_status(self, status: int, limit: Optional[int] = None) -> List[Artifact]:
        """
        根据产物状态查询产物列表
        Args:
            status: 产物状态
            limit: 限制返回数量

        Returns:
            List[Artifact]: 产物列表
        """
        try:
            artifacts_do = self._artifacts_repository.get_artifacts_by_status(status, limit)
            return [Artifact.from_do(artifact) for artifact in artifacts_do]
        except Exception as e:
            logger.error(f"查询状态产物列表失败: {e}")
            return []

    def update_artifact(self, artifact: Artifact) -> bool:
        """
        更新产物信息
        Args:
            artifact: 产物模型

        Returns:
            bool: 更新是否成功
        """
        try:
            # 先查询现有产物
            existing_artifact = self._artifacts_repository.get_artifact_by_id(artifact.artifact_id)
            if not existing_artifact:
                return False

            # 构建更新数据
            update_data = {}
            if artifact.title is not None:
                update_data["title"] = artifact.title
            if artifact.description is not None:
                update_data["description"] = artifact.description
            if artifact.content is not None:
                update_data["content"] = artifact.content
            if artifact.file_path is not None:
                update_data["file_path"] = artifact.file_path
            if artifact.file_url is not None:
                update_data["file_url"] = artifact.file_url
            if artifact.file_size is not None:
                update_data["file_size"] = artifact.file_size
            if artifact.mime_type is not None:
                update_data["mime_type"] = artifact.mime_type
            if artifact.file_hash is not None:
                update_data["file_hash"] = artifact.file_hash
            if artifact.thumbnail_path is not None:
                update_data["thumbnail_path"] = artifact.thumbnail_path
            if artifact.tags is not None:
                update_data["tags"] = artifact.tags
            if artifact.status is not None:
                update_data["status"] = artifact.status
            if artifact.expires_at is not None:
                update_data["expires_at"] = artifact.expires_at
            if artifact.artifact_metadata is not None:
                update_data["artifact_metadata"] = artifact.artifact_metadata

            # 更新产物
            updated_artifact = self._artifacts_repository.update_artifact(existing_artifact.id, update_data)
            return updated_artifact is not None
        except Exception as e:
            logger.error(f"更新产物失败: {e}")
            raise

    def delete_artifact(self, artifact_id: str) -> bool:
        """
        删除产物（软删除）
        Args:
            artifact_id: 产物ID

        Returns:
            bool: 删除是否成功
        """
        try:
            return self._artifacts_repository.delete_artifact(artifact_id)
        except Exception as e:
            logger.error(f"删除产物失败: {e}")
            raise

    def increment_download_count(self, artifact_id: str) -> bool:
        """
        增加下载次数
        Args:
            artifact_id: 产物ID

        Returns:
            bool: 更新是否成功
        """
        try:
            return self._artifacts_repository.increment_download_count(artifact_id)
        except Exception as e:
            logger.error(f"增加下载次数失败: {e}")
            raise

    def update_artifact_status(self, artifact_id: str, status: int) -> bool:
        """
        更新产物状态
        Args:
            artifact_id: 产物ID
            status: 新状态

        Returns:
            bool: 更新是否成功
        """
        try:
            return self._artifacts_repository.update_artifact_status(artifact_id, status)
        except Exception as e:
            logger.error(f"更新产物状态失败: {e}")
            raise

    def get_session_info(self, session_id: str):
        """
        获取会话信息
        Args:
            session_id: 会话ID

        Returns:
            会话信息对象
        """
        try:
            return self._session_repository.get_session_by_id(session_id)
        except Exception as e:
            logger.error(f"获取会话信息失败: {e}")
            raise

    def query_artifacts_by_message_id(self, message_id: str) -> List[Artifact]:
        """
        根据消息ID查询产物列表
        Args:
            message_id: 消息ID

        Returns:
            List[Artifact]: 产物列表
        """
        try:
            artifacts_do = self._artifacts_repository.get_artifacts_by_message_id(message_id)
            return [Artifact.from_do(artifact) for artifact in artifacts_do]
        except Exception as e:
            logger.error(f"查询消息产物列表失败: {e}")
            return []

    def search_artifacts_by_keyword(self, keyword: str, limit: Optional[int] = None) -> List[Artifact]:
        """
        根据关键词搜索产物
        Args:
            keyword: 搜索关键词
            limit: 限制返回数量

        Returns:
            List[Artifact]: 产物列表
        """
        try:
            # 获取所有已完成的产物
            artifacts_do = self._artifacts_repository.get_artifacts_by_status(ArtifactStatus.COMPLETED, limit)

            # 过滤包含关键词的产物
            filtered_artifacts = []
            for artifact in artifacts_do:
                if (keyword.lower() in (artifact.title or "").lower() or
                    keyword.lower() in (artifact.description or "").lower() or
                    keyword.lower() in (artifact.tags or "").lower()):
                    filtered_artifacts.append(artifact)

            return [Artifact.from_do(artifact) for artifact in filtered_artifacts]
        except Exception as e:
            logger.error(f"搜索产物失败: {e}")
            return []