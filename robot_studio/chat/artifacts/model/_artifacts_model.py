"""
产物管理数据模型
定义产物管理相关的Command、Query、Result等数据传输对象
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any

from robot_studio.database.mysql.table_schema._artifacts import ArtifactsDO


@dataclass
class Artifact:
    """产物模型"""
    artifact_id: Optional[str] = None
    session_id: Optional[str] = None
    message_id: Optional[str] = None
    type: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    file_path: Optional[str] = None
    file_url: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    file_hash: Optional[str] = None
    thumbnail_path: Optional[str] = None
    tags: Optional[str] = None
    status: Optional[int] = None
    download_count: Optional[int] = None
    expires_at: Optional[datetime] = None
    is_deleted: Optional[bool] = None
    gmt_create: Optional[datetime] = None
    gmt_modified: Optional[datetime] = None
    artifact_metadata: Optional[str] = None

    @classmethod
    def from_do(cls, artifact_do: ArtifactsDO) -> 'Artifact':
        """从ArtifactsDO转换为Artifact模型"""
        return cls(
            artifact_id=artifact_do.artifact_id,
            session_id=artifact_do.session_id,
            message_id=artifact_do.message_id,
            type=artifact_do.type,
            title=artifact_do.title,
            description=artifact_do.description,
            content=artifact_do.content,
            file_path=artifact_do.file_path,
            file_url=artifact_do.file_url,
            file_size=artifact_do.file_size,
            mime_type=artifact_do.mime_type,
            file_hash=artifact_do.file_hash,
            thumbnail_path=artifact_do.thumbnail_path,
            tags=artifact_do.tags,
            status=artifact_do.status,
            download_count=artifact_do.download_count,
            expires_at=artifact_do.expires_at,
            is_deleted=artifact_do.is_deleted,
            gmt_create=artifact_do.gmt_create,
            gmt_modified=artifact_do.gmt_modified,
            artifact_metadata=artifact_do.artifact_metadata
        )

    def to_do(self) -> ArtifactsDO:
        """转换为ArtifactsDO对象"""
        return ArtifactsDO(
            artifact_id=self.artifact_id,
            session_id=self.session_id,
            message_id=self.message_id,
            type=self.type,
            title=self.title,
            description=self.description,
            content=self.content,
            file_path=self.file_path,
            file_url=self.file_url,
            file_size=self.file_size,
            mime_type=self.mime_type,
            file_hash=self.file_hash,
            thumbnail_path=self.thumbnail_path,
            tags=self.tags,
            status=self.status,
            download_count=self.download_count,
            expires_at=self.expires_at,
            is_deleted=self.is_deleted,
            gmt_create=self.gmt_create,
            gmt_modified=self.gmt_modified,
            artifact_metadata=self.artifact_metadata
        )

# 状态枚举常量
class ArtifactStatus:
    PENDING = 1      # 待处理
    PROCESSING = 2   # 处理中
    COMPLETED = 3    # 已完成
    FAILED = 4       # 失败
    EXPIRED = 5      # 过期

# 产物类型常量
class ArtifactType:
    IMAGE = "image"
    CODE = "code"
    CHART = "chart"
    FILE = "file"
    DOC = "doc"


# Query模型
@dataclass
class GetArtifactListQuery:
    """获取产物列表查询"""
    session_id: Optional[str] = None
    artifact_type: Optional[str] = None
    status: Optional[int] = None
    limit: int = 20
    offset: int = 0

    def validate(self) -> bool:
        """验证查询参数"""
        return (
            self.limit > 0 and self.limit <= 100 and
            self.offset >= 0
        )


@dataclass
class SearchArtifactsQuery:
    """搜索产物查询"""
    keyword: Optional[str] = None
    artifact_type: Optional[str] = None
    session_id: Optional[str] = None
    tags: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    limit: int = 20
    offset: int = 0

    def validate(self) -> bool:
        """验证查询参数"""
        return (
            self.limit > 0 and self.limit <= 100 and
            self.offset >= 0
        )


# Command模型
@dataclass
class CreateArtifactCommand:
    """创建产物命令"""
    session_id: str
    message_id: Optional[str] = None
    type: str = ArtifactType.FILE
    title: str = ""
    description: Optional[str] = None
    content: Optional[str] = None
    file_path: Optional[str] = None
    file_url: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    file_hash: Optional[str] = None
    thumbnail_path: Optional[str] = None
    tags: Optional[str] = None
    status: int = ArtifactStatus.PENDING
    expires_at: Optional[datetime] = None
    artifact_metadata: Optional[str] = None

    def validate(self) -> bool:
        """验证命令参数"""
        return (
            bool(self.session_id) and
            bool(self.type) and
            bool(self.title)
        )


@dataclass
class UpdateArtifactCommand:
    """更新产物命令"""
    artifact_id: str
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    file_path: Optional[str] = None
    file_url: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    file_hash: Optional[str] = None
    thumbnail_path: Optional[str] = None
    tags: Optional[str] = None
    status: Optional[int] = None
    expires_at: Optional[datetime] = None
    artifact_metadata: Optional[str] = None

    def validate(self) -> bool:
        """验证命令参数"""
        return bool(self.artifact_id)


# Result模型
@dataclass
class ArtifactResult:
    """产物结果"""
    artifact_id: str
    session_id: str
    message_id: Optional[str]
    type: str
    title: str
    description: Optional[str]
    content: Optional[str]
    file_path: Optional[str]
    file_url: Optional[str]
    file_size: Optional[int]
    mime_type: Optional[str]
    file_hash: Optional[str]
    thumbnail_path: Optional[str]
    tags: Optional[str]
    status: int
    download_count: Optional[int]
    expires_at: Optional[str]
    gmt_create: Optional[str]
    gmt_modified: Optional[str]
    artifact_metadata: Optional[str]

    @classmethod
    def from_artifact(cls, artifact: Artifact) -> 'ArtifactResult':
        """从Artifact模型转换"""
        return cls(
            artifact_id=artifact.artifact_id or "",
            session_id=artifact.session_id or "",
            message_id=artifact.message_id,
            type=artifact.type or "",
            title=artifact.title or "",
            description=artifact.description,
            content=artifact.content,
            file_path=artifact.file_path,
            file_url=artifact.file_url,
            file_size=artifact.file_size,
            mime_type=artifact.mime_type,
            file_hash=artifact.file_hash,
            thumbnail_path=artifact.thumbnail_path,
            tags=artifact.tags,
            status=artifact.status or ArtifactStatus.PENDING,
            download_count=artifact.download_count,
            expires_at=artifact.expires_at.isoformat() if artifact.expires_at else None,
            gmt_create=artifact.gmt_create.isoformat() if artifact.gmt_create else None,
            gmt_modified=artifact.gmt_modified.isoformat() if artifact.gmt_modified else None,
            artifact_metadata=artifact.artifact_metadata
        )


@dataclass
class ArtifactListResult:
    """产物列表结果"""
    artifacts: List[ArtifactResult]
    total_count: int
    limit: int
    offset: int


@dataclass
class SearchArtifactsResult:
    """搜索产物结果"""
    artifacts: List[ArtifactResult]
    total_count: int
    limit: int
    offset: int
    search_params: Dict[str, Any]


@dataclass
class SessionArtifactStatsResult:
    """会话产物统计结果"""
    session_id: str
    session_title: Optional[str]
    total_artifacts: int
    total_size: int
    total_downloads: int
    type_distribution: Dict[str, int]
    status_distribution: Dict[str, int]
    last_updated: Optional[str]


@dataclass
class DeleteArtifactResult:
    """删除产物结果"""
    artifact_id: str
    success: bool
    message: str


# 状态工具类
class ArtifactStatusHelper:
    """产物状态辅助类"""

    @staticmethod
    def get_status_name(status_code: int) -> str:
        """获取状态名称"""
        status_map = {
            ArtifactStatus.PENDING: "待处理",
            ArtifactStatus.PROCESSING: "处理中",
            ArtifactStatus.COMPLETED: "已完成",
            ArtifactStatus.FAILED: "失败",
            ArtifactStatus.EXPIRED: "过期"
        }
        return status_map.get(status_code, "未知状态")

    @staticmethod
    def get_status_code(status_name: str) -> int:
        """获取状态码"""
        status_map = {
            "待处理": ArtifactStatus.PENDING,
            "处理中": ArtifactStatus.PROCESSING,
            "已完成": ArtifactStatus.COMPLETED,
            "失败": ArtifactStatus.FAILED,
            "过期": ArtifactStatus.EXPIRED
        }
        return status_map.get(status_name, ArtifactStatus.PENDING)