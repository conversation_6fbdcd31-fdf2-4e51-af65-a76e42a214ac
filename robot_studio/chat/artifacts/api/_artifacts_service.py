"""
产物管理API服务层
提供对外API接口，使用@api_handler装饰器统一异常处理
"""

import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi.responses import StreamingResponse
import io

from robot_studio.chat.artifacts.manager import ArtifactsManager
from robot_studio.chat.artifacts.model import (
    Artifact, GetArtifactListQuery, SearchArtifactsQuery, CreateArtifactCommand, UpdateArtifactCommand,
    ArtifactResult, ArtifactListResult, SearchArtifactsResult, SessionArtifactStatsResult, DeleteArtifactResult,
    ArtifactStatusHelper
)
from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import BaseResult
from robot_studio.chat.artifacts.model import ArtifactStatus

logger = logging.getLogger(__name__)


class ArtifactsService:
    """产物管理API服务层，提供对外API接口"""

    def __init__(self):
        self._artifacts_manager = ArtifactsManager()

    @api_handler
    def get_artifact_list(self, query: GetArtifactListQuery) -> BaseResult:
        """
        获取产物列表
        Args:
            query: 查询参数

        Returns:
            BaseResult[ArtifactListResult]: 产物列表结果
        """
        # 验证查询参数
        if not query.validate():
            return BaseResult.param_error_result("无效的查询参数")

        # 根据查询条件获取产物列表
        artifacts = []
        if query.session_id:
            artifacts = self._artifacts_manager.query_artifacts_by_session_id(query.session_id)
        elif query.artifact_type:
            artifacts = self._artifacts_manager.query_artifacts_by_type(query.artifact_type, query.limit + query.offset)
        elif query.status is not None:
            artifacts = self._artifacts_manager.query_artifacts_by_status(query.status, query.limit + query.offset)
        else:
            # 获取最新的已完成产物
            artifacts = self._artifacts_manager.query_artifacts_by_status(ArtifactStatus.COMPLETED, query.limit + query.offset)

        # 应用分页
        total_count = len(artifacts)
        paged_artifacts = artifacts[query.offset:query.offset + query.limit] if query.offset < len(artifacts) else []

        # 转换为结果对象
        artifact_results = [ArtifactResult.from_artifact(artifact) for artifact in paged_artifacts]

        # 构造返回结果
        result = ArtifactListResult(
            artifacts=artifact_results,
            total_count=total_count,
            limit=query.limit,
            offset=query.offset
        )

        return BaseResult.success_result(result)

    @api_handler
    def get_artifact_detail(self, artifact_id: str) -> BaseResult:
        """
        获取产物详情
        Args:
            artifact_id: 产物ID

        Returns:
            BaseResult[ArtifactResult]: 产物详情结果
        """
        if not artifact_id:
            return BaseResult.param_error_result("产物ID不能为空")

        # 查询产物
        artifact = self._artifacts_manager.query_artifact_by_id(artifact_id)
        if not artifact:
            return BaseResult.not_found_error_result("产物不存在")

        # 转换为结果对象
        result = ArtifactResult.from_artifact(artifact)

        return BaseResult.success_result(result)

    @api_handler
    def create_artifact(self, command: CreateArtifactCommand) -> BaseResult:
        """
        创建产物
        Args:
            command: 创建产物命令

        Returns:
            BaseResult[ArtifactResult]: 创建结果
        """
        # 验证命令
        if not command.validate():
            return BaseResult.param_error_result("无效的创建产物命令")

        # 创建Artifact对象
        artifact = Artifact(
            session_id=command.session_id,
            message_id=command.message_id,
            type=command.type,
            title=command.title,
            description=command.description,
            content=command.content,
            file_path=command.file_path,
            file_url=command.file_url,
            file_size=command.file_size,
            mime_type=command.mime_type,
            file_hash=command.file_hash,
            thumbnail_path=command.thumbnail_path,
            tags=command.tags,
            status=command.status,
            expires_at=command.expires_at,
            artifact_metadata=command.artifact_metadata
        )

        # 调用manager创建产物
        created_artifact = self._artifacts_manager.create_artifact(artifact)
        if not created_artifact:
            return BaseResult.server_error_result("创建产物失败")

        # 构造返回结果
        result = ArtifactResult.from_artifact(created_artifact)

        return BaseResult.success_result(result)

    @api_handler
    def update_artifact(self, command: UpdateArtifactCommand) -> BaseResult:
        """
        更新产物信息
        Args:
            command: 更新产物命令

        Returns:
            BaseResult: 更新结果
        """
        # 验证命令
        if not command.validate():
            return BaseResult.param_error_result("无效的更新产物命令")

        # 查询产物是否存在
        artifact = self._artifacts_manager.query_artifact_by_id(command.artifact_id)
        if not artifact:
            return BaseResult.not_found_error_result("产物不存在")

        # 更新产物信息
        if command.title is not None:
            artifact.title = command.title
        if command.description is not None:
            artifact.description = command.description
        if command.content is not None:
            artifact.content = command.content
        if command.file_path is not None:
            artifact.file_path = command.file_path
        if command.file_url is not None:
            artifact.file_url = command.file_url
        if command.file_size is not None:
            artifact.file_size = command.file_size
        if command.mime_type is not None:
            artifact.mime_type = command.mime_type
        if command.file_hash is not None:
            artifact.file_hash = command.file_hash
        if command.thumbnail_path is not None:
            artifact.thumbnail_path = command.thumbnail_path
        if command.tags is not None:
            artifact.tags = command.tags
        if command.status is not None:
            artifact.status = command.status
        if command.expires_at is not None:
            artifact.expires_at = command.expires_at
        if command.artifact_metadata is not None:
            artifact.artifact_metadata = command.artifact_metadata

        # 调用manager更新
        success = self._artifacts_manager.update_artifact(artifact)
        if not success:
            return BaseResult.server_error_result("更新产物失败")

        return BaseResult.success_result()

    @api_handler
    def delete_artifact(self, artifact_id: str) -> BaseResult:
        """
        删除产物
        Args:
            artifact_id: 产物ID

        Returns:
            BaseResult[DeleteArtifactResult]: 删除结果
        """
        if not artifact_id:
            return BaseResult.param_error_result("产物ID不能为空")

        # 查询产物是否存在
        artifact = self._artifacts_manager.query_artifact_by_id(artifact_id)
        if not artifact:
            return BaseResult.not_found_error_result("产物不存在")

        # 调用manager删除
        success = self._artifacts_manager.delete_artifact(artifact_id)
        if not success:
            return BaseResult.server_error_result("删除产物失败")

        # 构造返回结果
        result = DeleteArtifactResult(
            artifact_id=artifact_id,
            success=True,
            message="产物删除成功"
        )

        return BaseResult.success_result(result)

    @api_handler
    def download_artifact(self, artifact_id: str) -> StreamingResponse:
        """
        下载产物内容
        Args:
            artifact_id: 产物ID

        Returns:
            StreamingResponse: 文件流响应
        """
        if not artifact_id:
            raise ValueError("产物ID不能为空")

        # 查询产物
        artifact = self._artifacts_manager.query_artifact_by_id(artifact_id)
        if not artifact:
            raise ValueError("产物不存在")

        # 检查产物是否已过期
        if artifact.expires_at and artifact.expires_at < datetime.now():
            raise ValueError("产物已过期")

        # 增加下载次数
        self._artifacts_manager.increment_download_count(artifact_id)

        # 准备文件内容
        content = artifact.content or ""
        file_stream = io.BytesIO(content.encode('utf-8'))

        # 设置文件名
        filename = artifact.title or f"artifact_{artifact_id}"
        if not filename.endswith('.txt'):
            filename += '.txt'

        # 返回流响应
        return StreamingResponse(
            io.BytesIO(content.encode('utf-8')),
            media_type=artifact.mime_type or "application/octet-stream",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    @api_handler
    def get_artifacts_by_message(self, message_id: str) -> BaseResult:
        """
        根据消息ID获取产物列表
        Args:
            message_id: 消息ID

        Returns:
            BaseResult: 包含产物列表的结果，格式为 {'success': bool, 'artifacts': List[ArtifactResult]}
        """
        if not message_id:
            return BaseResult.param_error_result("消息ID不能为空")

        # 查询产物列表
        artifacts = self._artifacts_manager.query_artifacts_by_message_id(message_id)

        # 转换为结果对象
        artifact_results = [ArtifactResult.from_artifact(artifact) for artifact in artifacts]

        # 构造返回结果 - 为了兼容现有代码，返回特定格式
        result_data = {
            "success": True,
            "artifacts": artifact_results
        }

        return BaseResult.success_result(result_data)

    @api_handler
    def search_artifacts(self, query: SearchArtifactsQuery) -> BaseResult:
        """
        搜索产物
        Args:
            query: 搜索查询

        Returns:
            BaseResult[SearchArtifactsResult]: 搜索结果
        """
        # 验证查询参数
        if not query.validate():
            return BaseResult.param_error_result("无效的搜索参数")

        # 构建搜索条件
        artifacts = []

        if query.session_id:
            artifacts = self._artifacts_manager.query_artifacts_by_session_id(query.session_id)
        elif query.artifact_type:
            artifacts = self._artifacts_manager.query_artifacts_by_type(query.artifact_type)
        else:
            artifacts = self._artifacts_manager.query_artifacts_by_status(ArtifactStatus.COMPLETED)

        # 关键词过滤
        if query.keyword:
            filtered_artifacts = []
            keyword_lower = query.keyword.lower()
            for artifact in artifacts:
                if (keyword_lower in (artifact.title or "").lower() or
                    keyword_lower in (artifact.description or "").lower() or
                    keyword_lower in (artifact.tags or "").lower()):
                    filtered_artifacts.append(artifact)
            artifacts = filtered_artifacts

        # 标签过滤
        if query.tags:
            filtered_artifacts = []
            for artifact in artifacts:
                artifact_tags = (artifact.tags or "").lower()
                if any(tag.lower() in artifact_tags for tag in query.tags):
                    filtered_artifacts.append(artifact)
            artifacts = filtered_artifacts

        # 日期过滤
        if query.start_date or query.end_date:
            filtered_artifacts = []
            for artifact in artifacts:
                if artifact.gmt_create:
                    create_date = artifact.gmt_create.date()
                    if query.start_date:
                        start_date = datetime.strptime(query.start_date, "%Y-%m-%d").date()
                        if create_date < start_date:
                            continue
                    if query.end_date:
                        end_date = datetime.strptime(query.end_date, "%Y-%m-%d").date()
                        if create_date > end_date:
                            continue
                    filtered_artifacts.append(artifact)
            artifacts = filtered_artifacts

        # 应用分页
        total_count = len(artifacts)
        paged_artifacts = artifacts[query.offset:query.offset + query.limit] if query.offset < len(artifacts) else []

        # 转换为结果对象
        artifact_results = [ArtifactResult.from_artifact(artifact) for artifact in paged_artifacts]

        # 构造返回结果
        result = SearchArtifactsResult(
            artifacts=artifact_results,
            total_count=total_count,
            limit=query.limit,
            offset=query.offset,
            search_params={
                "keyword": query.keyword,
                "artifact_type": query.artifact_type,
                "session_id": query.session_id,
                "tags": query.tags,
                "start_date": query.start_date,
                "end_date": query.end_date
            }
        )

        return BaseResult.success_result(result)

    @api_handler
    def get_session_artifact_stats(self, session_id: str) -> BaseResult:
        """
        获取会话产物统计信息
        Args:
            session_id: 会话ID

        Returns:
            BaseResult[SessionArtifactStatsResult]: 统计结果
        """
        if not session_id:
            return BaseResult.param_error_result("会话ID不能为空")

        # 获取会话产物列表
        artifacts = self._artifacts_manager.query_artifacts_by_session_id(session_id)

        # 计算统计信息
        total_size = sum(artifact.file_size or 0 for artifact in artifacts)
        total_downloads = sum(artifact.download_count or 0 for artifact in artifacts)

        # 类型分布统计
        type_stats = {}
        for artifact in artifacts:
            artifact_type = artifact.type or "unknown"
            type_stats[artifact_type] = type_stats.get(artifact_type, 0) + 1

        # 状态分布统计
        status_stats = {}
        for artifact in artifacts:
            status_name = ArtifactStatusHelper.get_status_name(artifact.status or ArtifactStatus.PENDING)
            status_stats[status_name] = status_stats.get(status_name, 0) + 1

        # 获取会话信息
        session_info = self._artifacts_manager.get_session_info(session_id)

        # 获取最后更新时间
        last_updated = None
        if artifacts:
            modified_times = [artifact.gmt_modified for artifact in artifacts if artifact.gmt_modified]
            if modified_times:
                last_updated = max(modified_times).isoformat()

        # 构造返回结果
        result = SessionArtifactStatsResult(
            session_id=session_id,
            session_title=session_info.title if session_info else None,
            total_artifacts=len(artifacts),
            total_size=total_size,
            total_downloads=total_downloads,
            type_distribution=type_stats,
            status_distribution=status_stats,
            last_updated=last_updated
        )

        return BaseResult.success_result(result)