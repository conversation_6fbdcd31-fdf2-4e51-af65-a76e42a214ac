"""
聊天块API服务层
提供对外API接口，使用@api_handler装饰器统一异常处理
"""

import logging
from typing import Optional, Literal
import json

from robot_studio.component.chat_chunk.manager import ChatChunkManager
from robot_studio.component.chat_chunk.model import Chat<PERSON>hunk
from robot_studio.chat.chat_chunk.api.request import (
    CreateChatChunkCommand, UpdateChatChunkCommand, GetChatChunkListQuery, SearchChatChunksQuery
)
from robot_studio.chat.chat_chunk.api.result import (
    ChatChunkResult, ChatChunkListResult, SearchChatChunksResult, DeleteChatChunkResult,
    SessionChatChunkStatsResult, GetSessionMessagesResult
)
from robot_studio.common.api_handler import api_handler
from robot_studio.common.base_result import BaseResult
from robot_studio.chat.artifacts.manager import ArtifactsManager
from robot_studio.component.session.manager import SessionManager

logger = logging.getLogger(__name__)


class ChatChunkService:
    """聊天块API服务层，提供对外API接口"""

    def __init__(self):
        self._chat_chunk_manager = ChatChunkManager()
        self._artifacts_manager = ArtifactsManager()
        self._session_manager = SessionManager()

    @api_handler
    def create_chat_chunk(self, command: CreateChatChunkCommand) -> BaseResult:
        """
        创建聊天块
        Args:
            command: 创建命令

        Returns:
            BaseResult[ChatChunkResult]: 创建结果
        """
        # 验证命令参数
        if not command.validate():
            return BaseResult.param_error_result("无效的创建参数")

        # 创建聊天块模型
        chat_chunk = ChatChunk(
            session_id=command.session_id,
            role_type=command.role_type,
            role_entity=command.role_entity,
            task_id=command.task_id,
            span_id=command.span_id,
            parent_span_id=command.parent_span_id,
            parent_chunk_id=command.parent_chunk_id,
            chunk_type=command.chunk_type,
            chunk_sub_type=command.chunk_sub_type,
            chunk_status=command.chunk_status,
            content=command.content,
            runtime_params=command.runtime_params
        )

        # 创建聊天块
        created_chunk = self._chat_chunk_manager.create_chat_chunk(chat_chunk)

        result = ChatChunkResult.from_chat_chunk(created_chunk)
        return BaseResult.success_result(result)

    @api_handler
    def get_chat_chunk_detail(self, chunk_id: str) -> BaseResult:
        """
        获取聊天块详情
        Args:
            chunk_id: 聊天块ID

        Returns:
            BaseResult[ChatChunkResult]: 聊天块详情结果
        """
        if not chunk_id or not chunk_id.strip():
            return BaseResult.param_error_result("聊天块ID不能为空")

        chat_chunk = self._chat_chunk_manager.get_chat_chunk_by_id(chunk_id)
        if not chat_chunk:
            return BaseResult.error_result("20001", "聊天块不存在")

        result = ChatChunkResult.from_chat_chunk(chat_chunk)
        return BaseResult.success_result(result)

    @api_handler
    def get_chat_chunk_list(self, query: GetChatChunkListQuery) -> BaseResult:
        """
        获取聊天块列表
        Args:
            query: 查询参数

        Returns:
            BaseResult[ChatChunkListResult]: 聊天块列表结果
        """
        # 验证查询参数
        if not query.validate():
            return BaseResult.param_error_result("无效的查询参数")

        # 获取聊天块列表
        chat_chunks = self._chat_chunk_manager.get_chat_chunks_by_session_id(
            session_id=query.session_id,
            chunk_type=query.chunk_type,
            limit=query.limit,
            offset=query.offset
        )

        # 获取总数量
        total_count = self._chat_chunk_manager.get_session_chunk_count(query.session_id)

        # 判断是否还有更多数据
        has_more = False
        if query.limit and query.offset is not None:
            has_more = (query.offset + len(chat_chunks)) < total_count

        result = ChatChunkListResult.from_chat_chunks(
            chat_chunks=chat_chunks,
            total_count=total_count,
            has_more=has_more
        )
        return BaseResult.success_result(result)

    @api_handler
    def search_chat_chunks(self, query: SearchChatChunksQuery) -> BaseResult:
        """
        搜索聊天块
        Args:
            query: 搜索查询参数

        Returns:
            BaseResult[SearchChatChunksResult]: 搜索结果
        """
        # 验证查询参数
        if not query.validate():
            return BaseResult.param_error_result("无效的搜索参数")

        # 搜索聊天块
        chat_chunks = self._chat_chunk_manager.search_chat_chunks(
            session_id=query.session_id,
            keyword=query.keyword,
            chunk_type=query.chunk_type,
            start_time=query.start_time,
            end_time=query.end_time,
            task_id=query.task_id,
            span_id=query.span_id,
            role_type=query.role_type,
            chunk_sub_type=query.chunk_sub_type,
            chunk_status=query.chunk_status,
            limit=query.limit,
            offset=query.offset
        )

        # 判断是否还有更多数据
        has_more = False
        if query.limit and len(chat_chunks) == query.limit:
            has_more = True

        result = SearchChatChunksResult.from_search_results(
            chat_chunks=chat_chunks,
            total_count=len(chat_chunks),
            has_more=has_more
        )
        return BaseResult.success_result(result)

    @api_handler
    def update_chat_chunk(self, command: UpdateChatChunkCommand) -> BaseResult:
        """
        更新聊天块
        Args:
            command: 更新命令

        Returns:
            BaseResult[ChatChunkResult]: 更新结果
        """
        # 验证命令参数
        if not command.validate():
            return BaseResult.param_error_result("无效的更新参数")

        # 构建更新数据
        update_data = {}
        if command.content is not None:
            update_data["content"] = command.content
        if command.content_format is not None:
            update_data["content_format"] = command.content_format
        if command.token_count is not None:
            update_data["token_count"] = command.token_count
        if command.model_info is not None:
            update_data["model_info"] = command.model_info
        if command.status is not None:
            update_data["status"] = command.status

        # 处理JSON字段
        if command.usage_info is not None:
            update_data["usage_info"] = json.dumps(command.usage_info)
        if command.chunk_metadata is not None:
            update_data["chunk_metadata"] = json.dumps(command.chunk_metadata)
        if command.thinking_steps is not None:
            update_data["thinking_steps"] = json.dumps(command.thinking_steps)

        # 更新聊天块
        updated_chunk = self._chat_chunk_manager.update_chat_chunk(command.chunk_id, update_data)
        if not updated_chunk:
            return BaseResult.error_result("20001", "聊天块不存在或更新失败")

        result = ChatChunkResult(chat_chunk=updated_chunk)
        return BaseResult.success_result(result)

    @api_handler
    def delete_chat_chunk(self, chunk_id: str) -> BaseResult:
        """
        删除聊天块
        Args:
            chunk_id: 聊天块ID

        Returns:
            BaseResult[DeleteChatChunkResult]: 删除结果
        """
        if not chunk_id or not chunk_id.strip():
            return BaseResult.param_error_result("聊天块ID不能为空")

        success = self._chat_chunk_manager.delete_chat_chunk(chunk_id)

        result = DeleteChatChunkResult.create_success(chunk_id) if success else DeleteChatChunkResult.create_failure(chunk_id)
        return BaseResult.success_result(result)

    @api_handler
    def get_session_chat_chunk_stats(self, session_id: str) -> BaseResult:
        """
        获取会话聊天块统计信息
        Args:
            session_id: 会话ID

        Returns:
            BaseResult[SessionChatChunkStatsResult]: 统计结果
        """
        if not session_id or not session_id.strip():
            return BaseResult.param_error_result("会话ID不能为空")

        stats = self._chat_chunk_manager.get_session_chunk_stats(session_id)

        result = SessionChatChunkStatsResult.from_stats(
            session_id=stats["session_id"],
            total_chunks=stats["total_chunks"],
            user_chunks=stats["user_chunks"],
            assistant_chunks=stats["assistant_chunks"],
            system_chunks=stats.get("system_chunks", 0),
            thinking_chunks=stats.get("thinking_chunks", 0),
            total_tokens=stats.get("total_tokens", 0),
            total_artifacts=stats.get("total_artifacts", 0)
        )
        return BaseResult.success_result(result)



    @api_handler
    def get_chat_chunks_by_task_id(self, task_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> BaseResult:
        """
        根据任务ID获取聊天块列表
        Args:
            task_id: 任务ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            BaseResult[ChatChunkListResult]: 聊天块列表结果
        """
        if not task_id or not task_id.strip():
            return BaseResult.param_error_result("任务ID不能为空")

        chat_chunks = self._chat_chunk_manager.get_chat_chunks_by_task_id(task_id, limit, offset)

        result = ChatChunkListResult(
            chat_chunks=chat_chunks,
            total_count=len(chat_chunks),
            has_more=False  # 简化处理，不计算是否有更多
        )
        return BaseResult.success_result(result)

    @api_handler
    def get_chat_chunks_by_span_id(self, span_id: str, limit: Optional[int] = None, offset: Optional[int] = None) -> BaseResult:
        """
        根据逻辑单元ID获取聊天块列表
        Args:
            span_id: 逻辑单元ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            BaseResult[ChatChunkListResult]: 聊天块列表结果
        """
        if not span_id or not span_id.strip():
            return BaseResult.param_error_result("逻辑单元ID不能为空")

        chat_chunks = self._chat_chunk_manager.get_chat_chunks_by_span_id(span_id, limit, offset)

        result = ChatChunkListResult(
            chat_chunks=chat_chunks,
            total_count=len(chat_chunks),
            has_more=False  # 简化处理，不计算是否有更多
        )
        return BaseResult.success_result(result)

    @api_handler
    def get_chat_chunks_by_role_type(self, session_id: str, role_type: Literal['user', 'assistant'], limit: Optional[int] = None) -> BaseResult:
        """
        根据会话ID和角色类型获取聊天块列表
        Args:
            session_id: 会话ID
            role_type: 角色类型
            limit: 限制返回数量

        Returns:
            BaseResult[ChatChunkListResult]: 聊天块列表结果
        """
        if not session_id or not session_id.strip():
            return BaseResult.param_error_result("会话ID不能为空")
        if not role_type or not role_type.strip():
            return BaseResult.param_error_result("角色类型不能为空")

        chat_chunks = self._chat_chunk_manager.get_chat_chunks_by_role_type(session_id, role_type, limit)

        result = ChatChunkListResult(
            chat_chunks=chat_chunks,
            total_count=len(chat_chunks),
            has_more=False  # 简化处理，不计算是否有更多
        )
        return BaseResult.success_result(result)

    @api_handler
    def get_session_messages(self, session_id:str, limit:int, offset:int) -> BaseResult:
        """
        获取会话消息列表 - 迁移自SessionService，使用ChatChunk基础设施
        Args:
            query: 获取消息查询

        Returns:
            Result[GetSessionMessagesResult]: 消息列表结果
        """
        # 验证会话是否存在
        session = self._session_manager.query_session_by_id(session_id)
        if not session:
            return BaseResult.server_error_result("会话不存在")

        # 查询消息列表 - 直接使用ChatChunkManager
        message_list = self._chat_chunk_manager.get_chat_chunks_by_session_id(
            session_id=session_id,
            limit=limit,
            offset=offset
        )

        # 构造返回结果
        result = GetSessionMessagesResult.from_messages(
            session_id=session_id,
            session_title=session.title or "",
            messages=message_list,
            limit=limit,
            offset=offset
        )

        return BaseResult.success_result(result)
