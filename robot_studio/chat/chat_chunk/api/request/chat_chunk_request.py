
"""
ChatChunk请求模型
包含聊天块相关的命令和查询类
"""

from datetime import datetime
from typing import Optional, Dict, Any, Literal
from dataclasses import dataclass

from robot_studio.component.chat_chunk.model import RoleEntity


# 命令类定义
@dataclass
class CreateChatChunkCommand:
    """创建聊天块命令"""
    session_id: str
    role_type: Literal['user', 'assistant']
    chunk_type: Literal['Event', 'Message']
    content: str
    task_id: Optional[str] = None
    span_id: Optional[str] = None
    parent_span_id: Optional[str] = None
    chunk_sub_type: Optional[str] = None
    chunk_status: Optional[str] = None
    role_entity: Optional[RoleEntity] = None
    runtime_params: Optional[Dict[str, Any]] = None

    def validate(self) -> bool:
        """验证命令参数"""
        if not self.session_id or not self.session_id.strip():
            return False
        if not self.role_type or self.role_type not in ["user", "assistant"]:
            return False
        if not self.chunk_type or self.chunk_type not in ["Event", "Message"]:
            return False
        if not self.content:
            return False
        return True


@dataclass
class UpdateChatChunkCommand:
    """更新聊天块命令"""
    chunk_id: str
    session_id: Optional[str] = None
    role_type: Optional[Literal['user', 'assistant']] = None
    role_entity: Optional[RoleEntity] = None
    task_id: Optional[str] = None
    span_id: Optional[str] = None
    parent_span_id: Optional[str] = None
    chunk_type: Optional[Literal['Event', 'Message']] = None
    chunk_sub_type: Optional[str] = None
    chunk_status: Optional[str] = None
    content: Optional[str] = None
    runtime_params: Optional[Dict[str, Any]] = None

    def validate(self) -> bool:
        """验证命令参数"""
        if not self.chunk_id or not self.chunk_id.strip():
            return False
        return True


# 查询类定义
@dataclass
class GetChatChunkListQuery:
    """获取聊天块列表查询"""
    session_id: str
    chunk_type: Optional[Literal['Event', 'Message']] = None
    limit: Optional[int] = None
    offset: Optional[int] = None

    def validate(self) -> bool:
        """验证查询参数"""
        if not self.session_id or not self.session_id.strip():
            return False
        if self.limit is not None and (self.limit <= 0 or self.limit > 100):
            return False
        if self.offset is not None and self.offset < 0:
            return False
        return True


@dataclass
class SearchChatChunksQuery:
    """搜索聊天块查询"""
    session_id: Optional[str] = None
    keyword: Optional[str] = None
    chunk_type: Optional[Literal['Event', 'Message']] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    task_id: Optional[str] = None
    span_id: Optional[str] = None
    role_type: Optional[Literal['user', 'assistant']] = None
    chunk_sub_type: Optional[str] = None
    chunk_status: Optional[str] = None
    limit: Optional[int] = None
    offset: Optional[int] = None

    def validate(self) -> bool:
        """验证查询参数"""
        if self.limit is not None and (self.limit <= 0 or self.limit > 100):
            return False
        if self.offset is not None and self.offset < 0:
            return False
        if self.start_time and self.end_time and self.start_time > self.end_time:
            return False
        return True
