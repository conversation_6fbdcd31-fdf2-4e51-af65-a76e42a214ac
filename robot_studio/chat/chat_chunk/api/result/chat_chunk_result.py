
"""
ChatChunk响应模型
包含聊天块相关的结果类
"""

from typing import List
from dataclasses import dataclass

from robot_studio.component.chat_chunk.model import ChatChunk


# 结果类定义
@dataclass
class ChatChunkResult:
    """聊天块结果"""
    chat_chunk: ChatChunk

    @classmethod
    def from_chat_chunk(cls, chat_chunk: ChatChunk) -> "ChatChunkResult":
        """从ChatChunk对象创建结果"""
        return cls(chat_chunk=chat_chunk)


@dataclass
class ChatChunkListResult:
    """聊天块列表结果"""
    chat_chunks: List[ChatChunk]
    total_count: int
    has_more: bool

    @classmethod
    def from_chat_chunks(cls, chat_chunks: List[ChatChunk], total_count: int = None, has_more: bool = False) -> "ChatChunkListResult":
        """从ChatChunk列表创建结果"""
        if total_count is None:
            total_count = len(chat_chunks)
        return cls(
            chat_chunks=chat_chunks,
            total_count=total_count,
            has_more=has_more
        )


@dataclass
class SearchChatChunksResult:
    """搜索聊天块结果"""
    chat_chunks: List[ChatChunk]
    total_count: int
    has_more: bool

    @classmethod
    def from_search_results(cls, chat_chunks: List[ChatChunk], total_count: int = None, has_more: bool = False) -> "SearchChatChunksResult":
        """从搜索结果创建结果"""
        if total_count is None:
            total_count = len(chat_chunks)
        return cls(
            chat_chunks=chat_chunks,
            total_count=total_count,
            has_more=has_more
        )


@dataclass
class DeleteChatChunkResult:
    """删除聊天块结果"""
    success: bool
    chunk_id: str

    @classmethod
    def create_success(cls, chunk_id: str) -> "DeleteChatChunkResult":
        """创建成功结果"""
        return cls(success=True, chunk_id=chunk_id)

    @classmethod
    def create_failure(cls, chunk_id: str) -> "DeleteChatChunkResult":
        """创建失败结果"""
        return cls(success=False, chunk_id=chunk_id)


@dataclass
class SessionChatChunkStatsResult:
    """会话聊天块统计结果"""
    session_id: str
    total_chunks: int
    user_chunks: int
    assistant_chunks: int
    system_chunks: int = 0
    thinking_chunks: int = 0
    total_tokens: int = 0
    total_artifacts: int = 0

    @classmethod
    def from_stats(cls, session_id: str, total_chunks: int, user_chunks: int, assistant_chunks: int,
                   system_chunks: int = 0, thinking_chunks: int = 0, total_tokens: int = 0,
                   total_artifacts: int = 0) -> "SessionChatChunkStatsResult":
        """从统计数据创建结果"""
        return cls(
            session_id=session_id,
            total_chunks=total_chunks,
            user_chunks=user_chunks,
            assistant_chunks=assistant_chunks,
            system_chunks=system_chunks,
            thinking_chunks=thinking_chunks,
            total_tokens=total_tokens,
            total_artifacts=total_artifacts
        )


@dataclass
class GetSessionMessagesResult:
    """获取会话消息结果"""
    session_id: str
    session_title: str
    messages: List[ChatChunk]
    total_count: int
    limit: int
    offset: int

    @classmethod
    def from_messages(cls, session_id: str, session_title: str, messages: List[ChatChunk],
                     limit: int, offset: int) -> "GetSessionMessagesResult":
        """从消息列表创建结果"""
        return cls(
            session_id=session_id,
            session_title=session_title,
            messages=messages,
            total_count=len(messages),
            limit=limit,
            offset=offset
        )
