from typing import Self
from enum import Enum

from pydantic import BaseModel

from robot_studio.database.mysql.table_schema import CompanyDO


class CompanyType(Enum):
    """公司类型枚举"""

    REAL_ENTITY = "REAL_ENTITY"  # 真实的公司实体
    VIRTUAL_ENTITY = "VIRTUAL_ENTITY"  # 虚拟的公司实体
    VIRTUAL_INDUSTRY = "VIRTUAL_INDUSTRY"  # 虚拟的具体行业公司
    VIRTUAL_COMMON = "VIRTUAL_COMMON"  # 虚拟的通用公司


class Company(BaseModel):
    """公司领域模型"""

    cid: str | None = None
    """公司CID"""

    name: str | None = None
    """公司名称"""

    abbr: str | None = None
    """公司缩写"""

    logo: str | None = None
    """公司LogoUrl"""

    industry: str | None = None
    """归属行业"""

    company_type: CompanyType | None = None
    """公司类型"""

    @classmethod
    def from_do(cls, company: CompanyDO) -> Self:
        """
        数据库DO模型转为Model实例
        Args:
            company:

        Returns:

        """
        instance = cls()
        instance.cid = company.cid
        instance.name = company.name
        instance.abbr = company.abbr
        instance.logo = company.logo
        instance.industry = company.industry
        instance.company_type = (
            CompanyType(company.company_type) if company.company_type else None
        )
        return instance

    def to_do(self) -> CompanyDO:
        """
        Model实例转为DO模型
        Returns:
            CompanyDO: 转换的DO模型

        """
        return CompanyDO(
            cid=self.cid,
            name=self.name,
            abbr=self.abbr,
            logo=self.logo,
            industry=self.industry,
            company_type=self.company_type.value if self.company_type else None,
        )
