from robot_studio.company.model import Company, CompanyType
from robot_studio.database.mysql.repository import CompanyRepository


class CompanyManager:
    """公司客户信息管理器
    提供服务如下：
    1. 新增客户信息
    2. 更新客户信息
    3. 软删除客户信息
    4. 获取全量客户信息
    5. 获取三个企业模型（当前企业、行业虚拟企业、通用虚拟企业）
    """

    def __init__(self):
        self._company_repo = CompanyRepository()

    def query_company(self, cid: str) -> Company:
        """
        获取具体公司信息
        Args:
            cid: 工具公司的CID

        Returns:

        """
        _cmp = self._company_repo.get_company_by_cid(cid)
        assert _cmp is not None, f"公司信息不存在！cid={cid}"
        return Company.from_do(_cmp)

    def create_company(self, company: Company) -> Company:
        """
        创建公司信息
        Args:
            company: 公司信息

        Returns:

        """
        _cmp = self._company_repo.create_company(company.to_do())
        return Company.from_do(_cmp)

    def query_all_companies(self) -> list[Company]:
        """
        获取全部公司信息
        Returns:
            list[Company]: 所有公司信息

        """
        _companies = self._company_repo.get_all_companies()
        _companies = _companies or []
        return [Company.from_do(cmp) for cmp in _companies]

    def update_company(self, update_data: Company) -> Company:
        """
        更新公司信息
        Args:
            update_data: 更新数据

        Returns:
            Company: 更新后的公司信息

        """
        assert update_data.cid is not None, "公司CID不能为空"
        _cmp = self._company_repo.update_company(
            update_data.cid, update_data.model_dump(mode="json")
        )
        assert _cmp is not None, f"更新公司信息失败！cid={update_data.cid}"
        return Company.from_do(_cmp)

    def delete_company(self, cid: str) -> bool:
        """
        软删除公司信息
        Args:
            cid: 公司CID

        Returns:
            bool: 删除结果

        """
        return self._company_repo.delete_company(cid)

    def query_full_company_models(
        self, cid: str
    ) -> tuple[Company, Company | None, Company | None]:
        """
        根据当前CID获取三个企业模型
        Args:
            cid: 公司CID，可能是实体企业也可能是虚拟企业

        Returns:
            tuple[Company, Company | None, Company | None]:
                - 当前CID对应的企业模型
                - 当前企业所属行业的虚拟企业模型（每个行业只会有唯一一个虚拟企业实体）
                - 通用的虚拟企业（全局只会有一个）

        """
        # 获取当前企业模型
        current_company = self.query_company(cid)

        # 获取行业虚拟企业模型
        industry_virtual_company = None
        if current_company.industry:
            industry_virtual_do = self._company_repo.get_virtual_company_by_industry(
                current_company.industry
            )
            if industry_virtual_do:
                industry_virtual_company = Company.from_do(industry_virtual_do)

        # 获取通用虚拟企业模型
        common_virtual_company = None
        common_virtual_do = self._company_repo.get_common_virtual_company()
        if common_virtual_do:
            common_virtual_company = Company.from_do(common_virtual_do)

        return current_company, industry_virtual_company, common_virtual_company
