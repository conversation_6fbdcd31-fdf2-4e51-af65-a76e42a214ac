import uuid
from enum import Enum


class DataType(Enum):
    KNOWLEDGE = "kid_"
    """知识ID前缀"""

    MATERIAL = "mid_"
    """素材ID前缀"""

    GROUP = "gid_"
    """分组ID前缀"""

    SCHEMA = "sid_"
    """知识结构ID前缀"""

    COMPANY = "cid_"
    """公司ID前缀"""

    KNOWLEDGE_SCHEMA = "ksid_"
    """知识schema ID前缀"""

    USER = "uid_"
    """用户ID前缀"""

    ITERATION = "ite_"
    """迭代ID前缀"""

    COMPONENT = "cmp_"
    """组件ID前缀"""

    ARTIFACT = "ART_"
    """产物ID前缀"""

    SESSION = "SES_"
    """会话ID前缀"""

    MESSAGE = "MSG_"
    """消息ID前缀"""


def build_uuid(data_type: DataType) -> str:
    """
    构建Uuid
    Args:
        data_type: 数据类型枚举
    Returns:
        生成的UUID字符串
    """
    return data_type.value + uuid.uuid4().hex[:6]
