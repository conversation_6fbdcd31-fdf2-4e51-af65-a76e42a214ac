#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户上下文管理器
用于在请求期间存储和获取用户信息
"""

import threading
from typing import Optional, Dict, Any


class UserContext:
    """用户上下文管理器"""
    
    def __init__(self):
        self._local = threading.local()
    
    def set_user_info(self, uid: Optional[str] = None, username: Optional[str] = None, cid: Optional[str] = None):
        """设置用户信息"""
        if not hasattr(self._local, 'user_info'):
            self._local.user_info = {}
        
        if uid is not None:
            self._local.user_info['uid'] = uid
        if username is not None:
            self._local.user_info['username'] = username
        if cid is not None:
            self._local.user_info['cid'] = cid
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        if not hasattr(self._local, 'user_info'):
            return {'uid': None, 'username': None, 'cid': None}
        return self._local.user_info
    
    def get_uid(self) -> Optional[str]:
        """获取用户ID"""
        return self.get_user_info().get('uid')
    
    def get_username(self) -> Optional[str]:
        """获取用户名"""
        return self.get_user_info().get('username')
    
    def get_cid(self) -> Optional[str]:
        """获取企业ID"""
        return self.get_user_info().get('cid')
    
    def clear_user_info(self):
        """清除用户信息"""
        if hasattr(self._local, 'user_info'):
            del self._local.user_info


# 创建全局用户上下文实例
user_context = UserContext()


def set_user_info(uid: Optional[str] = None, username: Optional[str] = None, cid: Optional[str] = None):
    """设置用户信息的便捷函数"""
    user_context.set_user_info(uid, username, cid)


def get_user_info() -> Dict[str, Any]:
    """获取用户信息的便捷函数"""
    return user_context.get_user_info()


def get_uid() -> Optional[str]:
    """获取用户ID的便捷函数"""
    return user_context.get_uid()


def get_username() -> Optional[str]:
    """获取用户名的便捷函数"""
    return user_context.get_username()


def get_cid() -> Optional[str]:
    """获取企业ID的便捷函数"""
    return user_context.get_cid()


def clear_user_info():
    """清除用户信息的便捷函数"""
    user_context.clear_user_info() 