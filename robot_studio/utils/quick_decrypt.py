#!/usr/bin/env python3
"""
快速解密工具 - 简单的密文转明文工具

用法:
  python quick_decrypt.py "密文内容"
  python quick_decrypt.py --key "自定义密钥" "密文内容"
  echo "密文内容" | python quick_decrypt.py
  echo "密文内容" | python quick_decrypt.py --key "自定义密钥"
"""
import sys
import os
import argparse
from pathlib import Path

# Add the project root to sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from robot_studio.common.encryption_service import encryption_service


def main():
    import logging
    
    # 设置日志级别以显示解密过程信息
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr)  # 将日志输出到stderr，避免干扰主要输出
        ]
    )
    
    parser = argparse.ArgumentParser(
        description="快速解密工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_decrypt.py "encrypted_text"
  python quick_decrypt.py --key "custom_key" "encrypted_text"
  echo "encrypted_text" | python quick_decrypt.py
  echo "encrypted_text" | python quick_decrypt.py --key "custom_key"
        """
    )
    
    parser.add_argument("ciphertext", nargs="?", help="要解密的密文")
    parser.add_argument("--key", "-k", help="自定义加密密钥 (CONFIG_ENCRYPTION_KEY)")

    args = parser.parse_args()
    
    # 如果指定了自定义密钥，则设置它
    if args.key:
        # 临时设置环境变量
        os.environ['CONFIG_ENCRYPTION_KEY'] = args.key

    # 获取要解密的密文
    if args.ciphertext:
        # 命令行参数
        ciphertext = args.ciphertext
    elif not sys.stdin.isatty():
        # 管道输入
        ciphertext = sys.stdin.read().strip()
    else:
        # 交互输入
        ciphertext = input("请输入密文: ").strip()
    
    if not ciphertext:
        print("错误: 没有输入内容", file=sys.stderr)
        sys.exit(1)
    
    try:
        plaintext = encryption_service.decrypt(ciphertext, args.key)
        print(plaintext)
    except Exception as e:
        print(f"解密失败: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()