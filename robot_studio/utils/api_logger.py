#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI API日志处理器
记录所有API请求和响应数据到fastapi-digest.log文件
"""

import json
import logging
import time
from typing import Any, Dict, Optional
from urllib.parse import urlparse

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from robot_studio.utils.jwt_util import decode_token
from robot_studio.utils.otel_config import get_trace_id, get_span_id, add_trace_event, set_trace_attributes


class APILoggingMiddleware(BaseHTTPMiddleware):
    """FastAPI API日志中间件"""
    
    def __init__(self, app: ASGIApp, logger_name: str = "robot_studio.api_handler"):
        super().__init__(app)
        self.logger = logging.getLogger(logger_name)
    
    async def dispatch(self, request: Request, call_next):
        """处理请求和响应"""
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        request_info = await self._extract_request_info(request)
        
        # 构建用户信息字符串
        user_info = request_info.get("user_info", {})
        uid = user_info.get("uid") or "-"
        username = user_info.get("username") or "-"
        cid = user_info.get("cid") or "-"
        
        # 获取trace_id
        from opentelemetry.trace import get_current_span, format_trace_id
        try:
            span = get_current_span()
            if span and span.get_span_context() and span.get_span_context().trace_id != 0:
                trace_id = format_trace_id(span.get_span_context().trace_id)
            else:
                trace_id = "no-trace"
        except Exception:
            trace_id = "no-trace"
        
        # 记录请求日志
        self.logger.info(f"API请求开始: {json.dumps(request_info, ensure_ascii=False)}")
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 获取响应信息
        response_info = await self._extract_response_info(response, process_time)
        
        # 构建用户信息字符串（从请求信息中获取）
        user_info = request_info.get("user_info", {})
        uid = user_info.get("uid") or "-"
        username = user_info.get("username") or "-"
        cid = user_info.get("cid") or "-"
        
        # 获取trace_id
        from opentelemetry.trace import get_current_span, format_trace_id
        try:
            span = get_current_span()
            if span and span.get_span_context() and span.get_span_context().trace_id != 0:
                trace_id = format_trace_id(span.get_span_context().trace_id)
            else:
                trace_id = "no-trace"
        except Exception:
            trace_id = "no-trace"
        
        # 记录响应日志
        self.logger.info(f"API响应完成: {json.dumps(response_info, ensure_ascii=False)}")
        
        # 清除用户信息
        from robot_studio.utils.user_context import clear_user_info
        clear_user_info()
        
        return response
    
    async def _extract_request_info(self, request: Request) -> Dict[str, Any]:
        """提取请求信息"""
        
        # 获取请求体
        body = None
        try:
            if request.method in ["POST", "PUT", "PATCH"]:
                body_bytes = await request.body()
                if body_bytes:
                    # 尝试解析JSON
                    try:
                        body = json.loads(body_bytes.decode('utf-8'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # 如果不是JSON，记录为字符串
                        body = body_bytes.decode('utf-8', errors='ignore')
        except Exception as e:
            body = f"<无法读取请求体: {str(e)}>"
        
        # 获取查询参数
        query_params = dict(request.query_params)
        
        # 获取路径参数
        path_params = dict(request.path_params)

        # 获取请求头关键字段
        auth_header = request.headers.get("Authorization")
        uid = None
        username = None
        cid = None
        
        if auth_header and auth_header.startswith("Bearer "):
            try:
                token = auth_header.split(" ")[1]
                payload = decode_token(token)
                if payload:
                    uid = payload.get("uid")
                    username = payload.get("username")
                    cid = payload.get("cid")
            except Exception as e:
                # 如果token解析失败，记录错误但不影响请求处理
                pass
        
        # 设置用户信息到全局上下文
        from robot_studio.utils.user_context import set_user_info
        set_user_info(uid, username, cid)
            
        # 过滤请求头敏感信息
        headers = dict(request.headers)
        sensitive_headers = ['authorization', 'cookie', 'x-api-key']
        for header in sensitive_headers:
            if header in headers:
                headers[header] = '***'

        # 获取客户端信息
        client_host = request.client.host if request.client else "unknown"
        user_agent = headers.get('user-agent', 'unknown')
        
        # 解析URL
        parsed_url = urlparse(str(request.url))
        
        return {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": query_params,
            "path_params": path_params,
            "headers": headers,
            "body": body,
            "client_host": client_host,
            "user_agent": user_agent,
            "scheme": parsed_url.scheme,
            "netloc": parsed_url.netloc,
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
            "user_info": {
                "uid": uid,
                "username": username,
                "cid": cid
            }
        }
    
    async def _extract_response_info(self, response: Response, process_time: float) -> Dict[str, Any]:
        """提取响应信息"""
        
        # 获取响应头（安全地转换为字典）
        headers = {}
        try:
            headers = dict(response.headers)
        except Exception as e:
            # 如果无法获取响应头，记录错误但不影响请求处理
            headers = {"error": f"无法获取响应头: {str(e)}"}
        
        # 获取响应体（如果可能）
        body = None
        try:
            # 注意：这里我们只能获取到响应状态和头信息
            # 实际的响应体内容在中间件中无法直接获取
            # 如果需要记录响应体，需要在具体的路由处理函数中手动记录
            pass
        except Exception as e:
            body = f"<无法读取响应体: {str(e)}>"
        
        return {
            "status_code": response.status_code,
            "headers": headers,
            "body": body,
            "process_time": f"{process_time:.3f}s",
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
        }


class APILogger:
    """API日志记录器"""
    
    def __init__(self, logger_name: str = "robot_studio.api_handler"):
        self.logger = logging.getLogger(logger_name)
    
    def log_request(self, request_info: Dict[str, Any]):
        """记录请求日志"""
        self.logger.info(f"API请求: {json.dumps(request_info, ensure_ascii=False, indent=2)}")
    
    def log_response(self, response_info: Dict[str, Any]):
        """记录响应日志"""
        self.logger.info(f"API响应: {json.dumps(response_info, ensure_ascii=False, indent=2)}")
    
    def log_error(self, error_info: Dict[str, Any]):
        """记录错误日志"""
        self.logger.error(f"API错误: {json.dumps(error_info, ensure_ascii=False, indent=2)}")
    
    def log_complete_request(self, request_info: Dict[str, Any], response_info: Dict[str, Any]):
        """记录完整的请求-响应日志"""
        complete_info = {
            "request": request_info,
            "response": response_info,
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.logger.info(f"API完整记录: {json.dumps(complete_info, ensure_ascii=False, indent=2)}")


# 创建全局API日志记录器实例
api_logger = APILogger()


def log_api_request(request_info: Dict[str, Any]):
    """记录API请求的便捷函数"""
    api_logger.log_request(request_info)


def log_api_response(response_info: Dict[str, Any]):
    """记录API响应的便捷函数"""
    api_logger.log_response(response_info)


def log_api_error(error_info: Dict[str, Any]):
    """记录API错误的便捷函数"""
    api_logger.log_error(error_info)


def log_complete_api_call(request_info: Dict[str, Any], response_info: Dict[str, Any]):
    """记录完整API调用的便捷函数"""
    api_logger.log_complete_request(request_info, response_info) 