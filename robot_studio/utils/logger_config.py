import logging.config
import os
from pathlib import Path
from typing import Optional
from opentelemetry.trace import get_current_span, format_trace_id


class TraceIdFilter(logging.Filter):
    """添加trace ID到日志记录的过滤器"""
    
    def filter(self, record):
        # 从OpenTelemetry获取trace ID
        try:
            span = get_current_span()
            if span and span.get_span_context() and span.get_span_context().trace_id != 0:
                record.trace_id = format_trace_id(span.get_span_context().trace_id)
            else:
                record.trace_id = "no-trace"
        except Exception:
            record.trace_id = "no-trace"
        return True


class UserInfoFilter(logging.Filter):
    """添加用户信息到日志记录的过滤器"""
    
    def filter(self, record):
        # 从用户上下文中获取用户信息
        try:
            from robot_studio.utils.user_context import get_uid, get_username, get_cid
            
            uid = get_uid()
            username = get_username()
            cid = get_cid()
            
            record.user_uid = uid if uid else "-"
            record.user_username = username if username else "-"
            record.user_cid = cid if cid else "-"
        except Exception:
            record.user_uid = "-"
            record.user_username = "-"
            record.user_cid = "-"
        return True


class PathBasedLoggingConfig:
    """基于路径的日志配置类"""
    
    _initialized = False
    
    @classmethod
    def setup_path_based_logging(cls,
                                log_level: str = "INFO",
                                log_dir: Optional[str] = None,
                                max_bytes: int = 10 * 1024 * 1024,  # 10MB
                                backup_count: int = 5,
                                console_output: bool = True,
                                file_output: bool = True) -> None:
        """
        设置基于代码路径的日志配置
        
        Args:
            log_level: 日志级别
            log_dir: 日志文件目录
            max_bytes: 单个日志文件最大大小
            backup_count: 保留的日志文件备份数量
            console_output: 是否输出到控制台
            file_output: 是否输出到文件
        """
        if cls._initialized:
            return
            
        # 确定日志目录
        if log_dir is None:
            # 获取项目根目录（mindshake目录）
            project_root = Path(__file__).parent.parent.parent
            log_dir = project_root / "logs" / "robot_studio"
        else:
            log_dir = Path(log_dir)
        
        # 创建日志目录
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 构建日志配置字典
        config = cls._build_logging_config(
            log_dir=log_dir,
            log_level=log_level,
            max_bytes=max_bytes,
            backup_count=backup_count,
            console_output=console_output,
            file_output=file_output
        )
        
        # 应用配置
        logging.config.dictConfig(config)
        
        # 记录日志配置信息
        logger = logging.getLogger(__name__)
        logger.info(f"基于路径的日志目录: {log_dir}")
        logger.info(f"日志级别: {log_level}")
        logger.info(f"基于路径的日志配置完成")
        
        cls._initialized = True
    
    @classmethod
    def _build_logging_config(cls, log_dir: Path, log_level: str, max_bytes: int, 
                             backup_count: int, console_output: bool, file_output: bool) -> dict:
        """构建日志配置字典"""
        
        # 基础配置
        config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - [%(trace_id)s] - [%(user_uid)s] - [%(user_username)s] - [%(user_cid)s] - %(message)s',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
            },
            'filters': {
                'trace_id_filter': {
                    '()': TraceIdFilter,
                },
                'user_info_filter': {
                    '()': UserInfoFilter,
                }
            },
            'handlers': {},
            'loggers': {
                '': {  # root logger
                    'handlers': [],
                    'level': log_level.upper(),
                    'propagate': True
                }
            }
        }
        
        # 添加控制台处理器
        if console_output:
            config['handlers']['console'] = {
                'level': log_level.upper(),
                'formatter': 'standard',
                'filters': ['trace_id_filter', 'user_info_filter'],
                'class': 'logging.StreamHandler',
            }
            config['loggers']['']['handlers'].append('console')
        
        # 添加基于路径的文件处理器
        if file_output:
            # 为常见的模块路径创建处理器
            common_modules = [
                'robot_studio.data_asset.knowledge.core._knowledge_manager',
                'robot_studio.data_asset.knowledge.api._knowledge_service',
                'robot_studio.data_asset.material.core._material_manager',
                'robot_studio.auth.api._user_service',
                'robot_studio.component.rag.graph_rag.index._index',
                'robot_studio.database.mysql.db_engine',
                'robot_studio.utils.logger_config',
                'robot_studio.utils.log_util',
                'robot_studio.api_handler',
                'robot_studio.chat',
                'robot_studio.router',
            ]
            
            # 创建common-error日志处理器
            common_error_handler_name = 'common_error_file_handler'
            common_error_file_path = log_dir / 'common-error.log'
            config['handlers'][common_error_handler_name] = {
                'level': 'ERROR',
                'formatter': 'standard',
                'filters': ['trace_id_filter', 'user_info_filter'],
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': str(common_error_file_path),
                'maxBytes': max_bytes,
                'backupCount': backup_count,
                'encoding': 'utf-8'
            }
            
            for module_name in common_modules:
                # 生成文件名
                file_name = cls._get_log_file_name(module_name)
                file_path = log_dir / file_name
                
                # 创建处理器
                handler_name = f'{module_name}_file_handler'
                config['handlers'][handler_name] = {
                    'level': log_level.upper(),
                    'formatter': 'standard',
                    'filters': ['trace_id_filter', 'user_info_filter'],
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': str(file_path),
                    'maxBytes': max_bytes,
                    'backupCount': backup_count,
                    'encoding': 'utf-8'
                }
                
                # 创建logger配置，同时输出到模块文件和common-error文件
                config['loggers'][module_name] = {
                    'handlers': [handler_name, common_error_handler_name],
                    'level': log_level.upper(),
                    'propagate': False  # 避免重复记录
                }
            
            # 为root logger也添加common-error处理器
            if 'console' in config['handlers']:
                config['loggers']['']['handlers'].append(common_error_handler_name)
            else:
                config['loggers']['']['handlers'] = [common_error_handler_name]
        
        return config
    
    @classmethod
    def _get_log_file_name(cls, logger_name: str) -> str:
        """
        根据日志记录器名称获取日志文件名
        
        Args:
            logger_name: 日志记录器名称
            
        Returns:
            str: 日志文件名
        """
        # 将日志记录器名称转换为文件路径
        # 例如: robot_studio.data_asset.knowledge.core._knowledge_manager
        # 转换为: data_asset.knowledge.core.knowledge_manager.log
        
        if logger_name.startswith('robot_studio.'):
            # 移除 robot_studio. 前缀
            path_parts = logger_name[13:].split('.')
        else:
            path_parts = logger_name.split('.')
        
        # 特殊处理API处理器
        if logger_name == 'robot_studio.api_handler':
            return 'fastapi-digest.log'
        
        # 清理文件名（移除下划线前缀）
        if path_parts and path_parts[-1].startswith('_'):
            path_parts[-1] = path_parts[-1][1:]
        
        # 构建文件名（用点号连接所有部分）
        file_name = '.'.join(path_parts) + '.log'
        
        return file_name
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logging.Logger: 日志记录器
        """
        return logging.getLogger(name)


def setup_path_based_logging():
    """设置基于代码路径的日志配置"""
    PathBasedLoggingConfig.setup_path_based_logging(
        log_level="INFO",
        console_output=True,
        file_output=True
    )


def setup_debug_path_based_logging():
    """设置调试模式的基于路径的日志配置"""
    PathBasedLoggingConfig.setup_path_based_logging(
        log_level="DEBUG",
        console_output=True,
        file_output=True
    )


def setup_production_path_based_logging():
    """设置生产环境的基于路径的日志配置"""
    PathBasedLoggingConfig.setup_path_based_logging(
        log_level="WARNING",
        console_output=False,
        file_output=True,
        max_bytes=50 * 1024 * 1024,  # 50MB
        backup_count=10
    ) 