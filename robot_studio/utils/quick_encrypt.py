#!/usr/bin/env python3
"""
快速加密工具 - 简单的明文转密文工具

用法:
  python quick_encrypt.py "明文内容"
  python quick_encrypt.py --key "自定义密钥" "明文内容"
  echo "明文内容" | python quick_encrypt.py
  echo "明文内容" | python quick_encrypt.py --key "自定义密钥"
"""
import sys
import os
import argparse
from pathlib import Path

# Add the project root to sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from robot_studio.common.encryption_service import encryption_service


def main():
    import logging
    
    # 设置日志级别以显示加密过程信息
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr)  # 将日志输出到stderr，避免干扰主要输出
        ]
    )
    
    parser = argparse.ArgumentParser(
        description="快速加密工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_encrypt.py "my_password"
  python quick_encrypt.py --key "custom_key" "my_password"
  echo "my_password" | python quick_encrypt.py
  echo "my_password" | python quick_encrypt.py --key "custom_key"
        """
    )
    
    parser.add_argument("plaintext", nargs="?", help="要加密的明文")
    parser.add_argument("--key", "-k", help="自定义加密密钥 (CONFIG_ENCRYPTION_KEY)")

    args = parser.parse_args()
    
    # 如果指定了自定义密钥，则设置它
    if args.key:
        # 临时设置环境变量
        os.environ['CONFIG_ENCRYPTION_KEY'] = args.key

    # 获取要加密的明文
    if args.plaintext:
        # 命令行参数
        plaintext = args.plaintext
    elif not sys.stdin.isatty():
        # 管道输入
        plaintext = sys.stdin.read().strip()
    else:
        # 交互输入
        plaintext = input("请输入明文: ").strip()
    
    if not plaintext:
        print("错误: 没有输入内容", file=sys.stderr)
        sys.exit(1)
    
    try:
        ciphertext = encryption_service.encrypt(plaintext, args.key)
        print(ciphertext)
    except Exception as e:
        print(f"加密失败: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()