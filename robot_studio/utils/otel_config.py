#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenTelemetry配置和trace管理
为FastAPI请求生成唯一的trace标识，并在日志中透出
"""

import logging
import os
import uuid
from contextvars import ContextVar
from typing import Optional

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.trace import Span, Status, StatusCode

# 创建trace上下文变量
trace_id_var: ContextVar[Optional[str]] = ContextVar('trace_id', default=None)
span_id_var: ContextVar[Optional[str]] = ContextVar('span_id', default=None)


class TraceManager:
    """Trace管理器"""
    
    def __init__(self):
        self.tracer_provider = None
        self.tracer = None
        self._initialized = False
    
    def setup_otel(self, 
                   service_name: str = "robot-studio",
                   service_version: str = "1.0.0",
                   environment: str = "development",
                   enable_console_exporter: bool = True,
                   enable_otlp_exporter: bool = False,
                   otlp_endpoint: Optional[str] = None) -> None:
        """
        设置OpenTelemetry
        
        Args:
            service_name: 服务名称
            service_version: 服务版本
            environment: 环境
            enable_console_exporter: 是否启用控制台导出器
            enable_otlp_exporter: 是否启用OTLP导出器
            otlp_endpoint: OTLP端点
        """
        if self._initialized:
            return
        
        # 创建资源
        resource = Resource.create({
            "service.name": service_name,
            "service.version": service_version,
            "deployment.environment": environment,
        })
        
        # 创建TracerProvider
        self.tracer_provider = TracerProvider(resource=resource)
        
        # 添加控制台导出器（用于调试）
        if enable_console_exporter:
            console_exporter = ConsoleSpanExporter()
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
        
        # 添加OTLP导出器（用于生产环境）
        if enable_otlp_exporter and otlp_endpoint:
            otlp_exporter = OTLPSpanExporter(endpoint=otlp_endpoint)
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(otlp_exporter)
            )
        
        # 设置全局TracerProvider
        trace.set_tracer_provider(self.tracer_provider)
        
        # 创建tracer
        self.tracer = trace.get_tracer(service_name)
        
        # 设置日志instrumentation（在日志配置之前）
        LoggingInstrumentor().instrument(
            set_logging_format=True,
            log_level=logging.INFO
        )
        
        self._initialized = True
        
        logger = logging.getLogger(__name__)
        logger.info(f"OpenTelemetry已初始化 - 服务: {service_name}, 版本: {service_version}")
    
    def get_trace_id(self) -> Optional[str]:
        """获取当前trace ID"""
        return trace_id_var.get()
    
    def get_span_id(self) -> Optional[str]:
        """获取当前span ID"""
        return span_id_var.get()
    
    def set_trace_context(self, trace_id: str, span_id: str) -> None:
        """设置trace上下文"""
        trace_id_var.set(trace_id)
        span_id_var.set(span_id)
    
    def clear_trace_context(self) -> None:
        """清除trace上下文"""
        trace_id_var.set(None)
        span_id_var.set(None)
    
    def create_span(self, name: str, **kwargs) -> Span:
        """创建span"""
        if not self.tracer:
            raise RuntimeError("OpenTelemetry未初始化")
        return self.tracer.start_span(name, **kwargs)
    
    def get_current_span(self) -> Optional[Span]:
        """获取当前span"""
        return trace.get_current_span()
    
    def add_event(self, name: str, attributes: Optional[dict] = None) -> None:
        """添加事件到当前span"""
        current_span = self.get_current_span()
        if current_span:
            current_span.add_event(name, attributes or {})
    
    def set_attributes(self, attributes: dict) -> None:
        """设置当前span的属性"""
        current_span = self.get_current_span()
        if current_span:
            for key, value in attributes.items():
                current_span.set_attribute(key, value)
    
    def set_status(self, status: Status) -> None:
        """设置当前span的状态"""
        current_span = self.get_current_span()
        if current_span:
            current_span.set_status(status)


# 创建全局trace管理器实例
trace_manager = TraceManager()


def setup_otel_for_fastapi(**kwargs):
    """设置OpenTelemetry（不包含FastAPI instrumentation）"""
    
    # 设置OpenTelemetry
    trace_manager.setup_otel(**kwargs)
    
    return trace_manager


def instrument_fastapi_only(app):
    """仅为FastAPI应用添加instrumentation（OpenTelemetry已初始化）"""
    FastAPIInstrumentor.instrument_app(app)


def get_trace_id() -> Optional[str]:
    """获取当前trace ID的便捷函数"""
    return trace_manager.get_trace_id()


def get_span_id() -> Optional[str]:
    """获取当前span ID的便捷函数"""
    return trace_manager.get_span_id()


def add_trace_event(name: str, attributes: Optional[dict] = None) -> None:
    """添加trace事件的便捷函数"""
    trace_manager.add_event(name, attributes)


def set_trace_attributes(attributes: dict) -> None:
    """设置trace属性的便捷函数"""
    trace_manager.set_attributes(attributes)


def set_trace_status(status: Status) -> None:
    """设置trace状态的便捷函数"""
    trace_manager.set_status(status)


def create_trace_span(name: str, **kwargs) -> Span:
    """创建trace span的便捷函数"""
    return trace_manager.create_span(name, **kwargs) 