import bcrypt


def hash_password(password):
    password = password.encode('utf-8')
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password, salt)
    return hashed.decode('utf-8')


def verify_password(plain_password, hashed_password):
    plain_password = plain_password.encode('utf-8')
    hashed_password = hashed_password.encode('utf-8')
    return bcrypt.checkpw(plain_password, hashed_password)


if __name__ == '__main__':
    password = "123456!@#"
    hashed = hash_password(password)

    # 查看哈希密码的格式，它包含了盐值信息
    print(type(hashed))
    print(f"哈希密码: {hashed}")

    is_verified = verify_password(password, hashed)
    print(f"密码验证结果: {is_verified}")
