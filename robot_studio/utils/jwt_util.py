import jwt
import datetime
from fastapi import HTTPException, Request
from typing import Optional, Dict, Any
from jwt.exceptions import InvalidTokenError
import logging

# 初始化日志
logger = logging.getLogger(__name__)


# 安全设置
SECRET_KEY = "mindshake_secret_0430"  # 生产环境应使用安全随机密钥
ALGORITHM = "HS256"
# token有效期7天
ACCESS_TOKEN_EXPIRE_DAYS = 7


def gen_token(payload: dict):
    secret_key = SECRET_KEY
    time_out_date = datetime.datetime.now() + datetime.timedelta(days=ACCESS_TOKEN_EXPIRE_DAYS)
    payload["exp"] = time_out_date
    # 生成token
    token = jwt.encode(payload, secret_key, algorithm=ALGORITHM)
    return token


def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    解析 JWT Token，返回 payload 数据。

    Args:
        token (str): 待解析的 JWT Token

    Returns:
        dict | None: 解析成功返回 payload，失败返回 None
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token 已过期")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"无效的 Token: {e}")
        return None
    except Exception as e:
        logger.error(f"未知错误导致 Token 解析失败: {e}")
        return None


async def verify_jwt(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="无效的认证凭证")

    token = auth_header.split(" ")[1]
    payload = decode_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="无效或过期的令牌")

    # 存储用户ID 和 用户名
    uid = payload.get("uid")
    username = payload.get("username")

    if not uid or not username:
        raise InvalidTokenError("Token缺少必要的用户信息")
    else:
        request.state.uid = uid
        request.state.username = username

    # 验证通过但不返回任何值
    return


if __name__ == '__main__':
    payload_dict = {
        "telephone": "13855177418",  # 用户手机号
        "username": "测试用户2",  # 用户名
        "cid": "CID_0001",  # 企业ID
        "password": "123456!@#",  # 密码, 未加密
    }
    res_token = gen_token(payload=payload_dict)
    print(f"token: {res_token}")
    print(decode_token(res_token))
