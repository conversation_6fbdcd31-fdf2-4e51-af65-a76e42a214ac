import logging
from datetime import datetime
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List

from fastapi import APIRouter, Depends, UploadFile, HTTPException, status, Request
from pydantic import BaseModel

from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.material.api import MaterialGroupAPI
from robot_studio.data_asset.material.api import MaterialService
from robot_studio.data_asset.material.api.request.group_req import GroupCreateReq, GroupUpdateReq
from robot_studio.data_asset.material.api.request.material_req import MaterialBaseReq
from robot_studio.data_asset.material.api.result.group_res import GroupCreateRes, GroupListRes, SubTypeGroupListRes
from robot_studio.data_asset.material.api.result.material_res import CreateMaterialRes, MaterialRes, MaterialListRes
from robot_studio.data_asset.material.model import MaterialType
from robot_studio.utils.jwt_util import verify_jwt

logger = logging.getLogger(__name__)

material_router = APIRouter(
    prefix="/material",
    tags=["素材操作接口"],
    dependencies=[Depends(verify_jwt)]
)

# 服务实例
material_api = MaterialService()
material_group_api = MaterialGroupAPI()

# 常量定义
# 文件大小
MAX_FILE_SIZE = 500 * 1024 * 1024  # 500MB
# 允许的文件类型
ALLOWED_CONTENT_TYPES = {
    MaterialType.TEXT.name: [],
    MaterialType.IMAGE.name: ["image/jpeg", "image/png", "image/gif", "image/webp"],
    MaterialType.VIDEO.name: ["video/mp4", "video/avi", "video/mov", "video/wmv"]
}


class MaterialFormData(BaseModel):
    """素材表单数据模型"""
    uid: str
    cid: str
    name: str
    content_type: str
    content_digest: Optional[str] = None
    group_id: str
    tags: str
    valid_date: Optional[datetime] = None
    invalid_date: Optional[datetime] = None
    material_content: Optional[str] = None
    source: Optional[str] = None
    longtime_valid: Optional[bool] = False


class BatchMaterialRequest(BaseModel):
    """批量操作素材请求模型"""
    mids: List[str]


@material_router.post("/add_material", response_model=CreateMaterialRes)
async def add_material(
        request: Request,
        create_data: MaterialBaseReq,
) -> CreateMaterialRes:
    """
    创建素材接口
    支持表单数据和文件上传
    """
    # 创建素材
    return material_api.create_material(create_data)


@material_router.post("/delmaterial", response_model=BaseResult)
async def del_material(mid: str) -> BaseResult:
    return material_api.del_material(mid)


@material_router.post("/batchDelMaterial", response_model=BaseResult)
async def batch_del_material(request: BatchMaterialRequest) -> BaseResult:
    """批量删除素材"""
    return material_api.batch_del_material(request.mids)


@material_router.post("/onlineMaterial", response_model=BaseResult)
async def online_material(mid: str) -> BaseResult:
    return material_api.online_material(mid)


@material_router.post("/batchOnlineMaterial", response_model=BaseResult)
async def batch_online_material(request: BatchMaterialRequest) -> BaseResult:
    """批量上线素材"""
    return material_api.batch_online_material(request.mids)


@material_router.post("/offlineMaterial", response_model=BaseResult)
async def offline_material(mid: str) -> BaseResult:
    return material_api.offline_material(mid)


@material_router.post("/batchOfflineMaterial", response_model=BaseResult)
async def batch_offline_material(request: BatchMaterialRequest) -> BaseResult:
    """批量下线素材"""
    return material_api.batch_offline_material(request.mids)


@material_router.post("/modify_material", response_model=BaseResult)
async def update_material(
        request: Request,
        update_data: MaterialBaseReq,
) -> BaseResult:
    """
    更新素材接口
    支持表单数据和文件上传
    """
    # 更新素材
    return material_api.update_material(mid=update_data.material_id, update_data=update_data)


@material_router.get("/getmaterialbyid")
def get_material_by_id(mid: str) -> MaterialRes:
    return material_api.query_material_by_id(mid)


@material_router.get("/getmaterialbygroup")
def get_material_by_group(group_id: str) -> MaterialListRes:
    return material_api.query_material_by_group(group_id)


@material_router.post("/addgroup", response_model=GroupCreateRes)
async def add_material_group(req: GroupCreateReq, request: Request) -> GroupCreateRes:
    group_name = req.group_name
    group_desc = req.group_desc
    cid = req.cid
    sub_group_type = req.sub_group_type
    uid = getattr(request.state, "uid", None)
    username = getattr(request.state, "username", None)
    if req.tags is None or len(req.tags.strip()) == 0:
        # 空字符串与全空格字符串都不作为标签信息
        tags = []
    else:
        tags = req.tags.split(",")
    return (material_group_api.
            add_material_group(group_name,
                                                 group_desc=group_desc,
                                                 cid=cid,
                                                 sub_group_type=sub_group_type,
                                                 uid=uid,
                                                 username=username,
                                                 tags=tags))


@material_router.post("/delgroup", response_model=BaseResult)
async def delete_material_group(group_id: str) -> BaseResult:
    """
    删除素材分组（同时删除分组下的所有素材）
    Args:
        group_id: 素材组ID
    Returns:
        BaseResult: 删除结果
    """
    try:
        # 1. 首先查询该分组下的所有素材
        material_list_res = material_api.query_material_by_group(group_id)

        # 2. 如果查询素材成功且有素材，则批量删除这些素材
        if material_list_res.success and material_list_res.data:
            # 提取素材ID列表
            material_ids = [material.material_id for material in material_list_res.data if material.material_id]

            if material_ids:
                # 批量删除素材
                batch_delete_res = material_api.batch_del_material(material_ids)
                if not batch_delete_res.success:
                    return BaseResult(
                        success=False,
                        error_msg=f"删除分组下的素材失败: {batch_delete_res.error_msg}",
                        error_code="MATERIAL_DELETE_FAILED"
                    )

        # 3. 删除素材分组
        group_delete_res = material_group_api.del_group(group_id)

        if group_delete_res.success:
            return BaseResult(
                success=True,
                error_msg="成功删除素材分组及其下属素材"
            )
        else:
            return BaseResult(
                success=False,
                error_msg=f"删除素材分组失败: {group_delete_res.error_msg}",
                error_code=group_delete_res.error_code
            )

    except Exception as e:
        logger.error(f"删除素材分组失败: {e}")
        return BaseResult(
            success=False,
            error_msg=f"删除素材分组失败: {str(e)}",
            error_code="INTERNAL_ERROR"
        )


@material_router.post("/modifygroup", response_model=BaseResult)
async def update_material_group(group_id: str, req: GroupUpdateReq, request: Request) -> BaseResult:
    group_name = req.group_name
    group_desc = req.group_desc
    sub_group_type = req.sub_group_type
    cid = req.cid
    uid = getattr(request.state, "uid", None)
    username = getattr(request.state, "username", None)
    if req.tags is None or len(req.tags.strip()) == 0:
        # 空字符串与全空格字符串都不作为标签信息
        tags = []
    else:
        tags = req.tags.split(",")
    return material_group_api.update_group(group_id,
                                           cid=cid,
                                           group_name=group_name,
                                           group_desc=group_desc,
                                           uid=uid,
                                           username=username,
                                           tags=tags,
                                           sub_group_type=sub_group_type)


@material_router.get("/queryallmaterialgroups")
async def query_all_material_groups(cid: str) -> SubTypeGroupListRes:
    return material_group_api.query_all_material_groups(cid)


@material_router.get("/querymaterialgroupsbyplatform")
async def query_material_groups_by_platform(cid: str, platform: str) -> GroupListRes:
    return material_group_api.query_material_groups_by_platform(cid, platform=platform)


@material_router.get("/queryOnlineMediaMaterials")
async def query_online_media_materials(cid: str) -> BaseResult:
    """
    查询企业下所有在线状态的图片和视频素材，按素材组分组
    Args:
        cid: 企业ID
    Returns:
        BaseResult with data containing grouped materials:
        {
            "data": {
                "素材组名1": [
                    {
                        "material_id": "MID_xxx",
                        "name": "素材名称",
                        "url": "https://...",
                        "type": "IMAGE" or "VIDEO"
                    },
                    ...
                ],
                "素材组名2": [...],
                ...
            },
            "success": true,
            "error_msg": null,
            "error_code": null
        }
    """
    return material_api.query_online_media_materials_by_cid(cid)


async def _build_material_req(form_data: MaterialFormData, file: Optional[UploadFile] = None, uid: Optional[str] = None,
                              username: Optional[str] = None) -> MaterialBaseReq:
    """
    构建 CreateMaterialReq 对象的公共方法
    """
    file_name, file_content = await _process_upload_file(file)
    return MaterialBaseReq(
        uid=uid or "",
        cid=form_data.cid,
        name=form_data.name,
        content_type=form_data.content_type,
        content_digest=form_data.content_digest or "",
        group_id=form_data.group_id,
        tags=form_data.tags.split(",") if form_data.tags else [],
        valid_date=form_data.valid_date,
        invalid_date=form_data.invalid_date,
        material_content=form_data.material_content,
        source=form_data.source or "",
        longtime_valid=form_data.longtime_valid or False,
        file_name=file_name if file_name else None,
        file_content=file_content if file_content else None,
        user_name=username or "",
    )


async def _process_upload_file(file: Optional[UploadFile] = None) -> Tuple[Optional[str], Optional[bytes]]:
    """
    提取公共文件处理逻辑
    """
    if file and file.filename:
        try:
            file_content = await file.read()
            return file.filename, file_content
        except Exception as e:
            logger.error(f"读取上传文件失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"读取上传文件失败: {str(e)}"
            )
    return None, None


def _build_form_data(
        uid: str,
        cid: str,
        name: str,
        content_type: str,
        group_id: str,
        tags: str,
        valid_date: Optional[datetime] = None,
        invalid_date: Optional[datetime] = None,
        content_digest: Optional[str] = None,
        material_content: Optional[str] = None,
        source: Optional[str] = None,
        longtime_valid: Optional[bool] = False,
) -> MaterialFormData:
    return MaterialFormData(
        uid=uid,
        cid=cid,
        name=name,
        content_type=content_type,
        content_digest=content_digest,
        group_id=group_id,
        tags=tags,
        valid_date=valid_date,
        invalid_date=invalid_date,
        material_content=material_content,
        source=source,
        longtime_valid=longtime_valid
    )


def _validate_file(file: Optional[UploadFile], content_type: str) -> CreateMaterialRes:
    """验证上传文件"""
    if not file:
        return CreateMaterialRes(
            data=None,
            success=True,
            error_msg=None,
            error_code=None
        )

    # 检查文件大小
    if hasattr(file, 'size') and file.size and file.size > MAX_FILE_SIZE:
        return CreateMaterialRes(
            data=None,
            success=False,
            error_msg='文件大小超限',
            error_code="VALIDATION_ERROR"
        )

    # 检查文件类型
    if content_type in ALLOWED_CONTENT_TYPES:
        allowed_types = ALLOWED_CONTENT_TYPES[content_type]
        if allowed_types and file.content_type not in allowed_types:
            return CreateMaterialRes(
                data=None,
                success=False,
                error_msg='文件类型不支持',
                error_code="VALIDATION_ERROR"
            )
        return CreateMaterialRes(
            data=None,
            success=True,
            error_msg=None,
            error_code=None
        )
    else:
        return CreateMaterialRes(
            data=None,
            success=False,
            error_msg='文件类型不支持',
            error_code="VALIDATION_ERROR"
        )


def _validate_material_data(form_data: MaterialFormData) -> CreateMaterialRes:
    """验证素材数据"""
    # 验证内容类型
    if form_data.content_type not in [t.name for t in MaterialType]:
        return CreateMaterialRes(
            data=None,
            success=False,
            error_msg='操作类型不支持',
            error_code="VALIDATION_ERROR"
        )
    return CreateMaterialRes(
        data=None,
        success=True,
        error_msg=None,
        error_code=None
    )
    # 验证日期 联调时先不验证
    # if form_data.valid_date and form_data.invalid_date:
    #     if form_data.valid_date >= form_data.invalid_date:
    #         raise HTTPException(
    #             status_code=status.HTTP_400_BAD_REQUEST,
    #             detail="生效日期必须早于失效日期"
    #         )
