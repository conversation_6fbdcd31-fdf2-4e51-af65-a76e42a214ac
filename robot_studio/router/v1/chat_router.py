"""
聊天相关API路由
"""

from fastapi import APIRouter, Query, Path
from pydantic import BaseModel
from typing import Optional, List, Dict
import logging

from robot_studio.component.session.api import SessionService
from robot_studio.component.session.model import (
    CreateSessionCommand, GetSessionListQuery, UpdateSessionCommand,
    SearchSessionsQuery
)
from robot_studio.chat.artifacts.api import ArtifactsService
from robot_studio.chat.artifacts.model import (
    GetArtifactListQuery, SearchArtifactsQuery
)
from robot_studio.chat.chat_chunk.api import ChatChunkService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/chat", tags=["聊天服务"])

# 初始化服务
session_service = SessionService()
artifacts_service = ArtifactsService()
chat_chunk_service = ChatChunkService()


# 辅助函数：将Service的BaseResult转换为API响应格式
def handle_service_result(result):
    """
    处理Service层的BaseResult对象，转换为标准API响应格式
    """
    if result.success:
        # 如果结果是dataclass对象，转换为字典
        data = result.data
        if hasattr(data, '__dataclass_fields__'):
            from dataclasses import asdict
            data = asdict(data)
        return {
            "success": True,
            "data": data
        }
    else:
        return {
            "success": False,
            "error": result.error_msg
        }



class CreateSessionRequest(BaseModel):
    user_id: str
    title: Optional[str] = None
    component_config: Optional[Dict] = None


class UpdateSessionRequest(BaseModel):
    title: Optional[str] = None
    component_config: Optional[Dict] = None
    session_metadata: Optional[Dict] = None


class SearchRequest(BaseModel):
    keyword: Optional[str] = None
    artifact_type: Optional[str] = None
    session_id: Optional[str] = None
    tags: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None


# 会话管理接口
@router.post("/sessions")
async def create_session(request: CreateSessionRequest):
    """创建新会话"""
    command = CreateSessionCommand(
        user_id=request.user_id,
        title=request.title,
        component_config=request.component_config
    )
    result = session_service.create_session(command)
    return handle_service_result(result)


@router.get("/sessions/{user_id}")
async def get_user_sessions(
    user_id: str = Path(..., description="用户ID"),
    status: Optional[str] = Query(None, description="会话状态过滤"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取用户会话列表"""
    query = GetSessionListQuery(
        page=page,
        size=size,
        status=status
    )
    result = session_service.get_user_sessions(user_id, query)
    return handle_service_result(result)


@router.get("/sessions/detail/{session_id}")
async def get_session_detail(session_id: str = Path(..., description="会话ID")):
    """获取会话详情"""
    result = session_service.get_session_detail(session_id)
    return handle_service_result(result)


@router.put("/sessions/{session_id}")
async def update_session(
    session_id: str = Path(..., description="会话ID"),
    request: UpdateSessionRequest = None
):
    """更新会话信息"""
    command = UpdateSessionCommand(
        session_id=session_id,
        title=request.title if request else None,
        component_config=request.component_config if request else None,
        session_metadata=request.session_metadata if request else None
    )
    result = session_service.update_session(command)
    return handle_service_result(result)


@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str = Path(..., description="会话ID")):
    """删除会话"""
    result = session_service.delete_session(session_id)
    service_result = handle_service_result(result)
    # 调整响应格式以匹配原有的API
    if service_result["success"] and "data" in service_result:
        return {
            "success": service_result["data"]["success"],
            "message": service_result["data"]["message"]
        }
    return service_result


@router.post("/sessions/{session_id}/archive")
async def archive_session(session_id: str = Path(..., description="会话ID")):
    """归档会话"""
    result = session_service.archive_session(session_id)
    service_result = handle_service_result(result)
    # 调整响应格式以匹配原有的API
    if service_result["success"] and "data" in service_result:
        return {
            "success": service_result["data"]["success"],
            "message": service_result["data"]["message"]
        }
    return service_result


@router.get("/sessions/{session_id}/messages")
async def get_session_messages(
    session_id: str = Path(..., description="会话ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """根据会话ID获取消息列表 - 使用ChatChunkService"""
    result = chat_chunk_service.get_session_messages(session_id, limit, offset)
    return handle_service_result(result)


@router.post("/sessions/{user_id}/search")
async def search_sessions(
    user_id: str = Path(..., description="用户ID"),
    request: SearchRequest = None,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """搜索用户会话"""
    query = SearchSessionsQuery(
        user_id=user_id,
        keyword=request.keyword if request else None,
        status=None,  # status从query param获取更合适
        start_date=request.start_date if request else None,
        end_date=request.end_date if request else None,
        limit=limit,
        offset=offset
    )
    result = session_service.search_sessions(query)
    return handle_service_result(result)


# 产物管理接口
@router.get("/artifacts")
async def get_artifact_list(
    session_id: Optional[str] = Query(None, description="会话ID"),
    artifact_type: Optional[str] = Query(None, description="产物类型"),
    status: Optional[int] = Query(None, description="产物状态"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """获取产物列表"""
    query = GetArtifactListQuery(
        session_id=session_id,
        artifact_type=artifact_type,
        status=status,
        limit=limit,
        offset=offset
    )
    result = artifacts_service.get_artifact_list(query)
    return handle_service_result(result)


@router.get("/artifacts/{artifact_id}")
async def get_artifact_detail(artifact_id: str = Path(..., description="产物ID")):
    """获取产物详情"""
    result = artifacts_service.get_artifact_detail(artifact_id)
    return handle_service_result(result)


@router.get("/artifacts/{artifact_id}/download")
async def download_artifact(artifact_id: str = Path(..., description="产物ID")):
    """下载产物内容"""
    return artifacts_service.download_artifact(artifact_id)


@router.post("/artifacts/search")
async def search_artifacts(
    request: SearchRequest = None,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """搜索产物"""
    query = SearchArtifactsQuery(
        keyword=request.keyword if request else None,
        artifact_type=request.artifact_type if request else None,
        session_id=request.session_id if request else None,
        tags=request.tags if request else None,
        start_date=request.start_date if request else None,
        end_date=request.end_date if request else None,
        limit=limit,
        offset=offset
    )
    result = artifacts_service.search_artifacts(query)
    return handle_service_result(result)


@router.get("/artifacts/stats/{session_id}")
async def get_session_artifact_stats(session_id: str = Path(..., description="会话ID")):
    """获取会话产物统计信息"""
    result = artifacts_service.get_session_artifact_stats(session_id)
    return handle_service_result(result)


@router.delete("/artifacts/{artifact_id}")
async def delete_artifact(artifact_id: str = Path(..., description="产物ID")):
    """删除产物"""
    result = artifacts_service.delete_artifact(artifact_id)
    return handle_service_result(result)

