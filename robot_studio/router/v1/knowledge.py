from fastapi import APIRouter, Depends

from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import CreateKnowledgeReq, UpdateKnowledgeReq, \
    GroupKnowledgeReq, SingleKnowledgeReq, DelKnowledgeReq, OnlineReq
from robot_studio.data_asset.knowledge.api.result.knowledge_res import CreateKnowledgeRes, KnowledgeListRes, \
    KnowledgeUpdateRes, KnowledgeRes
from robot_studio.utils.jwt_util import verify_jwt

knowledge_router = APIRouter(
    prefix="/knowledge",
    tags=["知识操作接口"],
    # dependencies=[Depends(verify_jwt)]
)

knowledge_api = KnowledgeService()


@knowledge_router.post('/create_knowledge', response_model=CreateKnowledgeRes)
async def create_knowledge(req: CreateKnowledgeReq) -> CreateKnowledgeRes:
    """
    创建知识
    Args:
        req: 创建知识请求参数
    Returns:
        CreateKnowledgeRes: 创建的知识id
    """
    return knowledge_api.create_knowledge(req)


@knowledge_router.post('/query_group_knowledge', response_model=KnowledgeListRes)
async def query_group_knowledge(req: GroupKnowledgeReq) -> KnowledgeListRes:
    """
    查询知识详情
    Args:
        req:
    Returns:
        KnowledgeListRes: 知识详情列表
    """
    return knowledge_api.query_group_knowledge(req)


@knowledge_router.post('/query_knowledge_detail', response_model=KnowledgeRes)
async def query_knowledge_detail(req: SingleKnowledgeReq) -> KnowledgeRes:
    """
    查询知识详情
    Args:
        req:
    Returns:
        KnowledgeRes: 知识详情
    """
    return knowledge_api.query_knowledge_detail(req.knowledge_id)


@knowledge_router.post('/update_knowledge', response_model=KnowledgeUpdateRes)
async def update_knowledge(req: UpdateKnowledgeReq) -> KnowledgeUpdateRes:
    """
    更新知识
    Args:
        req: 更新知识请求参数
    Returns:
        KnowledgeUpdateRes: 更新结果
    """
    return knowledge_api.update_knowledge(req)


@knowledge_router.post('/batch_del', response_model=KnowledgeUpdateRes)
async def batch_del(req: DelKnowledgeReq) -> BaseResult:
    """
    上线知识
    Args:
        req:
    Returns:
        BaseResult: 下线结果
    """
    return await knowledge_api.batch_del(req)


@knowledge_router.post('/batch_online', response_model=KnowledgeUpdateRes)
async def batch_online(req: OnlineReq) -> KnowledgeUpdateRes:
    """
    上线知识
    Args:
        req:
    Returns:
        KnowledgeUpdateRes: 上线结果
    """
    return await knowledge_api.batch_online(req)
