import logging
from typing import List
from fastapi import APIRouter, Depends, Request

from robot_studio.common.base_result import BaseResult
from robot_studio.component.api import ComponentService
from robot_studio.component.api.request import ComponentRequest, BatchDeleteComponentReq
from robot_studio.component.api.result import CreateComponentRes, UpdateComponentRes
from robot_studio.utils.jwt_util import verify_jwt

logger = logging.getLogger(__name__)

component_router = APIRouter(
    prefix="/component", tags=["组件管理接口"], dependencies=[Depends(verify_jwt)]
)

# 服务实例
component_service = ComponentService()


@component_router.post("/create", response_model=CreateComponentRes)
async def create_component(component_req: ComponentRequest) -> CreateComponentRes:
    """
    创建新组件
    Args:
        component_req: 组件创建请求参数
    Returns:
        CreateComponentRes: 创建结果
    """
    try:
        # 调用服务创建组件
        result = component_service.create_new_component(component_req)
        return result
    except Exception as e:
        logger.error(f"创建组件失败: {e}")
        return CreateComponentRes(
            success=False,
            error_code="COMPONENT_CREATE_FAILED",
            error_msg=f"创建组件失败: {str(e)}",
        )


@component_router.put("/update", response_model=BaseResult)
async def update_component(component_req: ComponentRequest) -> BaseResult:
    """
    更新组件信息
    Args:
        component_req: 组件更新请求参数
    Returns:
        BaseResult: 更新结果
    """
    try:
        # 调用服务更新组件
        result = component_service.update_component(component_req)
        return result
    except Exception as e:
        logger.error(f"更新组件失败: {e}")
        return BaseResult(
            success=False,
            error_code="COMPONENT_UPDATE_FAILED",
            error_msg=f"更新组件失败: {str(e)}",
        )


@component_router.delete("/batch-delete", response_model=BaseResult)
async def batch_delete_components(delete_req: BatchDeleteComponentReq) -> BaseResult:
    """
    批量删除组件
    Args:
        delete_req: 批量删除组件请求参数
    Returns:
        BaseResult: 删除结果
    """
    try:
        # 调用服务批量删除组件
        result = component_service.batch_delete_component(delete_req.ids)
        return result
    except Exception as e:
        logger.error(f"批量删除组件失败: {e}")
        return BaseResult(
            success=False,
            error_code="COMPONENT_BATCH_DELETE_FAILED",
            error_msg=f"批量删除组件失败: {str(e)}",
        ) 