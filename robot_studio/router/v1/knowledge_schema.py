from fastapi import APIRouter, Depends

from robot_studio.common.base_request import BaseRequest
from robot_studio.common.base_result import BaseResult
from robot_studio.data_asset.knowledge.api import KnowledgeService
from robot_studio.data_asset.knowledge.api.request.knowledge_req import CreateTableSchemaReq, UpdateSchemaReq, DelSchemaReq
from robot_studio.data_asset.knowledge.api.result.knowledge_res import CreateKnowledgeSchemaRes, KnowledgeSchemaListRes
from robot_studio.utils.jwt_util import verify_jwt

# 创建路由器
knowledge_schema = APIRouter(
    prefix="/knowledge/schema",
    tags=["知识结构管理接口"],
    dependencies=[Depends(verify_jwt)]
)

# 创建知识服务API实例
knowledge_schema_api = KnowledgeService()


# API 端点
@knowledge_schema.post("/create", response_model=CreateKnowledgeSchemaRes)
async def create_schema(request: CreateTableSchemaReq):
    """
    创建知识结构
    """
    result = knowledge_schema_api.create_schema(req=request)
    return result


@knowledge_schema.post("/update", response_model=BaseResult)
async def update_schema(request: UpdateSchemaReq):
    """
    更新知识结构
    """
    result = knowledge_schema_api.update_schema(req=request)
    return result


@knowledge_schema.post("/delete", response_model=BaseResult)
async def delete_schema(request: DelSchemaReq):
    """
    删除知识结构
    """
    result = knowledge_schema_api.delete_schema_by_id(schema_id=request.schema_id)
    return result


@knowledge_schema.post("/query_by_cid", response_model=KnowledgeSchemaListRes)
async def query_schema(req: BaseRequest):
    """
    查询企业下的知识结构列表
    """
    result = knowledge_schema_api.query_schema_by_cid(cid=req.cid)
    return result
