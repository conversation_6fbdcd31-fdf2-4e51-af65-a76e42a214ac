from fastapi import APIRouter, HTTPException, Depends

from robot_studio.data_asset.knowledge.api import KnowledgeGroupService
from robot_studio.data_asset.knowledge.api.request.group_req import GroupCreateReq, GroupUpdateReq, GroupQueryReq, \
    GroupDeleteReq
from robot_studio.data_asset.knowledge.api.result.group_res import *
from robot_studio.utils.jwt_util import verify_jwt

# 创建路由器
group_service = APIRouter(
    prefix="/group",
    tags=["知识组管理接口"],
    dependencies=[Depends(verify_jwt)]
)

# 创建知识组API实例
knowledge_group_api = KnowledgeGroupService()


# API 端点
@group_service.post("/add", response_model=GroupCreateRes)
async def add_group(request: GroupCreateReq):
    """
    添加知识组
    """
    return knowledge_group_api.add_knowledge_group(request)


@group_service.post("/delete", response_model=BaseResult)
async def delete_group(request: GroupDeleteReq):
    """
    删除知识组（软删除）
    """
    result = knowledge_group_api.del_group(req=request)

    if not result.success:
        if result.error_code == "NOT_FOUND":
            raise HTTPException(status_code=404, detail=result.error_msg)
        elif result.error_code == "ALREADY_DELETED":
            raise HTTPException(status_code=400, detail=result.error_msg)
        else:
            raise HTTPException(status_code=500, detail=result.error_msg)

    return result


@group_service.post("/update", response_model=BaseResult)
async def update_group(request: GroupUpdateReq):
    """
    更新知识组
    """
    result = knowledge_group_api.update_group(req=request)

    if not result.success:
        if result.error_code == "NOT_FOUND":
            raise HTTPException(status_code=404, detail=result.error_msg)
        elif result.error_code == "ALREADY_DELETED":
            raise HTTPException(status_code=400, detail=result.error_msg)
        else:
            raise HTTPException(status_code=500, detail=result.error_msg)

    return result


@group_service.post("/query", response_model=GroupListRes)
async def query_groups(request: GroupQueryReq):
    """
    查询一个cid下的所有知识组
    """
    result = knowledge_group_api.query_groups(req=request)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.error_msg)

    return result
