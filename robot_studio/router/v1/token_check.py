from fastapi import APIRouter, Depends
from sqlmodel import Session, select
from robot_studio.database.mysql.db_engine import get_session
from robot_studio.auth.basic_info import BasicInfo

token_check = APIRouter(
    prefix="/token",
    tags=["Token检查接口"],
)


@token_check.get("/verifyCompanyByToken/")
def verify_company_by_token(com_token: str, session: Session = Depends(get_session)):
    statement = select(BasicInfo).where(BasicInfo.com_token == com_token)
    companies = session.exec(statement).all()
    return companies
