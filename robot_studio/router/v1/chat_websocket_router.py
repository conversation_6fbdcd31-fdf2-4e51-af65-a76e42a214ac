"""
🔥 Chainlit WebSocket聊天路由
实现WebSocket端点 /chat/ws，提供实时聊天功能
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, ValidationError
from typing import List

from robot_studio.chat.websocket.api import ChatWebSocketService
from robot_studio.chat.websocket.api.request import WebSocketChatRequest, MessageContent, WebSocketMessage
from robot_studio.chat.websocket.api.result import WebSocketChatResult

logger = logging.getLogger(__name__)

# 初始化服务
chat_websocket_service = ChatWebSocketService()

router = APIRouter(prefix="/chat", tags=["WebSocket聊天服务"])


class WebSocketConnectionManager:
    """WebSocket连接管理器 - 仅负责连接管理，不包含业务逻辑"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str = None):
        """建立WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "user_id": user_id,
            "connected_at": datetime.now(),
            "last_ping": datetime.now()
        }
        logger.info(f"[CHAINLIT-WS] 连接建立: {connection_id}, 用户: {user_id}")

    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.connection_metadata:
            del self.connection_metadata[connection_id]
        logger.info(f"[CHAINLIT-WS] 连接断开: {connection_id}")

    async def send_message(self, connection_id: str, message: Dict[str, Any]):
        """发送消息到指定连接"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                if websocket.client_state.name != 'CONNECTED':
                    logger.warning(f"[CHAINLIT-WS] WebSocket连接状态异常: {connection_id}, 状态: {websocket.client_state.name}")
                    self.disconnect(connection_id)
                    return False

                await websocket.send_text(json.dumps(message, ensure_ascii=False))
                return True
            except Exception as e:
                logger.error(f"[CHAINLIT-WS] 发送消息失败: {connection_id}, 错误: {str(e)}")
                self.disconnect(connection_id)
                return False
        else:
            logger.warning(f"[CHAINLIT-WS] 连接不存在: {connection_id}")
        return False

    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)

    def is_connected(self, connection_id: str) -> bool:
        """检查连接是否仍然活跃"""
        if connection_id not in self.active_connections:
            return False

        websocket = self.active_connections[connection_id]
        try:
            return websocket.client_state.name == 'CONNECTED'
        except Exception:
            self.disconnect(connection_id)
            return False

    def update_ping_time(self, connection_id: str):
        """更新心跳时间"""
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_ping"] = datetime.now()


# 全局连接管理器
connection_manager = WebSocketConnectionManager()


@router.websocket("/ws")
async def websocket_chat_endpoint(websocket: WebSocket):
    """
    WebSocket聊天端点 - 仅处理连接管理和消息路由

    支持的消息类型：
    - chat: 聊天消息
    - ping: 心跳检测
    """
    connection_id = f"ws_{datetime.now().timestamp()}_{id(websocket)}"

    try:
        # 建立连接
        await connection_manager.connect(websocket, connection_id)
        logger.info(f"[CHAINLIT-WS] 🚀 WebSocket连接建立: {connection_id}")

        while True:
            try:
                # 检查连接状态
                if not connection_manager.is_connected(connection_id):
                    logger.warning(f"[CHAINLIT-WS] 连接已断开，退出消息循环: {connection_id}")
                    break

                # 接收消息
                data = await websocket.receive_text()
                message_start_time = datetime.now()

                try:
                    # 解析消息
                    raw_message = json.loads(data)
                    message = WebSocketMessage(**raw_message)

                    logger.info(f"[CHAINLIT-WS] ⚡ 收到消息: {message.type}, 连接: {connection_id}")

                    # 处理不同类型的消息
                    if message.type == "ping":
                        await _handle_ping(websocket, connection_id)

                    elif message.type == "chat":
                        await _handle_chat_message(websocket, connection_id, message, message_start_time)

                    else:
                        logger.warning(f"[CHAINLIT-WS] 未知消息类型: {message.type}")
                        await _send_error(connection_id, f"未知消息类型: {message.type}")

                except ValidationError as e:
                    logger.error(f"[CHAINLIT-WS] 消息格式错误: {str(e)}")
                    await _send_error(connection_id, f"消息格式错误: {str(e)}")

                except json.JSONDecodeError as e:
                    logger.error(f"[CHAINLIT-WS] JSON解析错误: {str(e)}")
                    await _send_error(connection_id, f"JSON解析错误: {str(e)}")

            except WebSocketDisconnect:
                logger.info(f"[CHAINLIT-WS] 🔌 客户端主动断开连接: {connection_id}")
                break

            except Exception as e:
                logger.error(f"[CHAINLIT-WS] ❌ 处理消息异常: {connection_id}, 错误: {str(e)}")
                await _send_error(connection_id, f"处理消息失败: {str(e)}")

    except Exception as e:
        logger.error(f"[CHAINLIT-WS] ❌ WebSocket连接异常: {connection_id}, 错误: {str(e)}")

    finally:
        connection_manager.disconnect(connection_id)
        logger.info(f"[CHAINLIT-WS] 🔌 连接清理完成: {connection_id}")


async def _handle_ping(websocket: WebSocket, connection_id: str):
    """处理心跳消息 - 使用服务层"""
    # 更新最后心跳时间
    connection_manager.update_ping_time(connection_id)

    # 使用服务层创建pong响应
    pong_result = chat_websocket_service.create_pong_response()

    await websocket.send_text(json.dumps(pong_result.dict()))
    logger.debug(f"[CHAINLIT-WS] 💓 心跳响应: {connection_id}")


async def _handle_chat_message(websocket: WebSocket, connection_id: str, message: WebSocketMessage, start_time: datetime):
    """
    处理聊天消息 - 使用服务层处理业务逻辑
    """
    try:
        if not message.data:
            await _send_error(connection_id, "聊天消息数据为空")
            return

        # 发送消息确认（如果有ID）
        if message.id:
            ack_result = chat_websocket_service.create_ack_response(message.id)
            await websocket.send_text(json.dumps(ack_result.dict()))

        # VO转换：将WebSocket数据转换为聊天请求格式
        try:
            stream_request = WebSocketChatRequest(**message.data)
        except ValidationError as e:
            await _send_error(connection_id, f"请求参数验证失败: {str(e)}")
            return

        # 调用服务层验证会话
        is_valid, session, error_msg = chat_websocket_service.validate_chat_session(stream_request)
        if not is_valid:
            await _send_error(connection_id, error_msg)
            return

        logger.info(f"[CHAINLIT-WS] 🚀 开始WebSocket流式聊天, 会话:{stream_request.session_id}, 用户: {stream_request.user_id}")

        # 调用服务层生成消息ID
        user_message_id, assistant_message_id = chat_websocket_service.generate_message_ids(stream_request)

        # 获取用户消息内容
        last_user_content = stream_request.get_last_user_message()
        if not last_user_content:
            await _send_error(connection_id, "未找到用户消息")
            return

        # 调用服务层保存用户消息
        await chat_websocket_service.save_user_message(
            session=session,
            user_content=last_user_content,
            user_message_id=user_message_id,
            user_id=stream_request.user_id,
            user_name=stream_request.user_name
        )

        # 创建聊天处理请求
        from robot_studio.chat.websocket.api.request import ChatProcessRequest
        chat_request = ChatProcessRequest(
            websocket_request=stream_request,
            connection_id=connection_id,
            user_message_id=user_message_id,
            assistant_message_id=assistant_message_id,
            frontend_ai_message_id=stream_request.ai_message_id
        )

        # 调用服务层处理流式响应
        await _process_stream_response(websocket, connection_id, chat_request, session, start_time)

    except Exception as e:
        logger.error(f"[CHAINLIT-WS] ❌ 处理聊天消息失败: {connection_id}, 错误: {str(e)}")
        await _send_error(connection_id, f"处理聊天消息失败: {str(e)}")


async def _process_stream_response(
    websocket: WebSocket,
    connection_id: str,
    chat_request,
    session,
    start_time: datetime
):
    """
    处理流式响应 - 调用服务层并转换为WebSocket消息
    """
    try:
        # 调用服务层处理流式聊天
        async for result in chat_websocket_service.process_chat_stream(chat_request, session, start_time):
            # 检查连接状态
            if not connection_manager.is_connected(connection_id):
                logger.warning(f"[CHAINLIT-WS] 连接已断开，停止发送: {connection_id}")
                break

            # VO转换：将服务层结果转换为WebSocket消息格式 (BaseResult格式)
            ws_message = {
                "type": "chat",
                "data": result.dict()  # 使用BaseResult包装格式
            }

            # 发送WebSocket消息
            success = await connection_manager.send_message(connection_id, ws_message)
            if not success:
                logger.error(f"[CHAINLIT-WS] ❌ 发送消息失败，连接可能已断开: {connection_id}")
                break

            logger.info(f"[CHAINLIT-WS] ✅ 消息已发送: {result.data.chunk_sub_type if result.data else 'unknown'}")

    except Exception as e:
        logger.error(f"[CHAINLIT-WS] ❌ 处理流式响应失败: {connection_id}, 错误: {str(e)}")
        await _send_error(connection_id, f"处理流式响应失败: {str(e)}")


async def _send_error(connection_id: str, error_message: str):
    """发送错误消息 - 使用服务层创建错误响应"""
    from robot_studio.chat.websocket.api.result import ErrorResult
    error_result = ErrorResult.create(error_message)

    success = await connection_manager.send_message(connection_id, error_result.dict())
    if not success:
        logger.error(f"[CHAINLIT-WS] 发送错误消息失败: {connection_id}, 消息: {error_message}")


# WebSocket状态监控接口
@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    return {
        "active_connections": connection_manager.get_connection_count(),
        "service": "websocket-chat-service",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/ws/health")
async def websocket_health_check():
    """WebSocket服务健康检查"""
    return {
        "status": "healthy",
        "service": "chainlit-websocket-chat",
        "version": "1.0.0",
        "features": [
            "real-time-streaming",
            "message-acknowledgment",
            "heartbeat-detection",
            "real-time-communication"
        ],
        "performance_targets": {
            "processing_delay": "<20ms",
            "real_time_rate": ">98%",
            "connection_latency": "<500ms",
            "reconnect_success_rate": ">95%"
        },
        "active_connections": connection_manager.get_connection_count(),
        "timestamp": datetime.now().isoformat()
    }
