from typing import Any, Coroutine

from fastapi import APIRouter, HTTPException

from robot_studio.auth.api import UserService
from robot_studio.auth.api.request.user_req import CreateUserReq, LoginReq
from robot_studio.auth.api.result.company_res import CompanyListRes, CompanyRes
from robot_studio.auth.api.result.user_res import UserRes

router_auth = APIRouter(
    prefix="/auth",
    tags=["鉴权接口"],
)


user_service_api = UserService()


@router_auth.post('/login', response_model=UserRes)
async def login(req: LoginReq):
    result = user_service_api.login(req)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.error_msg)
    return result


@router_auth.post('/signup', response_model=UserRes)
async def sign_up(req: CreateUserReq):
    result = user_service_api.sign_up(req)

    if not result.success:
        raise HTTPException(status_code=500, detail=result.error_msg)
    return result


@router_auth.get('/getCompany', response_model=CompanyListRes)
async def get_company() -> CompanyListRes:
    return user_service_api.get_company()

@router_auth.get('/getCompanyByCid', response_model=CompanyRes)
async def get_company_by_cid(cid: str) -> CompanyRes:
    return user_service_api.get_company_by_cid(cid)


