import logging
from fastapi import APIRouter, Depends, Request

from robot_studio.common.base_result import BaseResult
from robot_studio.component.api import IterationService
from robot_studio.component.api.request import IterationReq, RelateComponentReq
from robot_studio.component.api.result import QueryIterationRes, IterationDetailRes
from robot_studio.utils.jwt_util import verify_jwt

logger = logging.getLogger(__name__)

iteration_router = APIRouter(
    prefix="/iteration", tags=["迭代管理接口"], dependencies=[Depends(verify_jwt)]
)

# 服务实例
iteration_service = IterationService()


@iteration_router.post("/create", response_model=BaseResult)
async def create_iteration(iteration_req: IterationReq) -> BaseResult:
    """
    创建迭代
    Args:
        request: FastAPI请求对象
        iteration_req: 迭代创建请求参数
    Returns:
        BaseResult: 创建结果
    """
    try:
        # 调用服务创建迭代
        result = iteration_service.create_iteration(iteration_req)
        return result
    except Exception as e:
        logger.error(f"创建迭代失败: {e}")
        return BaseResult(
            success=False,
            error_code="ITERATION_CREATE_FAILED",
            error_msg=f"创建迭代失败: {str(e)}",
        )


@iteration_router.delete("/{iterate_id}", response_model=BaseResult)
async def delete_iteration(iterate_id: str) -> BaseResult:
    """
    删除迭代
    Args:
        iterate_id: 迭代ID
    Returns:
        BaseResult: 删除结果
    """
    try:
        # 调用服务删除迭代
        result = iteration_service.delete_iteration(iterate_id)
        return result
    except Exception as e:
        logger.error(f"删除迭代失败: {e}")
        return BaseResult(
            success=False,
            error_code="ITERATION_DELETE_FAILED",
            error_msg=f"删除迭代失败: {str(e)}",
        )


@iteration_router.get("/list/{cid}", response_model=QueryIterationRes)
async def query_iteration_list(cid: str) -> QueryIterationRes:
    """
    查询企业下的迭代列表
    Args:
        cid: 企业ID
    Returns:
        QueryIterationRes: 查询结果
    """
    try:
        # 调用服务查询迭代列表
        result = iteration_service.query_cid_iterations(cid)
        return result
    except Exception as e:
        logger.error(f"查询迭代列表失败: {e}")
        return QueryIterationRes(
            success=False,
            error_code="ITERATION_QUERY_FAILED",
            error_msg=f"查询迭代列表失败: {str(e)}",
        )


@iteration_router.get("/detail/{iterate_id}", response_model=IterationDetailRes)
async def query_iteration_detail(iterate_id: str) -> IterationDetailRes:
    """
    查询迭代详情
    Args:
        iterate_id: 迭代ID
    Returns:
        IterationDetailRes: 查询结果
    """
    try:
        # 调用服务查询迭代详情
        result = iteration_service.query_iteration_detail(iterate_id)
        return result
    except Exception as e:
        logger.error(f"查询迭代详情失败: {e}")
        return IterationDetailRes(
            success=False,
            error_code="ITERATION_DETAIL_QUERY_FAILED",
            error_msg=f"查询迭代详情失败: {str(e)}",
        )


@iteration_router.post("/relate-components", response_model=BaseResult)
async def relate_components(relate_req: RelateComponentReq) -> BaseResult:
    """
    迭代关联组件
    Args:
        relate_req: 关联组件请求参数
    Returns:
        BaseResult: 关联结果
    """
    try:
        # 调用服务关联组件
        result = iteration_service.iteration_related_components(relate_req)
        return result
    except Exception as e:
        logger.error(f"迭代关联组件失败: {e}")
        return BaseResult(
            success=False,
            error_code="ITERATION_RELATE_COMPONENTS_FAILED",
            error_msg=f"迭代关联组件失败: {str(e)}",
        )
