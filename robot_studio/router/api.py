from fastapi import APIRouter

from robot_studio.router.v1.chat_websocket_router import router as chat_websocket_router
from robot_studio.router.v1.group_service import group_service
from robot_studio.router.v1.knowledge import knowledge_router
from robot_studio.router.v1.knowledge_schema import knowledge_schema
from robot_studio.router.v1.material import material_router
from robot_studio.router.v1.router_auth import router_auth
from robot_studio.router.v1.token_check import token_check
from robot_studio.router.v1.component import component_router
from robot_studio.router.v1.iteration import iteration_router
from robot_studio.router.v1.chat_router import router as chat_router

router = APIRouter()
router.include_router(router_auth)
router.include_router(token_check)
router.include_router(group_service)
router.include_router(knowledge_schema)
router.include_router(knowledge_router)
router.include_router(material_router)
router.include_router(component_router)
router.include_router(iteration_router)
router.include_router(chat_router)
router.include_router(chat_websocket_router)  # WebSocket聊天路由
