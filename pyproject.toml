[project]
name = "mindshake"
version = "0.1.0"
description = "mindshake robot studio"
readme = "README.md"
requires-python = ">=3.12, <3.13"
dependencies = [
    "alibabacloud-bailian20231229==2.0.5",
    "alibabacloud-oss-v2>=1.1.1",
    "autogen-agentchat>=0.5.3",
    "autogen-ext[openai]>=0.5.3",
    "bcrypt>=4.3.0",
    "chainlit>=2.5.5",
    "fastapi[standard]>=0.115.12",
    "graphrag==2.2.0",
    "mysql-connector-python>=9.3.0",
    "oss2>=2.19.1",
    "pymysql>=1.1.1",
    "pypinyin>=0.54.0",
    "sqlmodel>=0.0.24",
    "uvicorn>=0.34.1",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0",
    "opentelemetry-instrumentation-logging>=0.42b0",
    "opentelemetry-exporter-otlp-proto-grpc>=1.21.0",
    "black>=25.1.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "websockets>=15.0.1",
]
