# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist
/build

# misc
.DS_Store
npm-debug.log*
yarn-error.log
*.log

# test coverage
/coverage

# IDE and editor files
.idea
.vscode
.history

# package management
yarn.lock
package-lock.json
*bak

# umi
.umi
.umi-production
.umi-test
.temp/**

# functions
functions/*

# cache and temporary files
.firebase
.eslintcache
screenshot
*.md
