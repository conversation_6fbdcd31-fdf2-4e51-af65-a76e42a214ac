import { AvatarDropdown, AvatarName, Question } from '@/components';
import { currentUser as queryCurrentUser, getCompanyByCid } from '@/services/ant-design-pro/api';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history, Link } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import React from 'react';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';
const regPath = '/user/register';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  customData?: Record<string, any>;
  companyInfo?: API.GetCompanyByCidResponse['data'];
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser({
        skipErrorHandler: true,
      });
      return msg.data;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // 新增：从localStorage恢复登录态
  let loginInfo = null;
  try {
    loginInfo = JSON.parse(localStorage.getItem('loginInfo') || '{}');
  } catch (e) {}

  // 获取企业信息
  let companyInfo: API.GetCompanyByCidResponse['data'] | undefined = undefined;
  if (loginInfo?.cid) {
    try {
      console.log('正在获取企业信息，cid:', loginInfo.cid);
      const companyResponse = await getCompanyByCid(loginInfo.cid);
      console.log('企业信息API响应:', companyResponse);
      if (companyResponse.success) {
        companyInfo = companyResponse.data;
        console.log('获取企业信息成功:', companyInfo);
      } else {
        console.log('获取企业信息失败 - API返回失败:', companyResponse.error_msg);
      }
    } catch (error) {
      console.log('获取企业信息失败 - 异常:', error);
    }
  } else {
    console.log('无法获取企业信息 - 缺少cid:', loginInfo);
  }

  // 如果不是登录页面，执行
  const { location } = history;
  if (location.pathname !== loginPath && location.pathname !== regPath) {
    // 如果用户未登录且不在登录/注册页面，重定向到登录页
    if (!loginInfo?.hasLogin) {
      console.log('用户未登录，重定向到登录页');
      history.push(loginPath);
    }
  }
  const search = new URLSearchParams(window.location.search);
  return {
    fetchUserInfo,
    customData: {
      cid: loginInfo?.cid || 'CID_d9bf81',
      uid: loginInfo?.uid || '14',
      username: loginInfo?.username,
      hasLogin: !!loginInfo?.hasLogin,
    },
    companyInfo,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  // 动态设置标题：优先使用企业摘要名称，否则使用默认名称
  const title = initialState?.companyInfo?.abbr || 'MindShake';
  
  // 添加调试信息
  console.log('Layout - initialState:', initialState);
  console.log('Layout - companyInfo:', initialState?.companyInfo);
  console.log('Layout - title:', title);
  
  return {
    ...initialState?.settings,
    title,
    logo: '/assets/应用logo.jpeg',
    // actionsRender: () => [<Question key="doc" />],
    avatarProps: {
      // src: initialState?.currentUser?.avatar,
      // title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    // waterMarkProps: {
    //   content: initialState?.currentUser?.name,
    // },
    // footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.customData?.hasLogin && location.pathname !== loginPath && location.pathname !== regPath) {
        history.push(loginPath);
      }
    },
    // bgLayoutImgList: [
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
    //     left: 85,
    //     bottom: 100,
    //     height: '303px',
    //   },
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
    //     bottom: -68,
    //     right: -45,
    //     height: '303px',
    //   },
    //   {
    //     src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
    //     bottom: 0,
    //     left: 0,
    //     width: '331px',
    //   },
    // ],
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
