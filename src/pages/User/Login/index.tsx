
import { login, getCompanyByCid } from '@/services/ant-design-pro/api';
import { getFakeCaptcha } from '@/services/ant-design-pro/login';
import {
  LockOutlined,
  MobileOutlined,
  UserOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import {
  ProFormCheckbox,
} from '@ant-design/pro-components';
import { Helmet, history, useModel } from '@umijs/max';
import { Alert, message, Tabs, Button, Form, Input } from 'antd';
import { createStyles } from 'antd-style';
import React, { useEffect, useState } from 'react';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';
import '../styles.less';

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [type, setType] = useState<string>('mobile');
  const [loading, setLoading] = useState(false);
  const { initialState, setInitialState } = useModel('@@initialState');
  const [form] = Form.useForm();

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams) => {
    setLoading(true);
    try {
      // 登录
      const { telephone, password } = values;
      const msg = await login({ telephone, password });
      if (msg.success) {
        if(values.autoLogin) setLoginCookie(values);
        
        // 登录成功后，同时获取公司信息
        let companyInfo: API.GetCompanyByCidResponse['data'] | undefined = undefined;
        if (msg.cid) {
          try {
            const companyResponse = await getCompanyByCid(msg.cid);
            if (companyResponse.success) {
              companyInfo = companyResponse.data;
              console.log('获取企业信息成功:', companyInfo);
            }
          } catch (error) {
            console.log('获取企业信息失败:', error);
          }
        }
        
        // 写入localStorage，便于全局恢复登录态
        localStorage.setItem('loginInfo', JSON.stringify({
          hasLogin: true,
          cid: msg.cid,
          uid: msg.uid,
          username: msg.username,
          token: msg.token,
        }));
        
        const defaultLoginSuccessMessage = '登录成功！';
        message.success(defaultLoginSuccessMessage);
        
        // 更新initialState，包含公司信息
        setInitialState((s) => ({
          ...s,
          customData: {
            ...s?.customData,
            cid: msg.cid,
            hasLogin: true,
            uid: msg.uid,
            username: msg.username,
            token: msg.token,
          },
          companyInfo, // 同时更新公司信息
        }));
        
        setTimeout(() => {
          history.push('/database/knowledge');
        }, 200);
        return;
      }
      console.log(msg);
      // 如果失败去设置用户错误信息
      
    } catch (error) {
      const defaultLoginFailureMessage = '登录失败，请重试！';
      console.log(error);
      message.error(defaultLoginFailureMessage);
    } finally {
      setLoading(false);
    }
  };

  const setLoginCookie = (values: API.LoginParams) => {
    // 计算到次日零点的时间
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const expires = `expires=${tomorrow.toUTCString()}`;
    const cookieValue = encodeURIComponent(JSON.stringify(values));
    document.cookie = `loginInfo=${cookieValue}; ${expires}; path=/`;
  };

  useEffect(() => {
    const cookies = document.cookie.split(';');
    const loginCookie = cookies.find(c => c.trim().startsWith('loginInfo='));
    console.log(loginCookie)
    if (loginCookie) {
        const cookieValue = decodeURIComponent(loginCookie.split('=')[1]);
        const values = JSON.parse(cookieValue);
        console.log(values)
        if(values.autoLogin) handleSubmit(values);
    }
  }, []);
  
  return (
    <>
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
        </div>
      )}
      
      <div className="auth-container">
        <Helmet>
          <title>
            {'登录'}- {Settings.title}
          </title>
          <link rel="icon" type="image/jpeg" href="/assets/平台logo.jpeg" />
          <link rel="shortcut icon" type="image/jpeg" href="/assets/平台logo.jpeg" />
        </Helmet>

        {/* ICP备案信息 */}
        <div style={{
          position: 'absolute',
          bottom: '12px',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center',
          fontSize: '12px',
          color: '#999',
          zIndex: 10,
        }}>
          <a 
            href="https://beian.miit.gov.cn" 
            target="_blank" 
            rel="noopener noreferrer"
            style={{ 
              fontSize: '12px', 
              color: '#999',
              textDecoration: 'none'
            }}
          >
            浙ICP备2025180696号
          </a>
        </div>

        {/* 左侧展示区域 */}
        <div className="left-section">
          <div className="image-container">
            <img 
              src="/assets/logo.png" 
              alt="企业形象" 
              className="main-image"
            />
            <div className="floating-elements">
              <div className="floating-dot"></div>
              <div className="floating-dot"></div>
              <div className="floating-dot"></div>
            </div>
          </div>
          
          <div className="slogan-container">
            <h1 className="company-name">您的专属知识管家</h1>
            <p className="slogan">
              连接智慧，汇聚知识，创见未来
            </p>
            <div className="features">
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>数字资产</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>安全可靠</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>高效协作</span>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧表单区域 */}
        <div className="right-section">
          <div className="form-container">
            <div className="form-header">
              <h2 className="form-title">欢迎回来</h2>
              <p className="form-subtitle">请登录您的账户以继续</p>
            </div>

            <Form
              form={form}
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="telephone"
                rules={[
                  {
                    required: true,
                    message: '请输入手机号！',
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: '请输入正确的手机号格式！',
                  },
                ]}
              >
                <Input
                  prefix={<MobileOutlined />}
                  placeholder="请输入手机号"
                  autoComplete="tel"
                  maxLength={11}
                  allowClear
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  {
                    required: true,
                    message: '请输入密码！',
                  },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  autoComplete="current-password"
                  allowClear
                />
              </Form.Item>

              <Form.Item>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Form.Item name="autoLogin" valuePropName="checked" noStyle>
                    <ProFormCheckbox>自动登录</ProFormCheckbox>
                  </Form.Item>
                </div>
              </Form.Item>

              <div className="form-actions">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                >
                  登录
                </Button>
              </div>

              <div className="form-footer">
                <p>还没有账户？ <a href="/user/register">立即注册</a></p>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
