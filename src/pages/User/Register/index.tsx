import { Helmet, history, useModel } from '@umijs/max';
import Settings from '../../../../config/defaultSettings';
import { Button, Form, Input, Radio, Tag, Dropdown, Select, message } from 'antd';
import { UserOutlined, LockOutlined, MobileOutlined, BankOutlined, CheckOutlined } from '@ant-design/icons';
import '../styles.less';
import { useEffect, useState } from 'react';
import { getCompanyList, register } from '@/services/ant-design-pro/api';

const { Option } = Select;

type CompanyItem = {
  key: number;
  lable: string;
  cid: string;
  name: string;
  abbr: string;
}

const Register: React.FC = () => {
  const [companies, setCompanies] = useState<CompanyItem[]>([]);
  const [loading, setLoading] = useState(false); // 注册提交loading
  const [companiesLoading, setCompaniesLoading] = useState(true); // 企业列表加载loading
  const [form] = Form.useForm();
  const variant = Form.useWatch('variant', form);

  const updateCompanies = async () => {
    setCompaniesLoading(true);
    try {
      const res = await getCompanyList();
      console.log('获取企业列表响应:', res);
      
      if (res.success && res.data) {
        const arr: CompanyItem[] = res.data.map((company, index) => ({
          key: index,
          lable: company.name,
          cid: company.cid,
          name: company.name,
          abbr: company.abbr
        }));
        setCompanies(arr);
      } else {
        console.error('获取企业列表失败:', res.error_msg);
        message.error(res.error_msg || '获取企业列表失败');
        // 设置默认企业
        setCompanies([{
          key: 1,
          lable: '测试公司',
          cid: 'CID_d9bf81',
          name: '测试公司',
          abbr: '测试'
        }]);
      }
    } catch (err) {
      console.error('获取企业列表出错:', err);
      message.error('获取企业列表失败，请重试');
      // 设置默认企业
      setCompanies([{
        key: 1,
        lable: '测试公司',
        cid: 'CID_d9bf81',
        name: '测试公司',
        abbr: '测试'
      }]);
    } finally {
      setCompaniesLoading(false);
    }
  }

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      console.log('Received values of form: ', values);
      const regData = {
        telephone: values.phone,
        password: values.password,
        username: values.name,
        cid: values.company
      }
      await register(regData).then((res) => {
        console.log(res)
        message.success('注册成功!')
        setTimeout(() => {
          history.push('/user/login')
        }, 1000)
      })
    } catch (error) {
      console.error('注册失败:', error);
      message.error('注册失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  const onCancel = () => {
    history.push('/user/login')
  }

  useEffect(() => {
    updateCompanies()
  }, [])

  return (
    <>
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
        </div>
      )}

      <div className="auth-container">
        <Helmet>
          <title>
            {'注册'}- {Settings.title}
          </title>
        </Helmet>

        {/* 左侧展示区域 */}
        <div className="left-section">
          <div className="image-container">
            <img 
              src="/assets/logo.png" 
              alt="企业形象" 
              className="main-image"
            />
            <div className="floating-elements">
              <div className="floating-dot"></div>
              <div className="floating-dot"></div>
              <div className="floating-dot"></div>
            </div>
          </div>
          
          <div className="slogan-container">
            <h1 className="company-name">加入您的团队</h1>
            <p className="slogan">
              开启AI新体验~  
            </p>
            <div className="features">
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>快速注册</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>免费使用</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">
                  <CheckOutlined />
                </div>
                <span>即时开通</span>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧表单区域 */}
        <div className="right-section">
          <div className="form-container">
            <div className="form-header">
              <h2 className="form-title">注册账号</h2>
              <p className="form-subtitle">请填写以下信息完成注册</p>
            </div>

            <Form
              form={form}
              onFinish={onFinish}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="name"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  autoComplete="username"
                  allowClear
                />
              </Form.Item>

              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  {
                    pattern: /^1\d{10}$/,
                    message: '请输入正确的手机号格式！',
                  },
                ]}
              >
                <Input
                  prefix={<MobileOutlined />}
                  placeholder="请输入手机号"
                  autoComplete="tel"
                  maxLength={11}
                  allowClear
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  {
                    required: true,
                    message: '请输入密码',
                  },
                  {
                    min: 6,
                    message: '密码长度至少6位',
                  },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  autoComplete="new-password"
                  allowClear
                />
              </Form.Item>

              <Form.Item
                name="confirm"
                dependencies={['password']}
                rules={[
                  {
                    required: true,
                    message: '请确认密码！',
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('确认密码必须与密码相同！'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请确认密码"
                  autoComplete="new-password"
                  allowClear
                />
              </Form.Item>

              <Form.Item
                name="company"
                rules={[{ required: true, message: '请选择关联企业' }]}
              >
                <Select
                  placeholder={companiesLoading ? "正在加载企业列表..." : "请选择关联企业"}
                  showSearch
                  loading={companiesLoading}
                  suffixIcon={<BankOutlined />}
                  disabled={companiesLoading}
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {companies.map((item) => (
                    <Option key={item.key} value={item.cid}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <div className="form-actions">
                <div style={{ display: 'flex', gap: '12px' }}>
                  <Button
                    onClick={onCancel}
                    style={{ flex: 1 }}
                  >
                    返回登录
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    disabled={companiesLoading}
                    style={{ flex: 1 }}
                  >
                    立即注册
                  </Button>
                </div>
              </div>

              <div className="form-footer">
                <p>已有账户？ <a href="/user/login">立即登录</a></p>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Register;