.auth-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/logo.png') center/cover;
    opacity: 0.1;
    z-index: 0;
  }

  .left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px 40px;
    position: relative;
    z-index: 1;
    color: white;
    text-align: center;

    .image-container {
      position: relative;
      margin-bottom: 40px;
      animation: float 6s ease-in-out infinite;

      .main-image {
        width: 300px;
        height: 300px;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        object-fit: cover;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }

      .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .floating-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 50%;
          animation: float-dot 4s ease-in-out infinite;

          &:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
          }

          &:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 1s;
          }

          &:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 2s;
          }
        }
      }
    }

    .slogan-container {
      max-width: 500px;

      .company-name {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #fff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: fadeInUp 1s ease-out;
      }

      .slogan {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 30px;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .features {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.4s both;

        .feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.9rem;
          opacity: 0.8;

          .feature-icon {
            width: 16px;
            height: 16px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
          }
        }
      }
    }
  }

  .right-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    position: relative;
    z-index: 1;

    .form-container {
      width: 100%;
      max-width: 400px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      animation: slideInRight 0.8s ease-out;

      .form-header {
        text-align: center;
        margin-bottom: 30px;

        .form-title {
          font-size: 2rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 10px;
        }

        .form-subtitle {
          color: #666;
          font-size: 0.9rem;
        }
      }

      .ant-form-item {
        margin-bottom: 20px;

        .ant-form-item-label > label {
          color: #333;
          font-weight: 500;
        }

        // 确保输入框前缀图标样式正确
        .ant-input-prefix {
          color: #999;
          margin-right: 8px;
        }
      }

      .form-actions {
        margin-top: 30px;

        .ant-btn {
          height: 45px;
          border-radius: 10px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.ant-btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            width: 100%;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            }
          }

          &.ant-btn-default {
            border: 2px solid #e8e8e8;
            color: #666;

            &:hover {
              border-color: #667eea;
              color: #667eea;
            }
          }
        }
      }

      .form-footer {
        margin-top: 20px;
        text-align: center;
        font-size: 0.9rem;
        color: #666;

        a {
          color: #667eea;
          text-decoration: none;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-dot {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) scale(1.2);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;

    .left-section {
      padding: 40px 20px;

      .image-container .main-image {
        width: 200px;
        height: 200px;
      }

      .slogan-container .company-name {
        font-size: 2rem;
      }
    }

    .right-section {
      padding: 20px;

      .form-container {
        padding: 30px 20px;
      }
    }
  }
}

// 加载动画
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 