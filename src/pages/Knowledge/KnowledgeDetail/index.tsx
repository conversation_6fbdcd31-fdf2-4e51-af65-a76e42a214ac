import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Radio,
  DatePicker,
  Button,
  Card,
  Space,
  message,
  Tag,
  Modal,
  Upload,
  Divider,
  Mentions,
  Spin
} from 'antd';
import { EditableProTable, PageContainer } from '@ant-design/pro-components';
import { PlusOutlined, DeleteOutlined, UploadOutlined, FileTextOutlined, FileExcelOutlined, PictureOutlined, FolderOutlined, EditOutlined } from '@ant-design/icons';
import { useModel, useParams, useLocation, history } from '@umijs/max';
import { createKnowledge, getKnowledgeDetailByKnowledgeId, updateKnowledgeNew, createSchema, querySchemaByCid, deleteSchema, updateSchema, queryOnlineMediaMaterials } from '@/services/ant-design-pro/api';
import dayjs, { Dayjs } from 'dayjs';
import * as XLSX from 'xlsx';
import './index.css';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 类型定义
interface RouteParams extends Record<string, string | undefined> {
  mode?: string;
  id?: string;
}

interface LocationState {
  mode?: string;
  groupId?: string;
  groupData?: KnowledgeGroupData;
}

interface KnowledgeGroupData {
  id: string;
  name: string;
  description: string;
  createDate: string;
  creator: string;
  tags: string[];
  knowledgeCount: number;
}

interface TableColumn {
  id?: number;
  title: string;
  dataIndex: string;
  key: string;
  tooltip?: string;
  editable?: boolean;
  width?: number;
  renderFormItem?: () => React.ReactNode;
  isEntity?: boolean;
  entityName?: string;
}

interface TableStructure {
  id: string;
  name: string;
  description: string;
  type?: string;
  columns: TableColumn[];
}

interface TableRowData {
  id: number;
  [key: string]: any;
}

interface ImageItem {
  material_id: string;
  name: string;
  url: string;
  type?: 'IMAGE' | 'VIDEO' | 'TEXT';
}

interface ImageLibrary {
  [category: string]: ImageItem[];
}

interface MentionOption {
  value: string;
  label: React.ReactNode;
  category?: string;
  image?: ImageItem;
  type: 'category' | 'image';
}

interface FormValues {
  name: string;
  summary: string;
  tags?: string[];
  type: 'DOCUMENT' | 'TABLE';
  structure?: string;
  validPeriodType: 'permanent' | 'custom';
  validPeriod?: [Dayjs, Dayjs] | [Dayjs, Dayjs | null];
  content?: string;
  materialGroups?: string[]; // 新增：选中的素材组
}

interface StructureModalProps {
  visible: boolean;
  editingStructure: TableStructure | null;
  onSave: (data: Omit<TableStructure, 'id'>) => void | Promise<void>;
  onCancel: () => void;
}

interface ColumnModalProps {
  visible: boolean;
  structure: TableStructure | null;
  onSave: (data: Omit<TableStructure, 'id'>) => void;
  onCancel: () => void;
}

const KnowledgeDetail: React.FC = () => {
  // 从URL参数和路由状态获取数据
  const { mode, id } = useParams<RouteParams>();
  const location = useLocation();
  const locationState = (location.state as LocationState) || {};

  // 根据参数确定实际的属性值
  const actualMode = mode || locationState.mode || 'create';
  const knowledgeId = (actualMode !== 'create') ? id : null;
  const groupId = locationState.groupId || id; // 创建模式时id就是groupId
  const groupData = locationState.groupData;

  console.log('🟢 === 知识详情页面初始化 ===');
  console.log('🟢 实际模式:', actualMode);
  console.log('🟢 知识ID:', knowledgeId);
  console.log('🟢 组ID:', groupId);

  const onBack = (): void => {
    if (groupId) {
      history.push(`/database/knowledge/manage/${groupId}`, groupData);
    } else {
      history.push('/database/knowledge');
    }
  };

  const [form] = Form.useForm<FormValues>();
  const [isEditing, setIsEditing] = useState<boolean>(actualMode === 'create' || actualMode === 'edit');
  const [knowledgeType, setKnowledgeType] = useState<'DOCUMENT' | 'TABLE'>('DOCUMENT');
  const [validPeriodType, setValidPeriodType] = useState<'permanent' | 'custom'>('permanent');
  const [selectedStructure, setSelectedStructure] = useState<TableStructure | null>(null);
  const [tableData, setTableData] = useState<TableRowData[]>([]);

  const [showStructureModal, setShowStructureModal] = useState<boolean>(false);
  const [editingStructure, setEditingStructure] = useState<TableStructure | null>(null);
  const [showColumnModal, setShowColumnModal] = useState<boolean>(false);
  const [userStructures, setUserStructures] = useState<TableStructure[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);

  // 添加数据加载状态
  const [loading, setLoading] = useState<boolean>(actualMode !== 'create'); // 非创建模式需要加载数据

  // 服务端表结构数据
  const [serverSchemas, setServerSchemas] = useState<TableStructure[]>([]);
  const [schemaLoading, setSchemaLoading] = useState<boolean>(false);

  // Mentions组件相关状态
  const [mentionOptions, setMentionOptions] = useState<MentionOption[]>([]);

  // 素材库相关状态
  const [materialLibrary, setMaterialLibrary] = useState<ImageLibrary>({});
  const [materialLoading, setMaterialLoading] = useState<boolean>(false);

  // 新增：素材组选择状态
  const [selectedMaterialGroups, setSelectedMaterialGroups] = useState<string[]>([]);
  const [availableMaterialGroups, setAvailableMaterialGroups] = useState<string[]>([]);

  // 新增：图片预加载状态
  const [imageCache, setImageCache] = useState<Map<string, string>>(new Map());
  const [preloadingImages, setPreloadingImages] = useState<boolean>(false);

  // 新增：数据加载进度状态
  const [dataProcessingProgress, setDataProcessingProgress] = useState<{
    isProcessing: boolean;
    current: number;
    total: number;
    message: string;
  }>({
    isProcessing: false,
    current: 0,
    total: 0,
    message: ''
  });

  // 获取用户状态信息
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};

  // 将服务端数据转换为前端TableStructure格式
  const convertSchemaItemToTableStructure = (schemaItem: API.SchemaItem): TableStructure => {
    return {
      id: schemaItem.schema_id,
      name: schemaItem.name,
      description: schemaItem.desc || '',
      type: schemaItem.type,
      columns: schemaItem.config.map((configItem, index) => ({
        id: index + 1,
        title: configItem.col_name,
        dataIndex: configItem.col_name,
        key: configItem.col_name,
        tooltip: configItem.desc || '',
        isEntity: configItem.is_entity,
        entityName: configItem.entity_type || ''
      }))
    };
  };

  // 获取表结构数据
  useEffect(() => {
    const fetchSchemas = async () => {
      if (customData?.uid && customData?.cid && customData?.username) {
        try {
          setSchemaLoading(true);
          console.log('🔵 开始获取表结构列表');

          const response = await querySchemaByCid({
            uid: customData.uid,
            cid: customData.cid,
            user_name: customData.username
          });

          console.log('🔵 表结构列表API响应:', response);

          if (response.success && response.data) {
            const convertedSchemas = response.data.map(convertSchemaItemToTableStructure);
            setServerSchemas(convertedSchemas);
            console.log('🔵 转换后的表结构数据:', convertedSchemas);
          } else {
            console.log('🔴 获取表结构列表失败:', response.error_msg);
            message.error('获取表结构列表失败: ' + (response.error_msg || '未知错误'));
          }
        } catch (error) {
          console.error('🔴 获取表结构列表出错:', error);
          message.error('获取表结构列表失败');
        } finally {
          setSchemaLoading(false);
        }
      }
    };

    fetchSchemas();
  }, [customData]);

  // 图片预加载函数
  const preloadImages = async (materials: ImageItem[]): Promise<void> => {
    console.log('🟡 开始预加载图片/视频首帧，总数:', materials.length);
    setPreloadingImages(true);

    const newCache = new Map(imageCache);
    let loadedCount = 0;

    // 分批预加载，避免一次性加载太多
    const batchSize = 10;
    for (let i = 0; i < materials.length; i += batchSize) {
      const batch = materials.slice(i, i + batchSize);

      const promises = batch.map(async (material) => {
        if ((material.type === 'IMAGE' || material.type === 'VIDEO') && material.url) {
          try {
            return new Promise<{ id: string; dataUrl: string }>((resolve, reject) => {
              const img = new Image();
              img.crossOrigin = 'anonymous';

              img.onload = () => {
                try {
                  // 创建canvas来生成缩略图
                  const canvas = document.createElement('canvas');
                  const ctx = canvas.getContext('2d');

                  // 设置缩略图尺寸
                  const maxSize = 40;
                  let { width, height } = img;

                  // 计算缩放比例
                  if (width > height) {
                    if (width > maxSize) {
                      height = (height * maxSize) / width;
                      width = maxSize;
                    }
                  } else {
                    if (height > maxSize) {
                      width = (width * maxSize) / height;
                      height = maxSize;
                    }
                  }

                  canvas.width = width;
                  canvas.height = height;

                  if (ctx) {
                    ctx.drawImage(img, 0, 0, width, height);
                    const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                    resolve({ id: material.material_id, dataUrl });
                  } else {
                    reject(new Error('无法创建canvas上下文'));
                  }
                } catch (error) {
                  reject(error);
                }
              };

              img.onerror = () => {
                reject(new Error('图片加载失败'));
              };

              img.src = material.url;
            });
          } catch (error) {
            console.warn('预加载图片/视频首帧失败:', material.name, error);
            return null;
          }
        }
        return null;
      });

      const results = await Promise.allSettled(promises);
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          newCache.set(result.value.id, result.value.dataUrl);
        }
      });

      loadedCount += batch.length;
      console.log(`🟡 已预加载 ${loadedCount}/${materials.length} 张图片/视频首帧`);
    }

    setImageCache(newCache);
    setPreloadingImages(false);
    console.log('🟡 图片/视频首帧预加载完成，缓存数量:', newCache.size);
  };

  // 获取素材库数据
  useEffect(() => {
    const fetchMaterials = async () => {
      if (customData?.cid) {
        try {
          setMaterialLoading(true);
          console.log('🔵 开始获取在线素材库数据');

          const response = await queryOnlineMediaMaterials({
            cid: customData.cid
          });

          console.log('🔵 素材库API响应:', response);

          if (response.success && response.data) {
            setMaterialLibrary(response.data);
            // 提取可用的素材组名称
            const groups = Object.keys(response.data).filter(group =>
              response.data[group] && response.data[group].length > 0
            );
            setAvailableMaterialGroups(groups);
            console.log('🔵 素材库数据设置成功:', response.data);
            console.log('🔵 可用素材组:', groups);

            // 预加载所有图片和视频首帧素材
            const allMaterials = Object.values(response.data).flat();
            const imageMaterials = allMaterials.filter(material => material.type === 'IMAGE' || material.type === 'VIDEO');
            if (imageMaterials.length > 0) {
              console.log('🟡 开始预加载图片/视频首帧素材，数量:', imageMaterials.length);
              preloadImages(imageMaterials);
            }
          } else {
            console.log('🔴 获取素材库数据失败:', response.error_msg);
            setMaterialLibrary({});
            setAvailableMaterialGroups([]);
          }
        } catch (error) {
          console.error('🔴 获取素材库数据出错:', error);
          setMaterialLibrary({});
          setAvailableMaterialGroups([]);
        } finally {
          setMaterialLoading(false);
        }
      }
    };

    fetchMaterials();
  }, [customData]);

  const [editableForm] = Form.useForm(); // ProTable的可编辑表单实例

  // 设置表单默认值
  useEffect(() => {
    if (actualMode === 'create') {
      form.setFieldsValue({
        type: 'DOCUMENT',
        validPeriodType: 'permanent',
        materialGroups: [] // 新增：默认空素材组选择
      });
    }
  }, [actualMode, form]);

  // 获取知识详情数据
  useEffect(() => {
    const fetchKnowledgeDetail = async () => {
      if (actualMode !== 'create' && knowledgeId && customData?.uid && customData?.cid && customData?.username) {
        try {
          setLoading(true); // 开始加载

          const response = await getKnowledgeDetailByKnowledgeId({
            uid: customData.uid,
            cid: customData.cid,
            user_name: customData.username,
            knowledge_id: knowledgeId
          });

          if (response.success && response.data) {
            let knowledgeDetail: any;

            // 根据您提供的信息，API返回的是object，直接使用
            knowledgeDetail = response.data;

            // 验证必要字段
            if (!knowledgeDetail.name || !knowledgeDetail.desc) {
              console.error('🔴 知识详情数据格式错误：缺少必要字段', knowledgeDetail);
              throw new Error('知识详情数据格式错误：缺少必要字段');
            }

            // 转换API数据到表单格式
            const knowledgeType = knowledgeDetail.content_type; // 直接使用API返回的值：'TABLE' 或 'DOCUMENT'
            const validPeriodType = knowledgeDetail.longtime_valid ? 'permanent' : 'custom';

            const formData = {
              name: knowledgeDetail.name,
              summary: knowledgeDetail.desc,
              type: knowledgeType as 'DOCUMENT' | 'TABLE',
              tags: knowledgeDetail.tags || [],
              validPeriodType: validPeriodType as 'permanent' | 'custom',
              content: knowledgeDetail.document_content || '',
              validPeriod: knowledgeDetail.longtime_valid ?
                undefined :
                [dayjs(knowledgeDetail.valid_date), dayjs(knowledgeDetail.invalid_date)] as [Dayjs, Dayjs],
              materialGroups: knowledgeDetail.material_groups || [] // 新增：素材组数据
            };

            form.setFieldsValue(formData);
            setKnowledgeType(knowledgeType as 'DOCUMENT' | 'TABLE');
            setValidPeriodType(validPeriodType as 'permanent' | 'custom');

            // 设置选中的素材组
            setSelectedMaterialGroups(knowledgeDetail.material_groups || []);

            // 如果是表格类型，处理表格数据和结构
            if (knowledgeDetail.content_type === 'TABLE') {
              // 处理表格结构选择
              if (knowledgeDetail.schema_id) {
                // 设置表单中的structure字段
                const formDataWithStructure = {
                  ...formData,
                  structure: knowledgeDetail.schema_id
                };
                form.setFieldsValue(formDataWithStructure);
              }

              // 处理表格数据
              if (knowledgeDetail.table_data) {
                // 添加数据量检查
                if (knowledgeDetail.table_data.length > 100) {
                  message.warning(`表格数据量较大（${knowledgeDetail.table_data.length}行），加载可能需要一些时间`);
                }

                // 分批处理大量数据以提高性能
                const processTableDataInBatches = (data: any[], batchSize: number = 10) => {
                  // 设置进度提示
                  setDataProcessingProgress({
                    isProcessing: true,
                    current: 0,
                    total: Math.ceil(data.length / batchSize),
                    message: '正在处理表格数据...'
                  });

                  // 添加超时保护
                  const startTime = Date.now();
                  const timeout = 30000; // 30秒超时

                  // 使用setTimeout确保UI响应性，但间隔更短
                  const processBatch = (batchIndex: number, accumulatedResult: TableRowData[] = []) => {
                    try {
                      // 检查超时
                      if (Date.now() - startTime > timeout) {
                        console.error('🟡 数据处理超时，强制完成');
                        setDataProcessingProgress({
                          isProcessing: false,
                          current: 0,
                          total: 0,
                          message: ''
                        });
                        setTableData(accumulatedResult);
                        return;
                      }

                      if (batchIndex >= Math.ceil(data.length / batchSize)) {
                        // 处理完成
                        setDataProcessingProgress({
                          isProcessing: false,
                          current: 0,
                          total: 0,
                          message: ''
                        });
                        setTableData(accumulatedResult);
                        message.success(`表格数据加载完成，共${accumulatedResult.length}行数据`);
                        return;
                      }

                      const start = batchIndex * batchSize;
                      const end = Math.min(start + batchSize, data.length);
                      const batch = data.slice(start, end);

                      // 简化数据处理，减少出错概率
                      const batchResult = batch.map((row: any, index: number) => {
                        try {
                          const rowData: any = { id: start + index + 1 };

                          // 安全地处理cols数据
                          if (row.cols && Array.isArray(row.cols)) {
                            row.cols.forEach((col: any) => {
                              if (col && col.col_name) {
                                rowData[col.col_name] = col.col_value || '';
                              }
                            });
                          }

                          return rowData;
                        } catch (error) {
                          console.error(`🟡 处理第${start + index + 1}行数据时出错:`, error, row);
                          return {
                            id: start + index + 1,
                            error: '数据处理失败'
                          };
                        }
                      });

                      const newAccumulatedResult = [...accumulatedResult, ...batchResult];

                      // 更新进度
                      setDataProcessingProgress(prev => ({
                        ...prev,
                        current: batchIndex + 1,
                        message: `正在处理表格数据... (第${batchIndex + 1}批，共${Math.ceil(data.length / batchSize)}批)`
                      }));

                      // 使用setTimeout处理下一批，间隔更短
                      setTimeout(() => {
                        processBatch(batchIndex + 1, newAccumulatedResult);
                      }, 5); // 5ms间隔

                    } catch (error) {
                      console.error(`🟡 处理第${batchIndex + 1}批时出错:`, error);
                      // 即使出错也继续处理下一批
                      setTimeout(() => {
                        processBatch(batchIndex + 1, accumulatedResult);
                      }, 5);
                    }
                  };

                  // 开始处理第一批
                  processBatch(0);
                };

                // 如果数据量很大，使用分批处理
                if (knowledgeDetail.table_data.length > 50) {
                  // 尝试分批处理，如果失败则回退到直接处理
                  try {
                    console.log('🟡 开始分批处理数据，总数:', knowledgeDetail.table_data.length);

                    // 添加超时保护，如果分批处理超过10秒，直接使用同步处理
                    const batchTimeout = setTimeout(() => {
                      console.log('🟡 分批处理超时，切换到直接处理');
                      setDataProcessingProgress({
                        isProcessing: false,
                        current: 0,
                        total: 0,
                        message: ''
                      });

                      // 直接处理所有数据
                      const convertedTableData: TableRowData[] = knowledgeDetail.table_data.map((row: any, index: number) => {
                        try {
                          const rowData: any = { id: index + 1 };

                          // 安全地处理cols数据
                          if (row.cols && Array.isArray(row.cols)) {
                            row.cols.forEach((col: any) => {
                              if (col && col.col_name) {
                                rowData[col.col_name] = col.col_value || '';
                              }
                            });
                          }

                          return rowData;
                        } catch (rowError) {
                          console.error(`🟡 直接处理第${index + 1}行数据时出错:`, rowError, row);
                          return {
                            id: index + 1,
                            error: '数据处理失败'
                          };
                        }
                      });
                      setTableData(convertedTableData);
                    }, 10000); // 10秒超时

                    processTableDataInBatches(knowledgeDetail.table_data);

                    // 如果分批处理成功完成，清除超时定时器
                    const checkCompletion = () => {
                      if (!dataProcessingProgress.isProcessing) {
                        clearTimeout(batchTimeout);
                      } else {
                        setTimeout(checkCompletion, 100);
                      }
                    };
                    setTimeout(checkCompletion, 100);

                  } catch (error) {
                    console.error('🟡 分批处理失败，回退到直接处理:', error);
                    // 回退到直接处理
                    const convertedTableData: TableRowData[] = knowledgeDetail.table_data.map((row: any, index: number) => {
                      try {
                        const rowData: any = { id: index + 1 };

                        // 安全地处理cols数据
                        if (row.cols && Array.isArray(row.cols)) {
                          row.cols.forEach((col: any) => {
                            if (col && col.col_name) {
                              rowData[col.col_name] = col.col_value || '';
                            }
                          });
                        }

                        return rowData;
                      } catch (rowError) {
                        console.error(`🟡 直接处理第${index + 1}行数据时出错:`, rowError, row);
                        return {
                          id: index + 1,
                          error: '数据处理失败'
                        };
                      }
                    });
                    setTableData(convertedTableData);
                  }
                } else {
                  // 数据量较小，直接处理
                  const convertedTableData: TableRowData[] = knowledgeDetail.table_data.map((row: any, index: number) => {
                    try {
                      const rowData: any = { id: index + 1 };

                      // 安全地处理cols数据
                      if (row.cols && Array.isArray(row.cols)) {
                        row.cols.forEach((col: any) => {
                          if (col && col.col_name) {
                            rowData[col.col_name] = col.col_value || '';
                          }
                        });
                      }

                      return rowData;
                    } catch (error) {
                      console.error(`🟡 处理第${index + 1}行数据时出错:`, error, row);
                      return {
                        id: index + 1,
                        error: '数据处理失败'
                      };
                    }
                  });
                  setTableData(convertedTableData);
                }
              }
            }
          } else {
            console.log('🔴 获取知识详情失败:', response.error_msg);
            message.error('获取知识详情失败: ' + (response.error_msg || '未知错误'));
          }
        } catch (error) {
          console.error('🔴 获取知识详情出错:', error);
          message.error('获取知识详情失败: ' + (error instanceof Error ? error.message : '未知错误'));
        } finally {
          setLoading(false); // 结束加载
        }
      } else if (actualMode === 'create') {
        // 创建模式不需要加载数据，直接设置loading为false
        setLoading(false);
      }
    };

    fetchKnowledgeDetail();
  }, [actualMode, knowledgeId, form, customData]);

  // 当schema数据加载完成后，处理已存在知识的表格结构选择
  useEffect(() => {
    if (actualMode !== 'create' && knowledgeType === 'TABLE' && serverSchemas.length > 0) {
      const currentStructureId = form.getFieldValue('structure');
      if (currentStructureId) {
        const matchedStructure = serverSchemas.find(s => s.id === currentStructureId);
        if (matchedStructure && !selectedStructure) {
          console.log('🟢 根据schema_id设置表格结构:', matchedStructure);
          setSelectedStructure(matchedStructure);
        }
      }
    }
  }, [serverSchemas, knowledgeType, selectedStructure, actualMode, form]);

  // 监听知识类型变化，确保表格结构状态正确
  useEffect(() => {
    if (knowledgeType === 'TABLE') {
      const currentStructureId = form.getFieldValue('structure');
      if (currentStructureId && !selectedStructure) {
        // 如果有structure值但没有selectedStructure，尝试恢复
        const matchedStructure = serverSchemas.find(s => s.id === currentStructureId) ||
          userStructures.find(s => s.id === currentStructureId);
        if (matchedStructure) {
          setSelectedStructure(matchedStructure);
        }
      }
    }
  }, [knowledgeType, selectedStructure, serverSchemas, userStructures, form]);

  const handleTypeChange = (value: 'DOCUMENT' | 'TABLE'): void => {
    setKnowledgeType(value);
    if (value === 'DOCUMENT') {
      setSelectedStructure(null);
      setTableData([]);
      // 清空表格结构选择
      form.setFieldsValue({ structure: undefined });
    } else if (value === 'TABLE') {
      // 切换到表格模式时，如果有之前选中的结构，恢复它
      const currentStructureId = form.getFieldValue('structure');
      if (currentStructureId) {
        const matchedStructure = serverSchemas.find(s => s.id === currentStructureId) ||
          userStructures.find(s => s.id === currentStructureId);
        if (matchedStructure) {
          setSelectedStructure(matchedStructure);
        }
      }
    }
  };

  const handleStructureChange = (value: string): void => {
    console.log('🟡 === 表格结构选择变化 ===');
    console.log('🟡 选择的值:', value);
    console.log('🟡 服务端结构列表:', serverSchemas);
    console.log('🟡 用户自定义结构列表:', userStructures);

    if (value === 'add_new') {
      console.log('🟡 用户选择添加新结构');
      setShowStructureModal(true);
      // 重置下拉框的值，避免选中状态影响后续操作
      form.setFieldsValue({ structure: undefined });
      return;
    }

    const structure = serverSchemas.find(s => s.id === value) ||
      userStructures.find(s => s.id === value);

    console.log('🟡 找到的结构:', structure);

    setSelectedStructure(structure || null);
    setTableData([]);

    console.log('🟡 结构选择完成，开始滚动到表格区域');

    // 延迟滚动到表格区域，等待DOM更新
    setTimeout(() => {
      const tableElement = document.querySelector('.table-content');
      if (tableElement) {
        console.log('🟡 找到表格元素，开始滚动');
        tableElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      } else {
        console.log('🟡 未找到表格元素');
      }
    }, 100);
  };

  // 删除表格结构
  const handleDeleteSchema = async (schemaId: string, schemaName: string): Promise<void> => {
    if (!customData?.uid || !customData?.cid || !customData?.username) {
      message.error('用户信息缺失，无法删除');
      return;
    }

    Modal.confirm({
      title: '确认删除表格结构',
      content: `确定要删除表格结构"${schemaName}"吗？删除后不可恢复。`,
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteSchema({
            uid: customData.uid,
            cid: customData.cid,
            user_name: customData.username,
            schema_id: schemaId
          });

          if (response.success) {
            message.success('表格结构删除成功');

            // 从本地状态中移除被删除的表格结构
            setServerSchemas(prev => prev.filter(schema => schema.id !== schemaId));
            setUserStructures(prev => prev.filter(schema => schema.id !== schemaId));

            // 如果当前选中的结构被删除，清空选择
            if (selectedStructure?.id === schemaId) {
              setSelectedStructure(null);
              setTableData([]);
              form.setFieldsValue({ structure: undefined });
            }
          } else {
            throw new Error(response.error_msg || '删除失败');
          }
        } catch (error: any) {
          console.error('删除表格结构失败:', error);
          message.error(error.message || '删除表格结构失败，请重试');
        }
      }
    });
  };

  // 处理有效期类型变化
  const handleValidPeriodTypeChange = (e: any): void => {
    const newType = e.target.value;
    setValidPeriodType(newType);

    // 如果切换到自定义时间，设置默认的生效日期为当前时间
    if (newType === 'custom') {
      form.setFieldsValue({
        validPeriod: [dayjs(), null]
      });
    }
  };

    // 构建Mentions选项数据 - 修改为只显示选中的素材组
  useEffect(() => {
    const options: MentionOption[] = [];

    // 如果没有选择任何素材组，显示提示信息
    if (selectedMaterialGroups.length === 0) {
      options.push({
        value: 'no_groups_selected',
        label: (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px 0',
            color: '#999',
            fontStyle: 'italic'
          }}>
            <PictureOutlined style={{
              marginRight: 8,
              color: '#ccc',
              fontSize: '14px'
            }} />
            <span>请先选择要引入的素材组</span>
          </div>
        ),
        type: 'category'
      });
    } else {
      // 检查选中的素材组是否有素材数据
      const hasAnyMaterials = selectedMaterialGroups.some(group =>
        materialLibrary[group] && materialLibrary[group].length > 0
      );

      if (!hasAnyMaterials) {
        // 如果选中的素材组都没有素材，显示提示信息
        options.push({
          value: 'no_materials_in_selected_groups',
          label: (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 0',
              color: '#999',
              fontStyle: 'italic'
            }}>
              <PictureOutlined style={{
                marginRight: 8,
                color: '#ccc',
                fontSize: '14px'
              }} />
              <span>选中的素材组中暂无上线的素材</span>
            </div>
          ),
          type: 'category'
        });
      } else {
        // 添加分组选项
        selectedMaterialGroups.forEach(category => {
          const categoryMaterials = materialLibrary[category];
          if (categoryMaterials && categoryMaterials.length > 0) {
            options.push({
              value: `category_${category}`,
              label: (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '4px 0',
                  fontWeight: 600
                }}>
                  <FolderOutlined style={{
                    marginRight: 10,
                    color: '#1890ff',
                    fontSize: '16px'
                  }} />
                  <span style={{
                    fontSize: '15px',
                    color: '#262626'
                  }}>
                    {category}
                  </span>
                  <span style={{
                    marginLeft: 12,
                    color: '#999',
                    fontSize: '13px',
                    fontWeight: 400
                  }}>
                    ({categoryMaterials.length}项)
                  </span>
                </div>
              ),
              category,
              type: 'category'
            });

            // 添加该分组下的素材选项
            categoryMaterials.forEach(material => {
              const materialType = material.type === 'VIDEO' ? '视频' : material.type === 'TEXT' ? '文本' : '图片';
              options.push({
                value: `[${materialType}:${material.name}:${material.material_id}]`,
                label: (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '2px 0',
                    paddingLeft: '16px' // 缩进显示层级关系
                  }}>
                    <div style={{
                      width: 20,
                      height: 20,
                      marginRight: 8,
                      borderRadius: 3,
                      overflow: 'hidden',
                      background: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {(material.type === 'IMAGE' || material.type === 'VIDEO') ? (
                        <img
                          src={imageCache.get(material.material_id) || material.url}
                          alt={material.name}
                          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          onError={(e) => {
                            // 如果预加载的图片加载失败，回退到原图
                            const target = e.target as HTMLImageElement;
                            if (target.src !== material.url) {
                              target.src = material.url;
                            }
                          }}
                        />
                      ) : material.type === 'TEXT' ? (
                        <FileTextOutlined style={{ fontSize: 12, color: '#8c8c8c' }} />
                      ) : null}
                    </div>
                    <span style={{
                      fontSize: '13px',
                      color: '#595959'
                    }}>
                      {material.name}
                    </span>
                    <span style={{
                      marginLeft: 6,
                      padding: '0 3px',
                      borderRadius: 2,
                      fontSize: '11px',
                      background: material.type === 'VIDEO' ? '#e6f7ff' : material.type === 'TEXT' ? '#fff2e6' : '#f6ffed',
                      color: material.type === 'VIDEO' ? '#1890ff' : material.type === 'TEXT' ? '#fa8c16' : '#52c41a'
                    }}>
                      {materialType}
                    </span>
                  </div>
                ),
                category,
                image: material,
                type: 'image'
              });
            });
          }
        });
      }
    }

    setMentionOptions(options);
  }, [materialLibrary, selectedMaterialGroups, imageCache]);

  const getPageTitle = (): string => {
    return actualMode === 'create' ? '新建知识' : actualMode === 'view' ? '查看知识' : '编辑知识';
  };

  // 处理保存操作
  const handleSave = async (): Promise<void> => {
    try {
      console.log('🟢 === 开始保存知识 ===');
      console.log('🟢 当前模式:', actualMode);
      console.log('🟢 知识类型:', knowledgeType);
      console.log('🟢 用户信息:', {
        uid: customData?.uid,
        cid: customData?.cid,
        username: customData?.username
      });

      setUploading(true);

      const values = await form.validateFields();
      console.log('🟢 表单验证通过，表单数据:', values);

      if (actualMode === 'create') {
        // 创建模式
        console.log('🟢 === 创建模式 - 构建API参数 ===');

        // 构建基础参数
        const baseParams = {
          uid: customData?.uid || '',
          cid: customData?.cid || '',
          user_name: customData?.username || '',
          name: values.name,
          desc: values.summary,
          tags: values.tags || [],
          group_id: groupId || '',
          longtime_valid: validPeriodType === 'permanent',
          content_type: knowledgeType,
          material_groups: selectedMaterialGroups // 新增：选中的素材组
        };

        console.log('🟢 基础参数:', baseParams);

        // 处理有效期数据
        let validPeriodParams = {};
        if (validPeriodType === 'custom' && values.validPeriod) {
          validPeriodParams = {
            valid_date: values.validPeriod[0]?.format('YYYY-MM-DD'),
            invalid_date: values.validPeriod[1]?.format('YYYY-MM-DD')
          };
          console.log('🟢 有效期参数:', validPeriodParams);
        }

        // 处理表格数据
        let tableParams = {};
        if (knowledgeType === 'TABLE' && selectedStructure) {
          console.log('🟢 === 处理表格数据 ===');
          console.log('🟢 选中的表格结构:', selectedStructure);
          console.log('🟢 当前表格数据:', tableData);
          console.log('🟢 表格数据行数:', tableData.length);

          if (tableData.length === 0) {
            console.log('🟡 警告：表格数据为空，但仍会发送结构信息');
          }

          // 构建表格数据
          const processedTableData = tableData.map((row, rowIndex) => {
            const cols = selectedStructure.columns.map(col => {
              const colValue = row[col.dataIndex] || '';
              return {
                col_name: col.dataIndex,
                col_value: colValue
              };
            });

            if (rowIndex < 3) { // 只记录前3行的详细信息
              console.log(`🟢 第${rowIndex + 1}行数据:`, {
                rowId: row.id,
                cols: cols
              });
            }

            return { cols };
          });

          tableParams = {
            schema_id: selectedStructure.id,
            table_data: processedTableData
          };

          console.log('🟢 表格参数构建完成:', {
            schema_id: selectedStructure.id,
            table_data_length: processedTableData.length,
            sample_table_data: processedTableData.slice(0, 2)
          });
        }

        // 处理文档数据
        let documentParams = {};
        if (knowledgeType === 'DOCUMENT') {
          documentParams = {
            document_content: values.content
          };
          console.log('🟢 文档参数:', {
            content_length: values.content?.length || 0,
            content_preview: values.content?.substring(0, 100) + '...'
          });
        }

        // 合并所有参数
        const apiParams: API.CreateKnowledgeParams = {
          ...baseParams,
          ...validPeriodParams,
          ...tableParams,
          ...documentParams
        };

        console.log('🟢 === 最终API参数 ===');
        console.log('🟢 API接口: /Knowledge/create_knowledge');
        console.log('🟢 请求方法: POST');
        console.log('🟢 完整请求参数:', JSON.stringify(apiParams, null, 2));
        console.log('🟢 参数大小:', JSON.stringify(apiParams).length, '字符');
        console.log('🟢 ==========================');

        // 调用API保存知识
        console.log('🟢 开始调用API...');
        const startTime = Date.now();
        const response = await createKnowledge(apiParams);
        const endTime = Date.now();

        console.log('🟢 === API调用完成 ===');
        console.log('🟢 调用耗时:', endTime - startTime, 'ms');
        console.log('🟢 响应状态:', response.success);
        console.log('🟢 完整响应数据:', response);
        console.log('🟢 错误信息:', response.error_msg);
        console.log('🟢 返回数据:', (response as any).data);
        console.log('🟢 =================');

        if (response.success) {
          console.log('🟢 知识创建成功！');
          message.success('知识创建成功');
          // 保存成功后返回上一页
          setTimeout(() => {
            onBack();
          }, 1000);
        } else {
          console.log('🔴 API返回失败:', response.error_msg);
          throw new Error(response.error_msg || '创建失败');
        }
      } else {
        // 编辑模式
        console.log('🟢 === 编辑模式 - 构建API参数 ===');

        // 构建基础参数
        const baseParams = {
          uid: customData?.uid || '',
          cid: customData?.cid || '',
          user_name: customData?.username || '',
          name: values.name,
          desc: values.summary,
          tags: values.tags || [],
          group_id: groupId || '',
          longtime_valid: validPeriodType === 'permanent',
          content_type: knowledgeType,
          knowledge_id: knowledgeId || '',
          material_groups: selectedMaterialGroups // 新增：选中的素材组
        };

        console.log('🟢 基础参数:', baseParams);

        // 处理有效期数据
        let validPeriodParams = {};
        if (validPeriodType === 'custom' && values.validPeriod) {
          validPeriodParams = {
            valid_date: values.validPeriod[0]?.format('YYYY-MM-DD'),
            invalid_date: values.validPeriod[1]?.format('YYYY-MM-DD')
          };
          console.log('🟢 有效期参数:', validPeriodParams);
        }

        // 处理表格数据
        let tableParams = {};
        if (knowledgeType === 'TABLE' && selectedStructure) {
          console.log('🟢 === 处理表格数据（编辑模式）===');
          console.log('🟢 选中的表格结构:', selectedStructure);
          console.log('🟢 当前表格数据:', tableData);
          console.log('🟢 表格数据行数:', tableData.length);

          // 构建表格数据
          const processedTableData = tableData.map((row, rowIndex) => {
            const cols = selectedStructure.columns.map(col => {
              const colValue = row[col.dataIndex] || '';
              return {
                col_name: col.dataIndex,
                col_value: colValue
              };
            });

            if (rowIndex < 3) { // 只记录前3行的详细信息
              console.log(`🟢 第${rowIndex + 1}行数据:`, {
                rowId: row.id,
                cols: cols
              });
            }

            return { cols };
          });

          tableParams = {
            schema_id: selectedStructure.id,
            table_data: processedTableData
          };

          console.log('🟢 表格参数构建完成:', {
            schema_id: selectedStructure.id,
            table_data_length: processedTableData.length,
            sample_table_data: processedTableData.slice(0, 2)
          });
        }

        // 处理文档数据
        let documentParams = {};
        if (knowledgeType === 'DOCUMENT') {
          documentParams = {
            document_content: values.content
          };
          console.log('🟢 文档参数:', {
            content_length: values.content?.length || 0,
            content_preview: values.content?.substring(0, 100) + '...'
          });
        }

        // 合并所有参数
        const apiParams: API.UpdateKnowledgeParams = {
          ...baseParams,
          ...validPeriodParams,
          ...tableParams,
          ...documentParams
        };

        console.log('🟢 === 最终API参数（编辑模式）===');
        console.log('🟢 API接口: /Knowledge/update_knowledge');
        console.log('🟢 请求方法: POST');
        console.log('🟢 完整请求参数:', JSON.stringify(apiParams, null, 2));
        console.log('🟢 参数大小:', JSON.stringify(apiParams).length, '字符');
        console.log('🟢 ===========================');

        // 调用API修改知识
        console.log('🟢 开始调用API...');
        const startTime = Date.now();
        const response = await updateKnowledgeNew(apiParams);
        const endTime = Date.now();

        console.log('🟢 === API调用完成（编辑模式）===');
        console.log('🟢 调用耗时:', endTime - startTime, 'ms');
        console.log('🟢 响应状态:', response.success);
        console.log('🟢 完整响应数据:', response);
        console.log('🟢 错误信息:', response.error_msg);
        console.log('🟢 返回数据:', (response as any).data);
        console.log('🟢 =================');

        if (response.success) {
          console.log('🟢 知识修改成功！');
          message.success('知识修改成功');
          // 保存成功后返回上一页
          setTimeout(() => {
            onBack();
          }, 1000);
        } else {
          console.log('🔴 API返回失败:', response.error_msg);
          throw new Error(response.error_msg || '修改失败');
        }
      }

    } catch (error: any) {
      console.error('🔴 === 保存失败 ===');
      console.error('🔴 错误类型:', error.constructor.name);
      console.error('🔴 错误信息:', error.message);
      console.error('🔴 错误堆栈:', error.stack);
      console.error('🔴 完整错误对象:', error);
      console.error('🔴 ================');

      if (error.errorFields) {
        console.log('🔴 表单验证错误:', error.errorFields);
        message.error('请完善必填信息');
      } else {
        message.error(error.message || '保存失败，请重试');
      }
    } finally {
      setUploading(false);
      console.log('🟢 保存操作结束');
    }
  };

  // 处理取消操作
  const handleCancel = (): void => {
    Modal.confirm({
      title: '确认取消',
      content: '取消后未保存的内容将丢失，确定要取消吗？',
      okText: '确定',
      cancelText: '继续编辑',
      onOk: () => {
        onBack();
      },
    });
  };

  // 处理文件上传和内容解析
  const handleFileUpload = (file: any): boolean => {
    const fileTypes = ['text/plain', 'text/markdown', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    // 检查文件类型
    if (!fileTypes.includes(file.type) && !file.name.match(/\.(txt|md|doc|docx|pdf)$/i)) {
      message.error('请上传支持的文档格式：TXT、MD、DOC、DOCX、PDF');
      return false;
    }

    // 检查文件大小
    if (file.size > maxSize) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    setUploading(true);

    // 根据文件类型选择不同的解析方式
    if (file.name.match(/\.(doc|docx)$/i)) {
      handleWordUpload(file);
    } else if (file.name.match(/\.pdf$/i)) {
      handlePDFUpload(file);
    } else {
      // 处理文本文件（TXT、MD）
      handleTextUpload(file);
    }

    return false; // 阻止自动上传
  };

  // 处理Word文档上传
  const handleWordUpload = async (file: any): Promise<void> => {
    try {
      // 动态导入mammoth
      const mammoth = await import('mammoth');

      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });

      if (result.value && result.value.trim()) {
        handleParsedContent(result.value.trim(), file.name);

        // 如果有警告信息，显示给用户
        if (result.messages && result.messages.length > 0) {
          const warnings = result.messages.filter(msg => msg.type === 'warning');
          if (warnings.length > 0) {
            console.warn('Word文档解析警告:', warnings);
          }
        }
      } else {
        message.warning('Word文档中未找到可提取的文本内容');
        setUploading(false);
      }

    } catch (error) {
      console.error('Word文档解析失败:', error);
      message.error('Word文档解析失败，请检查文件是否损坏或为支持的格式');
      setUploading(false);
    }
  };

  // 处理PDF文档上传
  const handlePDFUpload = async (file: any): Promise<void> => {
    try {
      console.log('开始解析PDF文件:', file.name);

      // 通过CDN加载pdfjs-dist，避免模块导入问题
      if (!(window as any).pdfjsLib) {
        // 动态加载PDF.js脚本
        await new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      }

      const pdfjsLib = (window as any).pdfjsLib;

      // 设置worker路径
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      console.log(`PDF页数: ${pdf.numPages}`);

      let extractedText = '';

      // 遍历所有页面提取文本
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // 将文本项合并为字符串
          const pageText = textContent.items
            .map((item: any) => {
              // 确保item.str存在
              return typeof item.str === 'string' ? item.str : '';
            })
            .filter((str: string) => str.trim()) // 过滤空字符串
            .join(' ')
            .replace(/\s+/g, ' ') // 合并多个空格
            .trim();

          if (pageText) {
            if (pdf.numPages > 1) {
              extractedText += `\n\n=== 第${pageNum}页 ===\n${pageText}`;
            } else {
              extractedText += pageText;
            }
          }
        } catch (pageError) {
          console.warn(`解析第${pageNum}页时出错:`, pageError);
          extractedText += `\n\n=== 第${pageNum}页解析失败 ===\n`;
        }
      }

      if (extractedText.trim()) {
        // 清理提取的文本
        const cleanedText = extractedText
          .replace(/^=== 第1页 ===\n/, '') // 移除第一页的标题（如果只有一页）
          .trim();

        handleParsedContent(cleanedText, file.name);
        console.log('PDF解析成功，提取文本长度:', cleanedText.length);
      } else {
        message.warning('PDF文档中未找到可提取的文本内容，可能是图片或扫描件');
        setUploading(false);
      }

    } catch (error) {
      console.error('PDF文档解析失败:', error);
      // 提供更详细的错误信息
      let errorMessage = 'PDF文档解析失败';
      if (error instanceof Error) {
        if (error.message.includes('Invalid PDF')) {
          errorMessage = 'PDF文件格式无效或已损坏';
        } else if (error.message.includes('password')) {
          errorMessage = 'PDF文件受密码保护，请先解除保护';
        } else if (error.message.includes('Failed to load')) {
          errorMessage = 'PDF文件加载失败，请检查文件是否完整';
        } else {
          errorMessage = `PDF解析失败: ${error.message}`;
        }
      }
      message.error(errorMessage);
      setUploading(false);
    }
  };

  // 处理文本文件上传
  const handleTextUpload = (file: any): void => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        if (content && content.trim()) {
          handleParsedContent(content.trim(), file.name);
        } else {
          message.warning('文件内容为空');
          setUploading(false);
        }
      } catch (error) {
        console.error('文本文件解析失败:', error);
        message.error('文件解析失败，请检查文件格式');
        setUploading(false);
      }
    };

    reader.onerror = () => {
      message.error('文件读取失败');
      setUploading(false);
    };

    reader.readAsText(file, 'UTF-8');
  };

  // 处理表格文件上传（Excel/CSV）
  const handleTableFileUpload = (file: any): boolean => {
    console.log('🟡 === 开始处理表格文件上传 ===');
    console.log('🟡 上传文件信息:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    if (!selectedStructure) {
      console.log('🔴 表格结构未选择，无法上传文件');
      message.error('请先选择表格结构');
      return false;
    }

    console.log('🟡 当前选中的表格结构:', selectedStructure);

    const fileType = file.name.split('.').pop()?.toLowerCase();
    console.log('🟡 文件类型:', fileType);

    if (!['xlsx', 'xls', 'csv'].includes(fileType || '')) {
      console.log('🔴 不支持的文件类型:', fileType);
      message.error('只支持 Excel (.xlsx, .xls) 和 CSV (.csv) 文件');
      return false;
    }

    setUploading(true);
    console.log('🟡 开始解析文件...');

    if (fileType === 'csv') {
      handleCSVUpload(file);
    } else {
      handleExcelUpload(file);
    }

    return false; // 阻止默认上传行为
  };

  // 处理Excel文件上传
  const handleExcelUpload = (file: any): void => {
    console.log('🟡 === 开始解析Excel文件 ===');
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        console.log('🟡 Excel文件读取成功，开始解析...');
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

        console.log('🟡 Excel解析结果:', {
          sheetNames: workbook.SheetNames,
          selectedSheet: firstSheetName,
          dataRows: jsonData.length,
          sampleData: jsonData.slice(0, 3) // 显示前3行作为样本
        });

        processTableData(jsonData, file.name);
      } catch (error) {
        console.error('🔴 Excel解析失败:', error);
        message.error('Excel文件解析失败，请检查文件格式');
        setUploading(false);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  // 处理CSV文件上传
  const handleCSVUpload = (file: any): void => {
    console.log('🟡 === 开始解析CSV文件 ===');
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        console.log('🟡 CSV文件读取成功，开始解析...');
        const csvContent = e.target?.result as string;
        const lines = csvContent.split('\n').filter(line => line.trim());
        const csvData = lines.map(line => {
          // 简单的CSV解析，处理带引号的字段
          const result = [];
          let current = '';
          let inQuotes = false;

          for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              result.push(current.trim());
              current = '';
            } else {
              current += char;
            }
          }
          result.push(current.trim());
          return result;
        });

        console.log('🟡 CSV解析结果:', {
          totalLines: lines.length,
          dataRows: csvData.length,
          sampleData: csvData.slice(0, 3) // 显示前3行作为样本
        });

        processTableData(csvData, file.name);
      } catch (error) {
        console.error('🔴 CSV解析失败:', error);
        message.error('CSV文件解析失败，请检查文件格式');
        setUploading(false);
      }
    };
    reader.readAsText(file, 'UTF-8');
  };

  // 处理表格数据
  const processTableData = (rawData: any[][], fileName: string): void => {
    console.log('🟡 === 开始处理表格数据 ===');
    console.log('🟡 原始数据信息:', {
      totalRows: rawData.length,
      headers: rawData[0],
      sampleData: rawData.slice(0, 3)
    });

    if (!selectedStructure || rawData.length === 0) {
      console.log('🔴 数据验证失败:', {
        hasStructure: !!selectedStructure,
        dataLength: rawData.length
      });
      message.error('文件内容为空或表格结构未选择');
      setUploading(false);
      return;
    }

    const headers = rawData[0].map((header: any) => String(header).trim());
    const expectedHeaders = selectedStructure.columns.map(col => col.title);

    console.log('🟡 列名对比:', {
      fileHeaders: headers,
      expectedHeaders: expectedHeaders,
      headersMatch: headers.length === expectedHeaders.length
    });

    // 验证列名是否完全一致
    if (headers.length !== expectedHeaders.length) {
      console.log('🔴 列数不匹配:', {
        fileColumns: headers.length,
        expectedColumns: expectedHeaders.length
      });
      message.error(`列数不匹配！文件有${headers.length}列，表结构需要${expectedHeaders.length}列`);
      setUploading(false);
      return;
    }

    const missingHeaders = expectedHeaders.filter(expected => !headers.includes(expected));
    const extraHeaders = headers.filter(header => !expectedHeaders.includes(header));

    if (missingHeaders.length > 0 || extraHeaders.length > 0) {
      console.log('🔴 列名不匹配:', {
        missingHeaders,
        extraHeaders
      });
      let errorMsg = '列名不匹配！\n';
      if (missingHeaders.length > 0) {
        errorMsg += `缺少列：${missingHeaders.join(', ')}\n`;
      }
      if (extraHeaders.length > 0) {
        errorMsg += `多余列：${extraHeaders.join(', ')}\n`;
      }
      errorMsg += `期望的列名：${expectedHeaders.join(', ')}`;

      Modal.error({
        title: '列名验证失败',
        content: errorMsg,
        width: 500,
      });
      setUploading(false);
      return;
    }

    console.log('🟡 列名验证通过，开始转换数据格式...');

    // 转换数据格式
    const dataRows = rawData.slice(1); // 跳过表头
    const newTableData: TableRowData[] = dataRows.map((row, index) => {
      const rowData: any = { id: Date.now() + index };

      headers.forEach((header, colIndex) => {
        const columnConfig = selectedStructure.columns.find(col => col.title === header);
        if (columnConfig) {
          rowData[columnConfig.dataIndex] = String(row[colIndex] || '').trim();
        }
      });

      return rowData;
    });

    console.log('🟡 数据转换完成:', {
      convertedRows: newTableData.length,
      sampleConvertedData: newTableData.slice(0, 2),
      currentTableDataLength: tableData.length
    });

    // 询问用户是替换还是追加数据
    if (tableData.length > 0) {
      console.log('🟡 当前已有数据，显示选择对话框');
      Modal.confirm({
        title: '数据导入方式',
        content: `文件解析成功，共${newTableData.length}行数据。当前表格已有${tableData.length}行数据，请选择导入方式：`,
        okText: '替换现有数据',
        cancelText: '追加到末尾',
        onOk: () => {
          console.log('🟡 用户选择替换数据');
          setTableData(newTableData);
          message.success(`成功替换导入${newTableData.length}行数据（${fileName}）`);
          setUploading(false);
        },
        onCancel: () => {
          console.log('🟡 用户选择追加数据');
          const maxId = Math.max(...tableData.map(item => item.id), 0);
          const appendData = newTableData.map((item, index) => ({
            ...item,
            id: maxId + index + 1
          }));
          setTableData([...tableData, ...appendData]);
          message.success(`成功追加导入${newTableData.length}行数据（${fileName}）`);
          setUploading(false);
        },
      });
    } else {
      console.log('🟡 当前无数据，直接设置新数据');
      setTableData(newTableData);
      message.success(`成功导入${newTableData.length}行数据（${fileName}）`);
      setUploading(false);
    }
  };

  // 处理解析后的内容
  const handleParsedContent = (parsedContent: string, fileName: string): void => {
    const currentContent = form.getFieldValue('content') || '';

    // 如果当前有内容，询问用户是替换还是追加
    if (currentContent.trim()) {
      Modal.confirm({
        title: '文档内容处理',
        content: '当前已有文档内容，请选择处理方式：',
        okText: '替换现有内容',
        cancelText: '追加到末尾',
        onOk: () => {
          form.setFieldsValue({ content: parsedContent });
          message.success(`已成功解析并替换文档内容（${fileName}）`);
          setUploading(false);
        },
        onCancel: () => {
          const newContent = currentContent + '\n\n' + parsedContent;
          form.setFieldsValue({ content: newContent });
          message.success(`已成功解析并追加文档内容（${fileName}）`);
          setUploading(false);
        },
      });
    } else {
      // 没有现有内容，直接设置
      form.setFieldsValue({ content: parsedContent });
      message.success(`已成功解析文档内容（${fileName}）`);
      setUploading(false);
    }
  };

  // 处理素材组选择变化
  const handleMaterialGroupsChange = (groups: string[]): void => {
    console.log('🟡 === 素材组选择变化 ===');
    console.log('🟡 选中的素材组:', groups);
    setSelectedMaterialGroups(groups);
  };

  return (
    <PageContainer
      title={getPageTitle()}
      onBack={onBack}
      breadcrumb={{
        items: [
          {
            title: '数据资产',
          },
          {
            title: (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => history.push('/database/knowledge')}
              >
                知识库
              </span>
            ),
          },
          {
            title: (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  if (groupId) {
                    history.push(`/database/knowledge/manage/${groupId}`, groupData);
                  }
                }}
              >
                {groupData?.name || '知识组管理'}
              </span>
            ),
          },
          {
            title: getPageTitle(),
          },
        ],
      }}
    >


      <Card style={{ minHeight: 'calc(100vh - 200px)', position: 'relative' }}>
        {/* 全局数据加载进度提示 */}
        {dataProcessingProgress.isProcessing && (
          <div style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9999,
            padding: '20px 30px',
            backgroundColor: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 12
          }}>
            <Spin size="large" />
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '16px', fontWeight: 500, marginBottom: 8 }}>
                {dataProcessingProgress.message}
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                进度: {dataProcessingProgress.current}/{dataProcessingProgress.total}
              </div>
            </div>
          </div>
        )}

        <Spin spinning={loading} tip="正在加载知识详情...">
          <Form
            form={form}
            layout="vertical"
            disabled={!isEditing}
            style={{ paddingBottom: isEditing ? '40px' : '24px' }}
          >
          <div className="form-section">
            <h3 className="section-title">基本信息</h3>
            <Form.Item
              name="name"
              label="知识名称"
              rules={[{ required: true, message: '请输入知识名称' }]}
            >
              <Input placeholder="请输入知识名称" />
            </Form.Item>

            <Form.Item
              name="summary"
              label="知识摘要"
              rules={[{ required: true, message: '请输入知识摘要' }]}
            >
              <TextArea
                rows={3}
                placeholder="请输入知识摘要"
                showCount
                maxLength={500}
              />
            </Form.Item>

            <Form.Item
              name="tags"
              label="标签"
              tooltip="最多添加5个标签，每个标签不超过8个字符"
            >
              <Select
                mode="tags"
                placeholder="请输入标签，按回车添加"
                style={{ width: '400px', maxWidth: '100%' }}
                tokenSeparators={[',']}
                maxTagCount={5}
                maxTagTextLength={8}
                maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}个标签`}
              />
            </Form.Item>
          </div>

          <div className="form-section">
            <h3 className="section-title">有效期设置</h3>
            <Form.Item name="validPeriodType" initialValue="permanent">
              <Radio.Group
                value={validPeriodType}
                onChange={handleValidPeriodTypeChange}
              >
                <Radio value="permanent">永久有效</Radio>
                <Radio value="custom">自定义时间</Radio>
              </Radio.Group>
            </Form.Item>

            {validPeriodType === 'custom' && (
              <Form.Item
                name="validPeriod"
                label="有效期范围"
                rules={[{ required: true, message: '请选择有效期范围' }]}
              >
                <RangePicker
                  placeholder={['生效日期', '过期日期']}
                  style={{ width: 320 }}
                  defaultValue={[dayjs(), null]}
                />
              </Form.Item>
            )}
          </div>

          <div className="form-section">
            <h3 className="section-title">知识内容</h3>

            <div className="form-row">
              <Form.Item
                name="type"
                label="知识类型"
                rules={[{ required: true, message: '请选择知识类型' }]}
                className="form-item-compact"
              >
                                  <Select
                    placeholder="请选择知识类型"
                    onChange={handleTypeChange}
                    value={knowledgeType}
                    style={{ width: 120 }}
                  >
                    <Option value="DOCUMENT">文档</Option>
                    <Option value="TABLE">表格</Option>
                  </Select>
              </Form.Item>

              {knowledgeType === 'TABLE' && (
                <Form.Item
                  name="structure"
                  label="表格结构"
                  rules={[{ required: true, message: '请选择表格结构' }]}
                  className="form-item-compact"
                >
                  <Select
                    placeholder="请选择表格结构"
                    onChange={handleStructureChange}
                    optionLabelProp="label"
                    style={{ width: 250 }}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    loading={schemaLoading}
                  >
                    {serverSchemas.map(structure => (
                      <Option
                        key={structure.id}
                        value={structure.id}
                        label={structure.name}
                      >
                        <div className="structure-option" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div className="structure-name">{structure.name}</div>
                          {isEditing && (
                            <Button
                              type="text"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              style={{ fontSize: '12px', padding: '0 4px' }}
                              onClick={(e) => {
                                e.stopPropagation(); // 阻止触发下拉框选择
                                handleDeleteSchema(structure.id, structure.name);
                              }}
                              title="删除此表格结构"
                            />
                          )}
                        </div>
                      </Option>
                    ))}
                    {userStructures.map(structure => (
                      <Option
                        key={structure.id}
                        value={structure.id}
                        label={structure.name}
                      >
                        <div className="structure-option" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div className="structure-name" style={{ display: 'flex', alignItems: 'center' }}>
                            {structure.name}
                            <Tag color="orange" style={{ marginLeft: 8, fontSize: '12px' }}>
                              自定义
                            </Tag>
                          </div>
                          {isEditing && (
                            <Button
                              type="text"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              style={{ fontSize: '12px', padding: '0 4px' }}
                              onClick={(e) => {
                                e.stopPropagation(); // 阻止触发下拉框选择
                                handleDeleteSchema(structure.id, structure.name);
                              }}
                              title="删除此表格结构"
                            />
                          )}
                        </div>
                      </Option>
                    ))}
                    <Option key="add_new" value="add_new">
                      <div className="structure-option add-new-option">
                        <PlusOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                        <span style={{ color: '#1890ff' }}>添加新结构</span>
                      </div>
                    </Option>
                  </Select>
                </Form.Item>
              )}
            </div>

            {/* 表格编辑区域 */}
            {knowledgeType === 'TABLE' && selectedStructure && (
              <div className="table-content" style={{ marginTop: 24, position: 'relative' }}>
                <div style={{
                  marginBottom: 8,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  minHeight: '32px'
                }}>
                  <h3 className="section-title" style={{
                    marginBottom: 0,
                    marginTop: 0,
                    textDecoration: 'none',
                    borderBottom: 'none',
                    lineHeight: '32px',
                    fontSize: '16px',
                    fontWeight: 600
                  }}>{selectedStructure.name}</h3>
                </div>

                {/* 数据加载进度提示 */}
                {dataProcessingProgress.isProcessing && (
                  <div style={{
                    padding: '12px 16px',
                    backgroundColor: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: 6,
                    marginBottom: 16,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8
                  }}>
                    <Spin size="small" />
                    <span style={{ color: '#52c41a', fontSize: '14px' }}>
                      {dataProcessingProgress.message} ({dataProcessingProgress.current}/{dataProcessingProgress.total})
                    </span>
                  </div>
                )}

                {/* 素材组选择器和操作按钮容器 */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-end',
                  marginBottom: 8
                }}>
                  <Form.Item
                    name="materialGroups"
                    label="引用素材组"
                    tooltip="选择要引用的素材组，选中的素材组中的素材可以在表格内容中通过 @ 符号引用"
                    style={{ marginBottom: 0, marginRight: 8 }}
                  >
                    <Select
                      mode="multiple"
                      placeholder="请选择要引用的素材组"
                      value={selectedMaterialGroups}
                      onChange={handleMaterialGroupsChange}
                      style={{ width: '400px', maxWidth: '100%' }}
                      loading={materialLoading}
                      disabled={!isEditing}
                      showSearch
                      filterOption={(input, option) => {
                        if (!option?.children) return false;
                        return option.children.toString().toLowerCase().includes(input.toLowerCase());
                      }}
                      maxTagCount={5}
                      maxTagTextLength={10}
                      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}个素材组`}
                    >
                      {availableMaterialGroups.map(group => {
                        const materialCount = materialLibrary[group]?.length || 0;
                        return (
                          <Option key={group} value={group}>
                            <div style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              width: '100%'
                            }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <FolderOutlined style={{
                                  marginRight: 8,
                                  color: '#1890ff',
                                  fontSize: '14px'
                                }} />
                                <span>{group}</span>
                              </div>
                              <span style={{
                                color: '#999',
                                fontSize: '12px',
                                marginLeft: 8
                              }}>
                                {materialCount}项素材
                              </span>
                            </div>
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>

                  {/* 表格操作按钮 */}
                  {isEditing && (
                    <Space>
                      <Upload
                        accept=".xlsx,.xls,.csv"
                        showUploadList={false}
                        beforeUpload={handleTableFileUpload}
                      >
                        <Button
                          type="text"
                          size="small"
                          icon={<UploadOutlined />}
                          loading={uploading}
                          style={{
                            color: '#1890ff',
                            fontSize: '12px',
                            padding: '4px 8px',
                            height: '32px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                          }}
                        >
                          上传表格
                        </Button>
                      </Upload>
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => {
                          setEditingStructure(selectedStructure);
                          setShowStructureModal(true);
                        }}
                        style={{
                          color: '#1890ff',
                          fontSize: '12px',
                          padding: '4px 8px',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}
                      >
                        修改结构
                      </Button>
                    </Space>
                  )}
                </div>

                <EditableProTable<TableRowData>
                  rowKey="id"
                  value={tableData}
                  onChange={(value) => setTableData([...value])}
                  columns={[
                    ...selectedStructure.columns.map(col => ({
                      title: col.title,
                      dataIndex: col.dataIndex,
                      key: col.key,
                      tooltip: col.tooltip,
                      width: col.width || 200,
                      valueType: 'textarea' as const,
                      editable: () => isEditing,
                      ellipsis: true,
                      render: (text: any) => (
                        <div style={{
                          maxWidth: 180,
                          wordBreak: 'break-word',
                          whiteSpace: 'pre-wrap'
                        }}>
                          {text}
                        </div>
                      ),
                      renderFormItem: (item: any, { record, onChange, onBlur, value }: any) => {
                        return (
                          <Mentions
                            value={value}
                            rows={3}
                            placeholder={`请输入${col.title}，输入 @ 可插入素材引用`}
                            prefix="@"
                            options={mentionOptions.map(option => ({
                              value: option.value,
                              label: option.label
                            }))}
                            onChange={(val) => {
                              // 过滤掉提示选项，防止插入无效内容
                              const filteredVal = val?.replace(/@no_groups_selected|@no_materials_in_selected_groups/g, '@');
                              onChange?.(filteredVal);
                            }}
                            onBlur={onBlur}
                            onSelect={(option, prefix) => {
                              const selectedOption = mentionOptions.find(opt => opt.value === option.value);
                              if (selectedOption?.value === 'no_groups_selected') {
                                // 对于"请先选择要引入的素材组"选项，不插入任何内容，只显示提示
                                message.info('请先选择要引入的素材组');
                                return;
                              }
                              if (selectedOption?.value === 'no_materials_in_selected_groups') {
                                // 对于"选中的素材组中暂无上线的素材"选项，不插入任何内容，只显示提示
                                message.info('选中的素材组中暂无上线的素材');
                                return;
                              }
                              if (selectedOption?.type === 'image' && selectedOption.image) {
                                message.success(`已插入素材引用：${selectedOption.value}`);
                              }
                            }}
                            filterOption={(input, option) => {
                              const selectedOption = mentionOptions.find(opt => opt.value === option.value);
                              // 只对素材项进行过滤，分组项总是显示
                              if (selectedOption?.type === 'category') {
                                return true;
                              }
                              return selectedOption?.image?.name?.toLowerCase().includes(input.toLowerCase()) || false;
                            }}
                            getPopupContainer={() => document.body}
                            style={{ minHeight: '60px' }}
                          />
                        );
                      },
                    } as any)),
                    // 操作列（仅在编辑模式下显示，固定在右侧）
                    ...(isEditing ? [{
                      title: '操作',
                      valueType: 'option',
                      width: 120,
                      fixed: 'right',
                      render: (text: any, record: any, _: any, action: any) => [
                        <a
                          key="editable"
                          onClick={() => {
                            action?.startEditable?.(record.id);
                          }}
                        >
                          编辑
                        </a>,
                        <a
                          key="delete"
                          onClick={() => {
                            Modal.confirm({
                              title: '确认删除',
                              content: '确定删除这一行数据吗？',
                              okText: '确定',
                              cancelText: '取消',
                              onOk: () => {
                                const newData = tableData.filter(item => item.id !== record.id);
                                setTableData(newData);
                              },
                            });
                          }}
                          style={{ color: '#ff4d4f' }}
                        >
                          删除
                        </a>,
                      ],
                    }] : []),
                  ]}
                  recordCreatorProps={isEditing ? {
                    position: 'bottom',
                    record: () => ({
                      id: Date.now() + Math.random(),
                      ...selectedStructure.columns.reduce((acc, col) => {
                        acc[col.dataIndex] = '';
                        return acc;
                      }, {} as any)
                    }),
                    creatorButtonText: '添加一行数据',
                  } : false}
                  editable={{
                    type: 'multiple',
                    form: editableForm,
                    onSave: async (rowKey, data, row) => {
                      // 更新表格数据
                      const newData = [...tableData];
                      const index = newData.findIndex((item) => item.id === rowKey);
                      if (index > -1) {
                        newData[index] = { ...newData[index], ...data };
                        setTableData(newData);
                      }
                      return true;
                    },
                    onDelete: async (key) => {
                      const newData = tableData.filter(item => item.id !== key);
                      setTableData(newData);
                      message.success('删除成功');
                      return true;
                    },
                    deletePopconfirmMessage: '确定删除这一行吗？',
                    actionRender: (row, config, dom) => [
                      dom.save,
                      dom.cancel,
                    ],
                    onValuesChange: (record, recordList) => {
                      // 表单值变化时的回调
                    },
                  }}
                  pagination={tableData.length > 50 ? {
                    pageSize: 20,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50', '100'],
                    defaultPageSize: 20,
                  } : false}
                  scroll={{
                    x: 'max-content',
                    y: tableData.length > 50 ? 400 : undefined,
                    scrollToFirstRowOnChange: false
                  }}
                  style={{ marginBottom: isEditing ? 12 : 24 }}
                  search={false}
                  options={false}
                  size="small"
                  // 性能优化配置
                  rowSelection={undefined}
                />
              </div>
            )}

            {knowledgeType === 'DOCUMENT' && (
              <div style={{ position: 'relative' }}>
                {/* 文档内容标题 */}
                <div style={{
                  marginTop: 24,
                  marginBottom: 8
                }}>
                  <label style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#262626'
                  }}>
                    <span style={{ color: '#ff4d4f' }}>*</span> 文档内容
                  </label>
                </div>

                {/* 素材组选择器和操作按钮容器 */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-end',
                  marginBottom: 8
                }}>
                  <Form.Item
                    name="materialGroups"
                    label="引用素材组"
                    tooltip="选择要引用的素材组，选中的素材组中的素材可以在文档内容中通过 @ 符号引用"
                    style={{ marginBottom: 0, marginRight: 8 }}
                  >
                    <Select
                      mode="multiple"
                      placeholder="请选择要引用的素材组"
                      value={selectedMaterialGroups}
                      onChange={handleMaterialGroupsChange}
                      style={{ width: '400px', maxWidth: '100%' }}
                      loading={materialLoading}
                      disabled={!isEditing}
                      showSearch
                      filterOption={(input, option) => {
                        if (!option?.children) return false;
                        return option.children.toString().toLowerCase().includes(input.toLowerCase());
                      }}
                      maxTagCount={5}
                      maxTagTextLength={10}
                      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}个素材组`}
                    >
                      {availableMaterialGroups.map(group => {
                        const materialCount = materialLibrary[group]?.length || 0;
                        return (
                          <Option key={group} value={group}>
                            <div style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              width: '100%'
                            }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <FolderOutlined style={{
                                  marginRight: 8,
                                  color: '#1890ff',
                                  fontSize: '14px'
                                }} />
                                <span>{group}</span>
                              </div>
                              <span style={{
                                color: '#999',
                                fontSize: '12px',
                                marginLeft: 8
                              }}>
                                {materialCount}项素材
                              </span>
                            </div>
                          </Option>
                        );
                      })}
                    </Select>
                  </Form.Item>

                  {/* 文档操作按钮 */}
                  {isEditing && (
                    <Upload
                      accept=".txt,.md,.doc,.docx,.pdf"
                      showUploadList={false}
                      beforeUpload={handleFileUpload}
                      disabled={!isEditing}
                    >
                      <Button
                        type="text"
                        icon={<UploadOutlined />}
                        size="small"
                        style={{
                          color: '#1890ff',
                          fontSize: '12px',
                          padding: '4px 8px',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}
                        disabled={!isEditing}
                      >
                        上传文件
                      </Button>
                    </Upload>
                  )}
                </div>

                <Form.Item
                  name="content"
                  rules={[{ required: true, message: '请输入文档内容' }]}
                  style={{ marginBottom: 24 }}
                >
                  <Mentions
                    rows={12}
                    placeholder="请输入文档内容，输入 @ 符号可以插入素材引用，或点击上方按钮上传文档文件（支持TXT、MD、DOC、DOCX、PDF格式）"
                    style={{ width: '100%' }}
                    prefix="@"
                    options={mentionOptions.map(option => ({
                      value: option.value,
                      label: option.label
                    }))}
                    onChange={(val) => {
                      // 过滤掉提示选项，防止插入无效内容
                      const filteredVal = val?.replace(/@no_groups_selected|@no_materials_in_selected_groups/g, '@');
                      form.setFieldsValue({ content: filteredVal });
                    }}
                    onSelect={(option, prefix) => {
                      const selectedOption = mentionOptions.find(opt => opt.value === option.value);
                      if (selectedOption?.value === 'no_groups_selected') {
                        // 对于"请先选择要引入的素材组"选项，不插入任何内容，只显示提示
                        message.info('请先选择要引入的素材组');
                        return;
                      }
                      if (selectedOption?.value === 'no_materials_in_selected_groups') {
                        // 对于"选中的素材组中暂无上线的素材"选项，不插入任何内容，只显示提示
                        message.info('选中的素材组中暂无上线的素材');
                        return;
                      }
                      if (selectedOption?.type === 'image' && selectedOption.image) {
                        message.success(`已插入素材引用：${selectedOption.value}`);
                      }
                    }}
                      filterOption={(input, option) => {
                      const selectedOption = mentionOptions.find(opt => opt.value === option.value);
                      // 只对素材项进行过滤，分组项总是显示
                      if (selectedOption?.type === 'category') {
                        return true;
                      }
                      return selectedOption?.image?.name?.toLowerCase().includes(input.toLowerCase()) || false;
                    }}
                    getPopupContainer={() => document.body}
                  />
                </Form.Item>
              </div>
            )}
          </div>
        </Form>
        </Spin>

        {/* 在Card内部右下角的按钮组 */}
        {isEditing && (
          <div style={{
            position: 'absolute',
            bottom: 24,
            right: 24,
            display: 'flex',
            gap: 12,
            zIndex: 10
          }}>
            <Button
              onClick={handleCancel}
              size="large"
            >
              取消
            </Button>
            <Button
              type="primary"
              loading={uploading}
              onClick={handleSave}
              size="large"
            >
              保存
            </Button>
          </div>
        )}
      </Card>



      {/* 表格结构配置模态框 */}
      <Modal
        title={editingStructure ? "修改表格结构" : "添加表格结构"}
        open={showStructureModal}
        onCancel={() => {
          setShowStructureModal(false);
          setEditingStructure(null);
        }}
        footer={null}
        width={800}
      >
        <StructureModal
          visible={showStructureModal}
          editingStructure={editingStructure}
          onSave={async (data) => {
            try {
              if (editingStructure) {
                // 修改表结构
                const apiParams: API.UpdateSchemaParams = {
                  uid: customData?.uid || '',
                  cid: customData?.cid || '',
                  user_name: customData?.username || '',
                  type: data.type || '用户自定义',
                  name: data.name,
                  desc: data.description || '',
                  config: data.columns.map(col => ({
                    name: col.title,
                    desc: col.tooltip || '',
                    is_entity: col.isEntity || false,
                    entity_name: col.isEntity ? (col.entityName || '') : ''
                  })),
                  schema_id: editingStructure.id
                };

                const response = await updateSchema(apiParams);

                if (response.success) {
                  const updatedStructure: TableStructure = {
                    id: editingStructure.id,
                    ...data
                  };

                  // 更新服务端数据中的结构
                  setServerSchemas(prev => prev.map(schema =>
                    schema.id === editingStructure.id ? updatedStructure : schema
                  ));
                  setUserStructures(prev => prev.map(schema =>
                    schema.id === editingStructure.id ? updatedStructure : schema
                  ));

                  // 如果当前选中的是被修改的结构，更新选中状态
                  if (selectedStructure?.id === editingStructure.id) {
                    setSelectedStructure(updatedStructure);
                  }

                  setShowStructureModal(false);
                  setEditingStructure(null);
                  message.success('表结构修改成功');
                } else {
                  throw new Error(response.error_msg || '修改失败');
                }
              } else {
                // 创建表结构
                const apiParams: API.CreateSchemaParams = {
                  uid: customData?.uid || '',
                  cid: customData?.cid || '',
                  user_name: customData?.username || '',
                  type: data.type || '用户自定义',
                  name: data.name,
                  desc: data.description || '',
                  config: data.columns.map(col => ({
                    name: col.title,
                    desc: col.tooltip || '',
                    is_entity: col.isEntity || false,
                    entity_name: col.isEntity ? (col.entityName || '') : ''
                  }))
                };

                const response = await createSchema(apiParams);

                if (response.success) {
                  const newStructure: TableStructure = {
                    id: response.data, // 使用API返回的schema_id
                    ...data
                  };

                  // 将新结构添加到服务端数据中
                  setServerSchemas([...serverSchemas, newStructure]);
                  setShowStructureModal(false);
                  setEditingStructure(null);
                  form.setFieldsValue({ structure: newStructure.id });

                  // 直接设置选中的结构，避免异步状态更新导致的时序问题
                  setSelectedStructure(newStructure);
                  setTableData([]);

                  // 延迟滚动到表格区域，等待DOM更新
                  setTimeout(() => {
                    const tableElement = document.querySelector('.table-content');
                    if (tableElement) {
                      tableElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                      });
                    }
                  }, 100);

                  message.success('表结构创建成功');
                } else {
                  throw new Error(response.error_msg || '创建失败');
                }
              }
            } catch (error: any) {
              console.error(editingStructure ? '修改表结构失败:' : '创建表结构失败:', error);
              message.error(error.message || (editingStructure ? '修改表结构失败，请重试' : '创建表结构失败，请重试'));
            }
          }}
          onCancel={() => {
            setShowStructureModal(false);
            setEditingStructure(null);
          }}
        />
      </Modal>
    </PageContainer>
  );
};

// 表格结构配置组件
const StructureModal: React.FC<StructureModalProps> = ({ visible, editingStructure, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [columns, setColumns] = useState<Omit<TableColumn, 'id'>[]>([]);
  const [saving, setSaving] = useState<boolean>(false);

  useEffect(() => {
    if (visible) {
      if (editingStructure) {
        // 转换列数据格式以适应编辑
        const formattedColumns = editingStructure.columns.map(col => ({
          title: col.title,
          dataIndex: col.dataIndex,
          key: col.key,
          tooltip: col.tooltip || '',
          isEntity: col.isEntity || false,
          entityName: col.entityName || ''
        }));

        setColumns(formattedColumns);

        // 使用setTimeout确保表单已经渲染完成
        setTimeout(() => {
          const formValues = {
            type: editingStructure.type || '用户自定义',
            name: editingStructure.name,
            description: editingStructure.description || ''
          };

          form.setFieldsValue(formValues);
        }, 100);

      } else {
        form.resetFields();
        setColumns([]);
      }
    }
  }, [visible, editingStructure, form]);

  const handleAddColumn = () => {
    setColumns([...columns, {
      title: '',
      dataIndex: '',
      key: '',
      tooltip: '',
      isEntity: false,
      entityName: ''
    }]);
  };

  const handleColumnChange = (index: number, field: string, value: string | boolean) => {
    const newColumns = [...columns];
    newColumns[index] = { ...newColumns[index], [field]: value };
    // 自动生成dataIndex和key，直接使用title值（中文）
    if (field === 'title') {
      newColumns[index].dataIndex = value as string;
      newColumns[index].key = value as string;
    }
    setColumns(newColumns);
  };

  const handleRemoveColumn = (index: number) => {
    const newColumns = columns.filter((_, i) => i !== index);
    setColumns(newColumns);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      if (columns.length === 0) {
        message.error('请至少添加一列');
        return;
      }

      // 验证列信息
      for (let i = 0; i < columns.length; i++) {
        if (!columns[i].title.trim()) {
          message.error(`第${i + 1}列的标题不能为空`);
          return;
        }
        // 验证主体列配置
        if (columns[i].isEntity && !columns[i].entityName?.trim()) {
          message.error(`第${i + 1}列是主体列，主体名不能为空`);
          return;
        }
      }

      await onSave({
        type: values.type,
        name: values.name,
        description: values.description || '',
        columns: columns.map((col, index) => ({
          ...col,
          id: index + 1
        }))
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ padding: '20px 0' }}>
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="结构名称"
          rules={[{ required: true, message: '请输入结构名称' }]}
        >
          <Input placeholder="请输入结构名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="结构描述"
        >
          <TextArea rows={2} placeholder="请输入结构描述（可选）" />
        </Form.Item>

        <Form.Item
          name="type"
          label="表结构类型"
          rules={[{ required: true, message: '请输入表结构类型' }]}
        >
          <Input placeholder="请输入表结构类型（如：用户信息、产品数据等）" />
        </Form.Item>
      </Form>

      <Divider>列配置</Divider>

      {columns.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '40px 20px',
          color: '#999',
          border: '1px dashed #d9d9d9',
          borderRadius: 6,
          marginBottom: 16
        }}>
          <div style={{ marginBottom: 16 }}>暂无列配置，请添加列</div>
        </div>
      )}

      {columns.map((column, index) => (
        <div key={index} style={{
          border: '1px solid #d9d9d9',
          borderRadius: 6,
          padding: 16,
          marginBottom: 12,
          position: 'relative'
        }}>
          <Button
            type="text"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveColumn(index)}
            style={{ position: 'absolute', top: 8, right: 8 }}
          />

          <div style={{ marginBottom: 12 }}>
            <div>
              <label style={{ display: 'block', marginBottom: 4, fontSize: 12, color: '#666' }}>
                <span style={{ color: '#ff4d4f' }}>*</span> 列标题
              </label>
              <Input
                value={column.title}
                onChange={(e) => handleColumnChange(index, 'title', e.target.value)}
                placeholder="请输入列标题（字段名将自动与此同步）"
              />
            </div>
          </div>

          <div style={{ marginBottom: 12 }}>
            <label style={{ display: 'block', marginBottom: 4, fontSize: 12, color: '#666' }}>
              列说明
            </label>
            <Input
              value={column.tooltip}
              onChange={(e) => handleColumnChange(index, 'tooltip', e.target.value)}
              placeholder="请输入列说明（可选）"
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
            <div>
              <label style={{ display: 'block', marginBottom: 4, fontSize: 12, color: '#666' }}>
                是否主体列
              </label>
              <Select
                value={column.isEntity}
                onChange={(value) => handleColumnChange(index, 'isEntity', value)}
                placeholder="请选择"
                style={{ width: '100%' }}
              >
                <Option value={false}>否</Option>
                <Option value={true}>是</Option>
              </Select>
            </div>
            {column.isEntity && (
              <div>
                <label style={{ display: 'block', marginBottom: 4, fontSize: 12, color: '#666' }}>
                  <span style={{ color: '#ff4d4f' }}>*</span> 主体名
                </label>
                <Input
                  value={column.entityName}
                  onChange={(e) => handleColumnChange(index, 'entityName', e.target.value)}
                  placeholder="请输入主体名"
                />
              </div>
            )}
          </div>
        </div>
      ))}

      <div style={{ marginBottom: 24 }}>
        <Button type="dashed" onClick={handleAddColumn} icon={<PlusOutlined />} block>
          添加列
        </Button>
      </div>

      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button onClick={onCancel} disabled={saving}>取消</Button>
          <Button type="primary" onClick={handleSave} loading={saving}>
            保存
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default KnowledgeDetail;
