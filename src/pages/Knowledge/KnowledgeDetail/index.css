/* 知识详情页面样式 */
  
  /* 表单区域 */
  .form-section {
    margin-bottom: 32px;
  }
  
  .form-section:last-child {
    margin-bottom: 0;
  }
  
  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }
  
  /* 表单行布局 */
  .form-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
  }
  
  .form-item-compact {
    margin-bottom: 0;
  }
  
  /* 表单标签样式 */
  .structure-columns .ant-form-item-label > label {
    font-weight: normal;
    color: #262626;
    font-size: 14px;
  }
  
  /* 所有表单标签都不加粗 */
  .ant-form-item-label > label {
    font-weight: normal;
  }
  
  /* 知识详情中的标签样式 */
  .ant-select-multiple .ant-select-selection-item {
    background: #fafafa;
    border: 1px solid #d9d9d9;
    color: #595959;
    font-weight: normal;
    font-size: 12px;
    border-radius: 4px;
    padding: 0 7px;
    height: 22px;
    line-height: 20px;
  }
  
  .ant-select-multiple .ant-select-selection-item-remove {
    color: #8c8c8c;
    font-size: 10px;
  }
  
  .ant-select-multiple .ant-select-selection-item-remove:hover {
    color: #ff4d4f;
  }
  
  /* 标签输入框样式 */
  .ant-select-multiple .ant-select-selection-search-input {
    color: #262626;
    font-weight: normal;
  }
  
  /* 标签下拉选项样式 - 与其他下拉框保持一致 */
  .ant-select-dropdown .ant-select-item-option {
    color: #262626;
    font-weight: normal;
  }
  
  .ant-select-dropdown .ant-select-item-option-selected {
    background-color: #e6f7ff;
    color: #262626;
    font-weight: normal;
  }
  
  .ant-select-dropdown .ant-select-item-option-active {
    background-color: #f5f5f5;
  }
  
  /* 表格结构选择样式 */
  .structure-option {
    padding: 4px 0;
  }
  
  .structure-name {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 1.4;
  }
  
  .structure-description {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.3;
    margin-top: 2px;
  }

  /* 表格结构选项删除按钮样式 */
  .structure-option .ant-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .structure-option:hover .ant-btn {
    opacity: 1;
  }

  .structure-option .ant-btn:hover {
    background-color: #fff2f0;
    border-color: #ffccc7;
  }

  .structure-option .ant-btn:focus {
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
  
  /* 表格内容区域 */
  .table-content {
    margin-top: 16px;
  }
  
  .table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }
  
  .table-info-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .table-description {
    color: #8c8c8c;
    font-size: 13px;
  }
  
  /* 表格信息栏图标按钮样式 */
  .table-info .ant-btn[type="text"] {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    transition: all 0.2s;
  }
  
  .table-info .ant-btn[type="text"]:hover {
    background-color: #f5f5f5;
    color: #1890ff !important;
  }
  
  .table-info .ant-btn[type="text"]:active {
    background-color: #e6f7ff;
  }
  
  /* 图标按钮的图标大小 */
  .table-info .ant-btn[type="text"] .anticon {
    font-size: 14px;
  }
  
  /* 列设置弹窗样式 */
  .structure-columns {
    margin-top: 16px;
  }
  
  .column-form-item {
    margin-bottom: 8px;
  }
  
  .column-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  /* 文档内容头部样式 */
  .content-header {
    display: flex;
    align-items: center;
  }
  
  /* 上传按钮样式 */
  .content-header .ant-btn {
    border-color: #1890ff;
    color: #1890ff;
  }
  
  .content-header .ant-btn:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }
  
  /* 上传提示样式 */
  .upload-tips {
    margin-top: 8px;
    color: #8c8c8c;
    font-size: 12px;
  }
  
  /* Mentions 图片选择样式 */
  .mention-option {
    display: flex;
    align-items: center;
    padding: 8px 4px;
    gap: 12px;
  }
  
  .mention-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }
  
  .mention-info {
    flex: 1;
    min-width: 0;
  }
  
  .mention-category {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.2;
  }
  
  .mention-name {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 1.2;
    margin-top: 2px;
  }
  
  /* Mentions 下拉框样式优化 */
  .ant-mentions-dropdown {
    max-height: 300px;
  }
  
  .ant-mentions-dropdown-menu-item {
    padding: 4px 12px;
  }
  
  .ant-mentions-dropdown-menu-item:hover {
    background-color: #f5f5f5;
  }
  
  .ant-mentions-dropdown-menu-item-selected {
    background-color: #e6f7ff;
  }
  
  /* 图片分组标题样式 */
  .mention-category-header {
    padding: 8px 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    font-weight: 600;
    color: #595959;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  /* 分组选项样式 */
  .mention-category-option {
    display: flex;
    align-items: center;
    padding: 8px 4px;
    gap: 8px;
  }

  .category-count {
    font-size: 12px;
    color: #8c8c8c;
    margin-left: auto;
  }

  /* 图片选项样式 */
  .mention-image-option {
    display: flex;
    align-items: center;
    padding: 8px 4px;
    gap: 12px;
  }

  .mention-image-option .mention-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }

  .mention-image-option .mention-info {
    flex: 1;
    min-width: 0;
  }

  .mention-image-option .mention-name {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 1.2;
  }

  /* 返回按钮样式 */
  .mention-back-option {
    display: flex;
    align-items: center;
    padding: 8px 4px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 4px;
  }

  .mention-back-option span {
    font-size: 14px;
    font-weight: 500;
  }

  /* 素材选择器弹窗样式 */
  .image-selector-dropdown {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8e8e8;
    overflow: hidden;
  }

  .selector-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    font-size: 14px;
    color: #262626;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .selector-header-with-back {
    justify-content: space-between;
  }

  .back-btn {
    color: #1890ff;
    cursor: pointer;
    font-weight: 400;
    padding: 2px 0;
    font-size: 13px;
  }

  .back-btn:hover {
    color: #40a9ff;
  }

  .selector-option {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.15s;
  }

  .selector-option:hover {
    background-color: #f5f5f5;
  }

  .selector-option:last-child {
    border-bottom: none;
  }

  .category-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .category-count {
    font-size: 12px;
    color: #8c8c8c;
    margin-left: auto;
  }

  .material-list {
    max-height: 250px;
    overflow-y: auto;
  }

  .material-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
  }

  .material-preview {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .material-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-preview {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .material-info {
    flex: 1;
    min-width: 0;
  }

  .material-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .material-name {
    font-size: 14px;
    font-weight: 400;
    color: #262626;
    line-height: 1.4;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .material-type-tag {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 500;
    flex-shrink: 0;
    margin-left: 8px;
  }

  .image-tag {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  .video-tag {
    background-color: #fff2e8;
    color: #fa8c16;
  }

  /* Mentions下拉选项hover效果优化 */
  .ant-mentions-dropdown-menu-item:hover .mention-category-option,
  .ant-mentions-dropdown-menu-item:hover .mention-image-option,
  .ant-mentions-dropdown-menu-item:hover .mention-back-option {
    background-color: transparent;
  }
  
  