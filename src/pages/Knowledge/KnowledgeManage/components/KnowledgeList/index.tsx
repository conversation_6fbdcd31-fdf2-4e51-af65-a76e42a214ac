import React, { useState } from 'react';
import { <PERSON>, Tag, Button, Space, Avatar, Toolt<PERSON>, Popconfirm, Result, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  SendOutlined,
  TableOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  StopOutlined,
  PlusOutlined,
  FileAddOutlined
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { batchDeleteKnowledge, batchOnlineKnowledge } from '@/services/ant-design-pro/api';
import dayjs from 'dayjs';
import './index.css';

interface ValidPeriod {
  startTime?: string;
  endTime?: string;
  permanent?: boolean;
}

interface KnowledgeItem {
  id: number;
  knowledge_id: string;
  name: string;
  summary: string;
  creator: string;
  createTime: string;
  updateTime: string;
  validPeriod: ValidPeriod;
  status: 'ONLINE' | 'DRAFT';
  type: 'TABLE' | 'DOCUMENT';
  tags: string[];
  version: number;
}

interface KnowledgeListProps {
  data: KnowledgeItem[];
  selectedRowKeys: React.Key[];
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onBatchDelete: () => void;
  onBatchPublish: () => void;
  onNavigateToDetail?: (action: string, id?: string) => void;
  groupId?: string;
  loading?: boolean;
  onRefresh?: () => void;
  publishLoading?: boolean;
  onPublishLoadingChange?: (loading: boolean) => void;
  showUploadNotification?: () => void;
}

const KnowledgeList: React.FC<KnowledgeListProps> = ({ 
  data, 
  selectedRowKeys, 
  onSelectChange, 
  onBatchDelete, 
  onBatchPublish, 
  onNavigateToDetail, 
  groupId,
  loading = false,
  onRefresh,
  publishLoading,
  onPublishLoadingChange,
  showUploadNotification
}) => {
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};

  // 维护每一行单独的loading状态
  const [publishingIds, setPublishingIds] = useState<Set<number>>(new Set());

  // 标签颜色数组
  const tagColors = [
    'blue', 'green', 'orange', 'red', 'purple',
    'cyan', 'magenta', 'volcano', 'geekblue', 'gold'
  ];

  // 根据标签位置顺序分配颜色
  const getTagColor = (tag: string, index: number): string => {
    return tagColors[index % tagColors.length];
  };

  // 统一时间显示格式
  const formatTime = (timeString: string): string => {
    return dayjs(timeString).format('YY-MM-DD HH:mm');
  };

  const handleView = (record: KnowledgeItem) => {
    if (onNavigateToDetail) {
      onNavigateToDetail('view', record.knowledge_id);
    }
  };

  const handleEdit = (record: KnowledgeItem) => {
    if (onNavigateToDetail) {
      onNavigateToDetail('edit', record.knowledge_id);
    }
  };

  const handleDelete = async (record: KnowledgeItem) => {
    if (!customData?.uid || !customData?.cid || !customData?.username) {
      message.error('缺少必要的用户信息', 2);
      return;
    }

    try {
      const response = await batchDeleteKnowledge({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        del_ids: [record.id]
      });

      if (response.success) {
        message.success('删除成功', 2);
        // 调用刷新回调
        if (onRefresh) {
          onRefresh();
        }
      } else {
        message.error(response.error_msg || '删除失败', 2);
      }
    } catch (error) {
      console.error('删除知识失败:', error);
      message.error('删除失败', 2);
    }
  };

  const handlePublish = async (record: KnowledgeItem) => {
    if (!customData?.uid || !customData?.cid || !customData?.username) {
      message.error('缺少必要的用户信息', 2);
      return;
    }

    if (record.status !== 'DRAFT') {
      message.warning('只能发布草稿状态的知识', 2);
      return;
    }

    try {
      // 设置当前行的loading状态
      setPublishingIds(prev => new Set([...prev, record.id]));
      
      // 显示上线提示
      if (showUploadNotification) {
        showUploadNotification();
      }

      const response = await batchOnlineKnowledge({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        ids: [record.id]
      });

      if (response.success) {
        message.success('知识发布成功', 2);
        // 调用刷新回调
        if (onRefresh) {
          onRefresh();
        }
      } else {
        message.error(response.error_msg || '发布失败', 2);
      }
    } catch (error) {
      console.error('发布知识失败:', error);
      message.error('发布失败，请稍后重试', 2);
    } finally {
      // 移除当前行的loading状态
      setPublishingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(record.id);
        return newSet;
      });
    }
  };

  const getStatusInfo = (status: 'ONLINE' | 'DRAFT') => {
    return {
      text: status === 'ONLINE' ? '已发布' : '草稿',
      dotClass: status === 'ONLINE' ? 'status-dot-published' : 'status-dot-draft'
    };
  };

  const getTypeIcon = (type: 'TABLE' | 'DOCUMENT') => {
    return type === 'TABLE'
      ? <TableOutlined style={{ color: '#52c41a' }} />
      : <FileTextOutlined style={{ color: '#1890ff' }} />;
  };

  const getTypeText = (type: 'TABLE' | 'DOCUMENT'): string => {
    return type === 'TABLE' ? '表格' : '文档';
  };

  // 获取有效期状态和显示内容
  const getValidPeriodInfo = (validPeriod: ValidPeriod) => {
    if (validPeriod.permanent) {
      return {
        status: 'permanent',
        tag: '永久生效',
        tagColor: 'green',
        period: '永久有效'
      };
    }

    const now = dayjs();
    const startTime = dayjs(validPeriod.startTime);
    const endTime = dayjs(validPeriod.endTime);
    const formatDate = (dateString: string) => dayjs(dateString).format('YY-MM-DD');
    const period = `${formatDate(validPeriod.startTime!)} ~ ${formatDate(validPeriod.endTime!)}`;

    if (now.isBefore(startTime)) {
      // 未生效
      return {
        status: 'pending',
        tag: '未生效',
        tagColor: 'orange',
        period: period
      };
    } else if (now.isAfter(endTime)) {
      // 已过期
      return {
        status: 'expired',
        tag: '已过期',
        tagColor: 'red',
        period: period
      };
    } else {
      // 生效中
      return {
        status: 'active',
        tag: '生效中',
        tagColor: 'green',
        period: period
      };
    }
  };



  const columns: ColumnsType<KnowledgeItem> = [
    {
      title: '知识名称',
      dataIndex: 'name',
      key: 'name',
      width: 240,
      render: (text: string, record: KnowledgeItem) => (
        <div className="knowledge-name">
          <Tooltip title={text} placement="topLeft">
            <div className="name-text">{text}</div>
          </Tooltip>
          <div className="knowledge-tags">
            {/* 版本号标签 - 始终显示在第一位 */}
            <Tag color={getTagColor(`v${record.version}`, 0)}>
              v{record.version}
            </Tag>
            {/* 其他标签 */}
            {record.tags && record.tags.length > 0 && (
              <>
                {record.tags.slice(0, 1).map((tag, index) => (
                  <Tag key={tag} color={getTagColor(tag, index + 1)}>
                    {tag}
                  </Tag>
                ))}
                {record.tags.length > 1 && (
                  <Tooltip
                    title={
                      <div>
                        {record.tags.slice(1).map((tag, index) => (
                          <Tag key={tag} color={getTagColor(tag, index + 2)} style={{ margin: '2px' }}>
                            {tag}
                          </Tag>
                        ))}
                      </div>
                    }
                    placement="topLeft"
                  >
                    <Tag color="default" className="more-tags">
                      +{record.tags.length - 1}
                    </Tag>
                  </Tooltip>
                )}
              </>
            )}
          </div>
        </div>
      )
    },
    {
      title: '创建信息',
      key: 'createInfo',
      width: 180,
      render: (_: any, record: KnowledgeItem) => (
        <div className="create-info">
          <div className="creator-section">
            <Avatar
              size={24}
              style={{
                backgroundColor: '#1890ff',
                fontSize: '12px',
                marginRight: 8
              }}
            >
              {record.creator.charAt(0)}
            </Avatar>
            <span className="creator-name">{record.creator}</span>
          </div>
          <div className="time-section">
            <div className="time-item">
              <ClockCircleOutlined className="time-icon" />
              <span className="time-text">{formatTime(record.createTime)}</span>
            </div>
            <div className="time-item update-time">
              <SyncOutlined className="time-icon" />
              <span className="time-text">{formatTime(record.updateTime)}</span>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '知识摘要',
      dataIndex: 'summary',
      key: 'summary',
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <div className="summary-text">{text}</div>
        </Tooltip>
      )
    },
    {
      title: '知识类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: 'TABLE' | 'DOCUMENT') => (
        <div className="type-info">
          {getTypeIcon(type)}
          <span style={{ marginLeft: 6 }}>
            {getTypeText(type)}
          </span>
        </div>
      )
    },
    {
      title: '有效期',
      dataIndex: 'validPeriod',
      key: 'validPeriod',
      width: 180,
      render: (validPeriod: ValidPeriod) => {
        const periodInfo = getValidPeriodInfo(validPeriod);
        return (
          <div className="valid-period-wrapper">
            <Tag color={periodInfo.tagColor} className="period-status-tag">
              {periodInfo.tag}
            </Tag>
            <div className="period-text">
              {periodInfo.period}
            </div>
          </div>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: 'ONLINE' | 'DRAFT') => {
        const statusInfo = getStatusInfo(status);
        return (
          <div className="status-wrapper">
            <span className={`status-dot ${statusInfo.dotClass}`}></span>
            <span className="status-text">{statusInfo.text}</span>
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right',
      render: (_: any, record: KnowledgeItem) => (
        <Space size="small">
          <Tooltip title="查看" placement="top">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑" placement="top">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          {record.status === 'DRAFT' && (
            <Tooltip title="发布" placement="top">
              <Button
                type="text"
                size="small"
                icon={<SendOutlined />}
                loading={publishingIds.has(record.id)}
                onClick={() => handlePublish(record)}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确认删除"
            description={`确定要删除知识"${record.name}"吗？此操作不可恢复。`}
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
            placement="left"
          >
            <Tooltip title="删除" placement="top">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: KnowledgeItem) => ({
      name: record.name,
    }),
  };

  const hasSelected = selectedRowKeys.length > 0;
  const hasDraftSelected = selectedRowKeys.some(id => {
    const record = data.find(item => item.id === Number(id));
    return record && record.status === 'DRAFT';
  });

  console.log('🟡 KnowledgeList render - selectedRowKeys:', selectedRowKeys);
  console.log('🟡 KnowledgeList render - hasSelected:', hasSelected);
  console.log('🟡 KnowledgeList render - data:', data?.slice(0, 2));

  // 如果正在加载中，不显示空状态
  if (loading) {
    return null; // 让父组件的 Spin 组件处理加载状态
  }

  // 如果没有数据且不在加载中，显示空状态
  // 增加一个条件，确保 data 已经被定义（不是 undefined）
  if (data !== undefined && (!data || data.length === 0)) {
    return (
      <div className="knowledge-list">
        <div 
          className="empty-state-container"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '500px',
            backgroundColor: 'transparent', // 改为透明背景，避免白色闪现
            borderRadius: '8px'
          }}
        >
          <Result
            status="404"
            title="暂无知识"
            subTitle="该知识组暂时没有内容，点击「添加知识」创建您的第一个知识吧~"
            extra={
              <Button 
                type="primary" 
                icon={<FileAddOutlined />}
                size="large"
                onClick={() => {
                  if (onNavigateToDetail) {
                    onNavigateToDetail('create', groupId);
                  }
                }}
              >
                添加知识
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div className="knowledge-list">
      {hasSelected && (
        <div className="batch-operations">
          <Space>
            <span className="selected-info">
              已选择 {selectedRowKeys.length} 项
            </span>
            {hasDraftSelected && (
              <Popconfirm
                title="批量发布"
                description="确定要将选中的草稿状态知识批量发布吗？发布过程可能需要一些时间。"
                onConfirm={onBatchPublish}
                okText="确定"
                cancelText="取消"
              >
                <Button 
                  type="primary" 
                  icon={<SendOutlined />}
                  loading={publishLoading}
                >
                  批量发布
                </Button>
              </Popconfirm>
            )}
            <Popconfirm
              title="批量删除"
              description="确定要删除选中的知识吗？此操作不可恢复。"
              onConfirm={onBatchDelete}
              okText="确定"
              cancelText="取消"
            >
              <Button danger icon={<DeleteOutlined />}>
                批量删除
              </Button>
            </Popconfirm>
            <Button onClick={() => onSelectChange([])}>
              取消选择
            </Button>
          </Space>
        </div>
      )}
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={{
          total: data.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
        }}
        scroll={{ x: 1200 }}
        size="middle"
      />
    </div>
  );
};

export default KnowledgeList; 