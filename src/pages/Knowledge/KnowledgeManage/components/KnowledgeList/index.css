.knowledge-list {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .batch-operations {
    padding: 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }
  
  .selected-info {
    color: #1890ff;
    font-weight: 500;
  }
  
  .knowledge-name {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-left: 20px;
  }
  
  .name-text {
    font-weight: 500;
    color: #262626;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  
  .knowledge-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
  }
  
  .knowledge-tags .ant-tag {
    margin: 0;
    font-size: 11px;
    line-height: 18px;
    padding: 0 6px;
    border-radius: 2px;
  }
  
  .more-tags {
    cursor: pointer;
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #8c8c8c !important;
  }
  
  .more-tags:hover {
    background: #e6f7ff !important;
    border-color: #91d5ff !important;
    color: #1890ff !important;
  }
  
  /* Tooltip 内的标签样式 */
  .ant-tooltip-inner .ant-tag {
    margin: 2px;
  }
  
  .create-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px 0;
  }
  
  .creator-section {
    display: flex;
    align-items: center;
  }
  
  .creator-name {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
  }
  
  .time-section {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-left: 4px;
  }
  
  .time-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
  }
  
  .time-icon {
    font-size: 12px;
    color: #8c8c8c;
    width: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .time-text {
    color: #595959;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }
  
  .update-time .time-text {
    color: #8c8c8c;
  }
  
  /* 悬停效果 */
  .create-info:hover .creator-name {
    color: #1890ff;
  }
  
  .create-info:hover .time-text {
    color: #262626;
  }
  
  .summary-text {
    color: #595959;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    word-break: break-word;
  }
  
  .valid-period-wrapper {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .period-status-tag {
    margin: 0;
    font-size: 11px;
    line-height: 18px;
    padding: 0 6px;
    border-radius: 2px;
    align-self: flex-start;
  }
  
  .period-text {
    color: #595959;
    font-size: 12px;
    line-height: 1.4;
    margin-left: 4px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }
  
  .status-wrapper {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .status-dot-published {
    background-color: #52c41a;
    animation: pulse 2s infinite;
  }
  
  .status-dot-draft {
    background-color: #d9d9d9;
  }
  
  .status-text {
    font-size: 13px;
    color: #262626;
  }
  
  @keyframes pulse {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .type-info {
    display: flex;
    align-items: center;
  }
  
  /* 表格样式优化 */
  .knowledge-list .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
    color: #262626;
  }

  /* 调整表头第一列的间距 */
  .knowledge-list .ant-table-thead > tr > th:nth-child(2) {
    padding-left: 36px !important;
  }
  
  .knowledge-list .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }

  /* 统一悬浮时所有列的背景色，包括复选框列 */
  .knowledge-list .ant-table-tbody > tr:hover > td {
    background-color: #e6f7ff !important;
  }

  .knowledge-list .ant-table-tbody > tr:hover > .ant-table-selection-column {
    background-color: #e6f7ff !important;
  }

  .knowledge-list .ant-table-selection-column .ant-checkbox-wrapper {
    z-index: 1;
    position: relative;
    background: transparent !important;
  }

  /* 确保复选框内部样式不受影响 */
  .knowledge-list .ant-table-tbody > tr:hover > .ant-table-selection-column .ant-checkbox-inner {
    background-color: #ffffff !important;
  }

  /* 确保选中状态的复选框在悬浮时也保持正确的颜色 */
  .knowledge-list .ant-table-tbody > tr:hover > .ant-table-selection-column .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
  }
  
  .knowledge-list .ant-table-tbody > tr > td {
    padding: 12px 16px;
  }

  /* 复选框列边距调整 */
  .knowledge-list .ant-table-selection-column {
    padding-left: 20px !important;
    padding-right: 16px !important;
  }

  .knowledge-list .ant-table-thead .ant-table-selection-column {
    padding-left: 20px !important;
    padding-right: 16px !important;
  }
  
  /* 响应式处理 */
  @media (max-width: 1200px) {
    .knowledge-list .ant-table-container {
      overflow-x: auto;
    }
  }

  @media (max-width: 768px) {
    .knowledge-list .ant-table-selection-column {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }

    .knowledge-list .ant-table-thead .ant-table-selection-column {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }

    .knowledge-name {
      margin-left: 12px !important;
    }

    .knowledge-list .ant-table-thead > tr > th:nth-child(2) {
      padding-left: 28px !important;
    }
  }
  
  /* 操作按钮样式 */
  .knowledge-list .ant-btn-text:hover {
    background: rgba(24, 144, 255, 0.1);
  }
  
  .knowledge-list .ant-dropdown-trigger:hover {
    background: rgba(0, 0, 0, 0.04);
  }

  /* 分页器右边距调整 */
  .knowledge-list .ant-table-pagination {
    margin-right: 16px !important;
  }
  
  /* 空状态样式优化 */
  .knowledge-list .ant-result {
    padding: 60px 40px !important;
  }

  .knowledge-list .ant-result-title {
    color: #262626 !important;
    font-size: 20px !important;
    font-weight: 500 !important;
    margin-bottom: 16px !important;
  }

  .knowledge-list .ant-result-subtitle {
    color: #8c8c8c !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-bottom: 32px !important;
  }

  .knowledge-list .ant-result-extra {
    margin-top: 24px !important;
  }

  .knowledge-list .ant-result-extra .ant-btn {
    height: 40px !important;
    padding: 0 24px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
    transition: all 0.3s ease !important;
  }

  .knowledge-list .ant-result-extra .ant-btn:hover {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
    transform: translateY(-1px) !important;
  }

  /* 空状态容器动画 */
  .knowledge-list .empty-state-container {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  