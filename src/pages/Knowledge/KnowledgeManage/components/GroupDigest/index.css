.knowledge-group-summary,
  .material-group-summary {
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  
  .summary-content {
    padding: 12px 20px;
  }
  
  .group-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
  }
  
  .group-icon {
    margin-right: 12px;
    margin-top: 4px;
  }
  
  .group-info {
    flex: 1;
  }
  
  .group-name {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
  }
  
  .group-description {
    margin: 0;
    color: #595959;
    font-size: 14px;
    line-height: 1.6;
  }
  
  .group-meta {
    margin-left: 36px;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
  
  .meta-icon {
    color: #8c8c8c;
    margin-right: 8px;
  }
  
  .meta-label {
    color: #8c8c8c;
    margin-right: 4px;
  }
  
  .meta-value {
    color: #262626;
    display: flex;
    align-items: center;
  }
  

  
  @media (max-width: 768px) {
    .group-header {
      flex-direction: column;
    }
  
    .group-icon {
      margin-bottom: 12px;
    }
  
    .group-meta {
      margin-left: 0;
    }
  
    .meta-item {
      flex-wrap: wrap;
    }
  }
  