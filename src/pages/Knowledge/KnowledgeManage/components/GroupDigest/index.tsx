import React from 'react';
import { Card, Tag, Space, Avatar, Badge } from 'antd';
import {
  FolderOutlined,
  CalendarOutlined,
  UserOutlined,
  FileTextOutlined,
  TagsOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.css';

interface GroupDigestData {
  id?: string;
  name: string;
  description: string;
  createDate: string;
  creator: string;
  tags: string[];
  itemCount: number;
  itemCountLabel?: string; // 如 "个文档"、"个素材"
}

interface GroupDigestProps {
  data: GroupDigestData;
  className?: string;
}

const GroupDigest: React.FC<GroupDigestProps> = ({
  data,
  className = 'Knowledge-group-summary'
}) => {
  // 标签颜色数组
  const tagColors = [
    'blue', 'green', 'orange', 'red', 'purple',
    'cyan', 'magenta', 'volcano', 'geekblue', 'gold'
  ];

  // 根据标签位置顺序分配颜色
  const getTagColor = (tag: string, index: number) => {
    return tagColors[index % tagColors.length];
  };

  return (
    <Card className={className}>
      <div className="summary-content">
        <div className="group-header">
          <div className="group-icon">
            <FolderOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          </div>
          <div className="group-info">
            <h2 className="group-name">{data.name}</h2>
            <p className="group-description">{data.description}</p>
          </div>
        </div>

        <div className="group-meta">
          <Space size="large" wrap>
            <div className="meta-item">
              <FileTextOutlined className="meta-icon" />
              <span className="meta-label">数量：</span>
              <Badge
                count={data.itemCount}
                showZero
                style={{
                  backgroundColor: '#1890ff',
                  fontSize: '12px',
                  height: '20px',
                  minWidth: '20px',
                  lineHeight: '20px',
                  borderRadius: '10px'
                }}
              />
              <span className="meta-value" style={{ marginLeft: '8px' }}>
                {data.itemCountLabel || '个项目'}
              </span>
            </div>

            <div className="meta-item">
              <CalendarOutlined className="meta-icon" />
              <span className="meta-label">创建时间：</span>
              <span className="meta-value">
                {dayjs(data.createDate).format('YYYY-MM-DD HH:mm:ss')}
              </span>
            </div>

            <div className="meta-item">
              <UserOutlined className="meta-icon" />
              <span className="meta-label">创建人：</span>
              <span className="meta-value">
                <Avatar
                  size={24}
                  style={{
                    backgroundColor: '#1890ff',
                    fontSize: '12px',
                    marginRight: 8
                  }}
                >
                  {data.creator.charAt(0)}
                </Avatar>
                {data.creator}
              </span>
            </div>

            {data.tags && data.tags.length > 0 && (
              <div className="meta-item">
                <TagsOutlined className="meta-icon" />
                <span className="meta-label">标签：</span>
                <span className="meta-value">
                  <Space size={4}>
                    {data.tags.map((tag, index) => (
                      <Tag key={tag} color={getTagColor(tag, index)}>{tag}</Tag>
                    ))}
                  </Space>
                </span>
              </div>
            )}
          </Space>
        </div>
      </div>
    </Card>
  );
};

export default GroupDigest;
