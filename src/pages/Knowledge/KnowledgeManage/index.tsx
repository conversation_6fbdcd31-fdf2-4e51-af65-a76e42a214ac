import React, { useState, useEffect } from 'react';
import { Card, Button, Space, message, Spin, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useParams, history, useModel, useRequest, useLocation, Helmet } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-components';
import GroupDigest from './components/GroupDigest/index';
import KnowledgeList from './components/KnowledgeList/index';
import { getGroupKnowledge, batchDeleteKnowledge, batchOnlineKnowledge } from '@/services/ant-design-pro/api';
import './index.css';

interface KnowledgeGroupData {
  id: string;
  name: string;
  description: string;
  createDate: string;
  creator: string;
  tags: string[];
  knowledgeCount: number;
}

interface ValidPeriod {
  startTime?: string;
  endTime?: string;
  permanent?: boolean;
}

interface KnowledgeItem {
  id: number;
  knowledge_id: string;
  name: string;
  summary: string;
  creator: string;
  createTime: string;
  updateTime: string;
  validPeriod: ValidPeriod;
  status: 'ONLINE' | 'DRAFT';
  type: 'TABLE' | 'DOCUMENT';
  tags: string[];
  version: number;
}

interface KnowledgeManagementProps {
  onNavigateToDetail?: (action: string, id?: string) => void;
}

const KnowledgeManagement: React.FC<KnowledgeManagementProps> = ({ onNavigateToDetail }) => {
  const { groupId } = useParams<{ groupId: string }>();
  const location = useLocation();
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};

  const [knowledgeGroup, setKnowledgeGroup] = useState<KnowledgeGroupData | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [publishLoading, setPublishLoading] = useState<boolean>(false);
  const [hasShownNotification, setHasShownNotification] = useState<boolean>(false);

  // 从location.state获取知识组信息，或从URL参数获取
  useEffect(() => {
    const groupData = location.state as KnowledgeGroupData;
    if (groupData) {
      setKnowledgeGroup(groupData);
    } else {
      // 如果没有通过state传递，可以通过groupId查询基本信息
      // 这里使用简单的默认值，实际应用中可能需要额外的API查询
      setKnowledgeGroup({
        id: groupId || '',
        name: '知识组',
        description: '知识组描述',
        createDate: new Date().toISOString().split('T')[0],
        creator: '未知',
        tags: [],
        knowledgeCount: 0
      });
    }
  }, [location.state, groupId]);

  // 查询知识组下的知识列表
  const queryGroupKnowledge = async () => {
    if (!customData?.uid || !customData?.cid || !customData?.username || !groupId) {
      message.error('缺少必要的用户信息或知识组ID', 2);
      return [];
    }

    try {
      console.log('🟢 页面调用 getGroupKnowledge，参数:', {
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        group_id: groupId
      });

      const response = await getGroupKnowledge({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        group_id: groupId
      });

      console.log('🟢 页面接收到 getGroupKnowledge 响应:', response);

      if (response.success && response.data) {
        console.log('🟢 原始知识数据:', response.data);

        // 将API返回的数据格式转换为页面所需的格式
        const convertedData: KnowledgeItem[] = response.data.map((item: API.GroupKnowledgeItem) => ({
          id: item.id,
          knowledge_id: item.knowledge_id,
          name: item.name,
          summary: item.desc,
          creator: item.create_name,
          createTime: item.gmt_create,
          updateTime: item.gmt_modified,
          validPeriod: item.longtime_valid
            ? { permanent: true }
            : {
              startTime: item.valid_date,
              endTime: item.invalid_date
            },
          status: item.status as 'ONLINE' | 'DRAFT',
          type: item.content_type as 'TABLE' | 'DOCUMENT',
          tags: item.tags || [],
          version: item.version
        }));

        console.log('🟢 转换后的知识数据:', convertedData);
        return {
          data: convertedData,
          success: true
        };
      } else {
        console.log('🔴 接口返回失败:', response.error_msg || '获取知识列表失败');
        message.error(response.error_msg || '获取知识列表失败', 2);
        return [];
      }
    } catch (error) {
      console.error('🔴 获取知识列表出错:', error);
      message.error('获取知识列表失败', 2);
      return [];
    }
  };

  // 使用useRequest获取知识列表数据
  const { data, loading, refresh } = useRequest(queryGroupKnowledge);

  const knowledgeData: KnowledgeItem[] = (data as KnowledgeItem[]) || [];

  const handleAddKnowledge = () => {
    // 跳转到知识详情页面，创建模式
    history.push(`/database/knowledge/detail/create/${groupId}`, {
      mode: 'create',
      groupId: groupId,
      groupData: knowledgeGroup
    });
  };

  const handleBatchDelete = async () => {
    if (!customData?.uid || !customData?.cid || !customData?.username) {
      message.error('缺少必要的用户信息', 2);
      return;
    }

    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的知识', 2);
      return;
    }

    try {
      // 直接将selectedRowKeys转换为数字数组，因为rowKey就是id
      const delIds: number[] = selectedRowKeys.map(key => Number(key));

      console.log('批量删除的ID列表:', delIds);

      const response = await batchDeleteKnowledge({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        del_ids: delIds
      });

      if (response.success) {
        message.success(`成功删除 ${delIds.length} 条知识`, 2);
        setSelectedRowKeys([]);
        // 刷新列表
        refresh();
      } else {
        message.error(response.error_msg || '批量删除失败', 2);
      }
    } catch (error) {
      console.error('批量删除知识失败:', error);
      message.error('批量删除失败', 2);
    }
  };

  // 显示上线提示通知
  const showUploadNotification = () => {
    if (!hasShownNotification) {
      notification.info({
        message: '知识上线中',
        description: '正在进行知识理解，预计耗时约30秒，请稍候...',
        placement: 'top',
      });
      setHasShownNotification(true);
    }
  };

  const handleBatchPublish = async () => {
    if (!customData?.uid || !customData?.cid || !customData?.username) {
      message.error('缺少必要的用户信息', 2);
      return;
    }

    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要发布的知识', 2);
      return;
    }

    // 过滤出草稿状态的知识
    const draftIds: number[] = [];
    selectedRowKeys.forEach(key => {
      const record = knowledgeData.find(item => item.id === Number(key));
      if (record && record.status === 'DRAFT') {
        draftIds.push(record.id);
      }
    });

    if (draftIds.length === 0) {
      message.warning('选中的知识中没有草稿状态的知识，无法发布', 2);
      return;
    }

    if (draftIds.length !== selectedRowKeys.length) {
      message.warning(`只有草稿状态的知识才能发布，将发布 ${draftIds.length} 条草稿知识`, 3);
    }

    try {
      setPublishLoading(true);
      
      // 显示上线提示
      showUploadNotification();

      const response = await batchOnlineKnowledge({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username,
        ids: draftIds
      });

      if (response.success) {
        message.success(`成功发布 ${draftIds.length} 条知识`, 2);
        setSelectedRowKeys([]);
        // 刷新列表
        refresh();
      } else {
        message.error(response.error_msg || '批量发布失败', 2);
      }
    } catch (error) {
      console.error('批量发布知识失败:', error);
      message.error('批量发布失败，请稍后重试', 2);
    } finally {
      setPublishLoading(false);
    }
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 如果知识组信息还没加载完成，显示加载状态
  if (!knowledgeGroup) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
          <Spin size="large" />
        </div>
      </PageContainer>
    );
  }

  return (
    <>
      <Helmet>
        <title>知识管理</title>
      </Helmet>
      <PageContainer
      breadcrumb={{
        items: [
          {
            title: '数据资产',
          },
          {
            title: (
              <span
                style={{ cursor: 'pointer' }}
                onClick={() => history.push('/database/knowledge')}
              >
                知识库
              </span>
            ),
          },
          {
            title: `${knowledgeGroup.name}`,
          },
        ],
      }}
      extra={
        // 只有当知识列表非空时才显示右上角的添加按钮
        knowledgeData && knowledgeData.length > 0 ? (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddKnowledge}
          >
            添加知识
          </Button>
        ) : null
      }
    >
      <div className="knowledge-management">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <GroupDigest data={{
          name: knowledgeGroup.name,
          description: knowledgeGroup.description,
          createDate: knowledgeGroup.createDate,
          creator: knowledgeGroup.creator,
          tags: knowledgeGroup.tags,
          itemCount: knowledgeData.length,
          itemCountLabel: '个文档'
        }} />

          <div className="knowledge-list-section">
            <Spin spinning={loading}>
              <KnowledgeList
                data={knowledgeData}
                loading={loading}
                selectedRowKeys={selectedRowKeys}
                onSelectChange={onSelectChange}
                onBatchDelete={handleBatchDelete}
                onBatchPublish={handleBatchPublish}
                onNavigateToDetail={(action, knowledge_id) => {
                  if (action === 'create') {
                    handleAddKnowledge();
                  } else {
                    // 处理查看和编辑操作，knowledge_id是知识的身份ID
                    history.push(`/database/knowledge/detail/${action}/${knowledge_id}`, {
                      mode: action,
                      knowledgeId: knowledge_id,
                      groupId: groupId,
                      groupData: knowledgeGroup
                    });
                  }
                }}
                groupId={groupId}
                onRefresh={refresh}
                publishLoading={publishLoading}
                onPublishLoadingChange={setPublishLoading}
                showUploadNotification={showUploadNotification}
              />
            </Spin>
          </div>
        </Space>
      </div>
    </PageContainer>
    </>
  );
};

export default KnowledgeManagement; 