import {
  ClockCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileOutlined,
  FolderOpenOutlined,
  FolderOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest, useModel, history } from '@umijs/max';
import { Avatar, Button, Card, Form, Input, List, Modal, Result, Select, Tag, Tooltip, Typography, message } from 'antd';
import React, { useState } from 'react';
import { getKnowledgeGroup, addKnowledgeGroup, updateKnowledgeGroup, deleteKnowledgeGroup } from '@/services/ant-design-pro/api';
import BaseCard from '@/components/BaseCard';
import useStyles from './style';

const { Paragraph } = Typography;

// 知识组数据类型
interface KnowledgeGroup {
  id: string;
  title: string;
  description: string;
  fileCount: number;
  createTime: string;
  tags: string[];
  owner: {
    name: string;
    avatar: string | null;
  };
}

// 模拟数据
const mockData: KnowledgeGroup[] = [
  {
    id: '1',
    title: '产品文档',
    description: '产品相关的所有文档，包含产品需求文档、产品规划、竞品分析等重要内容',
    fileCount: 12,
    createTime: '2023-11-20',
    tags: ['产品需求', '竞品分析', '用户研究'],
    owner: {
      name: '张三',
      avatar: null,
    },
  },
  {
    id: '2',
    title: '技术文档',
    description: '技术架构与开发文档，包括系统设计、API文档、开发规范等',
    fileCount: 8,
    createTime: '2023-11-20',
    tags: ['系统架构', 'API文档'],
    owner: {
      name: '李四',
      avatar: null,
    },
  },
  {
    id: '3',
    title: '设计规范',
    description: 'UI设计规范文档，包含设计系统、组件库、交互规范等设计资源',
    fileCount: 5,
    createTime: '2023-11-20',
    tags: ['设计系统', '组件库', '交互规范', 'UI规范', '视觉设计'],
    owner: {
      name: '王五',
      avatar: null,
    },
  },
  {
    id: '4',
    title: '运维手册',
    description: '系统运维相关文档，涵盖部署指南、监控方案、应急预案等',
    fileCount: 15,
    createTime: '2023-11-20',
    tags: ['部署指南', '监控运维'],
    owner: {
      name: '赵六',
      avatar: null,
    },
  },
];

const KnowledgeGroups: React.FC = () => {
  const { styles } = useStyles();
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<KnowledgeGroup | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 获取用户状态信息
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};

  // 检查登录状态，如果未登录则重定向到登录页
  React.useEffect(() => {
    if (!customData?.hasLogin) {
      history.push('/user/login');
    }
  }, [customData?.hasLogin]);


  // API请求 - 获取知识组列表
  const queryKnowledgeGroups = async () => {
    console.log('用户状态数据:', customData);

    // 参数验证 - 如果未登录则直接返回空数据，不显示错误信息
    if (!customData?.hasLogin || !customData?.uid || !customData?.cid || !customData?.username) {
      console.log('用户未登录或缺少必要信息，跳过API请求');
      return { data: [] };
    }

    try {
      const response = await getKnowledgeGroup({
        uid: customData.uid,
        cid: customData.cid,
        user_name: customData.username
      });

      console.log('查询知识组响应:', response);

      if (response.success && response.data) {
        // 将API返回的数据格式转换为页面所需的格式
        const convertedData = response.data.map((group: API.KnowledgeGroupItem) => ({
          id: group.group_id,
          title: group.group_name,
          description: group.group_desc || '暂无描述',
          fileCount: group.num || 0, // 使用API返回的num字段
          createTime: group.gmt_create?.split(' ')[0] || '未知', // 只取日期部分
          tags: group.tags || [], // 使用API返回的tags字段
          owner: {
            name: group.create_user || '未知用户', // 如果create_user为空，显示"未知用户"
            avatar: null, // 不设置默认头像，让Avatar组件自动使用文字
          },
        }));

        return {
          data: convertedData,
        };
      } else {
        message.error(response.error_msg || '获取知识组列表失败', 2);
        return {
          data: [],
        };
      }
    } catch (error) {
      console.error('获取知识组列表出错:', error);
      message.error('获取知识组列表失败', 2);
      return {
        data: [],
      };
    }
  };

  // 获取知识组列表数据
  const { data, loading, refresh } = useRequest(() => {
    return queryKnowledgeGroups();
  }, {
    ready: !!customData?.hasLogin, // 只有在登录状态下才发送请求
  });

  const list = data || [];

  // 显示新建知识组弹框
  const showCreateModal = () => {
    setEditingItem(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  // 显示编辑知识组弹框
  const showEditModal = (item: KnowledgeGroup) => {
    setEditingItem(item);
    setIsModalVisible(true);
    // 预填充表单数据
    form.setFieldsValue({
      title: item.title,
      description: item.description,
      tags: item.tags || [],
    });
  };

  // 处理弹框取消
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingItem(null);
    form.resetFields();
    setSubmitLoading(false);
  };

  // 处理打开知识组管理页面
  const handleOpenKnowledgeGroup = (item: KnowledgeGroup) => {
    // 传递知识组信息到管理页面
    const groupData = {
      id: item.id,
      name: item.title,
      description: item.description,
      createDate: item.createTime,
      creator: item.owner.name,
      tags: item.tags,
      knowledgeCount: item.fileCount
    };

    history.push(`/database/knowledge/manage/${item.id}`, groupData);
  };

  // 处理删除知识组
  const handleDelete = (item: KnowledgeGroup) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除知识组"${item.title}"吗？此操作不可撤销。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 参数验证
          if (!customData?.uid || !customData?.cid || !customData?.username) {
            message.error('登录已失效，请重新登录');
            window.localStorage.removeItem('loginInfo');
            setTimeout(() => {
              window.location.href = '/user/login';
            }, 1200);
            return;
          }

          const response = await deleteKnowledgeGroup({
            uid: customData.uid,
            cid: customData.cid,
            user_name: customData.username,
            group_id: item.id
          });

          if (response.success) {
            message.success('知识组删除成功！', 1.5);
            // 局部刷新知识组列表
            refresh();
          } else {
            message.error(response.error_msg || '删除失败', 2);
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请重试', 2);
        }
      },
    });
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setSubmitLoading(true);
    try {
      if (editingItem) {
        // 编辑模式 - 使用正确的API入参格式
        // 参数验证
        if (!customData?.uid || !customData?.cid || !customData?.username) {
          message.error('登录已失效，请重新登录');
          window.localStorage.removeItem('loginInfo');
          setTimeout(() => {
            window.location.href = '/user/login';
          }, 1200);
          return;
        }

        const updateData = {
          uid: customData.uid,
          cid: customData.cid,
          user_name: customData.username,
          group_id: editingItem.id,
          group_name: values.title,
          group_desc: values.description,
          tags: values.tags || []
        };

        console.log('准备发送的更新数据:', updateData);

        const response = await updateKnowledgeGroup(updateData);

        if (response.success) {
          message.success('知识组更新成功！', 1.5);
          // 局部刷新知识组列表
          refresh();
        } else {
          message.error(response.error_msg || '更新失败', 2);
          return;
        }
      } else {
        // 新建模式 - 使用正确的API入参格式
        const createData = {
          uid: customData?.uid,
          cid: customData?.cid,
          user_name: customData?.username,
          group_name: values.title,
          group_desc: values.description,
          tags: values.tags || []
        };

        console.log('准备发送的创建数据:', createData);
        console.log('用户状态数据:', customData);

        // 参数验证
        if (!createData.uid || !createData.cid || !createData.user_name || !createData.group_name) {
          message.error('登录已失效，请重新登录');
          window.localStorage.removeItem('loginInfo');
          setTimeout(() => {
            window.location.href = '/user/login';
          }, 1200);
          return;
        }

        const response = await addKnowledgeGroup(createData);

        if (response.success) {
          message.success('知识组创建成功！', 1.5);
          // 局部刷新知识组列表
          refresh();
        } else {
          message.error(response.error_msg || '创建失败', 2);
          return;
        }
      }

      setIsModalVisible(false);
      setEditingItem(null);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingItem ? '更新失败，请重试' : '创建失败，请重试', 2);
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <>
      <style>
        {`
          .enhanced-create-btn {
            background: linear-gradient(135deg, #1890ff, #36cfc9) !important;
            border: none !important;
            border-radius: 6px !important;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25) !important;
            font-weight: 600 !important;
            font-size: 14px !important;
            height: 36px !important;
            padding: 0 18px !important;
            transition: all 0.3s ease !important;
          }
          
          .enhanced-create-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35) !important;
            background: linear-gradient(135deg, #40a9ff, #5cdbd3) !important;
          }
          
          .enhanced-create-btn:active {
            transform: translateY(0) !important;
          }
          
          .enhanced-create-btn .anticon {
            font-size: 14px !important;
            margin-right: 6px !important;
          }
        `}
      </style>
      <PageContainer
        extra={list.length > 0 ? [
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
            size="large"
            className="enhanced-create-btn"
          >
            新建知识组
          </Button>,
        ] : []}
      >
        <div style={{
          padding: '24px',
          backgroundColor: '#f5f5f5',
          borderRadius: '8px',
          minHeight: '500px'
        }}>
          {!loading && list.length === 0 ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '500px',
              backgroundColor: '#fff',
              borderRadius: '8px',
              margin: '-24px'
            }}>
              <Result
                status="404"
                title="暂无知识组"
                subTitle="创建您的第一个知识组，开始管理您的知识资产吧~"
                style={{ width: '100%' }}
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={showCreateModal}
                    size="large"
                    className="enhanced-create-btn"
                  >
                    新建知识组
                  </Button>
                }
              />
            </div>
          ) : (
            <div className={styles.cardList} style={{ padding: '20px' }}>
              <List<Partial<KnowledgeGroup>>
                rowKey="id"
                loading={loading}
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 2,
                  md: 3,
                  lg: 3,
                  xl: 4,
                  xxl: 4,
                }}
                dataSource={list}
                renderItem={(item) => {
                  if (item && item.id) {
                    return (
                      <List.Item key={item.id}>
                        <BaseCard
                          id={item.id}
                          title={item.title || ''}
                          content={item.description || '暂无描述'}
                          count={item.fileCount || 0}
                          countLabel="个知识"
                          createTime={item.createTime}
                          creator={item.owner?.name}
                          creatorAvatar={item.owner?.avatar || undefined}
                          tags={item.tags || []}
                          onEdit={() => showEditModal(item as KnowledgeGroup)}
                          onView={() => handleOpenKnowledgeGroup(item as KnowledgeGroup)}
                          onDelete={() => handleDelete(item as KnowledgeGroup)}
                          onDoubleClick={() => handleOpenKnowledgeGroup(item as KnowledgeGroup)}
                        />
                      </List.Item>
                    );
                  }
                  return null;
                }}
              />
            </div>
          )}
        </div>

        <Modal
          title={editingItem ? "编辑知识组" : "新建知识组"}
          open={isModalVisible}
          onOk={() => form.submit()}
          onCancel={handleCancel}
          okText={editingItem ? "更新" : "创建"}
          cancelText="取消"
          width={600}
          confirmLoading={submitLoading}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            style={{ marginTop: '20px' }}
          >
            <Form.Item
              name="title"
              label="知识组名称"
              rules={[
                { required: true, message: '请输入知识组名称' },
                { max: 50, message: '名称长度不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入知识组名称" />
            </Form.Item>

            <Form.Item
              name="description"
              label="知识组描述"
              rules={[
                { required: true, message: '请输入知识组描述' },
                { max: 200, message: '描述长度不能超过200个字符' }
              ]}
            >
              <Input.TextArea
                rows={4}
                placeholder="请输入知识组描述，简要说明该知识组的用途和包含的内容"
              />
            </Form.Item>

            <Form.Item
              name="tags"
              label="标签"
            >
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="请输入标签，按回车键添加"
                tokenSeparators={[',']}
                maxTagCount={10}
              />
            </Form.Item>
          </Form>
        </Modal>
      </PageContainer>
    </>
  );
};

export default KnowledgeGroups;
