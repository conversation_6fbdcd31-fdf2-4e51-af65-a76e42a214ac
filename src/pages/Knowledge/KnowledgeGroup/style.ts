import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => {
  return {
    cardList: {
      margin: '-24px -24px 0 -24px',
    },
    card: {
      '&:hover': {
        borderColor: token.colorPrimary,
      },
    },
    newButton: {
      width: '100%',
      height: '201px',
      color: token.colorTextSecondary,
      backgroundColor: token.colorBgContainer,
      borderColor: token.colorBorder,
      borderRadius: token.borderRadius,
      '&:hover': {
        color: token.colorPrimary,
        borderColor: token.colorPrimary,
      },
    },
    item: {
      height: '64px',
      marginBottom: '8px',
      color: token.colorTextSecondary,
    },
    cardItemContent: {
      display: 'flex',
      flexDirection: 'column',
      marginTop: '16px',
      marginBottom: '-4px',
      lineHeight: '20px',
      height: '44px',
      color: token.colorTextSecondary,
      fontSize: '14px',
    },
    cardInfo: {
      display: 'flex',
      justifyContent: 'space-between',
      marginBottom: '4px',
      marginTop: '4px',
    },
    avatarInfo: {
      display: 'flex',
      alignItems: 'center',
      '& > span': {
        marginLeft: '8px',
      },
    },
  };
});

export default useStyles;
