import { useState, useCallback, useEffect } from 'react';
import { flushSync } from 'react-dom';
import type { MessageInstance } from 'antd/es/message/interface';
import { chainlitPerfMonitor } from '../utils/chainlitPerformanceTest';
import { useWebSocketChat } from './useWebSocketChat';
import {
  createSession,
  getUserSessions,
  getSessionDetail,
  deleteSession,
  getSessionMessages,
  getArtifactList,
  downloadArtifact,
  deleteArtifact,
  SessionInfo,
  SessionDetail,
  ArtifactDetail,
  CreateSessionParams,
} from '@/services/chat';

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  isStreaming?: boolean; // 标识当前消息是否正在流式输出
  typing?: boolean; // 控制打字机效果
  chunkSubType?: string; // 🔥 NEW: 用于区分不同类型的流式内容
  thinking?: Array<{
    id: number;
    title: string;
    content: string;
    type: 'thinking' | 'tool' | 'search' | 'code';
  }>;
  artifacts?: ArtifactDetail[];
}

export interface Product {
  id: string;
  type: 'code' | 'webpage' | 'image';
  content: string;
  timestamp: string;
  title: string;
  description?: string;
}

export const useChat = (messageApi: MessageInstance, customData: any) => {
  const [currentSession, setCurrentSession] = useState<SessionDetail | null>(null);
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [streaming, setStreaming] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string>(''); // 初始为空，等待 customData
  const [currentUserName, setCurrentUserName] = useState<string>('用户');
  const [currentCid, setCurrentCid] = useState<string>('default_company');
  const [useWebSocket, setUseWebSocket] = useState(true); // 🔥 WebSocket开关

  // 监听传入的customData变化
  useEffect(() => {
    if (customData && (customData as any).uid ) {
      setCurrentUserId((customData as any).uid);
    }
    if (customData && (customData as any).username) {
      setCurrentUserName((customData as any).username);
    }
    if (customData && (customData as any).cid) {
      setCurrentCid((customData as any).cid);
    }
  }, [customData]);

  // 🔥 Chainlit WebSocket集成 - 按需连接，不自动连接
  const webSocketChat = useWebSocketChat(messageApi, {
    autoConnect: false, // 改为false，避免进入页面就自动连接
    enablePerformanceMonitoring: true,
    messageAcknowledgment: true,
  });

  // 🔥 Chainlit WebSocket事件监听器
  useEffect(() => {
    const handleMessageUpdate = (event: CustomEvent) => {
      const { messageId, content, chunkSubType, isStreaming, updateType } = event.detail;

      console.log(`[CHAINLIT-WS] 📝 Message update event: messageId=${messageId}, updateType=${updateType || 'append'}, content="${content}" (${content?.length || 0} chars), isStreaming=${isStreaming}`);

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };
            const oldLength = message.content.length;

            // 🔥 根据updateType决定是追加还是替换内容
            if (updateType === 'replace') {
              // 替换模式：用新内容完全替换现有内容
              message.content = content;
              console.log(`[CHAINLIT-WS] 🔄 Content REPLACED: ${oldLength} → ${message.content.length} chars`);
            } else {
              // 默认追加模式：将新内容追加到现有内容
              message.content += content;
              console.log(`[CHAINLIT-WS] ➕ Content APPENDED: ${oldLength} → ${message.content.length} chars (+${content?.length || 0})`);
            }

            message.typing = false;
            message.isStreaming = isStreaming;
            message.chunkSubType = chunkSubType;
            updated[messageIndex] = message;
            console.log(`[CHAINLIT-WS] ✅ Message ${messageId} updated successfully`);
          } else {
            console.warn(`[CHAINLIT-WS] ⚠️ Message ${messageId} not found for update`);
          }
          return updated;
        });
      });
    };

    const handleMessageComplete = (event: CustomEvent) => {
      const { messageId, chunkSubType, completeContent, updateType } = event.detail;

      console.log(`[CHAINLIT-WS] 🏁 Message complete event: messageId=${messageId}, updateType=${updateType}, hasCompleteContent=${!!completeContent}, contentLength=${completeContent?.length || 0}`);

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };
            const oldLength = message.content.length;

            // 🔥 如果有完整内容，替换累积的流式内容
            if (completeContent) {
              message.content = completeContent;
              console.log(`[CHAINLIT-WS] 🔄 COMPLETION REPLACEMENT: ${oldLength} → ${message.content.length} chars`);
              console.log(`[CHAINLIT-WS] 📋 Final content preview: "${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}"`);

              // 🔥 验证内容是否真的被替换了
              if (oldLength > 0 && message.content.length !== oldLength) {
                console.log(`[CHAINLIT-WS] ✅ Content successfully replaced - streaming chunks overridden by final complete message`);
              } else if (oldLength === 0) {
                console.log(`[CHAINLIT-WS] ✅ Complete content set for empty message`);
              } else {
                console.log(`[CHAINLIT-WS] ✅ Content length unchanged after replacement - duplicate content`);
              }
            } else {
              console.log(`[CHAINLIT-WS] ℹ️ Completion event without content - only marking as complete`);
            }

            message.typing = false;
            message.isStreaming = false;
            message.chunkSubType = chunkSubType;
            updated[messageIndex] = message;
            console.log(`[CHAINLIT-WS] ✅ Message ${messageId} marked as complete`);
          } else {
            console.warn(`[CHAINLIT-WS] ⚠️ Message ${messageId} not found for completion`);
          }
          return updated;
        });
      });
    };

    const handleArtifactsUpdate = (event: CustomEvent) => {
      const { messageId, artifacts } = event.detail;

      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex(msg => msg.id === messageId);

          if (messageIndex !== -1) {
            const message = { ...updated[messageIndex] };
            if (!message.artifacts) message.artifacts = [];

            artifacts.forEach((artifact: any) => {
              const existingArtifact = message.artifacts?.find(a => a.artifact_id === artifact.artifact_id);
              if (!existingArtifact) {
                message.artifacts?.push({
                  id: parseInt(artifact.artifact_id || '0'),
                  artifact_id: artifact.artifact_id,
                  session_id: currentSession?.session_id || '',
                  message_id: messageId,
                  type: artifact.type,
                  title: artifact.title,
                  description: artifact.description || '',
                  content: artifact.content || '',
                  file_path: artifact.file_path || '',
                  file_url: artifact.file_url || '',
                  file_size: artifact.file_size || 0,
                  mime_type: artifact.mime_type || '',
                  file_hash: artifact.file_hash || '',
                  thumbnail_path: artifact.thumbnail_path || '',
                  tags: artifact.tags || '',
                  status: artifact.status || 1,
                  download_count: artifact.download_count || 0,
                  expires_at: artifact.expires_at || '',
                  is_deleted: artifact.is_deleted || false,
                  gmt_create: artifact.gmt_create || new Date().toISOString(),
                  gmt_modified: artifact.gmt_modified || new Date().toISOString(),
                  artifact_metadata: artifact.artifact_metadata || '',
                });
              }
            });
            updated[messageIndex] = message;
          }
          return updated;
        });
      });
    };

    const handleStreamFinish = (event: CustomEvent) => {
      const { messageId, finishReason, usageInfo } = event.detail;

      console.log(`[CHAINLIT-WS] 🏁 Stream finished event received: ${finishReason}`, usageInfo);

      // 🔥 只处理全局流式状态，避免与handleMessageComplete重复处理消息状态
      setStreaming(false);
      console.log(`[CHAINLIT-WS] 🏁 Main streaming state set to false`);

      // 🔥 可选：记录使用信息，但不修改消息状态（已由handleMessageComplete处理）
      if (usageInfo) {
        console.log(`[CHAINLIT-WS] 📊 Usage info:`, usageInfo);
      }
    };

    // 注册事件监听器
    window.addEventListener('chainlit-message-update', handleMessageUpdate as EventListener);
    window.addEventListener('chainlit-message-complete', handleMessageComplete as EventListener);
    window.addEventListener('chainlit-artifacts-update', handleArtifactsUpdate as EventListener);
    window.addEventListener('chainlit-stream-finish', handleStreamFinish as EventListener);

    return () => {
      // 清理事件监听器
      window.removeEventListener('chainlit-message-update', handleMessageUpdate as EventListener);
      window.removeEventListener('chainlit-message-complete', handleMessageComplete as EventListener);
      window.removeEventListener('chainlit-artifacts-update', handleArtifactsUpdate as EventListener);
      window.removeEventListener('chainlit-stream-finish', handleStreamFinish as EventListener);
    };
  }, [currentSession]);

  // 测试流式输出的模拟函数
  const testStreaming = useCallback(async () => {
    const testMessage = "这是一个测试流式输出的消息。我们将模拟逐字符显示的效果，就像ChatGPT一样。这个功能可以让用户看到AI正在实时生成回复，提供更好的用户体验。";

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: '测试流式输出',
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);

    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`;
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      type: 'ai',
      content: '',
      timestamp: new Date().toISOString(),
      isStreaming: true,
      typing: true,
      thinking: [],
    };

    setMessages(prev => [...prev, aiMessage]);

    // 模拟逐字符流式输出
    for (let i = 0; i <= testMessage.length; i++) {
      await new Promise(resolve => {setTimeout(resolve, 50)}); // 50ms延迟

      setMessages(prev => {
        const updated = [...prev];
        const aiMessageIndex = updated.findIndex(msg => msg.id === aiMessageId);

        if (aiMessageIndex !== -1) {
          updated[aiMessageIndex] = {
            ...updated[aiMessageIndex],
            content: testMessage.substring(0, i),
            isStreaming: i < testMessage.length,
            typing: i < testMessage.length,
          };
        }

        return updated;
      });
    }
  }, []);

  // 创建新会话
  const createNewSession = useCallback(async (title?: string): Promise<SessionDetail | null> => {
    try {
      setLoading(true);
      const params: CreateSessionParams = {
        user_id: currentUserId,
        title: title || `对话 - ${new Date().toLocaleString()}`,
      };

      const response = await createSession(params);
      if (response.success && response.data) {
        const sessionDetail = await getSessionDetail(response.data.session_id);
        if (sessionDetail.success && sessionDetail.data) {
          setCurrentSession(sessionDetail.data);
          setMessages([]);
          setProducts([]);
          return sessionDetail.data;
        }
      } else {
        messageApi.error(response.error || '创建会话失败');
      }
    } catch (error) {
      console.error('创建会话失败:', error);
      messageApi.error('创建会话失败');
    } finally {
      setLoading(false);
    }
    return null;
  }, [currentUserId, messageApi]);

  // 获取用户会话列表
  const loadUserSessions = useCallback(async () => {
    // 如果没有有效的用户ID，不执行查询
    if (!currentUserId) {
      console.log('[CHAT] ⏳ Waiting for valid user ID before loading sessions');
      return;
    }

    try {
      setLoading(true);
      console.log(`[CHAT] 🔍 Loading sessions for user: ${currentUserId}`);
      const response = await getUserSessions(currentUserId);
      if (response.success && response.data) {
        setSessions(response.data.sessions);
        console.log(`[CHAT] ✅ Loaded ${response.data.sessions.length} sessions`);
      } else {
        messageApi.error(response.error || '获取会话列表失败');
      }
    } catch (error) {
      console.error('获取会话列表失败:', error);
      messageApi.error('获取会话列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentUserId, messageApi]);

  // 加载会话详情和消息
  const loadSession = useCallback(async (sessionId: string) => {
    try {
      // 移除全局loading设置，避免影响历史列表显示
      // setLoading(true);

      // 获取会话详情
      const sessionResponse = await getSessionDetail(sessionId);
      if (!sessionResponse.success || !sessionResponse.data) {
        messageApi.error('会话不存在');
        return;
      }

      setCurrentSession(sessionResponse.data);

      // 获取会话消息
      const messagesResponse = await getSessionMessages(sessionId);
      if (messagesResponse.success && messagesResponse.data) {
        const chatMessages: ChatMessage[] = messagesResponse.data.messages.map(msg => ({
          id: msg.message_id,
          type: msg.role_type === 'user' ? 'user' : 'ai',
          content: msg.content,
          timestamp: msg.gmt_create,
          isStreaming: false, // 历史消息不应该显示流式状态
        }));
        setMessages(chatMessages);
      }

      // 获取会话产物
      const artifactsResponse = await getArtifactList({ session_id: sessionId });
      if (artifactsResponse.success && artifactsResponse.data) {
        const productsList: Product[] = artifactsResponse.data.artifacts.map(artifact => ({
          id: artifact.artifact_id,
          type: artifact.type as 'code' | 'webpage' | 'image',
          content: artifact.content,
          timestamp: artifact.gmt_create,
          title: artifact.title,
          description: artifact.description,
        }));
        setProducts(productsList);
      }

    } catch (error) {
      console.error('加载会话失败:', error);
      messageApi.error('加载会话失败');
    }
    // 移除finally中的loading设置
    // finally {
    //   setLoading(false);
    // }
  }, [messageApi]);

  // 🔥 Chainlit架构：发送消息并获取真正的实时流式响应（WebSocket优先）
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    // 如果没有当前会话，自动创建一个新会话
    let sessionToUse = currentSession;
    if (!sessionToUse) {
      try {
        sessionToUse = await createNewSession();
        if (!sessionToUse) {
          messageApi.error('创建会话失败，无法发送消息');
          return;
        }
      } catch (error) {
        messageApi.error('创建会话失败，无法发送消息');
        return;
      }
    }

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setStreaming(true);
    console.log('[CHAINLIT] 🚀 Main streaming state set to true');

    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`;
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      type: 'ai',
      content: '',
      timestamp: new Date().toISOString(),
      isStreaming: true, // 标识为正在流式输出
      typing: true, // 启用打字机效果
      thinking: [],
    };

    setMessages(prev => [...prev, aiMessage]);

    try {
      // 🔥 使用WebSocket进行实时通信
      if (useWebSocket) {
        // 如果WebSocket未连接，先连接
        if (!webSocketChat.isConnected) {
          console.log('[CHAINLIT] 🔌 WebSocket not connected, connecting now...');
          await webSocketChat.connectWebSocket();
        }

        console.log('[CHAINLIT] 🚀 Using WebSocket for real-time streaming...');

        await webSocketChat.sendWebSocketMessage(
          sessionToUse.session_id,
          content.trim(),
          currentCid,
          currentUserId,
          currentUserName,
          aiMessageId
        );

        console.log('[CHAINLIT] ✅ WebSocket message sent successfully');

      } else {
        console.error('[CHAINLIT] ❌ WebSocket not connected');
        messageApi.error('WebSocket连接未建立，请刷新页面重试');

        // 移除失败的AI消息
        setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
        setStreaming(false);
        return;
      }

    } catch (error) {
      console.error('[CHAINLIT] ❌ Send message failed:', error);
      messageApi.error('发送消息失败');

      // 移除失败的AI消息
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      setStreaming(false);
    }
  }, [currentSession, currentUserId, createNewSession, messageApi, useWebSocket, webSocketChat]);

  // 删除会话
  const deleteSessionById = useCallback(async (sessionId: string) => {
    try {
      const response = await deleteSession(sessionId);
      if (response.success) {
        messageApi.success('会话删除成功');
        setSessions(prev => prev.filter(session => session.session_id !== sessionId));

        // 如果删除的是当前会话，清空当前会话
        if (currentSession?.session_id === sessionId) {
          setCurrentSession(null);
          setMessages([]);
          setProducts([]);
        }
      } else {
        messageApi.error(response.error || '删除会话失败');
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      messageApi.error('删除会话失败');
    }
  }, [currentSession, messageApi]);

  // 下载产物
  const downloadArtifactById = useCallback(async (artifactId: string) => {
    try {
      const response = await downloadArtifact(artifactId);
      const blob = new Blob([response]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `artifact-${artifactId}.txt`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      messageApi.success('产物下载成功');
    } catch (error) {
      console.error('下载产物失败:', error);
      messageApi.error('下载产物失败');
    }
  }, [messageApi]);

  // 删除产物
  const deleteArtifactById = useCallback(async (artifactId: string) => {
    try {
      const response = await deleteArtifact(artifactId);
      if (response.success) {
        messageApi.success('产物删除成功');
        setProducts(prev => prev.filter(product => product.id !== artifactId));
      } else {
        messageApi.error(response.error || '删除产物失败');
      }
    } catch (error) {
      console.error('删除产物失败:', error);
      messageApi.error('删除产物失败');
    }
  }, [messageApi]);

  return {
    // 状态
    currentSession,
    sessions,
    messages,
    products,
    loading,
    streaming: streaming || (webSocketChat?.streaming || false),
    currentUserId,

    // 🔥 WebSocket状态
    useWebSocket,
    connectionState: webSocketChat?.connectionState || 'disconnected',
    connectionLatency: webSocketChat?.connectionLatency,
    reconnectCount: webSocketChat?.reconnectCount || 0,
    isWebSocketConnected: webSocketChat?.isConnected || false,

    // 方法
    createNewSession,
    loadUserSessions,
    loadSession,
    sendMessage,
    deleteSessionById,
    downloadArtifactById,
    deleteArtifactById,
    testStreaming, // 添加测试函数

    // 🔥 WebSocket方法
    setUseWebSocket,
    connectWebSocket: webSocketChat?.connectWebSocket || (() => Promise.resolve()),
    disconnectWebSocket: webSocketChat?.disconnectWebSocket || (() => {}),
    getPerformanceMetrics: webSocketChat?.getPerformanceMetrics || (() => ({})),
    isChainlitCompliant: webSocketChat?.isChainlitCompliant || (() => false),

    // 🔥 状态重置方法
    setCurrentSession,
    setMessages,
  };
};
