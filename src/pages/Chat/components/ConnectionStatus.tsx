/**
 * 🔥 Chainlit WebSocket连接状态指示器
 * 显示实时连接状态，保持Chainlit风格的视觉反馈
 */

import React from 'react';
import { Badge, Tooltip } from 'antd';
import {
  WifiOutlined,
  LoadingOutlined,
  DisconnectOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import type { WebSocketConnectionState } from '../utils/webSocketManager';

interface ConnectionStatusProps {
  connectionState: WebSocketConnectionState;
  reconnectCount?: number;
  latency?: number;
  className?: string;
  style?: React.CSSProperties;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  connectionState,
  reconnectCount = 0,
  latency,
  className,
  style,
}) => {
  // 🔥 Chainlit风格：根据连接状态返回配置
  const getStatusConfig = () => {
    switch (connectionState) {
      case 'connected':
        return {
          status: 'success' as const,
          icon: <WifiOutlined />,
          text: 'Connected',
          color: '#52c41a',
          description: latency 
            ? `WebSocket connected • ${latency}ms latency` 
            : 'WebSocket connected',
        };
      
      case 'connecting':
        return {
          status: 'processing' as const,
          icon: <LoadingOutlined spin />,
          text: 'Connecting',
          color: '#1890ff',
          description: 'Establishing WebSocket connection...',
        };
      
      case 'reconnecting':
        return {
          status: 'warning' as const,
          icon: <SyncOutlined spin />,
          text: 'Reconnecting',
          color: '#faad14',
          description: reconnectCount > 0 
            ? `Reconnecting... (attempt ${reconnectCount})` 
            : 'Reconnecting...',
        };
      
      case 'disconnected':
        return {
          status: 'default' as const,
          icon: <DisconnectOutlined />,
          text: 'Disconnected',
          color: '#8c8c8c',
          description: 'WebSocket disconnected',
        };
      
      case 'error':
        return {
          status: 'error' as const,
          icon: <ExclamationCircleOutlined />,
          text: 'Error',
          color: '#ff4d4f',
          description: reconnectCount > 0 
            ? `Connection error • ${reconnectCount} reconnect attempts` 
            : 'Connection error',
        };
      
      default:
        return {
          status: 'default' as const,
          icon: <DisconnectOutlined />,
          text: 'Unknown',
          color: '#8c8c8c',
          description: 'Unknown connection state',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div 
      className={className}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        ...style,
      }}
    >
      <style>
        {`
          /* 🔥 Chainlit风格动画 */
          @keyframes chainlit-pulse {
            0% {
              opacity: 1;
              transform: scale(1);
            }
            50% {
              opacity: 0.7;
              transform: scale(1.05);
            }
            100% {
              opacity: 1;
              transform: scale(1);
            }
          }
          
          @keyframes chainlit-glow {
            0% { box-shadow: 0 0 5px ${config.color}33; }
            50% { box-shadow: 0 0 15px ${config.color}66; }
            100% { box-shadow: 0 0 5px ${config.color}33; }
          }
          
          .chainlit-connection-indicator {
            animation: ${connectionState === 'connected' ? 'chainlit-pulse 2s ease-in-out infinite' : 'none'};
          }
          
          .chainlit-connection-badge {
            animation: ${connectionState === 'connecting' || connectionState === 'reconnecting' 
              ? 'chainlit-glow 1.5s ease-in-out infinite' 
              : 'none'};
          }
        `}
      </style>

      <Tooltip title={config.description} placement="bottom">
        <div className="chainlit-connection-indicator">
          <Badge 
            status={config.status}
            className="chainlit-connection-badge"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
          />
          
          <span
            style={{
              fontSize: '14px',
              color: config.color,
              fontWeight: 500,
              marginLeft: '4px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
            }}
          >
            {config.icon}
            {config.text}
          </span>
        </div>
      </Tooltip>

      {/* 🔥 Chainlit风格：延迟指示器 */}
      {connectionState === 'connected' && latency !== undefined && (
        <Tooltip title={`WebSocket latency: ${latency}ms`}>
          <span
            style={{
              fontSize: '12px',
              color: latency < 100 ? '#52c41a' : latency < 300 ? '#faad14' : '#ff4d4f',
              fontWeight: 500,
              padding: '2px 6px',
              borderRadius: '4px',
              backgroundColor: latency < 100 ? '#f6ffed' : latency < 300 ? '#fffbe6' : '#fff2f0',
              border: `1px solid ${latency < 100 ? '#b7eb8f' : latency < 300 ? '#ffe58f' : '#ffccc7'}`,
            }}
          >
            {latency}ms
          </span>
        </Tooltip>
      )}

      {/* 🔥 Chainlit风格：重连计数器 */}
      {reconnectCount > 0 && (
        <Tooltip title={`Reconnection attempts: ${reconnectCount}`}>
          <span
            style={{
              fontSize: '12px',
              color: '#faad14',
              fontWeight: 500,
              padding: '2px 6px',
              borderRadius: '4px',
              backgroundColor: '#fffbe6',
              border: '1px solid #ffe58f',
            }}
          >
            ↻{reconnectCount}
          </span>
        </Tooltip>
      )}
    </div>
  );
};

export default ConnectionStatus;
