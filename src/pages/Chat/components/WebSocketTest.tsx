/**
 * 🔥 Chainlit WebSocket测试组件
 * 用于验证WebSocket升级是否成功，性能是否达到目标
 */

import React, { useState } from 'react';
import { Button, Card, Statistic, Row, Col, Alert, Space, Tag } from 'antd';
import {
  ThunderboltOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';

interface WebSocketTestProps {
  getPerformanceMetrics: () => any;
  isChainlitCompliant: () => boolean;
  connectionState: string;
  connectionLatency?: number;
  isWebSocketConnected: boolean;
}

const WebSocketTest: React.FC<WebSocketTestProps> = ({
  getPerformanceMetrics,
  isChainlitCompliant,
  connectionState,
  connectionLatency,
  isWebSocketConnected,
}) => {
  const [testResults, setTestResults] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  const runPerformanceTest = async () => {
    setTesting(true);

    try {
      // 等待一小段时间让性能数据稳定
      await new Promise(resolve => setTimeout(resolve, 1000));

      const metrics = getPerformanceMetrics();
      const compliant = isChainlitCompliant();

      setTestResults({
        metrics,
        compliant,
        timestamp: new Date().toLocaleString(),
      });

    } catch (error) {
      console.error('Performance test failed:', error);
    } finally {
      setTesting(false);
    }
  };

  const getStatusColor = (value: number, target: number, isLower = true) => {
    const isGood = isLower ? value < target : value > target;
    return isGood ? '#52c41a' : '#ff4d4f';
  };

  const getStatusIcon = (value: number, target: number, isLower = true) => {
    const isGood = isLower ? value < target : value > target;
    return isGood ? <CheckCircleOutlined /> : <CloseCircleOutlined />;
  };

  return (
    <Card
      title={
        <Space>
          <ThunderboltOutlined />
          WebSocket性能测试
        </Space>
      }
      size="small"
      style={{ marginBottom: '16px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 连接状态 */}
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="连接状态"
              value={connectionState}
              valueStyle={{
                color: isWebSocketConnected ? '#52c41a' : '#ff4d4f',
                fontSize: '16px'
              }}
              prefix={isWebSocketConnected ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="连接延迟"
              value={connectionLatency || 0}
              suffix="ms"
              valueStyle={{
                color: getStatusColor(connectionLatency || 0, 500),
                fontSize: '16px'
              }}
              prefix={getStatusIcon(connectionLatency || 0, 500)}
            />
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              icon={testing ? <SyncOutlined spin /> : <ThunderboltOutlined />}
              onClick={runPerformanceTest}
              loading={testing}
              disabled={!isWebSocketConnected}
            >
              运行性能测试
            </Button>
          </Col>
        </Row>

        {/* 测试结果 */}
        {testResults && (
          <>
            <Alert
              message={`Chainlit合规性检查: ${testResults.compliant ? '✅ 通过' : '❌ 未通过'}`}
              type={testResults.compliant ? 'success' : 'error'}
              showIcon
            />

            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="实时率"
                  value={testResults.metrics.realTimeRate?.toFixed(1) || 0}
                  suffix="%"
                  valueStyle={{
                    color: getStatusColor(testResults.metrics.realTimeRate || 0, 98, false),
                    fontSize: '14px'
                  }}
                  prefix={getStatusIcon(testResults.metrics.realTimeRate || 0, 98, false)}
                />
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>目标: &gt;98%</div>
              </Col>

              <Col span={6}>
                <Statistic
                  title="平均延迟"
                  value={testResults.metrics.averageChunkDelay?.toFixed(1) || 0}
                  suffix="ms"
                  valueStyle={{
                    color: getStatusColor(testResults.metrics.averageChunkDelay || 0, 15),
                    fontSize: '14px'
                  }}
                  prefix={getStatusIcon(testResults.metrics.averageChunkDelay || 0, 15)}
                />
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>目标: &lt;15ms</div>
              </Col>

              <Col span={6}>
                <Statistic
                  title="最大延迟"
                  value={testResults.metrics.maxChunkDelay || 0}
                  suffix="ms"
                  valueStyle={{
                    color: getStatusColor(testResults.metrics.maxChunkDelay || 0, 50),
                    fontSize: '14px'
                  }}
                  prefix={getStatusIcon(testResults.metrics.maxChunkDelay || 0, 50)}
                />
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>目标: &lt;50ms</div>
              </Col>

              <Col span={6}>
                <Statistic
                  title="处理速度"
                  value={testResults.metrics.chunksPerSecond?.toFixed(1) || 0}
                  suffix="chunk/s"
                  valueStyle={{ fontSize: '14px' }}
                />
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>chunks/秒</div>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  总块数: {testResults.metrics.totalChunks || 0}
                </div>
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  内容长度: {testResults.metrics.contentLength || 0} 字符
                </div>
              </Col>
              <Col span={12}>
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  测试时间: {testResults.timestamp}
                </div>
                <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  重连次数: {testResults.metrics.reconnectCount || 0}
                </div>
              </Col>
            </Row>

            {/* 性能标签 */}
            <Space wrap>
              <Tag color={testResults.metrics.realTimeRate > 98 ? 'green' : 'red'}>
                实时率: {testResults.metrics.realTimeRate?.toFixed(1)}%
              </Tag>
              <Tag color={testResults.metrics.averageChunkDelay < 15 ? 'green' : 'red'}>
                平均延迟: {testResults.metrics.averageChunkDelay?.toFixed(1)}ms
              </Tag>
              <Tag color={testResults.metrics.maxChunkDelay < 50 ? 'green' : 'red'}>
                最大延迟: {testResults.metrics.maxChunkDelay}ms
              </Tag>
              <Tag color={connectionLatency && connectionLatency < 500 ? 'green' : 'red'}>
                连接延迟: {connectionLatency}ms
              </Tag>
            </Space>
          </>
        )}
      </Space>
    </Card>
  );
};

export default WebSocketTest;
