/**
 * Chat Page - 基于 Ant Design X 的对话界面
 * 
 * 功能特性：
 * - Claude 风格的界面布局
 * - 左右分栏：对话结果区 + 思考过程区
 * - 对话结果与思考过程的关联
 * - 响应式设计
 * - 历史对话管理
 * - 推荐提示词
 */

import React from 'react';
import { ConfigProvider, App } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import ChatInterface from './ChatInterface';

// 自定义主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 8,
    colorBgContainer: '#ffffff',
    colorText: '#262626',
    colorTextSecondary: '#8c8c8c',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 12,
      boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
    },
    Drawer: {
      borderRadius: 0,
    },
    Timeline: {
      itemPaddingBottom: 16,
    },
  },
};

const ChatPage: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN} theme={theme}>
      <App>
        <div style={{ height: '100vh' }}>
          <ChatInterface />
        </div>
      </App>
    </ConfigProvider>
  );
};

export default ChatPage;