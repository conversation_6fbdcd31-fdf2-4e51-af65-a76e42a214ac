// 对话界面相关类型定义

export interface ThinkingStep {
  id: string;
  title: string;
  content: string;
  type: 'thinking' | 'search' | 'tool' | 'code';
  timestamp: string;
  status: 'pending' | 'processing' | 'completed';
  duration?: number; // 执行时长（毫秒）
  metadata?: Record<string, any>; // 额外的元数据
}

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  thinking?: ThinkingStep[];
  metadata?: {
    tokens?: number; // token消耗
    cost?: number;   // 成本
    model?: string;  // 使用的模型
  };
}

export interface HistoryItem {
  id: string;
  title: string;
  timestamp: string;
  preview: string;
  messageCount?: number;
  tags?: string[];
}

export interface PromptExample {
  title: string;
  prompt: string;
  icon: string;
  color: string;
  category?: string;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface ChatConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  streamMode: boolean;
}

// 对话状态管理
export interface ChatState {
  // 界面状态
  sidebarOpen: boolean;
  chatStarted: boolean;
  isTyping: boolean;
  
  // 选中状态
  selectedMessageId: string | null;
  selectedThinkingStep: string | null;
  
  // 数据状态
  messages: Message[];
  currentSession: ChatSession | null;
  historyList: HistoryItem[];
  
  // 输入状态
  inputValue: string;
  
  // 配置
  config: ChatConfig;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface StreamResponse {
  type: 'thinking' | 'content' | 'done' | 'error';
  data: any;
  messageId?: string;
  stepId?: string;
}

// 事件类型
export type ChatEventType = 
  | 'message-send'
  | 'message-receive'
  | 'thinking-start'
  | 'thinking-complete'
  | 'session-create'
  | 'session-load'
  | 'history-select';

export interface ChatEvent {
  type: ChatEventType;
  payload: any;
  timestamp: string;
}