/**
 * 🔥 Chainlit性能测试工具
 * 用于验证重构后的WebSocket流式聊天实现是否达到Chainlit的性能标准
 * 升级目标：<20ms处理延迟，>98%实时率
 */

export interface ChainlitPerformanceMetrics {
  totalChunks: number;
  totalProcessingTime: number;
  averageChunkDelay: number;
  maxChunkDelay: number;
  minChunkDelay: number;
  realTimeRate: number; // 实时率：<20ms处理的chunk百分比（WebSocket目标）
  contentLength: number;
  chunksPerSecond: number;
  connectionLatency?: number; // WebSocket连接延迟
  reconnectCount?: number; // 重连次数
}

export class ChainlitPerformanceMonitor {
  private startTime: number = 0;
  private chunkTimes: number[] = [];
  private chunkDelays: number[] = [];
  private totalContentLength: number = 0;
  private chunkCount: number = 0;
  private connectionLatency: number = 0;
  private reconnectCount: number = 0;

  start() {
    this.startTime = Date.now();
    this.chunkTimes = [];
    this.chunkDelays = [];
    this.totalContentLength = 0;
    this.chunkCount = 0;
    this.connectionLatency = 0;
    this.reconnectCount = 0;
    console.log('[CHAINLIT-PERF] 🚀 WebSocket Performance monitoring started');
  }

  recordConnection(latency: number) {
    this.connectionLatency = latency;
    console.log(`[CHAINLIT-PERF] WebSocket connection latency: ${latency}ms`);
  }

  recordReconnect() {
    this.reconnectCount++;
    console.log(`[CHAINLIT-PERF] WebSocket reconnect #${this.reconnectCount}`);
  }

  recordChunk(receiveTime: number, processTime: number, contentLength: number = 0) {
    this.chunkCount++;
    const delay = processTime - receiveTime;
    this.chunkTimes.push(receiveTime);
    this.chunkDelays.push(delay);
    this.totalContentLength += contentLength;

    console.log(`[CHAINLIT-PERF] Chunk #${this.chunkCount}: ${delay}ms delay, ${contentLength} chars`);
  }

  getMetrics(): ChainlitPerformanceMetrics {
    const totalTime = Date.now() - this.startTime;
    const avgDelay = this.chunkDelays.reduce((a, b) => a + b, 0) / this.chunkDelays.length;
    const maxDelay = Math.max(...this.chunkDelays);
    const minDelay = Math.min(...this.chunkDelays);
    // 🔥 WebSocket目标：<20ms处理延迟
    const realTimeChunks = this.chunkDelays.filter(delay => delay < 20).length;
    const realTimeRate = (realTimeChunks / this.chunkDelays.length) * 100;
    const chunksPerSecond = (this.chunkCount / totalTime) * 1000;

    const metrics: ChainlitPerformanceMetrics = {
      totalChunks: this.chunkCount,
      totalProcessingTime: totalTime,
      averageChunkDelay: avgDelay,
      maxChunkDelay: maxDelay,
      minChunkDelay: minDelay,
      realTimeRate,
      contentLength: this.totalContentLength,
      chunksPerSecond,
      connectionLatency: this.connectionLatency,
      reconnectCount: this.reconnectCount
    };

    console.log('[CHAINLIT-PERF] 📊 WebSocket Final Metrics:', metrics);
    return metrics;
  }

  isChainlitCompliant(): boolean {
    const metrics = this.getMetrics();

    // 🔥 WebSocket Chainlit标准（升级目标）：
    // 1. 实时率 > 98%（<20ms处理延迟）
    // 2. 平均延迟 < 15ms
    // 3. 最大延迟 < 50ms
    // 4. 连接延迟 < 500ms
    // 5. 重连成功率 > 95%

    const isCompliant =
      metrics.realTimeRate > 98 &&
      metrics.averageChunkDelay < 15 &&
      metrics.maxChunkDelay < 50 &&
      (metrics.connectionLatency || 0) < 500;

    console.log(`[CHAINLIT-PERF] ${isCompliant ? '✅' : '❌'} WebSocket Chainlit Compliance: ${isCompliant}`);
    console.log(`[CHAINLIT-PERF] Real-time rate: ${metrics.realTimeRate.toFixed(1)}% (target: >98%)`);
    console.log(`[CHAINLIT-PERF] Avg delay: ${metrics.averageChunkDelay.toFixed(1)}ms (target: <15ms)`);
    console.log(`[CHAINLIT-PERF] Max delay: ${metrics.maxChunkDelay}ms (target: <50ms)`);
    console.log(`[CHAINLIT-PERF] Connection latency: ${metrics.connectionLatency}ms (target: <500ms)`);
    console.log(`[CHAINLIT-PERF] Reconnects: ${metrics.reconnectCount}`);

    return isCompliant;
  }
}

// 全局性能监控器实例
export const chainlitPerfMonitor = new ChainlitPerformanceMonitor();



/**
 * 🔥 Chainlit风格的实时状态更新器
 * 使用flushSync确保立即DOM更新
 */
export class ChainlitStateUpdater {
  static updateMessageSync<T>(
    setMessages: React.Dispatch<React.SetStateAction<T[]>>,
    messageId: string,
    updater: (message: T) => T
  ) {
    // 使用React的flushSync确保同步更新
    import('react-dom').then(({ flushSync }) => {
      flushSync(() => {
        setMessages(prev => {
          const updated = [...prev];
          const messageIndex = updated.findIndex((msg: any) => msg.id === messageId);

          if (messageIndex !== -1) {
            updated[messageIndex] = updater(updated[messageIndex]);
          }

          return updated;
        });
      });
    });
  }
}
