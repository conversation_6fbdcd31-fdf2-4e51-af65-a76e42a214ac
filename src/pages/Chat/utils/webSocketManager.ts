/**
 * 🔥 Chainlit WebSocket管理器
 * 升级SSE到WebSocket，保持Chainlit架构优势，提升性能到<20ms延迟
 */

import { chainlitPerfMonitor } from './chainlitPerformanceTest';

export type WebSocketConnectionState =
  | 'connecting'
  | 'connected'
  | 'disconnected'
  | 'reconnecting'
  | 'error';

export interface WebSocketMessage {
  type: 'chat' | 'ping' | 'pong' | 'error' | 'ack';
  data?: any;
  id?: string;
  timestamp?: number;
}

export interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  connectionTimeout: number;
}

export class ChainlitWebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private connectionState: WebSocketConnectionState = 'disconnected';
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private connectionTimer: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private pendingMessages = new Map<string, { resolve: Function; reject: Function; timestamp: number }>();

  // Event handlers
  private onStateChange: ((state: WebSocketConnectionState) => void) | null = null;
  private onMessage: ((message: any) => void) | null = null;
  private onError: ((error: Event) => void) | null = null;

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: config.url || this.getWebSocketUrl(),
      reconnectInterval: config.reconnectInterval || 3000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      connectionTimeout: config.connectionTimeout || 10000,
    };
  }

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // 🔥 开发环境：前端8002端口，后端8000端口
    if (process.env.NODE_ENV === 'development') {
      return `${protocol}//localhost:8000/chat/ws`;
    }

    // 🔥 生产环境：使用当前host
    const host = window.location.host;
    return `${protocol}//${host}/chat/ws`;
  }

  // 🔥 Chainlit核心：建立WebSocket连接
  async connect(): Promise<void> {
    if (this.connectionState === 'connecting' || this.connectionState === 'connected') {
      return;
    }

    const connectStartTime = Date.now();
    this.setState('connecting');

    console.log('[CHAINLIT-WS] 🚀 Establishing WebSocket connection...');

    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.url);

        // 连接超时处理
        this.connectionTimer = setTimeout(() => {
          if (this.connectionState === 'connecting') {
            this.ws?.close();
            this.setState('error');
            reject(new Error('WebSocket connection timeout'));
          }
        }, this.config.connectionTimeout);

        this.ws.onopen = () => {
          const connectionLatency = Date.now() - connectStartTime;
          chainlitPerfMonitor.recordConnection(connectionLatency);

          console.log(`[CHAINLIT-WS] ✅ Connected in ${connectionLatency}ms`);

          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }

          this.setState('connected');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.flushMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          console.log(`[CHAINLIT-WS] 🔌 Connection closed: ${event.code} - ${event.reason}`);
          this.cleanup();

          if (event.code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            this.setState('disconnected');
          }
        };

        this.ws.onerror = (error) => {
          console.error('[CHAINLIT-WS] ❌ WebSocket error:', error);
          this.onError?.(error);
          this.setState('error');
          reject(error);
        };

      } catch (error) {
        console.error('[CHAINLIT-WS] ❌ Failed to create WebSocket:', error);
        this.setState('error');
        reject(error);
      }
    });
  }

  // 🔥 Chainlit核心：处理接收到的消息
  private handleMessage(event: MessageEvent) {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);

      console.log(`[CHAINLIT-WS] ⚡ Message received: ${message.type}`);

      // 处理心跳响应
      if (message.type === 'pong') {
        return;
      }

      // 处理消息确认
      if (message.type === 'ack' && message.id) {
        const pending = this.pendingMessages.get(message.id);
        if (pending) {
          pending.resolve(message);
          this.pendingMessages.delete(message.id);
        }
        return;
      }

      // 🔥 Chainlit模式：立即处理聊天消息
      if (message.type === 'chat' && this.onMessage) {
        const processStartTime = Date.now();
        this.onMessage(message.data);

        const processDelay = Date.now() - processStartTime;
        console.log(`[CHAINLIT-WS] ⚡ Message processed in ${processDelay}ms`);
      }

    } catch (error) {
      console.error('[CHAINLIT-WS] ❌ Failed to parse message:', error);
    }
  }

  // 🔥 Chainlit核心：发送消息（支持确认机制）
  async sendMessage(data: any, waitForAck = false): Promise<any> {
    const message: WebSocketMessage = {
      type: 'chat',
      data,
      id: waitForAck ? this.generateMessageId() : undefined,
      timestamp: Date.now(),
    };

    if (this.connectionState !== 'connected') {
      console.log('[CHAINLIT-WS] 📤 Queueing message (not connected)');
      this.messageQueue.push(message);

      if (this.connectionState === 'disconnected') {
        await this.connect();
      }
      return;
    }

    return new Promise((resolve, reject) => {
      try {
        if (waitForAck && message.id) {
          // 设置消息确认等待
          this.pendingMessages.set(message.id, {
            resolve,
            reject,
            timestamp: Date.now(),
          });

          // 超时处理
          setTimeout(() => {
            if (this.pendingMessages.has(message.id!)) {
              this.pendingMessages.delete(message.id!);
              reject(new Error('Message acknowledgment timeout'));
            }
          }, 5000);
        }

        this.ws!.send(JSON.stringify(message));
        console.log(`[CHAINLIT-WS] 📤 Message sent: ${message.type}`);

        if (!waitForAck) {
          resolve(undefined);
        }

      } catch (error) {
        console.error('[CHAINLIT-WS] ❌ Failed to send message:', error);
        if (message.id) {
          this.pendingMessages.delete(message.id);
        }
        reject(error);
      }
    });
  }

  // 自动重连机制
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.log('[CHAINLIT-WS] ❌ Max reconnect attempts reached');
      this.setState('error');
      return;
    }

    this.reconnectAttempts++;
    chainlitPerfMonitor.recordReconnect();

    console.log(`[CHAINLIT-WS] 🔄 Scheduling reconnect #${this.reconnectAttempts} in ${this.config.reconnectInterval}ms`);

    this.setState('reconnecting');

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('[CHAINLIT-WS] ❌ Reconnect failed:', error);
      });
    }, this.config.reconnectInterval);
  }

  // 心跳机制
  private startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.connectionState === 'connected') {
        this.sendPing();
      }
    }, this.config.heartbeatInterval);
  }

  private sendPing() {
    try {
      const pingMessage: WebSocketMessage = {
        type: 'ping',
        timestamp: Date.now(),
      };
      this.ws!.send(JSON.stringify(pingMessage));
    } catch (error) {
      console.error('[CHAINLIT-WS] ❌ Failed to send ping:', error);
    }
  }

  // 清理资源
  private cleanup() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
  }

  // 刷新消息队列
  private flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.ws!.send(JSON.stringify(message));
        console.log('[CHAINLIT-WS] 📤 Queued message sent');
      }
    }
  }

  // 状态管理
  private setState(state: WebSocketConnectionState) {
    if (this.connectionState !== state) {
      console.log(`[CHAINLIT-WS] 🔄 State: ${this.connectionState} → ${state}`);
      this.connectionState = state;
      this.onStateChange?.(state);
    }
  }

  // 工具方法
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 公共API
  getConnectionState(): WebSocketConnectionState {
    return this.connectionState;
  }

  isConnected(): boolean {
    return this.connectionState === 'connected';
  }

  // 事件监听器
  onConnectionStateChange(handler: (state: WebSocketConnectionState) => void) {
    this.onStateChange = handler;
  }

  onMessageReceived(handler: (message: any) => void) {
    this.onMessage = handler;
  }

  onErrorOccurred(handler: (error: Event) => void) {
    this.onError = handler;
  }

  // 断开连接
  disconnect() {
    console.log('[CHAINLIT-WS] 🔌 Disconnecting...');
    this.cleanup();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.setState('disconnected');
    this.messageQueue = [];
    this.pendingMessages.clear();
  }
}
