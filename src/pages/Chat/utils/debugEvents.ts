/**
 * 🔥 前端事件调试工具
 * 用于调试自定义事件的分发和接收
 */

export function startEventDebugging() {
  console.log('🔥 开始前端事件调试...');
  
  // 监听所有Chainlit自定义事件
  const events = [
    'chainlit-message-update',
    'chainlit-message-complete', 
    'chainlit-artifacts-update',
    'chainlit-stream-finish'
  ];
  
  events.forEach(eventName => {
    window.addEventListener(eventName, (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log(`🎯 [EVENT-DEBUG] ${eventName}:`, customEvent.detail);
    });
  });
  
  console.log('✅ 事件监听器已设置');
}

export function stopEventDebugging() {
  console.log('🔌 停止前端事件调试');
}
