import { PageContainer, ProTable, ModalForm, ProFormText, ProFormRadio, ProFormDateRangePicker, ProFormTextArea, ProFormUploadButton, ProFormDependency, type ProTableProps } from '@ant-design/pro-components';
import { Button, Tag, Popconfirm, message, Select, Form, Modal, Breadcrumb, Tooltip, Tabs, Card, Space, Avatar, Badge, Result, Spin, Table, Upload, Input, Radio, DatePicker, Progress } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  FolderOutlined,
  CalendarOutlined,
  UserOutlined,
  FileTextOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SendOutlined,
  TableOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  PlayCircleOutlined,
  StopOutlined,
  CloudDownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  SearchOutlined
} from '@ant-design/icons';
import './index.less'
import { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { delMaterial, getMaterialByGroup, onlineMaterial, getMaterialByID, offlineMaterial, getMaterialGroup, batchDelMaterial, batchOnlineMaterial, batchOfflineMaterial, addMaterial, modifyMaterialNew } from '@/services/ant-design-pro/api';
import { history, useModel, useParams, useLocation, Helmet } from '@umijs/max';
import dayjs from 'dayjs';
import GroupDigest from '@/pages/Knowledge/KnowledgeManage/components/GroupDigest';
import OSS from 'ali-oss';

// 分组数据类型定义
interface MaterialGroupData {
  id: string;
  name: string;
  description: string;
  createDate: string;
  creator: string;
  tags: string[];
  materialCount: number;
}

// OSS配置
const OSS_CONFIG = {
  accessKeyId: 'LTAI5t79TushHxYhZXDRWvr2',
  accessKeySecret: '******************************',
  region: 'oss-cn-qingdao',
  bucket: 'mindshake-yitong',
  endpoint: 'https://oss-cn-qingdao.aliyuncs.com',
  secure: true,
};

const MaterialManage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const location = useLocation();
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {}
  const { hasLogin, cid, uid } = customData || {}

  const [materialList, setMaterialList] = useState<API.Material[]>([])
  const [materialGroup, setMaterialGroup] = useState<MaterialGroupData | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [editId, setEditId] = useState<string | undefined>(undefined);
  const [formInitialValues, setFormInitialValues] = useState<any>({});
  const actionRef = useRef<any>();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewType, setPreviewType] = useState<'TEXT' | 'IMAGE' | 'VIDEO' | undefined>();
  const [previewContent, setPreviewContent] = useState<string | undefined>(undefined);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(undefined);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  const [onlineOperatingMaterials, setOnlineOperatingMaterials] = useState<Set<string>>(new Set());
  const [offlineOperatingMaterials, setOfflineOperatingMaterials] = useState<Set<string>>(new Set());
  const modalFormRef = useRef<any>(null);
  const batchFileSelectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 批量上传相关状态
  const [batchModalOpen, setBatchModalOpen] = useState(false);
  const [batchUploadType, setBatchUploadType] = useState<'TEXT' | 'IMAGE' | 'VIDEO'>('TEXT');
  const [batchMaterialList, setBatchMaterialList] = useState<Array<{
    id: string;
    name: string;
    desc: string;
    tags: string[];
    file: File;
    parsedContent?: string;
    parseError?: string;
    validType: 'forever' | 'custom';
    validDate?: string;
    invalidDate?: string;
    uploadStatus: 'pending' | 'uploading' | 'uploaded' | 'adding' | 'success' | 'failed';
    uploadProgress: number;
    errorMessage?: string;
  }>>([]);
  const [batchSubmitting, setBatchSubmitting] = useState(false);

  // 动态列宽状态
  const [validPeriodColumnWidth, setValidPeriodColumnWidth] = useState(90);

  // 批量修改有效期状态
  const [batchValidType, setBatchValidType] = useState<'forever' | 'custom'>('forever');
  const [batchValidDate, setBatchValidDate] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null]);

  // 分页状态管理
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });



  // OSS上传结果缓存 - 页面级别缓存，避免重复上传相同文件
  const [ossUploadCache, setOssUploadCache] = useState<Map<string, {
    ossResult: {
      oss_path: string;
      oss_url: string;
      oss_expiration: string;
    };
    uploadTime: number; // 上传时间戳，用于缓存管理
  }>>(new Map());

  // 使用 useMemo 缓存过滤后的数据，避免渲染时机不一致导致的问题
  const filteredMaterials = useMemo(() => {
    // 按修改时间降序排序
    const sortedMaterialList = [...materialList].sort((a, b) => {
      const dateA = new Date(a.gmt_modified || '').getTime();
      const dateB = new Date(b.gmt_modified || '').getTime();
      return dateB - dateA; // 降序排列
    });

    return {
      all: sortedMaterialList,
      TEXT: sortedMaterialList.filter(material => material.type === 'TEXT'),
      IMAGE: sortedMaterialList.filter(material => material.type === 'IMAGE'),
      VIDEO: sortedMaterialList.filter(material => material.type === 'VIDEO'),
    };
  }, [materialList]);

  // 缓存各类型素材数量
  const materialCounts = useMemo(() => {
    return {
      all: filteredMaterials.all.length,
      TEXT: filteredMaterials.TEXT.length,
      IMAGE: filteredMaterials.IMAGE.length,
      VIDEO: filteredMaterials.VIDEO.length,
    };
  }, [filteredMaterials]);

  // 按类型获取过滤后的素材（保持向后兼容）
  const getFilteredMaterials = (type?: string) => {
    if (!type || type === 'all') return filteredMaterials.all;
    return filteredMaterials[type as keyof typeof filteredMaterials] || [];
  };

  // 获取各类型素材数量（保持向后兼容）
  const getMaterialCounts = () => {
    return materialCounts;
  };

  // 标签颜色数组
  const tagColors = [
    'blue', 'green', 'orange', 'red', 'purple',
    'cyan', 'magenta', 'volcano', 'geekblue', 'gold'
  ];

  // 根据标签位置顺序分配颜色
  const getTagColor = (tag: string, index: number): string => {
    return tagColors[index % tagColors.length];
  };

  // 统一时间显示格式
  const formatTime = (timeString: string): string => {
    return dayjs(timeString).format('YY-MM-DD HH:mm');
  };

  // 获取素材类型图标
  const getTypeIcon = (type: 'TEXT' | 'IMAGE' | 'VIDEO') => {
    switch (type) {
      case 'TEXT':
        return <FileTextOutlined style={{ color: '#1890ff' }} />;
      case 'IMAGE':
        return <FileTextOutlined style={{ color: '#52c41a' }} />; // 可以替换为图片图标
      case 'VIDEO':
        return <PlayCircleOutlined style={{ color: '#722ed1' }} />;
      default:
        return <FileTextOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  // 获取素材类型文本
  const getTypeText = (type: 'TEXT' | 'IMAGE' | 'VIDEO'): string => {
    switch (type) {
      case 'TEXT':
        return '文本';
      case 'IMAGE':
        return '图片';
      case 'VIDEO':
        return '视频';
      default:
        return type;
    }
  };

  // 获取状态信息
  const getStatusInfo = (status: string) => {
    return {
      text: status === 'ONLINE' ? '已上线' : status === 'DRAFT' ? '草稿' : status,
      dotClass: status === 'ONLINE' ? 'status-dot-online' : 'status-dot-draft'
    };
  };

  // 获取有效期状态和显示内容
  const getValidPeriodInfo = (validDate?: string, invalidDate?: string, longtime_valid?: boolean) => {
    // 如果设置为长期有效，则显示永久生效
    if (longtime_valid) {
      return {
        status: 'permanent',
        tag: '永久生效',
        tagColor: 'green',
        period: '永久有效'
      };
    }

    // 非长期有效，显示具体时间范围和状态
    const now = dayjs();
    const formatDate = (dateString: string) => dayjs(dateString).format('YY-MM-DD');

    if (invalidDate && dayjs(invalidDate).isBefore(now)) {
      return {
        status: 'expired',
        tag: '已过期',
        tagColor: 'red',
        period: `${formatDate(validDate || '')} ~ ${formatDate(invalidDate)}`
      };
    } else {
      return {
        status: 'active',
        tag: '生效中',
        tagColor: 'blue',
        period: `${formatDate(validDate || '')} ~ ${formatDate(invalidDate || '')}`
      };
    }
  };

  const updateMaterials = async () => {
    if (!groupId) return;
    setLoading(true);
    try {
      const res = await getMaterialByGroup({ group_id: groupId });
      console.log('=== 查询分组素材列表API响应 ===');
      console.log('API接口: getMaterialByGroup');
      console.log('请求参数:', { group_id: groupId });
      console.log('响应结果:', res);
      console.log('素材数据:', res.data);
      if (res.data && res.data.length > 0) {
        console.log('第一个素材的完整数据:', res.data[0]);
        console.log('第一个素材的longtime_valid字段:', res.data[0].longtime_valid);
      }
      console.log('==============================');

      // 添加状态更新的调试日志，帮助排查分页显示问题
      const newMaterialList = res.data || [];
      console.log('=== 状态更新调试 ===');
      console.log('更新前 materialList 长度:', materialList.length);
      console.log('更新后 materialList 长度:', newMaterialList.length);
      console.log('====================');

      setMaterialList(newMaterialList);

      // 数据更新后，检查分页状态是否需要调整
      setTimeout(() => {
        const currentTypeData = getFilteredMaterials(activeTab === 'all' ? undefined : activeTab);
        const maxPage = Math.ceil(currentTypeData.length / pagination.pageSize);

        if (pagination.current > maxPage && maxPage > 0) {
          setPagination(prev => ({
            ...prev,
            current: maxPage,
          }));
        }
      }, 0);
    } catch (err) {
      console.log('=== 查询分组素材列表API错误 ===');
      console.log('错误信息:', err);
      console.log('==============================');
    } finally {
      setLoading(false);
    }
  }

  const handleDelete = async (id: string) => {
    console.log('delete', id);

    try {
      const response = await delMaterial({ mid: id });
      message.success('删除成功');
      updateMaterials();
    } catch (error) {
      message.error('删除失败');
    }
  }
  const handleOnline = async (id: string) => {
    console.log('online', id);

    // 添加到上线操作中的素材集合
    setOnlineOperatingMaterials(prev => new Set(prev).add(id));

    try {
      const response = await onlineMaterial({ mid: id });
      message.success('上线成功');
      updateMaterials();
    } catch (error) {
      message.error('上线失败');
    } finally {
      // 从上线操作中的素材集合中移除
      setOnlineOperatingMaterials(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  }

  const handleOffline = async (id: string) => {
    console.log('offline', id);

    // 添加到下线操作中的素材集合
    setOfflineOperatingMaterials(prev => new Set(prev).add(id));

    try {
      const response = await offlineMaterial({ mid: id });
      message.success('下线成功');
      updateMaterials();
    } catch (error) {
      message.error('下线失败');
    } finally {
      // 从下线操作中的素材集合中移除
      setOfflineOperatingMaterials(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  }

  const handleDownload = (record: API.Material) => {
    if (record.type === 'IMAGE' && record.oss_url) {
      const link = document.createElement('a');
      link.href = record.oss_url;
      link.download = record.name || '图片素材';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (record.type === 'TEXT' && record.material_content) {
      const blob = new Blob([record.material_content], { type: 'text/plain;charset=utf-8' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = (record.name || '文本素材') + '.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } else if (record.type === 'VIDEO' && record.oss_url) {
      const link = document.createElement('a');
      link.href = record.oss_url;
      link.download = (record.name || '视频素材') + '.mp4';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleEdit = async (id: string) => {
    setEditId(id ?? undefined);
    // 不再清理OSS上传缓存，保持页面级别缓存
    await getMaterialByID({ mid: id }).then((res) => {
      const data = res.data;
      // 根据长期有效字段设置有效期类型
      let validType = data.longtime_valid ? 'forever' : 'custom';
      let date: [dayjs.Dayjs | undefined, dayjs.Dayjs | undefined] = [undefined, undefined];
      // 如果不是长期有效且有时间数据，设置时间范围
      if (!data.longtime_valid && data.valid_date && data.invalid_date) {
        date = [dayjs(data.valid_date), dayjs(data.invalid_date)];
      }
      setFormInitialValues({
        name: data.name,
        type: data.type,
        desc: data.content_digest,
        tags: Array.isArray(data.tags)
          ? data.tags
          : (typeof data.tags === 'string' && !!data.tags
            ? String(data.tags).split(',')
            : []),
        validType,
        date,
        content:
          data.type === 'IMAGE' || data.type === 'VIDEO'
            ? [
              {
                url: data.oss_url,
                name: data.name,
                status: 'done',
                uid: data.material_id,
              },
            ]
            : data.type === 'TEXT'
              ? data.material_content
              : undefined,
      });
      setModalOpen(true);
    });
  }

  const handleCreate = () => {
    setEditId(undefined);
    setFormInitialValues({ type: 'TEXT', content: undefined, validType: 'forever', date: [undefined, undefined] });
    // 不再清理OSS上传缓存，保持页面级别缓存
    setModalOpen(true);
  }

  const handleBatchCreate = () => {
    setBatchMaterialList([]);
    setBatchUploadType('TEXT');
    setBatchSubmitting(false); // 确保打开弹框时重置状态
    setBatchModalOpen(true);
  }

  // 新增：智能文本文件读取函数，支持多种编码
  const readTextFileWithEncoding = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          if (content && content.trim()) {
            resolve(content.trim());
          } else {
            reject(new Error('文件内容为空'));
          }
        } catch (error) {
          reject(new Error('文件解析失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      // 尝试多种编码
      const encodings = ['UTF-8', 'GBK', 'GB2312', 'Big5'];

      const tryNextEncoding = (index: number) => {
        if (index >= encodings.length) {
          reject(new Error('无法识别文件编码，请确保文件为UTF-8编码'));
          return;
        }

        try {
          reader.readAsText(file, encodings[index]);
        } catch (error) {
          tryNextEncoding(index + 1);
        }
      };

      tryNextEncoding(0);
    });
  };

  // 新增：统一的文档解析方法
  const parseDocumentContent = async (file: File): Promise<string> => {
    try {
      if (file.name.match(/\.(doc|docx)$/i)) {
        // Word文档解析
        const mammoth = await import('mammoth');
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.extractRawText({ arrayBuffer });

        if (result.value && result.value.trim()) {
          return result.value.trim();
        } else {
          throw new Error('Word文档中未找到可提取的文本内容');
        }
      } else if (file.name.match(/\.pdf$/i)) {
        // PDF文档解析
        if (!(window as any).pdfjsLib) {
          // 动态加载PDF.js脚本
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
          });
        }

        const pdfjsLib = (window as any).pdfjsLib;
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

        let extractedText = '';

        // 遍历所有页面提取文本
        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          try {
            const page = await pdf.getPage(pageNum);
            const textContent = await page.getTextContent();

            // 将文本项合并为字符串
            const pageText = textContent.items
              .map((item: any) => {
                return typeof item.str === 'string' ? item.str : '';
              })
              .filter((str: string) => str.trim()) // 过滤空字符串
              .join(' ')
              .replace(/\s+/g, ' ') // 合并多个空格
              .trim();

            if (pageText) {
              if (pdf.numPages > 1) {
                extractedText += `\n\n=== 第${pageNum}页 ===\n${pageText}`;
              } else {
                extractedText += pageText;
              }
            }
          } catch (pageError) {
            console.warn(`解析第${pageNum}页时出错:`, pageError);
            extractedText += `\n\n=== 第${pageNum}页解析失败 ===\n`;
          }
        }

        if (extractedText.trim()) {
          // 清理提取的文本
          return extractedText
            .replace(/^=== 第1页 ===\n/, '') // 移除第一页的标题（如果只有一页）
            .trim();
        } else {
          throw new Error('PDF文档中未找到可提取的文本内容，可能是图片或扫描件');
        }
      } else {
        // 处理文本文件（TXT、MD）- 使用智能编码检测
        return await readTextFileWithEncoding(file);
      }
    } catch (error) {
      console.error(`文档解析失败: ${file.name}`, error);
      throw new Error(`文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 批量文件选择处理
  const handleBatchFileSelect = async (files: File[], isAppend: boolean = false) => {
    console.log('=== handleBatchFileSelect 被调用 ===');
    console.log('文件数量:', files.length);
    console.log('文件名称列表:', files.map(f => f.name));
    console.log('是否追加模式:', isAppend);
    console.log('==============================');
    
    if (!files || files.length === 0) return;

    // 检查总数量限制（包括现有文件）
    const currentCount = batchMaterialList.length;
    const totalCount = isAppend ? currentCount + files.length : files.length;
    
    if (totalCount > 20) {
      message.error(`文件总数不能超过20个，当前已有${currentCount}个，本次选择${files.length}个`);
      return;
    }

    // 文件验证
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    files.forEach(file => {
      let isValid = false;
      let maxSize = 0;

      if (batchUploadType === 'TEXT') {
        const textTypes = ['text/plain', 'text/markdown', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf'];
        isValid = textTypes.includes(file.type) || !!file.name.match(/\.(txt|md|doc|docx|pdf)$/i);
        maxSize = 10 * 1024 * 1024; // 10MB
      } else if (batchUploadType === 'IMAGE') {
        const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        isValid = imageTypes.includes(file.type) || !!file.name.match(/\.(jpg|jpeg|png|gif|webp)$/i);
        maxSize = 30 * 1024 * 1024; // 30MB
      } else if (batchUploadType === 'VIDEO') {
        const videoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv', 'video/wmv', 'video/flv', 'video/webm'];
        isValid = videoTypes.includes(file.type) || !!file.name.match(/\.(mp4|mov|avi|mkv|wmv|flv|webm)$/i);
        maxSize = 500 * 1024 * 1024; // 500MB
      }

      if (!isValid) {
        invalidFiles.push(`${file.name} (格式不支持)`);
      } else if (file.size > maxSize) {
        invalidFiles.push(`${file.name} (文件过大: ${(file.size / 1024 / 1024).toFixed(2)}MB)`);
      } else {
        validFiles.push(file);
      }
    });

    // 显示验证结果
    if (invalidFiles.length > 0) {
      message.warning(`以下文件不符合要求，已跳过：${invalidFiles.slice(0, 3).join(', ')}${invalidFiles.length > 3 ? '...' : ''}`);
    }

    if (validFiles.length === 0) {
      message.error('没有符合要求的文件');
      return;
    }

    try {
      const newMaterials: Array<{
        id: string;
        name: string;
        desc: string;
        tags: string[];
        file: File;
        parsedContent?: string;
        parseError?: string;
        validType: 'forever' | 'custom';
        validDate?: string;
        invalidDate?: string;
        uploadStatus: 'pending' | 'uploading' | 'uploaded' | 'adding' | 'success' | 'failed';
        uploadProgress: number;
        errorMessage?: string;
      }> = [];

      for (const file of validFiles) {
        const fileName = file.name;
        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;

        let parsedContent = '';
        let parseError = '';

        // 文本类型时，需要解析文本类文件内容
        if (batchUploadType === 'TEXT') {
          try {
            parsedContent = await parseDocumentContent(file);

            console.log(`批量新增 - 文件 ${file.name} 读取成功:`, {
              originalLength: parsedContent?.length,
              trimmedLength: parsedContent?.trim().length,
              first100Chars: parsedContent?.substring(0, 100),
              encoding: 'auto-detected'
            });
          } catch (error) {
            console.error(`文档解析失败: ${file.name}`, error);
            parseError = `文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`;
          }
        }

        newMaterials.push({
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: nameWithoutExt,
          desc: `${nameWithoutExt}`,
          tags: [] as string[],
          file,
          parsedContent, // 保存解析后的内容
          parseError, // 保存解析错误信息
          validType: 'forever' as const,
          validDate: undefined,
          invalidDate: undefined,
          uploadStatus: parseError ? 'failed' as const : 'pending' as const,
          uploadProgress: 0,
          errorMessage: parseError || undefined,
        });
      }

      // 根据模式决定是替换还是追加
      if (isAppend) {
        setBatchMaterialList(prev => [...prev, ...newMaterials]);
      } else {
        setBatchMaterialList(newMaterials);
      }

      // 显示解析结果
      const successCount = newMaterials.filter(m => !m.parseError).length;
      const failCount = newMaterials.filter(m => m.parseError).length;

      if (failCount > 0) {
        message.warning(`文件解析完成，成功 ${successCount} 个，失败 ${failCount} 个`);
      } else {
        message.success(`文件解析完成，共解析 ${successCount} 个文件`);
      }

    } catch (error) {
      message.error('文件解析过程中发生错误');
      console.error('批量文件解析失败:', error);
    }
  };

  // 更新批量素材信息
  const handleBatchMaterialUpdate = (materialId: string, field: string, value: any) => {
    setBatchMaterialList(prev => prev.map(m =>
      m.id === materialId ? { ...m, [field]: value } : m
    ));

    // 当有效期类型改变时，根据所有行的有效期类型动态调整列宽
    if (field === 'validType') {
      setBatchMaterialList(prev => {
        const hasCustomValidType = prev.some(m => m.validType === 'custom');
        setValidPeriodColumnWidth(hasCustomValidType ? 220 : 90);
        return prev;
      });
    }
  };

  // 删除批量素材项
  const handleBatchMaterialDelete = (materialId: string) => {
    setBatchMaterialList(prev => prev.filter(m => m.id !== materialId));
  };

  // 批量修改有效期
  const handleBatchUpdateValidPeriod = () => {
    if (batchValidType === 'custom' && (!batchValidDate[0] || !batchValidDate[1])) {
      message.warning('请选择完整的日期范围');
      return;
    }

    setBatchMaterialList(prev => {
      const newList = prev.map(material => {
        const newMaterial = { ...material };

        if (batchValidType === 'forever') {
          newMaterial.validType = 'forever';
          newMaterial.validDate = undefined;
          newMaterial.invalidDate = undefined;
        } else {
          newMaterial.validType = 'custom';
          newMaterial.validDate = batchValidDate[0]?.format('YYYY-MM-DD');
          newMaterial.invalidDate = batchValidDate[1]?.format('YYYY-MM-DD');
        }

        return newMaterial;
      });

      // 根据批量修改后的结果调整列宽
      const hasCustomValidType = newList.some(m => m.validType === 'custom');
      setValidPeriodColumnWidth(hasCustomValidType ? 220 : 90);

      return newList;
    });

    message.success('批量修改有效期成功');
  };

  // 重试单个文件
  const handleRetryFile = async (materialId: string) => {
    const materialIndex = batchMaterialList.findIndex(m => m.id === materialId);
    if (materialIndex === -1) return;

    const material = batchMaterialList[materialIndex];

    // 重置状态
    setBatchMaterialList(prev => prev.map((item, index) =>
      index === materialIndex ? { ...item, uploadStatus: 'pending', uploadProgress: 0, errorMessage: undefined } : item
    ));

    // 重新处理这个文件
    try {
              const apiParams: any = {
          uid: uid,
          cid: cid,
          user_name: customData?.username || '用户',
          name: material.name,
          content_type: batchUploadType,
          content_digest: material.desc,
          group_id: groupId || '',
          tags: material.tags,
          longtime_valid: material.validType === 'forever',
          source: 'INTERNAL',
        };

      // 设置有效期时间
      if (material.validType === 'custom' && material.validDate && material.invalidDate) {
        const validDate = new Date(material.validDate + 'T00:00:00');
        const invalidDate = new Date(material.invalidDate + 'T23:59:59');
        apiParams.valid_date = validDate.toISOString();
        apiParams.invalid_date = invalidDate.toISOString();
      }

      // 更新状态为上传中
      setBatchMaterialList(prev => prev.map((item, index) =>
        index === materialIndex ? { ...item, uploadStatus: 'uploading', uploadProgress: 0 } : item
      ));

      // 根据类型处理内容
      if (batchUploadType === 'TEXT') {
        // 使用已解析的内容，如果没有则重新解析
        let content = material.parsedContent;
        if (!content) {
          content = await parseDocumentContent(material.file);
        }
        apiParams.material_content = content;

        setBatchMaterialList(prev => prev.map((item, index) =>
          index === materialIndex ? { ...item, uploadStatus: 'uploaded', uploadProgress: 50 } : item
        ));
      } else {
        const fileIdentifier = generateFileIdentifier(material.file);

        let ossResult;
        const cachedResult = ossUploadCache.get(fileIdentifier);
        if (cachedResult) {
          ossResult = cachedResult.ossResult;
        } else {
          ossResult = await uploadToOSS(material.file);
          if (!ossResult) {
            throw new Error(`文件 ${material.name} 上传失败`);
          }
        }

        apiParams.oss_url = ossResult.oss_url;
        apiParams.oss_expiration = ossResult.oss_expiration;
        apiParams.oss_path = ossResult.oss_path;

        setBatchMaterialList(prev => prev.map((item, index) =>
          index === materialIndex ? { ...item, uploadStatus: 'uploaded', uploadProgress: 50 } : item
        ));
      }

      // 更新状态为添加中
      setBatchMaterialList(prev => prev.map((item, index) =>
        index === materialIndex ? { ...item, uploadStatus: 'adding', uploadProgress: 75 } : item
      ));

      // 调用API添加素材
      const result = await addMaterial(apiParams);

      if (result.success) {
        setBatchMaterialList(prev => prev.map((item, index) =>
          index === materialIndex ? { ...item, uploadStatus: 'success', uploadProgress: 100 } : item
        ));
        // 移除单个文件重试成功的提示，避免多个提示
      } else {
        throw new Error(result.error_msg || '添加失败');
      }

    } catch (error) {
      console.error(`重试文件 ${material.name} 失败:`, error);

      setBatchMaterialList(prev => prev.map((item, index) =>
        index === materialIndex ? {
          ...item,
          uploadStatus: 'failed',
          uploadProgress: 0,
          errorMessage: error instanceof Error ? error.message : '未知错误'
        } : item
      ));
      message.error(`文件 ${material.name} 重试失败`);
    }
  };

  // 批量提交素材
  const handleBatchSubmit = async () => {
    if (batchMaterialList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    // 检查是否有解析失败的文件
    const failedFiles = batchMaterialList.filter(m => m.parseError);
    if (failedFiles.length > 0) {
      message.warning(`有 ${failedFiles.length} 个文件解析失败，请重新选择或检查文件格式`);
      return;
    }

    // 筛选出需要上传的文件（失败和待上传的）
    const filesToUpload = batchMaterialList.filter(m => 
      m.uploadStatus === 'pending' || m.uploadStatus === 'failed'
    );

    if (filesToUpload.length === 0) {
      message.info('所有文件都已上传成功，无需重复上传');
      return;
    }

    setBatchSubmitting(true);

    // 逐个处理需要上传的文件，显示进度
    for (let i = 0; i < batchMaterialList.length; i++) {
      const material = batchMaterialList[i];
      
      // 跳过已成功的文件
      if (material.uploadStatus === 'success') {
        continue;
      }

      // 更新状态为上传中
      setBatchMaterialList(prev => prev.map((item, index) =>
        index === i ? { ...item, uploadStatus: 'uploading', uploadProgress: 0, errorMessage: undefined } : item
      ));

      try {
        const apiParams: any = {
          uid: uid,
          cid: cid,
          user_name: customData?.username || '用户',
          name: material.name,
          content_type: batchUploadType,
          content_digest: material.desc,
          group_id: groupId || '',
          tags: material.tags,
          longtime_valid: material.validType === 'forever',
          source: 'INTERNAL',
        };

        // 设置有效期时间
        if (material.validType === 'custom' && material.validDate && material.invalidDate) {
          const validDate = new Date(material.validDate + 'T00:00:00');
          const invalidDate = new Date(material.invalidDate + 'T23:59:59');
          apiParams.valid_date = validDate.toISOString();
          apiParams.invalid_date = invalidDate.toISOString();
        }

        // 根据类型处理内容
        if (batchUploadType === 'TEXT') {
          // 使用已解析的内容
          apiParams.material_content = processTextContent(material.parsedContent || '');

          // 更新进度为50%（内容已解析）
          setBatchMaterialList(prev => prev.map((item, index) =>
            index === i ? { ...item, uploadStatus: 'uploaded', uploadProgress: 50 } : item
          ));
        } else {
          // 图片/视频类型：上传到OSS
          const fileIdentifier = generateFileIdentifier(material.file);

          let ossResult;

          // 检查缓存
          const cachedResult = ossUploadCache.get(fileIdentifier);
          if (cachedResult) {
            ossResult = cachedResult.ossResult;
          } else {
            // 上传到OSS
            ossResult = await uploadToOSS(material.file);

            if (!ossResult) {
              throw new Error(`文件 ${material.name} 上传失败`);
            }
          }

          apiParams.oss_url = ossResult.oss_url;
          apiParams.oss_expiration = ossResult.oss_expiration;
          apiParams.oss_path = ossResult.oss_path;

          // 更新进度为50%（OSS上传完成）
          setBatchMaterialList(prev => prev.map((item, index) =>
            index === i ? { ...item, uploadStatus: 'uploaded', uploadProgress: 50 } : item
          ));
        }

        // 更新状态为添加中
        setBatchMaterialList(prev => prev.map((item, index) =>
          index === i ? { ...item, uploadStatus: 'adding', uploadProgress: 75 } : item
        ));

        // 调用API添加素材
        const result = await addMaterial(apiParams);

        if (result.success) {
          // 更新状态为成功
          setBatchMaterialList(prev => prev.map((item, index) =>
            index === i ? { ...item, uploadStatus: 'success', uploadProgress: 100 } : item
          ));
        } else {
          throw new Error(result.error_msg || '添加失败');
        }

      } catch (error) {
        console.error(`处理文件 ${material.name} 失败:`, error);

        // 更新状态为失败
        setBatchMaterialList(prev => prev.map((item, index) =>
          index === i ? {
            ...item,
            uploadStatus: 'failed',
            uploadProgress: 0,
            errorMessage: error instanceof Error ? error.message : '未知错误'
          } : item
        ));
      }
    }

    // 使用 setState 的回调函数来确保获取最新状态
    setBatchMaterialList(prev => {
      const successCount = prev.filter(item => item.uploadStatus === 'success').length;
      const failCount = prev.filter(item => item.uploadStatus === 'failed').length;
      const pendingCount = prev.filter(item => item.uploadStatus === 'pending').length;

      if (failCount === 0 && pendingCount === 0) {
        message.success(`批量新增成功，共添加 ${successCount} 个素材`);
        // 不自动关闭弹框，让用户手动关闭
        updateMaterials();
      } else {
        message.warning(`批量新增完成，成功 ${successCount} 个，失败 ${failCount} 个，待上传 ${pendingCount} 个`);
      }

      return prev; // 返回原状态，不改变
    });

    // 重置batchSubmitting状态，让按钮恢复正常，但保持上传状态列显示
    setBatchSubmitting(false);
  };

  // 处理文件上传和内容解析（参考知识详情页实现）
  const handleFileUpload = (file: any): boolean => {
    const fileTypes = ['text/plain', 'text/markdown', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    // 检查文件类型
    if (!fileTypes.includes(file.type) && !file.name.match(/\.(txt|md|doc|docx|pdf)$/i)) {
      message.error('请上传支持的文档格式：TXT、MD、DOC、DOCX、PDF');
      return false;
    }

    // 检查文件大小
    if (file.size > maxSize) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    // 根据文件类型选择不同的解析方式
    if (file.name.match(/\.(doc|docx)$/i)) {
      handleWordUpload(file);
    } else if (file.name.match(/\.pdf$/i)) {
      handlePDFUpload(file);
    } else {
      // 处理文本文件（TXT、MD）
      handleTextUpload(file);
    }

    return false; // 阻止自动上传
  };

  // 处理Word文档上传
  const handleWordUpload = async (file: any): Promise<void> => {
    try {
      const content = await parseDocumentContent(file);
      handleParsedContent(content, file.name);
      autoFillMaterialInfo(file.name);

      // 如果有警告信息，显示给用户（Word文档特有的功能）
      // 注意：这里无法获取mammoth的警告信息，因为parseDocumentContent是通用方法
      // 如果需要显示警告，可以在parseDocumentContent中返回警告信息
    } catch (error) {
      console.error('Word文档解析失败:', error);
      message.error('Word文档解析失败，请检查文件是否损坏或为支持的格式');
    }
  };

  // 处理PDF文档上传
  const handlePDFUpload = async (file: any): Promise<void> => {
    try {
      console.log('开始解析PDF文件:', file.name);

      const content = await parseDocumentContent(file);
      handleParsedContent(content, file.name);
      autoFillMaterialInfo(file.name);

      console.log('PDF解析成功，提取文本长度:', content.length);
    } catch (error) {
      console.error('PDF文档解析失败:', error);
      // 提供更详细的错误信息
      let errorMessage = 'PDF文档解析失败';
      if (error instanceof Error) {
        if (error.message.includes('Invalid PDF')) {
          errorMessage = 'PDF文件格式无效或已损坏';
        } else if (error.message.includes('password')) {
          errorMessage = 'PDF文件受密码保护，请先解除保护';
        } else if (error.message.includes('Failed to load')) {
          errorMessage = 'PDF文件加载失败，请检查文件是否完整';
        } else {
          errorMessage = `PDF解析失败: ${error.message}`;
        }
      }
      message.error(errorMessage);
    }
  };

  // 处理文本文件上传
  const handleTextUpload = (file: any): void => {
    parseDocumentContent(file)
      .then((content) => {
        console.log(`单条新增 - 文件 ${file.name} 读取成功:`, {
          originalLength: content?.length,
          trimmedLength: content?.trim().length,
          first100Chars: content?.substring(0, 100),
          encoding: 'auto-detected'
        });
        handleParsedContent(content, file.name);
        autoFillMaterialInfo(file.name);
      })
      .catch((error) => {
        console.error('文本文件解析失败:', error);
        message.error('文件解析失败，请检查文件格式');
      });
  };

  // 处理解析后的内容
  const handleParsedContent = (parsedContent: string, fileName: string): void => {
    if (!modalFormRef.current) {
      message.error('无法找到表单实例');
      return;
    }

    const currentContent = modalFormRef.current.getFieldValue('content') || '';

    // 如果当前有内容，询问用户是替换还是追加
    if (currentContent.trim()) {
      Modal.confirm({
        title: '文档内容处理',
        content: '当前已有文本内容，请选择处理方式：',
        okText: '替换现有内容',
        cancelText: '追加到末尾',
        onOk: () => {
          // 替换内容
          modalFormRef.current?.setFieldsValue({ content: parsedContent });
          message.success(`已成功解析并替换文本内容（${fileName}）`);
        },
        onCancel: () => {
          // 追加内容
          const newContent = currentContent + '\n\n' + parsedContent;
          modalFormRef.current?.setFieldsValue({ content: newContent });
          message.success(`已成功解析并追加文本内容（${fileName}）`);
        },
      });
    } else {
      // 没有现有内容，直接设置
      modalFormRef.current.setFieldsValue({ content: parsedContent });
      message.success(`已成功解析文本内容（${fileName}）`);
    }
  };

  // 自动填充素材名称和描述
  const autoFillMaterialInfo = (fileName: string): void => {
    if (!modalFormRef.current) {
      return;
    }

    // 获取文件名（不含扩展名）
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
    
    // 检查当前表单中是否已有名称和描述
    const currentName = modalFormRef.current.getFieldValue('name');
    const currentDesc = modalFormRef.current.getFieldValue('desc');
    
    // 如果名称和描述都为空，则自动填充
    if (!currentName && !currentDesc) {
      modalFormRef.current.setFieldsValue({
        name: nameWithoutExt,
        desc: nameWithoutExt
      });
    } else if (!currentName) {
      // 只有名称为空时填充名称
      modalFormRef.current.setFieldsValue({
        name: nameWithoutExt
      });
    } else if (!currentDesc) {
      // 只有描述为空时填充描述
      modalFormRef.current.setFieldsValue({
        desc: nameWithoutExt
      });
    }
  };

  // 处理文本内容的通用函数（用于批量新增）
  const processTextContent = (content: string): string => {
    // 确保内容经过与单条新增相同的处理
    console.log('批量新增 - 处理前内容长度:', content.length);
    console.log('批量新增 - 处理前内容前100字符:', content.substring(0, 100));
    const processed = content.trim();
    console.log('批量新增 - 处理后内容长度:', processed.length);
    console.log('批量新增 - 处理后内容前100字符:', processed.substring(0, 100));
    return processed;
  };

  // 生成文件唯一标识（用于缓存判断）
  const generateFileIdentifier = (file: File) => {
    return `${file.name}_${file.size}_${file.lastModified}_${file.type}`;
  };

  // 生成唯一的文件key
  const generateFileKey = (fileName: string, contentType: string) => {
    // 清理文件名，移除特殊字符
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9.\u4e00-\u9fa5_-]/g, '_');

    // 原文件名 + 时间戳
    const timestamp = Date.now();
    const nameWithoutExt = cleanFileName.substring(0, cleanFileName.lastIndexOf('.')) || cleanFileName;
    const ext = cleanFileName.substring(cleanFileName.lastIndexOf('.') + 1) || 'unknown';
    return `material/${contentType}/${nameWithoutExt}_${timestamp}.${ext}`;
  };

  // 上传文件到OSS
  const uploadToOSS = async (file: File) => {
    try {
      console.log('开始上传文件:', file.name, '文件类型:', file.type, '文件大小:', file.size);

      // 文件大小检查
      const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.size > MAX_FILE_SIZE) {
        message.error('文件大小不能超过100MB');
        return null;
      }

      const client = new OSS(OSS_CONFIG);

      // 判断文件类型
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');

      if (!isImage && !isVideo) {
        message.error('只支持上传图片和视频文件');
        return null;
      }

      // 额外的图片格式检查
      if (isImage) {
        const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedImageTypes.includes(file.type)) {
          message.error('图片格式仅支持 JPEG、PNG、GIF、WebP');
          return null;
        }
      }

      const contentType = isImage ? 'IMAGE' : 'VIDEO';
      const key = generateFileKey(file.name, contentType);

      console.log('生成的OSS key:', key);

      // 设置上传参数
      const options = {
        headers: {
          'Content-Type': file.type,
          'Cache-Control': 'public, max-age=31536000', // 1年缓存
        },
      };

      // 上传文件
      console.log('开始上传到OSS...');
      const result = await client.put(key, file, options);

      console.log('上传结果:', result);

      if (result.res && result.res.status === 200) {
        // 生成预签名URL，有效期7天
        const signedUrl = client.signatureUrl(key, {
          expires: 604800, // 7天
        });

        console.log('生成的预签名URL:', signedUrl);

        // 计算过期时间 (当前时间 + 7天)
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7);

        const ossResult = {
          oss_path: key,
          oss_url: signedUrl,
          oss_expiration: expirationDate.toISOString(),
        };

        // 缓存上传结果到Map中
        const fileIdentifier = generateFileIdentifier(file);
        setOssUploadCache(prevCache => {
          const newCache = new Map(prevCache);
          newCache.set(fileIdentifier, {
            ossResult: ossResult,
            uploadTime: Date.now()
          });
          return newCache;
        });

        return ossResult;
      } else {
        console.error('上传响应状态异常:', result);
        message.error('上传失败：响应状态异常');
        return null;
      }
    } catch (error) {
      console.error('上传失败详细信息:', error);

      let errorMessage = '未知错误';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = (error as any).message || (error as any).code || '上传请求失败';
      }

      message.error(`上传失败: ${errorMessage}`);
      return null;
    }
  };

  // 日期兼容函数
  const getDateString = (val: any) => {
    if (!val) return '';
    if (typeof val === 'string') return val.replace(' ', 'T');
    if (typeof val.format === 'function') {
      // 转换为ISO格式
      return val.toISOString();
    }
    if (val instanceof Date) {
      return val.toISOString();
    }
    return '';
  };

  const handleFinish = async (values: any) => {
    try {
      // 准备基础数据
      let tagsArr = values.tags;
      if (typeof tagsArr === 'string') tagsArr = tagsArr.split(' ');
      if (!Array.isArray(tagsArr)) tagsArr = [];

      // 准备API参数
      const apiParams: any = {
        uid: uid,
        cid: cid,
        user_name: customData?.username || '用户',
        name: values.name,
        content_type: values.type,
        content_digest: values.desc,
        group_id: groupId || '',
        tags: Array.isArray(tagsArr) ? tagsArr : [],
        longtime_valid: values.validType === 'forever',
        source: 'INTERNAL',
      };

      // 设置material_id (修改时需要，新增时不设置)
      if (editId) {
        apiParams.material_id = editId;
      }

      // 有效期处理
      if (values.validType === 'custom' && values.date && values.date.length === 2) {
        apiParams.valid_date = getDateString(values.date[0]);
        apiParams.invalid_date = getDateString(values.date[1]);
      } else if (values.validType === 'forever') {
        // 永久有效时不设置时间字段
      } else {
        // 如果没有设置时间，提供默认值
        const now = new Date();
        const futureDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 1年后
        apiParams.valid_date = now.toISOString();
        apiParams.invalid_date = futureDate.toISOString();
      }

      // 处理不同类型的素材内容
      if (values.type === 'TEXT') {
        // 文本类型：直接设置内容
        apiParams.material_content = values.content;
      } else if ((values.type === 'IMAGE' || values.type === 'VIDEO') && values.content?.[0]?.originFileObj) {
        // 图片/视频类型：检查缓存或上传到OSS
        const currentFile = values.content[0].originFileObj;
        const currentFileIdentifier = generateFileIdentifier(currentFile);

        let ossResult;

        // 检查是否有缓存的上传结果
        const cachedResult = ossUploadCache.get(currentFileIdentifier);
        if (cachedResult) {
          console.log('=== 使用缓存的OSS上传结果 ===');
          console.log('文件标识:', currentFileIdentifier);
          console.log('缓存结果:', cachedResult.ossResult);
          console.log('上传时间:', new Date(cachedResult.uploadTime).toLocaleString());
          console.log('=============================');

          ossResult = cachedResult.ossResult;
          // 使用缓存时不显示提示消息，让缓存机制对用户透明
        } else {
          // 需要重新上传到OSS
          console.log('=== 开始上传新文件到OSS ===');
          console.log('文件标识:', currentFileIdentifier);
          console.log('==========================');

          message.loading('正在上传文件到OSS...', 0);
          ossResult = await uploadToOSS(currentFile);
          message.destroy(); // 关闭loading消息

          if (!ossResult) {
            message.error('文件上传失败，请重试');
            return false;
          }
        }

        // 设置OSS相关参数
        apiParams.oss_url = ossResult.oss_url;
        apiParams.oss_expiration = ossResult.oss_expiration;
        apiParams.oss_path = ossResult.oss_path;
      }

      console.log('=== 素材API调用 ===');
      console.log('操作类型:', editId ? '修改' : '新增');
      console.log('表单原始数据:', values);
      console.log('处理后的tags:', tagsArr);
      console.log('API参数:', JSON.stringify(apiParams, null, 2));
      console.log('==================');

      // 调用对应的API
      let result;
      if (editId) {
        // 修改素材
        result = await modifyMaterialNew(apiParams);
      } else {
        // 新增素材
        result = await addMaterial(apiParams);
      }

      console.log('=== 素材API响应 ===');
      console.log('响应结果:', result);
      console.log('==================');

      if (result.success) {
        message.success(editId ? '更新成功' : '新增成功');
        setModalOpen(false);
        // 保持OSS上传缓存，不清理
        updateMaterials();
        return true;
      } else {
        message.error(result.error_msg || (editId ? '更新失败' : '新增失败'));
        return false;
      }

    } catch (error: any) {
      console.log('=== 素材API错误 ===');
      console.log('错误对象:', error);
      console.log('错误响应:', error.response?.data);
      console.log('错误状态:', error.response?.status);
      console.log('==================');

      let errorMessage = editId ? '更新失败' : '新增失败';
      if (error.response?.data?.error_msg) {
        errorMessage = error.response.data.error_msg;
      } else if (error.response?.status === 422) {
        errorMessage = '请求参数格式错误，请检查输入信息';
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      return false;
    }
  }

  const handlePreview = (record: API.Material) => {
    setPreviewType(record.type as any);
    if (record.type === 'TEXT') {
      setPreviewContent(record.material_content);
      setPreviewUrl(undefined);
    } else if (record.type === 'IMAGE' || record.type === 'VIDEO') {
      setPreviewContent(undefined);
      setPreviewUrl(record.oss_url || undefined);
    }
    setPreviewOpen(true);
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的素材', 2);
      return;
    }

    try {
      setBatchLoading(true);
      const mids = selectedRowKeys.map(key => String(key));

      const response = await batchDelMaterial({ mids });

      if (response.success) {
        message.success(`成功删除 ${selectedRowKeys.length} 个素材`, 2);
        setSelectedRowKeys([]);
        updateMaterials();
      } else {
        message.error(response.error_msg || '批量删除失败', 2);
      }
    } catch (error) {
      message.error('批量删除失败', 2);
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量上线
  const handleBatchOnline = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要上线的素材', 2);
      return;
    }

    // 过滤出草稿状态的素材
    const draftMaterials = materialList.filter(material =>
      selectedRowKeys.includes(material.material_id) && material.status === 'DRAFT'
    );

    if (draftMaterials.length === 0) {
      message.warning('选中的素材中没有草稿状态的素材，无法上线', 2);
      return;
    }

    if (draftMaterials.length !== selectedRowKeys.length) {
      message.warning(`只有草稿状态的素材才能上线，将上线 ${draftMaterials.length} 个草稿素材`, 3);
    }

    try {
      setBatchLoading(true);
      const mids = draftMaterials.map(material => material.material_id);

      const response = await batchOnlineMaterial({ mids });

      if (response.success) {
        message.success(`成功上线 ${draftMaterials.length} 个素材`, 2);
        setSelectedRowKeys([]);
        updateMaterials();
      } else {
        message.error(response.error_msg || '批量上线失败', 2);
      }
    } catch (error) {
      message.error('批量上线失败', 2);
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量下线
  const handleBatchOffline = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要下线的素材', 2);
      return;
    }

    // 过滤出已上线状态的素材
    const onlineMaterials = materialList.filter(material =>
      selectedRowKeys.includes(material.material_id) && material.status === 'ONLINE'
    );

    if (onlineMaterials.length === 0) {
      message.warning('选中的素材中没有已上线状态的素材，无法下线', 2);
      return;
    }

    if (onlineMaterials.length !== selectedRowKeys.length) {
      message.warning(`只有已上线状态的素材才能下线，将下线 ${onlineMaterials.length} 个已上线素材`, 3);
    }

    try {
      setBatchLoading(true);
      const mids = onlineMaterials.map(material => material.material_id);

      const response = await batchOfflineMaterial({ mids });

      if (response.success) {
        message.success(`成功下线 ${onlineMaterials.length} 个素材`, 2);
        setSelectedRowKeys([]);
        updateMaterials();
      } else {
        message.error(response.error_msg || '批量下线失败', 2);
      }
    } catch (error) {
      message.error('批量下线失败', 2);
    } finally {
      setBatchLoading(false);
    }
  };

  // 选择变化处理
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 从location.state获取分组信息，参考知识管理的实现
  useEffect(() => {
    const groupData = location.state as MaterialGroupData;
    if (groupData) {
      setMaterialGroup(groupData);
    } else {
      // 如果没有通过state传递，使用默认值
      setMaterialGroup({
        id: groupId || '',
        name: '素材分组',
        description: '暂无分组描述',
        createDate: '',
        creator: '未知用户',
        tags: [],
        materialCount: 0
      });
    }
  }, [location.state, groupId]);

  useEffect(() => {
    console.log(groupId)
    if (groupId) {
      updateMaterials();
    }
  }, [groupId])

  useEffect(() => {
    if (!hasLogin) {
      history.push('/user/login')
    }
  }, [])

  // 当切换标签页时，确保分页状态合理
  useEffect(() => {
    const currentTypeData = getFilteredMaterials(activeTab === 'all' ? undefined : activeTab);
    const maxPage = Math.ceil(currentTypeData.length / pagination.pageSize);

    // 如果当前页码超出了数据范围，回到第一页
    if (pagination.current > maxPage && maxPage > 0) {
      setPagination(prev => ({
        ...prev,
        current: 1,
      }));
    }

    // 切换标签页时清空选择状态，避免选中的素材在新标签页中不可见
    setSelectedRowKeys([]);
  }, [activeTab, materialList, pagination.pageSize]);



  // 为批量上传文件创建预览URL的缓存
  const [filePreviewUrls, setFilePreviewUrls] = useState<Map<string, string>>(new Map());

  // 获取文件预览URL
  const getFilePreviewUrl = (fileId: string, file: File): string => {
    if (filePreviewUrls.has(fileId)) {
      return filePreviewUrls.get(fileId)!;
    }

    const url = URL.createObjectURL(file);
    setFilePreviewUrls(prev => new Map(prev).set(fileId, url));
    return url;
  };

  // 清理预览URL
  useEffect(() => {
    return () => {
      filePreviewUrls.forEach(url => {
        URL.revokeObjectURL(url);
      });
    };
  }, []);

  // 当批量上传列表变化时，清理不再需要的URL
  useEffect(() => {
    const currentFileIds = new Set(batchMaterialList.map(m => m.id));
    const urlsToRevoke: string[] = [];

    filePreviewUrls.forEach((url, fileId) => {
      if (!currentFileIds.has(fileId)) {
        urlsToRevoke.push(url);
        URL.revokeObjectURL(url);
      }
    });

    if (urlsToRevoke.length > 0) {
      setFilePreviewUrls(prev => {
        const newMap = new Map(prev);
        urlsToRevoke.forEach(url => {
          const entryToDelete = Array.from(newMap.entries()).find(([, value]) => value === url);
          if (entryToDelete) {
            newMap.delete(entryToDelete[0]);
          }
        });
        return newMap;
      });
    }
  }, [batchMaterialList]);

  // 添加调试用的 effect，监控数据状态变化
  useEffect(() => {
    console.log('=== 素材数据状态变化 ===');
    console.log('materialList 长度:', materialList.length);
    console.log('materialCounts:', materialCounts);
    console.log('当前标签页:', activeTab);
    console.log('分页状态:', pagination);
    console.log('========================');
  }, [materialList, materialCounts, activeTab, pagination])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (batchFileSelectTimeoutRef.current) {
        clearTimeout(batchFileSelectTimeoutRef.current);
      }
    };
  }, []);

  const columns: ColumnsType<API.Material> = [
    {
      title: '内容预览',
      dataIndex: 'preview',
      key: 'preview',
      width: 120,
      render: (_: any, record: API.Material) => {
        if (record.type === 'TEXT') {
          const content = record.material_content || '';
          const shortContent = content.length > 20 ? content.slice(0, 20) + '...' : content;

          // 对于Tooltip显示的内容，限制在200个字符内，并保持换行
          const tooltipContent = content.length > 200
            ? content.slice(0, 200) + '...'
            : content;

          return (
            <Tooltip
              placement="topLeft"
              title={
                <div style={{
                  maxWidth: '400px',
                  maxHeight: '300px',
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  lineHeight: 1.4
                }}>
                  {tooltipContent}
                </div>
              }
              overlayStyle={{ maxWidth: '450px' }}
            >
              <div className="preview-text" onClick={() => handlePreview(record)}>
                {shortContent}
              </div>
            </Tooltip>
          );
        } else if (record.type === 'IMAGE' && record.oss_url) {
          return (
            <img
              src={record.oss_url}
              alt="预览"
              className="preview-image"
              onClick={() => handlePreview(record)}
            />
          );
        } else if (record.type === 'VIDEO' && record.oss_url) {
          // 如果有视频首帧预览图，显示预览图
          if (record.oss_preview_url) {
            return (
              <div
                className="preview-video-with-thumbnail"
                onClick={() => handlePreview(record)}
                style={{
                  position: 'relative',
                  width: '80px',
                  height: '60px',
                  borderRadius: '4px',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  border: '1px solid #f0f0f0'
                }}
              >
                <img
                  src={record.oss_preview_url}
                  alt="视频预览"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
                {/* 播放图标覆盖层 */}
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: 0,
                  height: 0,
                  borderLeft: '8px solid rgba(255,255,255,0.9)',
                  borderTop: '6px solid transparent',
                  borderBottom: '6px solid transparent',
                  borderRadius: '2px',
                  filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'
                }} />
                {/* 视频标识 */}
                <div style={{
                  position: 'absolute',
                  bottom: 2,
                  right: 2,
                  fontSize: 8,
                  background: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '1px 3px',
                  borderRadius: 2,
                  fontWeight: 'bold'
                }}>
                  VIDEO
                </div>
              </div>
            );
          } else {
            // 没有预览图时显示原来的兜底图
            return (
              <div
                className="preview-video"
                onClick={() => handlePreview(record)}
              >
                <div style={{
                  width: 0,
                  height: 0,
                  borderLeft: '8px solid #666',
                  borderTop: '6px solid transparent',
                  borderBottom: '6px solid transparent',
                  marginLeft: '4px'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: 4,
                  right: 4,
                  fontSize: 10,
                  background: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '1px 3px',
                  borderRadius: 2
                }}>
                  MP4
                </div>
              </div>
            );
          }
        }
        return <span style={{ color: '#999' }}>无预览</span>;
      },
    },
    {
      title: '素材名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="搜索素材名称"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button onClick={() => clearFilters && clearFilters()} size="small" style={{ width: 90 }}>
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      onFilter: (value: boolean | React.Key, record: API.Material) => {
        const searchValue = String(value).toLowerCase();
        return record.name?.toLowerCase().includes(searchValue) || false;
      },
      onFilterDropdownVisibleChange: (visible: boolean) => {
        if (visible) {
          // 可以在这里添加额外的逻辑
        }
      },
      render: (text: string, record: API.Material) => (
        <div className="material-name">
          <Tooltip title={text} placement="topLeft">
            <div className="name-text">{text}</div>
          </Tooltip>
          <div className="material-tags">
            {/* 类型标签 - 始终显示在第一位 */}
            <Tag color={getTagColor(getTypeText(record.type as any), 0)}>
              {getTypeText(record.type as any)}
            </Tag>
            {/* 其他标签 */}
            {record.tags && record.tags.length > 0 && (
              <>
                {record.tags.slice(0, 1).map((tag: string, index: number) => (
                  <Tag key={tag} color={getTagColor(tag, index + 1)}>
                    {tag}
                  </Tag>
                ))}
                {record.tags.length > 1 && (
                  <Tooltip
                    title={
                      <div>
                        {record.tags.slice(1).map((tag: string, index: number) => (
                          <Tag key={tag} color={getTagColor(tag, index + 2)} style={{ margin: '2px' }}>
                            {tag}
                          </Tag>
                        ))}
                      </div>
                    }
                    placement="topLeft"
                  >
                    <Tag color="default" className="more-tags">
                      +{record.tags.length - 1}
                    </Tag>
                  </Tooltip>
                )}
              </>
            )}
          </div>
        </div>
      )
    },
    {
      title: '创建信息',
      key: 'createInfo',
      width: 180,
      sorter: (a: API.Material, b: API.Material) => {
        const dateA = new Date(a.gmt_modified || '').getTime();
        const dateB = new Date(b.gmt_modified || '').getTime();
        return dateA - dateB;
      },
      sortDirections: ['ascend', 'descend'],
      render: (_: any, record: API.Material) => (
        <div className="create-info">
          <div className="creator-section">
            <Avatar
              size={24}
              style={{
                backgroundColor: '#1890ff',
                fontSize: '12px',
                marginRight: 8
              }}
            >
              {(record.user_name || '未知').charAt(0)}
            </Avatar>
            <span className="creator-name">{record.user_name || '未知'}</span>
          </div>
          <div className="time-section">
            <div className="time-item">
              <ClockCircleOutlined className="time-icon" />
              <span className="time-text">{formatTime(record.gmt_create || '')}</span>
            </div>
            <div className="time-item update-time">
              <SyncOutlined className="time-icon" />
              <span className="time-text">{formatTime(record.gmt_modified || '')}</span>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '素材描述',
      dataIndex: 'content_digest',
      key: 'content_digest',
      width: 150,
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <div className="summary-text">{text || '暂无描述'}</div>
        </Tooltip>
      )
    },
    {
      title: '素材类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: 'TEXT' | 'IMAGE' | 'VIDEO') => (
        <div className="type-info">
          {getTypeIcon(type)}
          <span style={{ marginLeft: 6 }}>
            {getTypeText(type)}
          </span>
        </div>
      )
    },
    {
      title: '有效期',
      key: 'validPeriod',
      width: 90,
      render: (_: any, record: API.Material) => {
        const periodInfo = getValidPeriodInfo(record.valid_date, record.invalid_date, record.longtime_valid);
        return (
          <div className="valid-period-wrapper">
            <Tag color={periodInfo.tagColor} className="period-status-tag">
              {periodInfo.tag}
            </Tag>
            <div className="period-text">
              {periodInfo.period}
            </div>
          </div>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '已上线', value: 'ONLINE' },
        { text: '草稿', value: 'DRAFT' },
      ],
      onFilter: (value: boolean | React.Key, record: API.Material) => {
        return record.status === String(value);
      },
      render: (status: string) => {
        const statusInfo = getStatusInfo(status);
        return (
          <div className="status-wrapper">
            <span className={`status-dot ${statusInfo.dotClass}`}></span>
            <span className="status-text">{statusInfo.text}</span>
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right' as const,
      render: (_: any, record: API.Material) => {
        const isOnlineOperating = onlineOperatingMaterials.has(record.material_id);
        const isOfflineOperating = offlineOperatingMaterials.has(record.material_id);
        const isAnyOperating = isOnlineOperating || isOfflineOperating;

        return (
          <Space size="small">
            <Tooltip title="预览" placement="top">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                disabled={isAnyOperating}
                onClick={() => handlePreview(record)}
              />
            </Tooltip>
            <Tooltip title="编辑" placement="top">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                disabled={isAnyOperating}
                onClick={() => handleEdit(record.material_id)}
              />
            </Tooltip>
            {record.status === 'DRAFT' && (
              <Tooltip title="上线" placement="top">
                <Button
                  type="text"
                  size="small"
                  icon={<SendOutlined />}
                  loading={isOnlineOperating}
                  onClick={() => handleOnline(record.material_id)}
                />
              </Tooltip>
            )}
            {record.status === 'ONLINE' && (
              <Tooltip title="下线" placement="top">
                <Button
                  type="text"
                  size="small"
                  icon={<StopOutlined />}
                  loading={isOfflineOperating}
                  onClick={() => handleOffline(record.material_id)}
                />
              </Tooltip>
            )}
            <Tooltip title="下载" placement="top">
              <Button
                type="text"
                size="small"
                icon={<CloudDownloadOutlined />}
                disabled={isAnyOperating}
                onClick={() => handleDownload(record)}
              />
            </Tooltip>
            <Popconfirm
              title="确认删除"
              description={`确定要删除素材"${record.name}"吗？此操作不可恢复。`}
              onConfirm={() => handleDelete(record.material_id)}
              okText="确定"
              cancelText="取消"
              placement="left"
            >
              <Tooltip title="删除" placement="top">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  disabled={isAnyOperating}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      }
    }
  ];



  // 渲染空状态组件
  const renderEmptyState = () => (
    <div
      className="empty-state-container"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '60px 0',
        backgroundColor: '#fff',
        borderRadius: '8px',
        border: '1px solid #f0f0f0',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
      }}
    >
      <Result
        status="404"
        title="暂无素材"
        subTitle="该素材组暂时没有内容，点击「新增素材」创建您的第一个素材吧~"
        extra={
          <Space>
            <Button
              icon={<PlusOutlined />}
              size="large"
              onClick={handleCreate}
            >
              新增素材
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              onClick={handleBatchCreate}
            >
              批量新增
            </Button>
          </Space>
        }
      />
    </div>
  );

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.Material) => ({
      name: record.name,
    }),
  };

  const hasSelected = selectedRowKeys.length > 0;
  const hasDraftSelected = selectedRowKeys.some(id => {
    const record = materialList.find(item => item.material_id === String(id));
    return record && record.status === 'DRAFT';
  });
  const hasOnlineSelected = selectedRowKeys.some(id => {
    const record = materialList.find(item => item.material_id === String(id));
    return record && record.status === 'ONLINE';
  });

  // 使用 useCallback 缓存渲染表格函数，确保引用稳定
  const renderTableOrEmpty = useCallback((filteredData: API.Material[]) => {
    // 添加调试信息
    console.log('=== renderTableOrEmpty 被调用 ===');
    console.log('传入的 filteredData 长度:', filteredData.length);
    console.log('当前 activeTab:', activeTab);
    console.log('当前 pagination:', pagination);
    console.log('前5条数据:', filteredData.slice(0, 5));
    console.log('==============================');

    if (filteredData.length === 0) {
      // 如果当前筛选结果为空，显示筛选无结果的提示
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '40px 0',
            backgroundColor: '#fff',
            borderRadius: '8px',
            color: '#999'
          }}
        >
          该类型暂无素材
        </div>
      );
    } else {
      // 有数据时显示表格
      return (
        <div className="material-list">
          {hasSelected && (
            <div className="batch-operations">
              <Space>
                <span className="selected-info">
                  已选择 {selectedRowKeys.length} 项
                </span>
                {hasDraftSelected && (
                  <Popconfirm
                    title="批量上线"
                    description="确定要将选中的草稿状态素材批量上线吗？"
                    onConfirm={handleBatchOnline}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="primary"
                      icon={<SendOutlined />}
                      loading={batchLoading}
                    >
                      批量上线
                    </Button>
                  </Popconfirm>
                )}
                {hasOnlineSelected && (
                  <Popconfirm
                    title="批量下线"
                    description="确定要将选中的已上线状态素材批量下线吗？"
                    onConfirm={handleBatchOffline}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      icon={<StopOutlined />}
                      loading={batchLoading}
                    >
                      批量下线
                    </Button>
                  </Popconfirm>
                )}
                <Popconfirm
                  title="批量删除"
                  description="确定要删除选中的素材吗？此操作不可恢复。"
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button danger icon={<DeleteOutlined />} loading={batchLoading}>
                    批量删除
                  </Button>
                </Popconfirm>
                <Button onClick={() => onSelectChange([])}>
                  取消选择
                </Button>
              </Space>
            </div>
          )}
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={filteredData}
            rowKey="material_id"
            pagination={{
              ...pagination,
              total: filteredData.length, // 使用实际传入的数据长度，而不是重新计算
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number, range: [number, number]) =>
                `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              onChange: (page: number, pageSize: number) => {
                setPagination({
                  current: page,
                  pageSize: pageSize,
                });
              },
              onShowSizeChange: (current: number, size: number) => {
                setPagination({
                  current: 1, // 切换分页大小时回到第一页
                  pageSize: size,
                });
                setSelectedRowKeys([]);
              },
              pageSizeOptions: ['10', '20', '50'],
              // 移除 getPopupContainer 配置，使用默认的弹出层容器
            }}
            scroll={{ x: 1200 }}
            size="middle"
          />
        </div>
      );
    }
  }, [
    selectedRowKeys,
    hasSelected,
    hasDraftSelected,
    hasOnlineSelected,
    batchLoading,
    handleBatchOnline,
    handleBatchOffline,
    handleBatchDelete,
    onSelectChange,
    rowSelection,
    columns,
    pagination,
    activeTab
  ]);

  // 缓存 tabItems 以避免重复计算和状态不一致
  const tabItems = useMemo(() => [
    {
      key: 'IMAGE',
      label: `图片 (${materialCounts.IMAGE})`,
      children: renderTableOrEmpty(filteredMaterials.IMAGE),
    },
    {
      key: 'TEXT',
      label: `文本 (${materialCounts.TEXT})`,
      children: renderTableOrEmpty(filteredMaterials.TEXT),
    },
    {
      key: 'VIDEO',
      label: `视频 (${materialCounts.VIDEO})`,
      children: renderTableOrEmpty(filteredMaterials.VIDEO),
    },
    {
      key: 'all',
      label: `全部 (${materialCounts.all})`,
      children: renderTableOrEmpty(filteredMaterials.all),
    },
  ], [materialCounts, filteredMaterials, renderTableOrEmpty]);

  // 如果分组信息还没加载完成，显示加载状态
  if (!materialGroup) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '40px 0' }}>
          <span>加载中...</span>
        </div>
      </PageContainer>
    );
  }

  return (
    <>
      <Helmet>
        <title>素材管理</title>
      </Helmet>
      <PageContainer
        title="素材管理"
        breadcrumb={{
          items: [
            {
              title: '数据资产',
            },
            {
              title: (
                <span
                  style={{ cursor: 'pointer' }}
                  onClick={() => history.push('/database/material')}
                >
                  素材库
                </span>
              ),
            },
            {
              title: `${materialGroup.name}`,
            },
          ],
        }}
        extra={
          // 只有当素材列表非空时才显示右上角的添加按钮
          materialList && materialList.length > 0 ? (
            <Space>
              <Button
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                新增素材
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleBatchCreate}
              >
                批量新增
              </Button>
            </Space>
          ) : null
        }
      >
        <div className="material-management">
          {/* 摘要描述区域 */}
          <GroupDigest
            className="material-group-summary"
            data={{
              name: materialGroup.name || '-',
              description: materialGroup.description || '暂无分组描述',
              createDate: materialGroup.createDate || '',
              creator: materialGroup.creator || '-',
              tags: materialGroup.tags || [],
              itemCount: materialList.length,
              itemCountLabel: '个素材'
            }}
          />

          <div className="material-list-section" style={{ marginTop: 16 }}>
            <Spin spinning={loading}>
              {materialList && materialList.length > 0 ? (
                <div
                  style={{
                    background: '#fff',
                    borderRadius: 8,
                    padding: 16,
                    paddingBottom: 48,
                    boxSizing: 'border-box',
                    // 确保容器有足够的高度显示分页组件
                    minHeight: '500px',
                    // 确保内容不会被截断
                    position: 'relative',
                  }}
                >
                  <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    items={tabItems}
                    type="card"
                  />
                </div>
              ) : (
                !loading && renderEmptyState()
              )}
            </Spin>
          </div>
        </div>

        <ModalForm
          title={editId ? '编辑素材' : '新增素材'}
          open={modalOpen}
          onOpenChange={(open) => {
            setModalOpen(open);
            // 不再清理OSS上传缓存，保持页面级别缓存
          }}
          initialValues={formInitialValues}
          onFinish={handleFinish}
          modalProps={{ destroyOnClose: true, bodyStyle: { padding: 24 } }}
          width={520}
          layout="vertical"
          formRef={modalFormRef}
        >
          <ProFormText name="name" label="素材名称" rules={[{ required: true, message: '请输入素材名称' }]} />
          <ProFormRadio.Group
            name="type"
            label="素材类型"
            options={[
              { label: '文本', value: 'TEXT' },
              { label: '图片', value: 'IMAGE' },
              { label: '视频', value: 'VIDEO' },
            ]}
            rules={[{ required: true, message: '请选择素材类型' }]}
            disabled={!!editId}
          />
          <ProFormTextArea name="desc" label="素材描述" rules={[{ required: true, message: '请输入素材描述' }]} />
          <Form.Item
            name="tags"
            label="标签"
            style={{ marginBottom: 16 }}
            rules={[{ required: false }]}
          >
            <Select
              mode="tags"
              style={{ width: '100%' }}
              placeholder="输入标签后回车添加"
              allowClear
            />
          </Form.Item>
          <ProFormRadio.Group
            name="validType"
            label="有效期类型"
            initialValue="forever"
            options={[
              { label: '永久有效', value: 'forever' },
              { label: '自定义', value: 'custom' },
            ]}
            rules={[{ required: true, message: '请选择有效期类型' }]}
            style={{ marginBottom: 16 }}
          />
          <ProFormDependency name={['validType']}>
            {({ validType }) =>
              validType === 'custom' ? (
                <ProFormDateRangePicker
                  name="date"
                  label="生效时间"
                  rules={[{ required: true, message: '请选择生效时间' }]}
                  style={{ marginBottom: 16 }}
                />
              ) : null
            }
          </ProFormDependency>
          <ProFormDependency name={['type']}>
            {({ type }) => type === 'TEXT' ? (
              <div style={{ position: 'relative' }}>
                {/* 文本内容标题和上传按钮 */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 8
                }}>
                  <label style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#262626'
                  }}>
                    <span style={{ color: '#ff4d4f' }}>*</span> 素材内容
                  </label>
                  <Upload
                    accept=".txt,.md,.doc,.docx,.pdf"
                    showUploadList={false}
                    beforeUpload={handleFileUpload}
                    onChange={(info) => {
                      // 当文件被选择时，自动填充素材名称和描述
                      if (info.fileList && info.fileList.length > 0) {
                        const file = info.fileList[0];
                        if (file.originFileObj) {
                          autoFillMaterialInfo(file.name);
                        }
                      }
                    }}
                  >
                    <Button
                      type="text"
                      icon={<UploadOutlined />}
                      size="small"
                      style={{
                        color: '#1890ff',
                        fontSize: '12px',
                        padding: '4px 8px',
                        height: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                    >
                      上传文件
                    </Button>
                  </Upload>
                </div>

                <ProFormTextArea
                  name="content"
                  rules={[
                    {
                      required: true,
                      message: '请输入文本内容',
                    },
                  ]}
                  fieldProps={{
                    placeholder: "请输入文本内容，或点击上方按钮上传文档文件（支持TXT、MD、DOC、DOCX、PDF格式）",
                    rows: 6
                  }}
                  style={{ marginBottom: 16 }}
                />
              </div>
            ) : null}
          </ProFormDependency>
          <ProFormDependency name={['type']}>
            {({ type }) => type === 'IMAGE' ? (
              <ProFormUploadButton
                name="content"
                label="上传图片"
                max={1}
                fieldProps={{
                  accept: '.jpg,.jpeg,.png',
                  listType: 'picture-card',
                  beforeUpload: (file) => {
                    // 检查文件大小（30MB = 30 * 1024 * 1024 bytes）
                    const isLessThan30M = file.size / 1024 / 1024 < 30;
                    if (!isLessThan30M) {
                      message.error(`图片大小不能超过30MB！当前文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB`);
                      return Upload.LIST_IGNORE; // 完全阻止文件被添加到列表
                    }
                    return false; // 禁用自动上传，但允许文件被添加到列表
                  },
                  onChange: (info) => {
                    // 当文件被添加到列表时，自动填充素材名称和描述
                    if (info.fileList && info.fileList.length > 0) {
                      const file = info.fileList[0];
                      if (file.originFileObj) {
                        autoFillMaterialInfo(file.name);
                      }
                    }
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ]}
                extra="仅支持jpg、png格式，文件不超过30M"
                style={{ marginBottom: 16 }}
              />
            ) : null}
          </ProFormDependency>
          <ProFormDependency name={['type']}>
            {({ type }) => type === 'VIDEO' ? (
              <ProFormUploadButton
                name="content"
                label="上传视频"
                max={1}
                fieldProps={{
                  accept: '.mp4,.mov,.avi,.mkv,.wmv,.flv,.webm',
                  listType: 'picture-card',
                  beforeUpload: (file) => {
                    // 检查文件大小（500MB = 500 * 1024 * 1024 bytes）
                    const isLessThan500M = file.size / 1024 / 1024 < 500;
                    if (!isLessThan500M) {
                      message.error(`视频大小不能超过500MB！当前文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB`);
                      return Upload.LIST_IGNORE; // 完全阻止文件被添加到列表
                    }
                    return false; // 禁用自动上传，但允许文件被添加到列表
                  },
                  onChange: (info) => {
                    // 当文件被添加到列表时，自动填充素材名称和描述
                    if (info.fileList && info.fileList.length > 0) {
                      const file = info.fileList[0];
                      if (file.originFileObj) {
                        autoFillMaterialInfo(file.name);
                      }
                    }
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '请上传视频',
                  },
                ]}
                extra="支持mp4、mov、avi、mkv、wmv、flv、webm等主流视频格式，文件不超过500M"
                style={{ marginBottom: 16 }}
              />
            ) : null}
          </ProFormDependency>
        </ModalForm>

        {/* 批量新增模态框 */}
        <Modal
          title="批量新增素材"
          open={batchModalOpen}
          onCancel={() => {
            setBatchModalOpen(false);
            setBatchSubmitting(false); // 关闭弹框时重置状态
          }}
          width={batchMaterialList.length > 0 ? 1200 : 600}
          footer={[
            <Button key="cancel" onClick={() => setBatchModalOpen(false)}>
              退出
            </Button>,
            <Button
              key="submit"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleBatchSubmit}
              disabled={batchMaterialList.length === 0 || batchMaterialList.some(item => !!item.parseError)}
              loading={batchSubmitting}
            >
              批量上传
            </Button>,
          ]}
        >
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            padding: '20px 0',
            width: '100%'
          }}>
            <div style={{
              marginBottom: 24,
              display: 'flex',
              alignItems: 'center',
              width: '100%'
            }}>
              <span style={{
                fontWeight: 500,
                color: '#262626',
                marginRight: 12,
                fontSize: 14
              }}>
                素材类型：
              </span>
              <Radio.Group
                value={batchUploadType}
                onChange={(e) => {
                  setBatchUploadType(e.target.value);
                  setBatchMaterialList([]); // 切换类型时清空列表
                }}
                disabled={batchMaterialList.length > 0}
              >
                <Radio value="TEXT" style={{ marginRight: 16 }}>文本</Radio>
                <Radio value="IMAGE" style={{ marginRight: 16 }}>图片</Radio>
                <Radio value="VIDEO">视频</Radio>
              </Radio.Group>


            </div>

            {batchMaterialList.length === 0 && (
              <div style={{ width: '100%' }}>
                <Upload.Dragger
                  multiple
                  accept={
                    batchUploadType === 'TEXT'
                      ? '.txt,.md,.doc,.docx,.pdf'
                      : batchUploadType === 'IMAGE'
                        ? '.jpg,.jpeg,.png,.gif,.webp'
                        : '.mp4,.mov,.avi,.mkv,.wmv,.flv,.webm'
                  }
                  beforeUpload={(file, fileList) => {
                    // 使用防抖，避免多次调用
                    if (batchFileSelectTimeoutRef.current) {
                      clearTimeout(batchFileSelectTimeoutRef.current);
                    }
                    batchFileSelectTimeoutRef.current = setTimeout(() => {
                      // 拖拽上传时替换列表
                      handleBatchFileSelect(fileList as File[], false);
                    }, 100);
                    return false;
                  }}
                  showUploadList={false}
                  style={{
                    border: '2px dashed #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa',
                    padding: '32px 16px'
                  }}
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </p>
                  <p className="ant-upload-text" style={{ fontSize: 16, marginBottom: 8 }}>
                    点击或拖拽文件到此区域上传
                  </p>
                  <p className="ant-upload-hint" style={{ color: '#666', fontSize: 12 }}>
                    {batchUploadType === 'TEXT' && '支持格式：TXT、MD、DOC、DOCX、PDF，系统将自动读取文件内容'}
                    {batchUploadType === 'IMAGE' && '支持格式：JPG、PNG、GIF、WebP，单文件不超过30MB，系统将自动上传'}
                    {batchUploadType === 'VIDEO' && '支持格式：MP4、MOV、AVI、MKV、WMV、FLV、WebM，单文件不超过500MB，系统将自动上传'}
                  </p>
                </Upload.Dragger>
              </div>
            )}
          </div>

          {batchMaterialList.length > 0 && (
            <div>
              <div style={{ marginBottom: 12, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>已选择 {batchMaterialList.length} 个文件</span>
                <div style={{ display: 'flex', gap: 8 }}>
                  <Upload
                    multiple
                    accept={
                      batchUploadType === 'TEXT'
                        ? '.txt,.md,.doc,.docx,.pdf'
                        : batchUploadType === 'IMAGE'
                          ? '.jpg,.jpeg,.png,.gif,.webp'
                          : '.mp4,.mov,.avi,.mkv,.wmv,.flv,.webm'
                    }
                    beforeUpload={(file, fileList) => {
                      // 使用防抖，避免多次调用
                      if (batchFileSelectTimeoutRef.current) {
                        clearTimeout(batchFileSelectTimeoutRef.current);
                      }
                      batchFileSelectTimeoutRef.current = setTimeout(() => {
                        // 点击"添加文件"按钮时，追加到现有列表
                        handleBatchFileSelect(fileList as File[], true);
                      }, 100);
                      return false;
                    }}
                    showUploadList={false}
                  >
                    <Button
                      icon={<UploadOutlined style={{ color: '#1890ff' }} />}
                      size="small"
                      style={{
                        color: '#1890ff',
                        borderColor: '#1890ff',
                        backgroundColor: '#fff'
                      }}
                    >
                      添加文件
                    </Button>
                  </Upload>
                  <Button
                    size="small"
                    danger
                    onClick={() => setBatchMaterialList([])}
                  >
                    清空列表
                  </Button>
                </div>
              </div>

              {/* 批量修改有效期 */}
              <div style={{
                marginBottom: 16,
                padding: '12px 16px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 12,
                  flexWrap: 'wrap'
                }}>
                  <span style={{
                    fontWeight: 500,
                    color: '#262626',
                    fontSize: 14
                  }}>
                    批量设置有效期：
                  </span>
                  <Radio.Group
                    value={batchValidType}
                    onChange={(e) => {
                      setBatchValidType(e.target.value);
                      if (e.target.value === 'forever') {
                        setBatchValidDate([null, null]);
                      }
                    }}
                    size="small"
                  >
                    <Radio value="forever" style={{ marginRight: 16 }}>永久有效</Radio>
                    <Radio value="custom">自定义</Radio>
                  </Radio.Group>
                  {batchValidType === 'custom' && (
                    <DatePicker.RangePicker
                      value={batchValidDate}
                      onChange={(dates) => setBatchValidDate(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null])}
                      style={{ width: '240px' }}
                      size="small"
                      placeholder={['开始', '结束']}
                      format="YYYY-MM-DD"
                    />
                  )}
                  <Button
                    type="primary"
                    size="small"
                    onClick={handleBatchUpdateValidPeriod}
                    disabled={batchMaterialList.length === 0}
                  >
                    应用
                  </Button>
                </div>
              </div>

              <div style={{ maxHeight: 400, overflowY: 'auto' }}>
                <Table
                  dataSource={batchMaterialList}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  scroll={{ x: batchMaterialList.some(item => item.uploadStatus !== 'pending' || item.parseError) ? 1160 + (validPeriodColumnWidth - 90) : 960 + (validPeriodColumnWidth - 90) }}
                  columns={[
                    {
                      title: '文件预览',
                      key: 'fileName',
                      width: 100,
                      align: 'center' as const,
                      render: (_, record) => (
                        <div style={{ textAlign: 'center' }}>
                          {/* 预览图 */}
                          {batchUploadType === 'IMAGE' && (
                            <div style={{ marginBottom: 8 }}>
                              <img
                                src={getFilePreviewUrl(record.id, record.file)}
                                alt="预览"
                                style={{
                                  width: '60px',
                                  height: '45px',
                                  objectFit: 'cover',
                                  borderRadius: '4px',
                                  border: '1px solid #f0f0f0',
                                  display: 'block',
                                  cursor: 'pointer',
                                  margin: '0 auto'
                                }}
                                onError={(e) => {
                                  // 预览失败时显示默认图标
                                  const img = e.target as HTMLImageElement;
                                  img.style.display = 'none';
                                  const fallback = img.parentElement?.querySelector('.image-fallback') as HTMLElement;
                                  if (fallback) {
                                    fallback.style.display = 'flex';
                                  }
                                }}
                                onClick={() => {
                                  // 点击预览大图
                                  const previewUrl = getFilePreviewUrl(record.id, record.file);
                                  setPreviewType('IMAGE');
                                  setPreviewUrl(previewUrl);
                                  setPreviewContent(undefined);
                                  setPreviewOpen(true);
                                }}
                              />
                              <div
                                className="image-fallback"
                                style={{
                                  width: '60px',
                                  height: '45px',
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: '4px',
                                  border: '1px solid #f0f0f0',
                                  display: 'none',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                  color: '#52c41a',
                                  margin: '0 auto'
                                }}
                              >
                                <FileTextOutlined />
                              </div>
                            </div>
                          )}
                          {batchUploadType === 'VIDEO' && (
                            <div style={{ marginBottom: 8 }}>
                              <video
                                src={getFilePreviewUrl(record.id, record.file)}
                                style={{
                                  width: '60px',
                                  height: '45px',
                                  objectFit: 'cover',
                                  borderRadius: '4px',
                                  border: '1px solid #f0f0f0',
                                  display: 'block',
                                  cursor: 'pointer',
                                  margin: '0 auto'
                                }}
                                muted
                                preload="metadata"
                                onError={(e) => {
                                  // 预览失败时显示默认图标
                                  const container = e.target as HTMLVideoElement;
                                  container.style.display = 'none';
                                  const fallback = container.parentElement?.querySelector('.video-fallback') as HTMLElement;
                                  if (fallback) {
                                    fallback.style.display = 'flex';
                                  }
                                }}
                                onClick={() => {
                                  // 点击预览视频
                                  const previewUrl = getFilePreviewUrl(record.id, record.file);
                                  setPreviewType('VIDEO');
                                  setPreviewUrl(previewUrl);
                                  setPreviewContent(undefined);
                                  setPreviewOpen(true);
                                }}
                              />
                              <div
                                className="video-fallback"
                                style={{
                                  width: '60px',
                                  height: '45px',
                                  backgroundColor: '#f0f0f0',
                                  borderRadius: '4px',
                                  border: '1px solid #f0f0f0',
                                  display: 'none',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                  color: '#722ed1',
                                  margin: '0 auto'
                                }}
                              >
                                <PlayCircleOutlined />
                              </div>
                            </div>
                          )}
                          {batchUploadType === 'TEXT' && (
                            <div style={{ marginBottom: 8 }}>
                              <div
                                style={{
                                  width: '60px',
                                  height: '45px',
                                  backgroundColor: '#f5f5f5',
                                  borderRadius: '4px',
                                  border: '1px solid #f0f0f0',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                  color: '#1890ff',
                                  cursor: 'pointer',
                                  margin: '0 auto'
                                }}
                                onClick={async () => {
                                  // 点击预览文本内容
                                  try {
                                    let content = record.parsedContent;
                                    if (!content) {
                                      content = await parseDocumentContent(record.file);
                                    }
                                    setPreviewType('TEXT');
                                    setPreviewContent(content);
                                    setPreviewUrl(undefined);
                                    setPreviewOpen(true);
                                  } catch (error) {
                                    message.error('文件读取失败');
                                  }
                                }}
                              >
                                <FileTextOutlined />
                              </div>
                            </div>
                          )}
                          {/* 文件名和大小 */}
                          <div style={{ fontSize: 11, color: '#666', lineHeight: 1.3, marginBottom: 2 }}>
                            {record.file.name}
                          </div>
                          <div style={{ fontSize: 10, color: '#999' }}>
                            {(record.file.size / 1024 / 1024).toFixed(2)} MB
                          </div>
                        </div>
                      ),
                    },
                    {
                      title: '素材名称',
                      key: 'name',
                      width: 140,
                      render: (_, record) => (
                        <Input.TextArea
                          value={record.name}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleBatchMaterialUpdate(record.id, 'name', e.target.value)}
                          placeholder="素材名称"
                          autoSize={{ minRows: 2, maxRows: 4 }}
                          style={{ resize: 'none' }}
                          disabled={record.uploadStatus === 'success'}
                        />
                      ),
                    },
                    {
                      title: '素材描述',
                      key: 'desc',
                      width: 160,
                      render: (_, record) => (
                        <Input.TextArea
                          value={record.desc}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleBatchMaterialUpdate(record.id, 'desc', e.target.value)}
                          placeholder="素材描述"
                          autoSize={{ minRows: 2, maxRows: 4 }}
                          style={{ resize: 'none' }}
                          disabled={record.uploadStatus === 'success'}
                        />
                      ),
                    },
                    {
                      title: '标签',
                      key: 'tags',
                      width: 120,
                      render: (_, record) => (
                        <Select
                          mode="tags"
                          value={record.tags}
                          onChange={(value) => handleBatchMaterialUpdate(record.id, 'tags', value)}
                          placeholder="添加标签"
                          style={{ width: '100%' }}
                          disabled={record.uploadStatus === 'success'}
                        />
                      ),
                    },
                    {
                      title: '有效期',
                      key: 'validPeriod',
                      width: validPeriodColumnWidth,
                      render: (_, record) => (
                        <div>
                          <Radio.Group
                            value={record.validType}
                            onChange={(e) => {
                              const value = e.target.value;
                              handleBatchMaterialUpdate(record.id, 'validType', value);
                              if (value === 'forever') {
                                handleBatchMaterialUpdate(record.id, 'validDate', undefined);
                                handleBatchMaterialUpdate(record.id, 'invalidDate', undefined);
                              }
                            }}
                            style={{ marginBottom: 8, display: 'flex', flexDirection: 'column' }}
                            size="small"
                            disabled={record.uploadStatus === 'success'}
                          >
                            <Radio value="forever" style={{ marginBottom: 4 }}>永久有效</Radio>
                            <Radio value="custom">自定义</Radio>
                          </Radio.Group>
                          {record.validType === 'custom' && (
                            <DatePicker.RangePicker
                              value={record.validDate && record.invalidDate ? [
                                dayjs(record.validDate),
                                dayjs(record.invalidDate)
                              ] : [null, null]}
                              onChange={(dates) => {
                                if (dates && dates[0] && dates[1]) {
                                  handleBatchMaterialUpdate(record.id, 'validDate', dates[0].format('YYYY-MM-DD'));
                                  handleBatchMaterialUpdate(record.id, 'invalidDate', dates[1].format('YYYY-MM-DD'));
                                } else {
                                  handleBatchMaterialUpdate(record.id, 'validDate', undefined);
                                  handleBatchMaterialUpdate(record.id, 'invalidDate', undefined);
                                }
                              }}
                              style={{ width: '240px' }}
                              size="small"
                              placeholder={['开始', '结束']}
                              format="YYYY-MM-DD"
                              disabled={record.uploadStatus === 'success'}
                            />
                          )}
                        </div>
                      ),
                    },
                    ...(batchMaterialList.some(item => item.uploadStatus !== 'pending' || item.parseError) ? [{
                      title: '上传状态',
                      key: 'uploadStatus',
                      width: 80,
                      fixed: 'right' as const,
                      render: (_: any, record: any) => {
                        const getStatusInfo = (status: string, parseError?: string) => {
                          if (parseError) {
                            return { text: '解析失败', color: '#ff4d4f', dotColor: '#ff4d4f' };
                          }
                          switch (status) {
                            case 'pending':
                              return { text: '待上传', color: '#8c8c8c', dotColor: '#8c8c8c' };
                            case 'uploading':
                              return { text: '上传中', color: '#1890ff', dotColor: '#1890ff' };
                            case 'uploaded':
                              return { text: '上传完成', color: '#52c41a', dotColor: '#52c41a' };
                            case 'adding':
                              return { text: '添加中', color: '#1890ff', dotColor: '#1890ff' };
                            case 'success':
                              return { text: '上传成功', color: '#52c41a', dotColor: '#52c41a' };
                            case 'failed':
                              return { text: '上传失败', color: '#ff4d4f', dotColor: '#ff4d4f' };
                            default:
                              return { text: '未知', color: '#8c8c8c', dotColor: '#8c8c8c' };
                          }
                        };

                        const statusInfo = getStatusInfo(record.uploadStatus, record.parseError);

                        return (
                          <div>
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              marginBottom: 4
                            }}>
                              <div style={{
                                width: 6,
                                height: 6,
                                borderRadius: '50%',
                                backgroundColor: statusInfo.dotColor,
                                marginRight: 6,
                                flexShrink: 0
                              }} />
                              <span style={{
                                color: '#262626',
                                fontSize: 14
                              }}>
                                {statusInfo.text}
                              </span>
                            </div>
                            {record.uploadStatus === 'success' && (
                              <div style={{
                                fontSize: 11,
                                color: '#8c8c8c',
                                marginTop: 2,
                                marginLeft: 12
                              }}>
                                云端资源已就绪
                              </div>
                            )}
                            {(record.uploadStatus === 'failed' || record.parseError) && (record.errorMessage || record.parseError) && (
                              <div style={{
                                fontSize: 11,
                                color: '#ff4d4f',
                                marginTop: 2,
                                marginLeft: 12
                              }}>
                                {record.errorMessage || record.parseError}
                              </div>
                            )}
                            {(record.uploadStatus === 'failed' || record.parseError) && (
                              <Button
                                type="link"
                                size="small"
                                style={{
                                  padding: 0,
                                  height: 'auto',
                                  fontSize: 12,
                                  marginTop: 4,
                                  marginLeft: 12
                                }}
                                onClick={() => handleRetryFile(record.id)}
                              >
                                重试
                              </Button>
                            )}
                          </div>
                        );
                      },
                    }] : []),
                    {
                      title: '操作',
                      key: 'action',
                      width: 80,
                      fixed: 'right' as const,
                      render: (_, record) => (
                        <Button
                          type="link"
                          size="small"
                          danger
                          onClick={() => handleBatchMaterialDelete(record.id)}
                          disabled={record.uploadStatus === 'uploading' || record.uploadStatus === 'adding' || record.uploadStatus === 'success' || !!record.parseError}
                        >
                          删除
                        </Button>
                      ),
                    },
                  ]}
                />
              </div>
            </div>
          )}
        </Modal>

        <Modal
          open={previewOpen}
          onCancel={() => setPreviewOpen(false)}
          footer={null}
          width={previewType === 'TEXT' ? 520 : 720}
          title="内容预览"
          styles={{
            body: {
              maxHeight: '70vh',
              overflow: 'hidden',
              padding: 0
            }
          }}
        >
          {previewType === 'TEXT' && (
            <div
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                fontSize: 16,
                minHeight: 80,
                maxHeight: '60vh',
                overflow: 'auto',
                padding: '16px 24px',
                lineHeight: 1.6,
                backgroundColor: '#fafafa',
                border: '1px solid #f0f0f0',
                borderRadius: 6,
                margin: '16px 24px'
              }}
            >
              {previewContent}
            </div>
          )}
          {previewType === 'IMAGE' && previewUrl && (
            <div
              style={{
                maxHeight: '60vh',
                overflow: 'auto',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'flex-start',
                padding: '16px 24px',
                backgroundColor: '#f8f8f8'
              }}
            >
              <img
                src={previewUrl}
                alt="预览"
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  display: 'block',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  borderRadius: 4
                }}
              />
            </div>
          )}
          {previewType === 'VIDEO' && previewUrl && (
            <div
              style={{
                maxHeight: '60vh',
                overflow: 'auto',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'flex-start',
                padding: '16px 24px',
                backgroundColor: '#000'
              }}
            >
              <video
                src={previewUrl}
                controls
                style={{
                  maxWidth: '100%',
                  maxHeight: '60vh',
                  display: 'block'
                }}
              />
            </div>
          )}
        </Modal>
      </PageContainer>
    </>
  );
};

export default MaterialManage;