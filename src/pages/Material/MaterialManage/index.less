.material-card{
    width:240px;
    height:320px;
    img{
        width:240px;
    }
    overflow: hidden;
    .info{
        width:100%;
        height:100%;
        background:rgba(0,0,0,0.5);
        color:#fff;
        position: absolute;
        top:280px;
        left:0;
        display: flex;
        flex-direction: column;
        padding:10px;
        gap:5px;
        .title{
            font-weight: bold;
            font-size: 16px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
        .title, .desc{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .button-group{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
            margin-top:5px;
            gap:10px;
        }
        transition: all 0.3s ease;
    }
}

.material-card:hover{
    .info{
        top:90px;
    }
}

.material-container{
    width:100%;
    height:100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap:10px;
}

.material-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // 确保表格容器有足够的高度
  min-height: 400px;
  // 确保内容可以正常滚动
  overflow: visible;
}

// 添加素材管理页面的整体样式
.material-management {
  width: 100%;
  // 确保页面有足够的高度
  min-height: calc(100vh - 200px);
  // 确保内容可以正常显示
  overflow: visible;
}

// 添加素材列表区域的样式
.material-list-section {
  width: 100%;
  // 确保容器不会被父元素高度限制
  min-height: 500px;
  // 允许内容溢出显示
  overflow: visible;
  // 确保容器有足够的空间显示表格和分页
  display: flex;
  flex-direction: column;
  // 确保容器有足够的空间
  flex: 1;
}

// 确保表格内容区域正常显示
.material-list .ant-table-wrapper {
  // 确保表格容器不会被截断
  overflow: visible;
  // 确保表格有足够的高度
  min-height: 300px;
}

// 确保分页组件正常显示
.material-list .ant-table-pagination {
  margin: 16px 0;
  // 确保分页组件不被隐藏
  position: relative;
  z-index: 10;
  // 确保分页组件有足够的空间
  padding: 8px 0;
  // 确保分页组件始终可见
  background: #fff;
  // 移除分割线
  // 添加右侧边距，避免贴紧表格
  padding-right: 16px;
}

// 确保分页组件的下拉菜单正常显示
.material-list .ant-table-pagination .ant-select-dropdown {
  z-index: 2000 !important;
}

// 确保分页组件的弹出层正常显示
.material-list .ant-table-pagination .ant-pagination-options {
  z-index: 1000;
}

// 确保表格内容区域可以正常滚动
.material-list .ant-table-container {
  // 允许表格内容正常显示
  overflow: visible;
}

// 确保表格体可以正常滚动
.material-list .ant-table-tbody {
  // 确保表格行正常显示
  overflow: visible;
}

// 确保表格头部正常显示
.material-list .ant-table-thead {
  // 确保表头正常显示
  overflow: visible;
}

// 确保表格行正常显示
.material-list .ant-table-tbody > tr {
  // 确保行正常显示
  display: table-row;
}

// 确保表格单元格正常显示
.material-list .ant-table-tbody > tr > td {
  // 确保单元格正常显示
  display: table-cell;
}

.batch-operations {
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.selected-info {
  color: #1890ff;
  font-weight: 500;
}

.material-name {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.name-text {
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.material-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.material-tags .ant-tag {
  margin: 0;
  font-size: 11px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 2px;
}

.more-tags {
  cursor: pointer;
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #8c8c8c !important;
}

.more-tags:hover {
  background: #e6f7ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}

/* Tooltip 内的标签样式 */
.ant-tooltip-inner .ant-tag {
  margin: 2px;
}

.create-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.creator-section {
  display: flex;
  align-items: center;
}

.creator-name {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-left: 4px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.time-icon {
  font-size: 12px;
  color: #8c8c8c;
  width: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-text {
  color: #595959;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.update-time .time-text {
  color: #8c8c8c;
}

/* 悬停效果 */
.create-info:hover .creator-name {
  color: #1890ff;
}

.create-info:hover .time-text {
  color: #262626;
}

.summary-text {
  color: #595959;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  word-break: break-word;
}

.valid-period-wrapper {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.period-status-tag {
  margin: 0;
  font-size: 11px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 2px;
  align-self: flex-start;
}

.period-text {
  color: #595959;
  font-size: 12px;
  line-height: 1.4;
  margin-left: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.status-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot-online {
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

.status-dot-draft {
  background-color: #d9d9d9;
}

.status-text {
  font-size: 13px;
  color: #262626;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.type-info {
  display: flex;
  align-items: center;
}

.material-list .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

/* 调整表头第一列的间距 */
.material-list .ant-table-thead > tr > th:nth-child(2) {
  padding-left: 40px !important;
}

/* 调整表格体第一列的间距 */
.material-list .ant-table-tbody > tr > td:nth-child(2) {
  padding-left: 40px !important;
}

/* 调整表头第三列（素材名称）的间距 */
.material-list .ant-table-thead > tr > th:nth-child(3) {
  padding-left: 24px !important;
}

/* 调整表格体第三列（素材名称）的间距 */
.material-list .ant-table-tbody > tr > td:nth-child(3) {
  padding-left: 24px !important;
}

/* 统一悬浮时所有列的背景色，包括复选框列 */
.material-list .ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff !important;
}

.material-list .ant-table-tbody > tr:hover > .ant-table-selection-column {
  background-color: #e6f7ff !important;
}

.material-list .ant-table-selection-column .ant-checkbox-wrapper {
  z-index: 1;
  position: relative;
  background: transparent !important;
}

/* 确保复选框内部样式不受影响 */
.material-list .ant-table-tbody > tr:hover > .ant-table-selection-column .ant-checkbox-inner {
  background-color: #ffffff !important;
}

/* 确保选中状态的复选框在悬浮时也保持正确的颜色 */
.material-list .ant-table-tbody > tr:hover > .ant-table-selection-column .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.material-list .ant-table-tbody > tr > td {
  padding: 12px 16px;
}

/* 复选框列边距调整 */
.material-list .ant-table-selection-column {
  padding-left: 20px !important;
  padding-right: 16px !important;
}

.material-list .ant-table-thead .ant-table-selection-column {
  padding-left: 20px !important;
  padding-right: 16px !important;
}

@media (max-width: 1200px) {
  .material-list .ant-table-container {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  .material-list .ant-table-selection-column {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }

  .material-list .ant-table-thead .ant-table-selection-column {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }



  .material-list .ant-table-thead > tr > th:nth-child(2) {
    padding-left: 28px !important;
  }

  .material-list .ant-table-tbody > tr > td:nth-child(2) {
    padding-left: 28px !important;
  }

  .material-list .ant-table-thead > tr > th:nth-child(3) {
    padding-left: 20px !important;
  }

  .material-list .ant-table-tbody > tr > td:nth-child(3) {
    padding-left: 20px !important;
  }
}

.material-list .ant-btn-text:hover {
  background-color: #f0f0f0;
}

.material-list .ant-dropdown-trigger:hover {
  background-color: #f0f0f0;
}

.material-list .ant-result {
  padding: 40px 0;
}

.material-list .ant-result-title {
  color: #262626;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.material-list .ant-result-subtitle {
  color: #8c8c8c;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.material-list .ant-result-extra {
  margin-top: 32px;
}

.material-list .ant-result-extra .ant-btn {
  height: 40px;
  padding: 0 24px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  border: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.material-list .ant-result-extra .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.material-list .empty-state-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.preview-video {
  width: 80px;
  height: 60px;
  background: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.preview-video:hover {
  background: #e6f7ff;
  transform: scale(1.05);
}

.preview-text {
  max-width: 100px;
  word-break: break-all;
  white-space: pre-wrap;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.4;
  color: #595959;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.preview-text:hover {
  background: #f5f5f5;
  color: #262626;
}

.material-description {
  color: #595959;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  cursor: pointer;
}

// 解决分页条数选择下拉被裁切问题
.ant-select-dropdown {
  z-index: 2000 !important;
}

// 确保表格容器不会限制分页组件的显示
.material-list .ant-table-wrapper {
  // 移除可能影响分页显示的样式限制
  overflow: visible;
  // 确保表格容器有足够的高度
  min-height: 300px;
  // 确保分页组件有足够的显示空间
  display: flex;
  flex-direction: column;
  // 确保容器不会被父元素限制
  position: relative;
}

// 确保表格内容区域不会覆盖分页组件
.material-list .ant-table-container {
  // 允许表格内容正常显示
  overflow: visible;
  // 确保表格体不会超出容器
  flex: 1;
  // 确保表格内容区域有足够的空间
  min-height: 200px;
}

// 确保表格体可以正常滚动，但不影响分页组件
.material-list .ant-table-tbody {
  // 确保表格行正常显示
  overflow: visible;
  // 设置最大高度，超出时显示滚动条，但确保分页组件可见
  max-height: calc(100vh - 500px);
  overflow-y: auto;
  // 确保滚动条不会影响分页组件
  margin-bottom: 60px;
}

// 确保表格头部正常显示
.material-list .ant-table-thead {
  // 确保表头正常显示
  overflow: visible;
  // 确保表头不会被截断
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fafafa;
}

// 确保 Tabs 组件不会影响分页组件的显示
.material-list .ant-tabs-content-holder {
  // 确保内容区域正常显示
  overflow: visible;
  // 确保分页组件有足够的显示空间
  min-height: 400px;
}

// 确保 Tabs 内容区域不会限制分页组件
.material-list .ant-tabs-tabpane {
  // 确保标签页内容正常显示
  overflow: visible;
  // 确保分页组件有足够的显示空间
  padding-bottom: 20px;
}

// 确保表格容器有足够的底部空间显示分页组件
.material-list {
  // 确保容器有足够的底部空间
  padding-bottom: 20px;
  // 确保分页组件不会被截断
  position: relative;
}

// 确保分页组件的下拉菜单和弹出层正常显示
.material-list .ant-table-pagination .ant-select {
  z-index: 1000;
}

.material-list .ant-table-pagination .ant-pagination-options-size-changer {
  z-index: 1000;
}

// 确保分页组件的快速跳转输入框正常显示
.material-list .ant-table-pagination .ant-pagination-options-quick-jumper {
  z-index: 1000;
}

// 确保分页组件的所有交互元素都有正确的层级
.material-list .ant-table-pagination .ant-pagination-item,
.material-list .ant-table-pagination .ant-pagination-prev,
.material-list .ant-table-pagination .ant-pagination-next,
.material-list .ant-table-pagination .ant-pagination-jump-prev,
.material-list .ant-table-pagination .ant-pagination-jump-next {
  z-index: 1000;
}