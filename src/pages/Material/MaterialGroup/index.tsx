import { PageContainer } from '@ant-design/pro-components';
import { Button, List, Modal, Input, Form, Tabs, message, Select, Result, Spin } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useState, useRef } from 'react'
import { history, useModel } from '@umijs/max';
import { addMaterialGroup, deleteMaterialGroup, getMaterialGroup, updateMaterialGroup } from '@/services/ant-design-pro/api';
import BaseCard from '@/components/BaseCard';
import './index.less'

// 投放平台枚举
const PLATFORM_ENUM = {
    COMMON: "通用",
    WX: "公众号", 
    SPH: "视频号",
    XHS: "小红书",
    DY: "抖音",
    KS: "快手"
};

// 投放平台顺序（用于默认排序）
const PLATFORM_ORDER = Object.keys(PLATFORM_ENUM);

// 投放平台选项
const PLATFORM_OPTIONS = Object.entries(PLATFORM_ENUM).map(([value, label]) => ({
    value,
    label
}));

// 获取平台中文名称的辅助函数
const getPlatformLabel = (platform: string): string => {
    return PLATFORM_ENUM[platform as keyof typeof PLATFORM_ENUM] || platform;
};

// 获取平台的默认排序权重
const getPlatformOrder = (platform: string): number => {
    const index = PLATFORM_ORDER.indexOf(platform);
    return index === -1 ? 999 : index;
};

const MaterialGroup = () => {
    const [materialGroups, setMaterialGroups] = useState<API.MaterialGroup[]>([]);
    const [materialGroupList, setMaterialGroupList] = useState<API.MaterialGroupList[]>([])
    const [sortedMaterialGroupList, setSortedMaterialGroupList] = useState<API.MaterialGroupList[]>([])
    const [currentSubType, setCurrentSubType] = useState<string>("");
    const [currentGroupIndex, setCurrentGroupIndex] = useState<number>(0);
    const [showModal, setShowModal] = useState<boolean>(false)
    const [modalType, setModalType] = useState<'create' | 'edit'>('create')
    const [editingGroup, setEditingGroup] = useState<API.MaterialGroup | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const { initialState } = useModel('@@initialState');
    const { customData } = initialState || {}
    const { cid, uid, hasLogin } = customData || {}
    const [form] = Form.useForm();
    const [draggedTabIndex, setDraggedTabIndex] = useState<number | null>(null);
    const [submitting, setSubmitting] = useState<boolean>(false);
    const tabsRef = useRef<HTMLDivElement>(null);

    const handleViewGroup = (group: API.MaterialGroup) => {
        // 传递分组信息到管理页面，参考知识管理的实现
        const groupData = {
            id: group.group_id,
            name: group.group_name,
            description: group.group_desc || '暂无分组描述',
            createDate: group.gmt_create || '',
            creator: group.username || '未知用户',
            tags: Array.isArray((group as any).tags) 
                ? (group as any).tags 
                : (typeof (group as any).tags === 'string' && (group as any).tags)
                  ? (group as any).tags.split(',')
                  : [],
            materialCount: Number(group.resource_count) || 0
        };
        
        // 使用与知识管理一致的方式传递数据
        history.push(`/database/material/group/${group.group_id}`, groupData);
    }

    // 排序材料组列表
    const sortMaterialGroupList = (list: API.MaterialGroupList[]): API.MaterialGroupList[] => {
        return [...list].sort((a, b) => {
            const orderA = getPlatformOrder(a.sub_type);
            const orderB = getPlatformOrder(b.sub_type);
            return orderA - orderB;
        });
    }

    // 处理Tab拖拽排序
    const handleTabsEdit = (targetKey: string, action: 'add' | 'remove') => {
        // 这里我们只处理拖拽，不处理添加和删除
    }

    // 处理Tab拖拽重新排序
    const handleTabDragEnd = (fromIndex: number, toIndex: number) => {
        if (fromIndex === toIndex) return;
        
        const newSortedList = [...sortedMaterialGroupList];
        const [movedItem] = newSortedList.splice(fromIndex, 1);
        newSortedList.splice(toIndex, 0, movedItem);
        
        setSortedMaterialGroupList(newSortedList);
        
        // 如果当前活跃的tab被移动了，需要更新currentGroupIndex
        if (currentGroupIndex === fromIndex) {
            setCurrentGroupIndex(toIndex);
        } else if (currentGroupIndex > fromIndex && currentGroupIndex <= toIndex) {
            setCurrentGroupIndex(currentGroupIndex - 1);
        } else if (currentGroupIndex < fromIndex && currentGroupIndex >= toIndex) {
            setCurrentGroupIndex(currentGroupIndex + 1);
        }
    }

    // 拖拽开始
    const handleDragStart = (e: React.DragEvent, index: number) => {
        setDraggedTabIndex(index);
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.currentTarget.outerHTML);
        e.dataTransfer.setDragImage(e.currentTarget as Element, 0, 0);
    }

    // 拖拽结束
    const handleDragEnd = () => {
        setDraggedTabIndex(null);
    }

    // 拖拽悬停
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }

    // 拖拽放置
    const handleDrop = (e: React.DragEvent, dropIndex: number) => {
        e.preventDefault();
        if (draggedTabIndex !== null && draggedTabIndex !== dropIndex) {
            handleTabDragEnd(draggedTabIndex, dropIndex);
        }
        setDraggedTabIndex(null);
    }

    // 创建可拖拽的Tab标签
    const createDraggableTabLabel = (item: API.MaterialGroupList, index: number) => {
        const isDragging = draggedTabIndex === index;
        const isDropTarget = draggedTabIndex !== null && draggedTabIndex !== index;
        
        return (
            <div
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                style={{
                    cursor: 'move',
                    opacity: isDragging ? 0.5 : 1,
                    transition: 'all 0.2s ease',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    backgroundColor: isDropTarget ? '#f0f8ff' : 'transparent',
                    border: isDropTarget ? '2px dashed #1890ff' : '2px solid transparent',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                }}
                title="拖拽以调整顺序"
            >
                <span style={{ fontSize: '12px', opacity: 0.6 }}>⋮⋮</span>
                {`${getPlatformLabel(item.sub_type)} (${item.groups.length})`}
            </div>
        );
    }



    const handleCreateNew = () => {
        setModalType('create');
        setEditingGroup(null);
        form.resetFields();
        // 如果当前tab有对应的平台，设置为默认值
        if (currentSubType && PLATFORM_ENUM[currentSubType as keyof typeof PLATFORM_ENUM]) {
            form.setFieldsValue({
                sub_group_type: currentSubType
            });
        }
        setShowModal(true);
    }

    const handleModifyGroup = (item: API.MaterialGroup) => {
        setModalType('edit');
        setEditingGroup(item);
        let tagsValue = (item as any).tags;
        if (Array.isArray(tagsValue)) {
          // do nothing
        } else if (typeof tagsValue === 'string' && tagsValue) {
          tagsValue = tagsValue.split(',');
    } else {
          tagsValue = [];
        }
        form.setFieldsValue({
            id: item.group_id,
            name: item.group_name,
            desc: item.group_desc,
            sub_group_type: item.sub_group_type || currentSubType || undefined,
            tags: tagsValue,
        })
        setShowModal(true)
    }

    const handleModalCancel = () => {
        setShowModal(false);
        setEditingGroup(null);
        form.resetFields();
    }

    const handleSubmit = async (values: any) => {
        setSubmitting(true); // 立即设置loading状态
        try {
            let tags = values.tags;
            if (Array.isArray(tags)) tags = tags.join(',');
            
            if (modalType === 'create') {
                await addMaterialGroup({
                    uid,
                    cid,
                    group_name: values.name,
                    group_desc: values.desc,
                    sub_group_type: values.sub_group_type,
                    tags,
                })
                message.success('新增分组成功！', 1.5);
            } else {
                await updateMaterialGroup(values.id, cid, {
                    group_name: values.name,
                    group_desc: values.desc,
                    sub_group_type: values.sub_group_type,
                    tags,
                    cid: cid, 
                    uid: uid,
                });
                message.success('修改分组成功！', 1.5);
            }
            
            updateMaterialGroups();
            setShowModal(false);
            setEditingGroup(null);
            form.resetFields();
    } catch (error) {
            message.error(`${modalType === 'create' ? '新增' : '修改'}分组失败，请重试！`, 2);
        } finally {
            setSubmitting(false); // 无论成功失败都要取消loading状态
        }
    }

    const handleDeleteGroup = (id: string, name: string) => {
        Modal.confirm({
            title: '确认删除',
            content: `确定要删除素材组"${name}"吗？此操作不可撤销。`,
            okText: '确认删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                try {
                    await deleteMaterialGroup(id)
                    message.success('删除分组成功！', 1.5);
                    updateMaterialGroups()
      } catch (error) {
                    message.error('删除分组失败，请重试！', 2);
                }
        },
      });
    }

    const updateMaterialGroups = async () => {
        setLoading(true);
        try {
            await getMaterialGroup(cid).then((res) => {
                if (res.success === false) {
                    message.error(res.error_msg || '获取分组失败！', 2);
                    return;
                }
                setMaterialGroupList(res.groups);
                
                // 按照PLATFORM_ENUM顺序排序
                const sorted = sortMaterialGroupList(res.groups);
                setSortedMaterialGroupList(sorted);
                
                if (sorted.length > 0) {
                    // 确保currentGroupIndex在有效范围内
                    const validIndex = currentGroupIndex < sorted.length ? currentGroupIndex : 0;
                    setCurrentGroupIndex(validIndex);
                    setMaterialGroups(sorted[validIndex]?.groups || [])
                    setCurrentSubType(sorted[validIndex]?.sub_type || "")
                }
            })
        }catch (error) {
            message.error('获取分组失败，请重试！', 2);
        } finally {
            setLoading(false);
        }
    }

    const handleTabChange = (key: string) => {
        const index = parseInt(key);
        setMaterialGroups(sortedMaterialGroupList[index]?.groups || []);
        setCurrentSubType(sortedMaterialGroupList[index]?.sub_type || "");
        setCurrentGroupIndex(index);
    }

    // 处理标签数据
    const formatTags = (tags: any): string[] => {
        if (Array.isArray(tags)) {
            return tags;
        } else if (typeof tags === 'string' && tags) {
            return tags.split(',').filter(tag => tag.trim());
        }
        return [];
    }

    useEffect(() => {
        if (!hasLogin) {
            history.push('/user/login')
            return;
        }
        updateMaterialGroups()
    }, [])

    // 监听页面可见性变化，确保从其他页面回退时状态正确
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden && hasLogin) {
                // 页面变为可见时，重新获取数据以确保状态同步
                updateMaterialGroups();
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [hasLogin])

    // 添加ESC键返回功能
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                if (showModal) {
                    handleModalCancel();
                }
            }
        }

        document.addEventListener('keydown', handleKeyDown)
        return () => {
            document.removeEventListener('keydown', handleKeyDown)
        }
    }, [showModal])

    return (
        <>
            <style>
                {`
                    .enhanced-create-btn {
                        background: linear-gradient(135deg, #1890ff, #36cfc9) !important;
                        border: none !important;
                        border-radius: 6px !important;
                        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25) !important;
                        font-weight: 600 !important;
                        font-size: 14px !important;
                        height: 36px !important;
                        padding: 0 18px !important;
                        transition: all 0.3s ease !important;
                    }
                    
                    .enhanced-create-btn:hover {
                        transform: translateY(-1px) !important;
                        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35) !important;
                        background: linear-gradient(135deg, #40a9ff, #5cdbd3) !important;
                    }
                    
                    .enhanced-create-btn:active {
                        transform: translateY(0) !important;
                    }
                    
                    .enhanced-create-btn .anticon {
                        font-size: 14px !important;
                        margin-right: 6px !important;
                    }

                    .material-library-tabs .ant-tabs-content-holder {
                        padding: 0 !important;
                        width: 100% !important;
                    }

                    .material-library-tabs .ant-tabs-tabpane {
                        padding: 12px !important;
                        background-color: #f5f5f5 !important;
                        border-radius: 8px !important;
                        min-height: 450px !important;
                        width: 100% !important;
                        box-sizing: border-box !important;
                        margin: 0 !important;
                    }

                    .material-library-tabs {
                        width: 100% !important;
                    }

                    .material-container {
                        width: 100% !important;
                        overflow: hidden !important;
                        padding: 0 !important;
                        margin: 0 !important;
                    }

                    /* 确保List网格中的卡片高度一致 */
                    .material-library-tabs .ant-list-item {
                        display: flex !important;
                        height: 100% !important;
                    }

                    .material-library-tabs .ant-list-item .base-card {
                        width: 100% !important;
                        height: 100% !important;
                    }

                    .material-library-tabs {
                        width: 100% !important;
                        margin: 0 !important;
                    }

                    .material-library-tabs .ant-tabs-content-holder {
                        padding: 0 !important;
                    }

                    .material-library-tabs .ant-tabs-tab {
                        position: relative !important;
                    }

                    .material-library-tabs .ant-tabs-tab:hover {
                        background-color: #f0f0f0 !important;
                    }

                    .material-library-tabs .ant-tabs-tab[aria-selected="true"] {
                        background-color: #e6f7ff !important;
                    }

                    .material-library-tabs .ant-tabs-tab .ant-tabs-tab-btn {
                        font-weight: normal !important;
                    }

                    .material-library-tabs .ant-tabs-tab[aria-selected="true"] .ant-tabs-tab-btn {
                        font-weight: normal !important;
                    }
                `}
            </style>
            
            <PageContainer
                extra={
                    /* 加载中或没有素材组时隐藏右上角按钮，避免与Result中的按钮重复 */
                    loading || sortedMaterialGroupList.length === 0 ? [] : [
                        <Button
                            key="create"
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleCreateNew}
                            size="large"
                            className="enhanced-create-btn"
                        >
                            新建素材组
                        </Button>,
                    ]
                }
            >
                {/* 如果正在加载，显示全页面loading */}
                {loading ? (
                <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minHeight: '500px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '8px',
                        margin: '24px'
                    }}>
                        <Spin size="large" tip="正在加载素材分组数据..." />
                </div>
                ) : (
                    /* 检查是否完全没有素材组 */
                    !loading && sortedMaterialGroupList.length === 0 ? (
                <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '500px',
        backgroundColor: '#fff',
        borderRadius: '8px',
                            width: '100%',
                            margin: 0
                        }}>
      <Result
        status="404"
                                title="暂无素材组"
                                subTitle="开始创建您的第一个素材组，管理和组织您的素材内容"
                                style={{ width: '100%' }}
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
                                        onClick={handleCreateNew}
            size="large"
                                        className="enhanced-create-btn"
          >
                                        新建素材组
          </Button>
        }
      />
    </div>
                    ) : (
                        <div className='material-container' style={{ margin: 0, width: '100%' }}>
                        <Tabs
                            type="card"
                            activeKey={currentGroupIndex.toString()}
                            onChange={handleTabChange}
                            className="material-library-tabs"
                            items={sortedMaterialGroupList.map((item, index) => ({
                                key: index.toString(),
                                label: createDraggableTabLabel(item, index),
                                children: (
                                    <div>
                                        {item.groups.length === 0 ? (
                                            <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
                                                minHeight: '400px',
            backgroundColor: '#fff',
            borderRadius: '8px',
                                                margin: '0',
                                                width: '100%'
                                            }}>
                                                <Result
                                                    status="404"
                                                    title="暂无素材组"
                                                    subTitle={`为${getPlatformLabel(item.sub_type)}平台创建专属的素材组，更好地管理您的内容`}
                                                    style={{ width: '100%' }}
      extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
                                                            onClick={handleCreateNew}
                                                            size="large"
                                                            className="enhanced-create-btn"
          >
                                                            新建素材组
          </Button>
                                                    }
                                                />
                                            </div>
                                        ) : (
                                            <div style={{ padding: '4px' }}>
                                                <List
                                                    grid={{
                                                        gutter: 16,
                                                        xs: 1,
                                                        sm: 2,
                                                        md: 3,
                                                        lg: 3,
                                                        xl: 4,
                                                        xxl: 4,
                                                    }}
                                                    dataSource={item.groups}
                                                    renderItem={(group) => (
                                                        <List.Item key={group.group_id}>
                                                            <BaseCard
                                                                id={group.group_id}
                                                                title={group.group_name}
                                                                content={group.group_desc || '暂无描述'}
                                                                count={Number(group.resource_count) || 0}
                                                                countLabel="个素材"
                                                                createTime={group.gmt_create?.split(' ')[0] || '未知'}
                                                                creator={group.username || '未知用户'}
                                                                tags={formatTags((group as any).tags)}
                                                                onEdit={() => handleModifyGroup(group)}
                                                                onView={() => handleViewGroup(group)}
                                                                onDelete={() => handleDeleteGroup(group.group_id, group.group_name)}
                                                                onDoubleClick={() => handleViewGroup(group)}
                                                            />
                                                        </List.Item>
                                                    )}
                  />
                </div>
              )}
          </div>
                                )
                            }))}
                        />
      </div>
                    )
                )}

                {/* 素材组Modal */}
                <Modal
                    title={modalType === 'create' ? '新建素材组' : '编辑素材组'}
                    open={showModal}
                    onOk={() => form.submit()}
                    onCancel={handleModalCancel}
                    okText={modalType === 'create' ? '创建' : '更新'}
                    cancelText="取消"
                    width={600}
                    destroyOnClose
                    confirmLoading={submitting}
                    okButtonProps={{
                        loading: submitting,
                        disabled: submitting
                    }}
                    cancelButtonProps={{
                        disabled: submitting
                    }}
                >
                    <Form
                        form={form}
        layout="vertical"
                        onFinish={handleSubmit}
                        style={{ marginTop: '20px' }}
                    >
                        {/* 编辑模式下的隐藏字段 */}
                        {modalType === 'edit' && (
                            <Form.Item name="id" hidden>
                                <Input />
                            </Form.Item>
                        )}

                        <Form.Item
                            name="name"
                            label="分组名称"
                            rules={[
                                { required: true, message: '请输入分组名称' },
                                { max: 50, message: '名称长度不能超过50个字符' }
                            ]}
                        >
                            <Input placeholder="请输入分组名称" />
                        </Form.Item>

        <Form.Item
                            name="sub_group_type"
                            label="投放平台"
                            rules={[{ required: true, message: "请选择投放平台" }]}
        >
          <Select
                                placeholder="请选择投放平台"
                                options={PLATFORM_OPTIONS}
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
          />
        </Form.Item>

                        <Form.Item
                            name="desc"
                            label="分组描述"
                rules={[
                                { required: true, message: '请输入分组描述' },
                                { max: 200, message: '描述长度不能超过200个字符' }
                            ]}
                        >
                            <Input.TextArea
                                rows={4}
                                placeholder="请输入分组描述，简要说明该分组的用途和包含的内容"
                            />
                        </Form.Item>

                        <Form.Item
                            name="tags"
                            label="标签"
                        >
                            <Select
                                mode="tags"
                                style={{ width: '100%' }}
                                placeholder="请输入标签，按回车键添加"
                                tokenSeparators={[',']}
                                maxTagCount={10}
                            />
                        </Form.Item>
                    </Form>
      </Modal>
    </PageContainer>
    </>
    )
}

export default MaterialGroup
