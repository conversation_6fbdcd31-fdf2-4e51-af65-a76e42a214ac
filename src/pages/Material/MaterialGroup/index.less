.ant-pro-page-container {
  padding: 16px 24px;

  .ant-pro-page-container-children-content {
    padding: 0;
    margin: 0;
  }
}

.material-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);

  &:focus {
    outline: none;
  }

  *:focus {
    outline: none !important;
  }

  .material-library-tabs {
    .ant-tabs-nav {
      margin: 0 0 16px 0;
    }

    .ant-tabs-tab {
      font-weight: 500;
      padding: 8px 16px;
    }

    .ant-tabs-tab-active {
      font-weight: 600;
    }

    .ant-tabs-content-holder {
      background: transparent;
    }

    .ant-tabs-tabpane {
      background-color: #f5f5f5;
      border-radius: 8px;
      min-height: 500px;
    }
  }

  .ant-tabs-card {
    .ant-tabs-nav {
      margin: 0;
      padding: 16px 16px 0;
      
      .ant-tabs-tab {
        padding: 8px 16px;
        border: none;
        background: transparent;
        border-radius: 4px 4px 0 0;

        &:focus {
          outline: none;
        }

        &-active {
          background: @primary-1;
          color: @primary-color;
        }
      }
    }

    .ant-tabs-content {
      padding: 16px;
      width: 100%;

      &:focus {
        outline: none;
      }

      .material-list-container {
        min-height: calc(100vh - 360px);
        
        .material-list {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          width: 100%;
          
          &:focus {
            outline: none;
          }
          
          .material-card {
            width: calc(25% - 12px); // 4列布局，减去gap
            min-width: 240px;
          }
        }
      }

      .material-list-container {
        min-height: calc(100vh - 420px);
        width: 100%;
        position: relative;
        padding-bottom: 60px;

        .empty-tip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: @text-color-secondary;
        }
      }

    }
  }
}

.modal-form {
  .ant-form-item {
    margin-bottom: 24px;
    
    .ant-form-item-label {
      padding-bottom: 8px;
      
      label {
        font-weight: 500;
        color: @text-color;
        font-size: 14px;
      }
    }
    
    .ant-input,
    .ant-input-textarea {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s;
      
      &:hover {
        border-color: @primary-color;
      }
      
      &:focus,
      &.ant-input-focused {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
    
    .ant-input-textarea {
      .ant-input {
        border: none;
        box-shadow: none;
        
        &:hover,
        &:focus {
          border: none;
          box-shadow: none;
        }
      }
    }
  }
}

// 确保MaterialLibrary的样式不会影响其他页面
.material-container .modal-form {
  .ant-form-item {
    .ant-input,
    .ant-input-textarea {
      &:focus,
      &.ant-input-focused {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .material-container {
    .material-library-tabs {
      .ant-tabs-tab {
        padding: 6px 12px;
        font-size: 13px;
      }
    }
  }
}
