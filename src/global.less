html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

// 主内容区域占用剩余空间，至少撑满视口
.ant-pro-layout-content {
  flex: 1 0 auto;
  min-height: calc(100vh - 80px); // 减少预留空间，让页面更紧凑
}

// 页脚始终在底部
.ant-pro-global-footer {
  flex-shrink: 0;
}

// 响应式处理小屏幕
@media (max-width: 768px) {
  .ant-pro-layout-content {
    min-height: calc(100vh - 70px); // 小屏幕进一步减少空间
  }
}

@media (max-width: 480px) {
  .ant-pro-layout-content {
    min-height: calc(100vh - 60px); // 超小屏幕最小化空间
  }
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}
