import { outLogin } from '@/services/ant-design-pro/api';
import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Spin, Avatar, Space } from 'antd';
import { createStyles } from 'antd-style';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { customData } = initialState || {};
  const { username } = customData || {};
  return <span className="anticon">{username || '用户'}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    avatar: {
      marginRight: 8,
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    
    // 清除登录信息
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const expires = `expires=${tomorrow.toUTCString()}`;
    const cookieValue = encodeURIComponent(JSON.stringify({autoLogin:false}));
    document.cookie = `loginInfo=${cookieValue}; ${expires}; path=/`;
    
    // 跳转到登录页
    history.push('/user/login');
  };
  
  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        flushSync(() => {
          setInitialState((s) => ({ 
            ...s, 
            customData: {
              ...s?.customData,
              hasLogin: false,
              username: undefined,
              uid: undefined,
              cid: undefined,
            }
          }));
        });
        loginOut();
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { customData } = initialState;
  const { hasLogin, username } = customData || {};

  if (!hasLogin || !username) {
    return loading;
  }

  const menuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 获取用户名的首字
  const getAvatarText = (name: string) => {
    return name ? name.charAt(0).toUpperCase() : 'U';
  };

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      <span className={styles.action}>
        <Avatar 
          size="small" 
          className={styles.avatar}
          style={{
            backgroundColor: '#1890ff',
            color: '#fff',
            fontWeight: 'bold'
          }}
        >
          {getAvatarText(username)}
        </Avatar>
        <span>{username}</span>
      </span>
    </HeaderDropdown>
  );
};
