import React from 'react';
import { Card, Typography, Avatar, Tag, Tooltip } from 'antd';
import { FileOutlined, EditOutlined, EyeOutlined, DeleteOutlined, ClockCircleOutlined, FolderOutlined, FolderOpenOutlined } from '@ant-design/icons';
import './index.less';

const { Title, Paragraph } = Typography;

export interface BaseCardProps {
  id: string;
  title: string;
  content: string;
  count?: number;
  countLabel?: string; // 自定义计数单位，如 "个文件"、"个知识"、"个素材"
  createTime?: string;
  creator?: string;
  creatorAvatar?: string;
  tags?: string[];
  onEdit?: (id: string) => void;
  onView?: (id: string) => void;
  onDelete?: (id: string) => void;
  onDoubleClick?: (id: string) => void;
  className?: string;
  style?: React.CSSProperties;
  icon?: React.ReactNode; // 自定义主图标，不传则使用默认的FolderOutlined
  viewIcon?: React.ReactNode; // 自定义查看图标，不传则使用默认的FolderOpenOutlined
  maxDisplayTags?: number; // 最多显示多少个标签，默认为2
}

const BaseCard: React.FC<BaseCardProps> = ({
  id,
  title,
  content,
  count = 0,
  countLabel = '个文件',
  createTime,
  creator,
  creatorAvatar,
  tags = [],
  onEdit,
  onView,
  onDelete,
  onDoubleClick,
  className = '',
  style = {},
  icon,
  viewIcon,
  maxDisplayTags = 2, // 默认值设为2，与KnowledgeGroup保持一致
}) => {
  // 构建actions数组
  const actions = [];
  if (onEdit) {
    actions.push(
      <EditOutlined 
        key="edit" 
        onClick={() => onEdit(id)}
        title="编辑"
      />
    );
  }
  if (onView) {
    const defaultViewIcon = <FolderOpenOutlined />; // 默认查看图标
    const finalViewIcon = viewIcon || defaultViewIcon;
    
    actions.push(
      React.cloneElement(
        finalViewIcon as React.ReactElement,
        {
          key: "view",
          onClick: () => onView(id),
          title: "查看"
        }
      )
    );
  }
  if (onDelete) {
    actions.push(
      <DeleteOutlined 
        key="delete" 
        onClick={() => onDelete(id)}
        title="删除"
      />
    );
  }

  // 标签显示逻辑
  // 作者名称缩略函数
  const truncateCreator = (creator: string) => {
    if (creator.length <= 4) return creator;
    return creator.substring(0, 4) + '...';
  };

  // 检测是否为小屏幕
  const [isSmallScreen, setIsSmallScreen] = React.useState(false);

  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth <= 1100); // 1100px 作为分界点
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 动态计算可显示的标签数量
  const [visibleTagCount, setVisibleTagCount] = React.useState(3);
  const tagsContainerRef = React.useRef<HTMLDivElement>(null);

  // 检测标签是否被截断并调整显示数量
  const checkTagTruncation = React.useCallback(() => {
    if (!tagsContainerRef.current) return;
    
    const container = tagsContainerRef.current;
    const containerWidth = container.offsetWidth;
    
    // 估算单个标签的平均宽度（包括间距）
    const estimatedTagWidth = isSmallScreen ? 60 : 90; // 小屏50px+间距，大屏80px+间距
    const maxPossibleTags = Math.floor(containerWidth / estimatedTagWidth);
    
    // 确保至少显示1个标签，最多显示3个
    const newVisibleCount = Math.max(1, Math.min(3, maxPossibleTags));
    setVisibleTagCount(newVisibleCount);
  }, [isSmallScreen]);

  // 监听容器大小变化和屏幕尺寸变化
  React.useEffect(() => {
    checkTagTruncation();
    
    const resizeObserver = new ResizeObserver(checkTagTruncation);
    if (tagsContainerRef.current) {
      resizeObserver.observe(tagsContainerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, [checkTagTruncation, tags]);

  // 根据动态计算结果显示标签
  const displayedTags = tags.slice(0, visibleTagCount);
  const remainingTags = tags.slice(visibleTagCount);
  const remainingCount = remainingTags.length;

  // 随机颜色数组
  const tagColors = ['blue', 'green', 'orange', 'purple', 'cyan', 'magenta', 'red', 'volcano', 'gold', 'lime'];
  
  // 根据标签位置生成固定颜色
  const getTagColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };

  // 标签内容缩略函数
  const truncateTag = (tag: string) => {
    if (tag.length <= 4) return tag; // 4个字以内不缩略
    return tag.length > 6 ? tag.substring(0, 4) + '...' : tag;
  };

  // 小屏标签内容缩略函数（更激进）
  const truncateTagSmall = (tag: string) => {
    if (tag.length <= 2) return tag; // 2个字以内不缩略
    return tag.length > 3 ? tag.substring(0, 2) + '...' : tag;
  };

  // 根据屏幕尺寸选择缩略函数
  const getTruncatedTag = (tag: string) => {
    return isSmallScreen ? truncateTagSmall(tag) : truncateTag(tag);
  };

  // 默认主图标 - 复用KnowledgeGroup的设计
  const defaultMainIcon = <FolderOutlined style={{ fontSize: 48, color: '#1890ff', marginRight: '16px' }} />;
  const finalIcon = icon || defaultMainIcon;

  return (
    <Card
      hoverable
      className={`base-card ${className}`}
      style={{
        backgroundColor: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        ...style
      }}
      actions={actions.length > 0 ? actions : undefined}
      onDoubleClick={() => onDoubleClick?.(id)}
    >
      {/* 标题部分 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {finalIcon}
        <Tooltip title={title} placement="top">
          <Title
            level={4}
            style={{ 
              margin: 0, 
              fontSize: '18px', 
              fontWeight: 'bold',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              flex: 1,
              minWidth: 0 // 确保flex子元素可以收缩
            }}
          >
            {title}
          </Title>
        </Tooltip>
      </div>

      {/* 描述部分 */}
      <Tooltip title={content} placement="top">
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: false,
          }}
          style={{
            marginTop: '8px',
            marginBottom: '12px',
            height: '44px', // 固定高度：2行文本 * 22px行高
            lineHeight: '22px',
            overflow: 'hidden',
            color: '#6b7280',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {content || '暂无描述'}
        </Paragraph>
      </Tooltip>

      {/* 文件数量和标签 */}
      <div className="basecard-stats"
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          marginTop: '16px',
          minHeight: '32px', // 固定高度确保一致性
        }}
      >
        <div className="basecard-count">
          <FileOutlined style={{ marginRight: '6px', fontSize: '14px' }} />
          <span>{count} {countLabel}</span>
        </div>

        {/* 标签区域 - 移动到数量下方 */}
        <div className="basecard-tags"
          style={{ 
            display: 'flex', 
            gap: '4px', 
            alignItems: 'flex-start',
            minHeight: '20px', // 标签区域固定最小高度
            width: '100%', // 固定宽度
            overflow: 'hidden' // 隐藏溢出内容
          }}
          ref={tagsContainerRef} // 添加ref到容器
        >
          {tags.length > 0 ? (
            <>
              {displayedTags.map((tag, index) => (
                <Tooltip key={index} title={tag} placement="top">
                  <Tag color={getTagColor(index)}>
                    {getTruncatedTag(tag)}
                  </Tag>
                </Tooltip>
              ))}
              {remainingCount > 0 && (
                <Tooltip
                  title={
                    <div>
                      {remainingTags.map((tag, index) => (
                        <Tag key={index} style={{ marginBottom: '2px' }}>
                          {getTruncatedTag(tag)}
                        </Tag>
                      ))}
                    </div>
                  }
                  placement="top"
                >
                  <span style={{ fontSize: '12px', color: '#999', cursor: 'pointer', fontWeight: 'bold' }}>
                    ...
                  </span>
                </Tooltip>
              )}
            </>
          ) : (
            <Tag color="default" style={{ fontSize: '12px', color: '#999' }}>
              暂无标签
            </Tag>
          )}
        </div>
      </div>

      {/* 分隔线 */}
      {(creator || createTime) && (
        <div style={{ borderTop: '1px solid #f0f0f0', marginTop: '6px' }} />
      )}

      {/* 底部信息 */}
      {(creator || createTime) && (
        <div className="basecard-footer" style={{ marginTop: '6px' }}>
          {creator && (
            <div className="basecard-user" style={{ width: '120px', minWidth: '120px' }}>
              <Avatar
                src={creatorAvatar}
                size="small"
                style={{ backgroundColor: '#1890ff' }}
              >
                {creator.charAt(0)}
              </Avatar>
              <Tooltip title={creator} placement="top">
                <span className="basecard-username">{creator}</span>
              </Tooltip>
            </div>
          )}
          {createTime && (
            <div className="basecard-time">
              <ClockCircleOutlined style={{ marginRight: '4px' }} /> 
              创建于 {createTime}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default BaseCard; 