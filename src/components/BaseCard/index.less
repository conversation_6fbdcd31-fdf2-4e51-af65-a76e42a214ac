.base-card {
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  height: 100%; // 确保卡片填满容器高度
  display: flex;
  flex-direction: column;
  
  .ant-card-body {
    padding: 16px;
    flex: 1; // 让卡片主体填充剩余空间
    display: flex;
    flex-direction: column;
  }

  .ant-card-actions {
    border-top: 1px solid #f0f0f0;
    
    .ant-card-actions > li {
      margin: 8px 0;
    }

    .anticon {
      font-size: 16px;
      color: #666;
      transition: color 0.3s ease;
      
      &:hover {
        color: #1890ff;
      }
    }
  }

  &:hover {
    border-color: #1890ff !important;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  // 标题样式
  .ant-typography h4 {
    color: #262626;
    font-weight: 600;
  }

  // 描述文本样式
  .ant-typography {
    margin-bottom: 0;
  }

  // 标签样式优化
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    margin: 0;
  }
}

// 卡片网格布局样式
.card-grid {
  .ant-list-grid .ant-col > .ant-list-item {
    margin-bottom: 16px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .base-card {
    .ant-card-body {
      padding: 12px;
    }
  }
}

.basecard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px 12px;
  min-width: 0;

  .basecard-user {
    display: flex;
    align-items: center;
    min-width: 0;
    width: 120px;
    min-width: 120px;
    overflow: hidden;
    
    .basecard-username {
      margin-left: 6px;
      max-width: 80px; // 120px - 头像宽度 - 间距
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      color: #333;
      display: inline-block;
      vertical-align: middle;
      flex: 1; // 占据剩余空间
      min-width: 0; // 允许收缩
    }
  }
  .basecard-time {
    font-size: 13px;
    color: #888;
    white-space: nowrap;
    margin-top: 2px;
  }
}

@media (max-width: 480px) {
  .basecard-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px 0;
    .basecard-time {
      margin-top: 2px;
    }
  }
}

.basecard-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;

  .basecard-count {
    display: flex;
    align-items: center;
    min-width: 0;
    span {
      max-width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      color: #666;
    }
  }

  .basecard-tags {
    display: flex;
    gap: 4px;
    align-items: flex-start;
    flex-wrap: nowrap; // 强制不换行
    min-height: 20px;
    width: 100%;
    overflow: hidden; // 隐藏溢出内容
    
    .ant-tag {
      max-width: 80px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
      font-size: 12px;
      padding: 2px 6px;
      flex-shrink: 0; // 防止标签被压缩
    }
  }
}

@media (max-width: 1100px) {
  .basecard-stats {
    gap: 6px;
    
    .basecard-count {
      span {
        max-width: 120px;
      }
    }
    
    .basecard-tags {
      width: 100%;
      flex-wrap: nowrap; // 强制不换行
      overflow: hidden; // 隐藏溢出内容
      
      .ant-tag {
        max-width: 50px; // 更小的最大宽度
        min-width: 30px; // 设置最小宽度
        font-size: 11px;
        padding: 1px 4px;
        flex-shrink: 0; // 防止标签被压缩
      }
    }
  }
} 