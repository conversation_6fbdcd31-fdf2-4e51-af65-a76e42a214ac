import React from 'react';
import { Card, Typography } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import './index.less';

const { Text } = Typography;

export interface AddCardProps {
  onClick?: () => void;
  text?: string;
  className?: string;
}

const AddCard: React.FC<AddCardProps> = ({
  onClick,
  text = '新增分组',
  className = '',
}) => {
  return (
    <Card 
      className={`material-card add-card ${className}`} 
      hoverable 
      onClick={onClick}
    >
      <div className="add-content">
        <PlusOutlined className="add-icon" />
        <Text className="add-text">{text}</Text>
      </div>
    </Card>
  );
};

export default AddCard; 