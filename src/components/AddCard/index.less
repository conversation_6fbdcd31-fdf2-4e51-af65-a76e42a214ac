// 新增卡片特有样式
.material-card.add-card {
  border: 2px dashed #d9d9d9;
  background: #fafafa;
  cursor: pointer;
  
  &:hover {
    border-color: @primary-color;
    background: #f0f8ff;
  }
  
  .ant-card-body {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .add-content {
    text-align: center;
    
    .add-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
      transition: all 0.3s;
    }
    
    .add-text {
      font-size: 16px;
      color: #8c8c8c;
      font-weight: 500;
      transition: all 0.3s;
    }
  }
  
  &:hover {
    .add-icon {
      color: @primary-color;
    }
    
    .add-text {
      color: @primary-color;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .material-card.add-card {
    // 高度继承自material-card
  }
}

@media (max-width: 768px) {
  .material-card.add-card {
    // 高度继承自material-card
    
    .add-content {
      .add-icon {
        font-size: 40px;
      }
      
      .add-text {
        font-size: 14px;
      }
    }
  }
} 