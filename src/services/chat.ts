import { request } from '@umijs/max';

// 聊天相关API接口

// 会话管理接口
export interface CreateSessionParams {
  user_id: string;
  title?: string;
  component_config?: any;
}

export interface UpdateSessionParams {
  title?: string;
  component_config?: any;
  session_metadata?: string;
}

export interface SessionInfo {
  session_id: string;
  title: string;
  status: number;
  message_count: number;
  artifact_count: number;
  last_message_time: string;
  created_at: string;
  updated_at: string;
  preview: string;
}

export interface SessionDetail {
  session_id: string;
  user_id: string;
  title: string;
  status: number;
  component_config: string;
  message_count: number;
  artifact_count: number;
  last_message_time: string;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  session_metadata: string;
  token_stats: any;
}

export interface MessageInfo {
  message_id: string;
  session_id: string;
  // parent_message_id?: string;
  // content: string;
  // token_count: number;
  // model_info: string;
  // usage_info: string;
  // message_metadata: string;
  // artifact_count: number;
  // artifacts: ArtifactInfo[];
  // status: number;
  // created_at: string;
  // updated_at: string;


  role_type: string;
  role_entity: RoleEntity;
  task_id: string;
  span_id: string;
  parent_span_id: string;
  parent_chunk_id: string;
  chunk_id: string;
  chunk_type: string;
  chunk_sub_type: string;
  content: string;
  runtime_params: string;

}

export interface RoleEntity {
  user_id: string;
  user_name: string;
  component_id: string;
  component_name: string;
  version: string;
  version_status: string;
}

export interface ArtifactInfo {
  artifact_id: string;
  type: string;
  title: string;
  status: number;
}

// 产物相关接口
export interface ArtifactDetail {
  id: number;
  artifact_id: string;
  session_id: string;
  message_id: string;
  type: string;
  title: string;
  description: string;
  content: string;
  file_path: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  file_hash: string;
  thumbnail_path: string;
  tags: string;
  status: number;
  download_count: number;
  expires_at: string;
  is_deleted: boolean;
  gmt_create: string;
  gmt_modified: string;
  artifact_metadata: string;
}

export interface SearchParams {
  keyword?: string;
  artifact_type?: string;
  session_id?: string;
  tags?: string[];
  start_date?: string;
  end_date?: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SessionListResponse {
  sessions: SessionInfo[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface MessageListResponse {
  session_id: string;
  session_title: string;
  messages: MessageInfo[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface ArtifactListResponse {
  artifacts: ArtifactDetail[];
  total_count: number;
  limit: number;
  offset: number;
}

// 会话管理API
export async function createSession(params: CreateSessionParams) {
  return request<ApiResponse<{ session_id: string; title: string; status: number; created_at: string }>>('/chat/sessions', {
    method: 'POST',
    data: params,
  });
}

export async function getUserSessions(
  user_id: string,
  params?: {
    status?: number;
    limit?: number;
    offset?: number;
  }
) {
  return request<ApiResponse<SessionListResponse>>(`/chat/sessions/${user_id}`, {
    method: 'GET',
    params,
  });
}

export async function getSessionDetail(session_id: string) {
  return request<ApiResponse<SessionDetail>>(`/chat/sessions/detail/${session_id}`, {
    method: 'GET',
  });
}

export async function updateSession(session_id: string, params: UpdateSessionParams) {
  return request<ApiResponse<{ session_id: string; title: string; updated_at: string }>>(`/chat/sessions/${session_id}`, {
    method: 'PUT',
    data: params,
  });
}

export async function deleteSession(session_id: string) {
  return request<ApiResponse<{ message: string }>>(`/chat/sessions/${session_id}`, {
    method: 'DELETE',
  });
}

export async function archiveSession(session_id: string) {
  return request<ApiResponse<{ message: string }>>(`/chat/sessions/${session_id}/archive`, {
    method: 'POST',
  });
}

export async function getSessionMessages(
  session_id: string,
  params?: {
    limit?: number;
    offset?: number;
  }
) {
  return request<ApiResponse<MessageListResponse>>(`/chat/sessions/${session_id}/messages`, {
    method: 'GET',
    params,
  });
}

export async function searchSessions(
  user_id: string,
  searchParams: SearchParams,
  params?: {
    limit?: number;
    offset?: number;
  }
) {
  return request<ApiResponse<SessionListResponse>>(`/chat/sessions/${user_id}/search`, {
    method: 'POST',
    data: searchParams,
    params,
  });
}



// 产物管理API
export async function getArtifactList(params?: {
  session_id?: string;
  artifact_type?: string;
  status?: number;
  limit?: number;
  offset?: number;
}) {
  return request<ApiResponse<ArtifactListResponse>>('/chat/artifacts', {
    method: 'GET',
    params,
  });
}

export async function getArtifactDetail(artifact_id: string) {
  return request<ApiResponse<ArtifactDetail>>(`/chat/artifacts/${artifact_id}`, {
    method: 'GET',
  });
}

export async function downloadArtifact(artifact_id: string) {
  return request(`/chat/artifacts/${artifact_id}/download`, {
    method: 'GET',
    responseType: 'blob',
  });
}

export async function searchArtifacts(
  searchParams: SearchParams,
  params?: {
    limit?: number;
    offset?: number;
  }
) {
  return request<ApiResponse<ArtifactListResponse>>('/chat/artifacts/search', {
    method: 'POST',
    data: searchParams,
    params,
  });
}

export async function getSessionArtifactStats(session_id: string) {
  return request<ApiResponse<any>>(`/chat/artifacts/stats/${session_id}`, {
    method: 'GET',
  });
}

export async function deleteArtifact(artifact_id: string) {
  return request<ApiResponse<{ message: string }>>(`/chat/artifacts/${artifact_id}`, {
    method: 'DELETE',
  });
}

// 健康检查
export async function healthCheck() {
  return request<{ status: string; service: string; timestamp: string }>('/chat/health', {
    method: 'GET',
  });
}
