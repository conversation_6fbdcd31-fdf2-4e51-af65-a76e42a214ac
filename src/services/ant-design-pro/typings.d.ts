// @ts-ignore
/* eslint-disable */

declare namespace API {
  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
  
  // ------------------------- 自定义 --------------------------
  type CompanyList = {
    success: boolean;
    error_msg: string;
    error_code: string;
    data: Company[];
  }

  type Company = {
    cid: string;
    name: string;
    abbr: string;
    logo: string;
    industry: string;
  }

  type GetCompanyByCidResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: {
      cid: string;
      name: string;
      abbr: string;
      logo: string;
      industry: string;
    };
  }

  type LoginParams = {
    username?: string;
    telephone?: string;
    password: string;
    email?: string;
    autoLogin?: boolean;
  };

  type LoginResult = {
    success?: boolean;
    error_msg: string;
    error_code: number;
    data: string;
    cid: string;
    token: string;
    uid: string;
    username:string;
};


  type CommonResponse = {
    success: boolean;
    error_msg: string;
    error_code: number;
  }

  type DataResponse = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: any;
  }
  // 知识组
  type GroupAddResponse = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: any;
    group_id:string;
  }
  type KnowledgeGroupResponse = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: any;
    groups:KnowledgeGroup[];
  }

  type KnowledgeGroupQueryResponse = {
    success: boolean;
    error_code: number | null;
    error_msg: string | null;
    data: KnowledgeGroupItem[];
  }

  type KnowledgeGroupItem = {
    success: boolean;
    error_code: number | null;
    error_msg: string | null;
    data: any;
    group_id: string;
    group_name: string;
    group_desc: string;
    group_type: string;
    cid: string;
    gmt_create: string;
    gmt_modified: string;
    create_user: string | null;
    num: number;
    tags: string[];
  }

  // 查询知识组下知识列表的响应类型
  type GroupKnowledgeResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: GroupKnowledgeItem[];
  }

  // 查询知识详情的响应类型
  type KnowledgeDetailResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: GroupKnowledgeItem | GroupKnowledgeItem[]; // 支持单个对象或数组
  }

  // 知识组下的知识项
  type GroupKnowledgeItem = {
    id: number; // 知识的数据库唯一UID
    knowledge_id: string; // 知识的身份ID
    name: string;
    desc: string;
    tags: string[];
    group_id: string;
    longtime_valid: boolean;
    valid_date?: string;
    invalid_date?: string;
    content_type: string;
    schema_id?: string;
    table_data?: TableData[];
    document_content?: string;
    status: string;
    version: number;
    gmt_create: string;
    gmt_modified: string;
    create_uid: string;
    create_name: string;
  }

  // 表格数据类型
  type TableData = {
    cols: TableColumn[];
  }

  type TableColumn = {
    col_name: string;
    col_value: string;
  }

  type KnowledgeGroup = {
    group_id: string;
    group_name: string;
    group_desc: string;
    group_type: string;
    cid: string;
    gmt_create: string;
    gmt_modified: string;
  }

  type Scheme = {
    cid:string;
    config:any;
    desc:string;
    name:string;
    scheme_id:string;
    type:string;
  }

  // 素材组
  type MaterialGroupListByCIDParams = {
    cid: string;
  }

  type MaterialGroupListByCID ={
    success: boolean;
    error_msg: string;
    error_code: number;
    data: any;
    groups: MaterialGroupList[];
  }

  type MaterialGroupListByPlatformParams = {
    platform: string;
    cid: string;
  }

  type MaterialGroupListByPlatform = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: MaterialGroup[];
  }

  type MaterialGroupList = {
    sub_type: string;
    groups: MaterialGroup[];
  }

  type MaterialGroup = {
    group_id: string;
    group_name: string;
    group_desc: string;
    sub_group_type: string;
    cid: string;
    rel_resource_id: string;
    rel_resource_type: string;
    username: string;
    gmt_create: string;
    resource_count: string;
  }

  // 素材
  type MaterialByGIDParams = {
    group_id: string;
  }
  
  type MaterialByGID = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: Material[];
  }

  type MaterialByMID = {
    success: boolean;
    error_msg: string;
    error_code: number;
    data: Material;
  }


  type Material = {
    material_id: string;
    name: string;
    type: string;
    content_digest: string;
    group_id: string;
    tags: string[];
    valid_date: string;
    invalid_date: string;
    longtime_valid: boolean;
    material_content: string;
    source: string?;
    oss_url: string?;
    oss_preview_url?: string;
    status: string;
    uid: string;
    user_name: string;
    gmt_create: string;
    gmt_modified: string;
  }

  // 创建知识的请求参数
  type CreateKnowledgeParams = {
    uid: string;
    cid: string;
    user_name: string;
    name: string;
    desc: string;
    tags: string[];
    group_id: string;
    longtime_valid: boolean;
    valid_date?: string;
    invalid_date?: string;
    content_type: string;
    schema_id?: string;
    table_data?: TableData[];
    document_content?: string;
  }

  // 修改知识的请求参数
  type UpdateKnowledgeParams = {
    uid: string;
    cid: string;
    user_name: string;
    name: string;
    desc: string;
    tags: string[];
    group_id: string;
    longtime_valid: boolean;
    valid_date?: string;
    invalid_date?: string;
    content_type: string;
    schema_id?: string;
    table_data?: TableData[];
    document_content?: string;
    knowledge_id: string;
  }

  // 批量删除知识接口参数类型
  type BatchDeleteKnowledgeParams = {
    uid: string;
    cid: string;
    user_name: string;
    del_ids: number[];
  }

  // 批量上线知识参数
  type BatchOnlineKnowledgeParams = {
    uid: string;
    cid: string;
    user_name: string;
    ids: number[];
  }

  // 创建表结构的请求参数
  type CreateSchemaParams = {
    uid: string;
    cid: string;
    user_name: string;
    type: string;
    name: string;
    desc: string;
    config: SchemaColumnConfig[];
  }

  // 表结构列配置
  type SchemaColumnConfig = {
    name: string;
    desc: string;
    is_entity: boolean;
    entity_name?: string;
  }

  // 创建表结构的响应
  type CreateSchemaResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: string;
  }

  // 查询表结构的请求参数
  type QuerySchemaByCidParams = {
    uid: string;
    cid: string;
    user_name: string;
  }

  // 表结构配置项
  type SchemaConfigItem = {
    col_name: string;
    desc: string | null;
    is_entity: boolean;
    entity_type: string | null;
  }

  // 表结构项
  type SchemaItem = {
    schema_id: string;
    type: string;
    name: string;
    desc: string | null;
    config: SchemaConfigItem[];
    cid: string;
  }

  // 查询表结构的响应
  type QuerySchemaByCidResponse = {
    success: boolean;
    error_code: string | null;
    error_msg: string | null;
    data: SchemaItem[];
  }

  // 删除表格结构的参数类型
  type DeleteSchemaParams = {
    uid: string;
    cid: string;
    user_name: string;
    schema_id: string;
  }

  // 删除表格结构的响应类型
  type DeleteSchemaResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: string;
  }

  // 修改表格结构的参数类型
  type UpdateSchemaParams = {
    uid: string;
    cid: string;
    user_name: string;
    type: string;
    name: string;
    desc: string;
    config: SchemaColumnConfig[];
    schema_id: string;
  }

  // 修改表格结构的响应类型
  type UpdateSchemaResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: string;
  }

  // 查询在线媒体素材响应类型
  type QueryOnlineMediaMaterialsResponse = {
    success: boolean;
    error_code: string;
    error_msg: string;
    data: ImageLibrary;
  }

  // 素材库类型定义（与前端的 ImageLibrary 接口保持一致）
  type ImageLibrary = {
    [category: string]: ImageItem[];
  }

  type ImageItem = {
    material_id: string;
    name: string;
    url: string;
    type?: 'IMAGE' | 'VIDEO';
  }
}
