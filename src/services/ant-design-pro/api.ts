// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取当前的用户 GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }) {
  return request<{
    data: API.CurrentUser;
  }>('/api/currentUser', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 退出登录接口 POST /api/login/outLogin */
export async function outLogin(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/login/outLogin', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/notices */
export async function getNotices(options?: { [key: string]: any }) {
  return request<API.NoticeIconList>('/api/notices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取规则列表 GET /api/rule */
export async function rule(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.RuleList>('/api/rule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>('/api/rule', {
    method: 'POST',
    data: {
      method: 'update',
      ...(options || {}),
    }
  });
}

/** 新建规则 POST /api/rule */
export async function addRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>('/api/rule', {
    method: 'POST',
    data: {
      method: 'post',
      ...(options || {}),
    }
  });
}

/** 删除规则 DELETE /api/rule */
export async function removeRule(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/rule', {
    method: 'POST',
    data: {
      method: 'delete',
      ...(options || {}),
    }
  });
}

// -------------------- 自定义接口 -------------------
// ------------------ 知识库接口 -------------------
export async function getKnowledgeGroup(options: {uid: string, cid: string, user_name: string}) {
  return request<API.KnowledgeGroupQueryResponse>('/group/query', {
    method: 'POST',
    data: {
      uid: options.uid,
      cid: options.cid,
      user_name: options.user_name
    }
  });
}

export async function updateKnowledgeGroup(options: {
  uid: string;
  cid: string;
  user_name: string;
  group_id: string;
  group_name: string;
  group_desc: string;
  tags: string[];
}) {
  return request<API.CommonResponse>('/group/update', {
    method: 'POST',
    data: {
      uid: options.uid,
      cid: options.cid,
      user_name: options.user_name,
      group_id: options.group_id,
      group_name: options.group_name,
      group_desc: options.group_desc,
      tags: options.tags || []
    }
  });
}

export async function deleteKnowledgeGroup(options: {uid: string, cid: string, user_name: string, group_id: string}) {
  return request<API.CommonResponse>('/group/delete', {
    method: 'POST',
    data: {
      uid: options.uid,
      cid: options.cid,
      user_name: options.user_name,
      group_id: options.group_id
    }
  });
}

export async function addKnowledgeGroup(options?: { [key: string]: any }) {
  console.log('创建知识分组:', options, options || {})
  return request<API.GroupAddResponse>('/group/add', {
    method: 'POST',
    data: {
      ...(options || {})
    }
  });
}

export async function getKnowledgeList(gid: string) {
  return request<API.DataResponse>(`/knowledge/queryKnowledgeByGroupId/${gid}`, {
    method: 'GET'
  });
}

export async function getSchemeList(cid: string) {
  return request<API.DataResponse>(`/knowledge/schema/querySchemeByCid/${cid}`, {
    method: 'GET'
  });
}

export async function getSchemeDetail(schema_id: string) {
  return request<API.DataResponse>(`/knowledge/schema/querySchemaBySchemaId/${schema_id}`, {
    method: 'GET'
  });
}

export async function addKnowledge(options?: { [key: string]: any }) {
  return request<API.DataResponse>('/knowledge/createKnowledge', {
    method: 'POST',
    data: {
      ...(options || {})
    }
  });
}

export async function getKnowledgeDetail(kid: string) {
  return request<API.DataResponse>(`/knowledge/queryMaxVersionKnowledgeByKid/${kid}`, {
    method: 'GET'
  });
}

export async function updateKnowledge(kid: string, options?: { [key: string]: any }) {
  return request<API.DataResponse>(`/knowledge/updateKnowledge/${kid}`, {
    method: 'POST',
    data: {
      ...(options || {})
    }
  });
}

export async function deleteKnowledge(kid: string) {
  return request<API.DataResponse>(`/knowledge/deleteKnowledgeById/${kid}`, {
    method: 'DELETE'
  });
}

export async function onlineKnowledge(kid: string) {
  return request<API.DataResponse>(`/knowledge/onlineKnowledgeById/${kid}`, {
    method: 'PUT'
  });
}

export async function offlineKnowledge(kid: string) {
  return request<API.DataResponse>(`/knowledge/offlineKnowledgeById/${kid}`, {
    method: 'PUT'
  });
}

// 查询知识组下的知识列表
export async function getGroupKnowledge(options: {
  uid: string;
  cid: string;
  user_name: string;
  group_id: string;
}) {
  console.log('🔵 getGroupKnowledge 接口入参:', options);
  
  const response = await request<API.GroupKnowledgeResponse>('/knowledge/query_group_knowledge', {
    method: 'POST',
    data: {
      uid: options.uid,
      cid: options.cid,
      user_name: options.user_name,
      group_id: options.group_id
    }
  });
  
  console.log('🔵 getGroupKnowledge 接口出参:', response);
  
  return response;
}

// 查询知识详情（根据knowledge_id）
export async function getKnowledgeDetailByKnowledgeId(options: {
  uid: string;
  cid: string;
  user_name: string;
  knowledge_id: string;
}) {
  console.log('🔵 getKnowledgeDetailByKnowledgeId 接口入参:', options);
  
  const response = await request<API.KnowledgeDetailResponse>('/knowledge/query_knowledge_detail', {
    method: 'POST',
    data: {
      uid: options.uid,
      cid: options.cid,
      user_name: options.user_name,
      knowledge_id: options.knowledge_id
    }
  });
  
  console.log('🔵 getKnowledgeDetailByKnowledgeId 接口出参:', response);
  
  return response;
}

// 创建知识
export async function createKnowledge(options: API.CreateKnowledgeParams) {
  return request<API.CommonResponse>('/knowledge/create_knowledge', {
    method: 'POST',
    data: options
  });
}

// 修改知识
export async function updateKnowledgeNew(options: API.UpdateKnowledgeParams) {
  console.log('🔵 updateKnowledgeNew 接口入参:', options);
  const response = await request<API.CommonResponse>('/knowledge/update_knowledge', {
    method: 'POST',
    data: options
  });
  console.log('🔵 updateKnowledgeNew 接口出参:', response);
  return response;
}

// 批量删除知识
export async function batchDeleteKnowledge(options: API.BatchDeleteKnowledgeParams) {
  console.log('🔴 batchDeleteKnowledge 接口入参:', options);
  const response = await request<API.CommonResponse>('/knowledge/batch_del', {
    method: 'POST',
    data: options
  });
  console.log('🔴 batchDeleteKnowledge 接口响应:', response);
  return response;
}

// 批量上线知识
export async function batchOnlineKnowledge(options: API.BatchOnlineKnowledgeParams) {
  console.log('🟢 batchOnlineKnowledge 接口入参:', options);
  const response = await request<API.CommonResponse>('/knowledge/batch_online', {
    method: 'POST',
    data: options
  });
  console.log('🟢 batchOnlineKnowledge 接口响应:', response);
  return response;
}

// 创建表结构
export async function createSchema(options: API.CreateSchemaParams) {
  console.log('🟡 createSchema 接口入参:', options);
  const response = await request<API.CreateSchemaResponse>('/knowledge/schema/create', {
    method: 'POST',
    data: options
  });
  console.log('🟡 createSchema 接口响应:', response);
  return response;
}

// 根据CID查询表结构列表
export async function querySchemaByCid(options: API.QuerySchemaByCidParams) {
  console.log('🔵 querySchemaByCid 接口入参:', options);
  const response = await request<API.QuerySchemaByCidResponse>('/knowledge/schema/query_by_cid', {
    method: 'POST',
    data: options
  });
  console.log('🔵 querySchemaByCid 接口响应:', response);
  return response;
}

// 删除表格结构
export async function deleteSchema(options: API.DeleteSchemaParams) {
  console.log('🔴 deleteSchema 接口入参:', options);
  const response = await request<API.DeleteSchemaResponse>('/knowledge/schema/delete', {
    method: 'POST',
    data: options
  });
  console.log('🔴 deleteSchema 接口响应:', response);
  return response;
}

// 修改表格结构
export async function updateSchema(options: API.UpdateSchemaParams) {
  console.log('🟠 updateSchema 接口入参:', options);
  const response = await request<API.UpdateSchemaResponse>('/knowledge/schema/update', {
    method: 'POST',
    data: options
  });
  console.log('🟠 updateSchema 接口响应:', response);
  return response;
}

// ------------------ 素材库接口 -------------------
// ------------------ 素材组 -------------------
// 获取素材分类列表 GET /material/queryallmaterialgroups
export async function getMaterialGroup(cid: string) {
  return request<API.MaterialGroupListByCID>('/material/queryallmaterialgroups', {
    method: 'GET',
    params: {
      cid: cid
    }
  });
}

export async function addMaterialGroup(options?: { [key: string]: any }) {
  return request<API.GroupAddResponse>('/material/addgroup', {
    method: 'POST',
    data: {
      ...(options || {})
    }
  });
}

export async function deleteMaterialGroup(gid: string) {
  return request<API.CommonResponse>(`/material/delgroup`, {
    method: 'POST',
    params: {
      group_id: gid
    }
  });
}

export async function updateMaterialGroup(gid: string, cid: string, options?: { [key: string]: any }) {
  return request<API.CommonResponse>('/material/modifygroup', {
    method: 'POST',
    params: {
      group_id: gid,
      cid
    },
    data: {
      ...(options || {})
    }
  });
}


/** 通过group_id获取素材列表 GET /material/getmaterialbygroup */
export async function getMaterialByGroup(options?: { [key: string]: any }) {
  return request<API.MaterialByGID>('/material/getmaterialbygroup', {
    method: 'GET',
    params: {
      ...(options || {})
    }
  });
}

// ------------------ 素材 -------------------
/** 通过material_id获取素材信息 GET /material/getmaterialbyid */
export async function getMaterialByID(options?: { [key: string]: any }) {
  return request<API.MaterialByMID>('/material/getmaterialbyid', {
    method: 'GET',
    params: {
      ...(options || {})
    }
  });
}

/** 通过material_id删除素材 POST /material/delmaterial */
export async function delMaterial(options?: { [key: string]: any }) {
  console.log('options', options, options || {})
  return request<API.CommonResponse>('/material/delmaterial', {
    method: 'POST',
    params: {
      ...(options || {})
    }
  });
}





/** 素材上线 POST /material/onlineMaterial */
export async function onlineMaterial(options?: { [key: string]: any }) {
  console.log('options', options, options || {})
  return request<API.CommonResponse>('/material/onlineMaterial', {
    method: 'POST',
    params: {
      ...(options || {})
    }
  });
}

/** 素材下线 POST /material/lineMaterial */
export async function offlineMaterial(options?: { [key: string]: any }){
  console.log('options', options, options || {})
  return request<API.CommonResponse>('/material/offlineMaterial', {
    method: 'POST',
    params:{
      ...(options || {})
      }
  });
}




// ------------------ 登录/注册 -------------------
// 获取公司列表 GET /auth/getCompany
export async function getCompanyList() {
  return request<API.CompanyList>('/auth/getCompany', {
    method: 'GET'
  });
}

// 根据cid获取企业信息 GET /auth/getCompanyByCid
export async function getCompanyByCid(cid: string) {
  return request<API.GetCompanyByCidResponse>('/auth/getCompanyByCid', {
    method: 'GET',
    params: {
      cid
    }
  });
}
// 登录 POST /auth/login
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

// 注册 POST /auth/signup
export async function register(options?: { [key: string]: any }) {
  return request<API.CommonResponse>('/auth/signup', {
    method: 'POST',
    data: {
      ...(options || {})
    }
  });
}

// ------------------ 素材批量操作接口 -------------------
/** 批量删除素材 POST /material/batchDelMaterial */
export async function batchDelMaterial(options: { mids: string[] }) {
  console.log('批量删除素材:', options);
  return request<API.CommonResponse>('/material/batchDelMaterial', {
    method: 'POST',
    data: {
      mids: options.mids
    }
  });
}

/** 批量上线素材 POST /material/batchOnlineMaterial */
export async function batchOnlineMaterial(options: { mids: string[] }) {
  console.log('批量上线素材:', options);
  return request<API.CommonResponse>('/material/batchOnlineMaterial', {
    method: 'POST',
    data: {
      mids: options.mids
    }
  });
}

/** 批量下线素材 POST /material/batchOfflineMaterial */
export async function batchOfflineMaterial(options: { mids: string[] }) {
  console.log('批量下线素材:', options);
  return request<API.CommonResponse>('/material/batchOfflineMaterial', {
    method: 'POST',
    data: {
      mids: options.mids
    }
  });
}

/** 查询在线媒体素材 GET /material/queryOnlineMediaMaterials */
export async function queryOnlineMediaMaterials(options: { cid: string }) {
  console.log('查询在线媒体素材:', options);
  return request<API.QueryOnlineMediaMaterialsResponse>('/material/queryOnlineMediaMaterials', {
    method: 'GET',
    params: {
      cid: options.cid
    }
  });
}

// ------------------ 新的素材API -------------------
/** 新增素材 POST /material/add_material */
export async function addMaterial(options: any) {
  console.log('新增素材:', options);
  return request<{
    success: boolean;
    error_code?: string;
    error_msg?: string;
    data?: string;
  }>('/material/add_material', {
    method: 'POST',
    data: options,
  });
}

/** 修改素材 POST /material/modify_material */
export async function modifyMaterialNew(options: any) {
  console.log('修改素材:', options);
  return request<{
    success: boolean;
    error_code?: string;
    error_msg?: string;
    data?: string;
  }>('/material/modify_material', {
    method: 'POST',
    data: options,
  });
}