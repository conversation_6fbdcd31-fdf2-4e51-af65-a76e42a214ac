# 基础镜像 Ubuntu 24.10
FROM ubuntu:24.10
LABEL authors="mindshake"

# 初始化基础目录
RUN mkdir -p /home/<USER>/mindshake && mkdir -p /home/<USER>/mindshake/graph_data

# 设置时区UTC+8
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装编译工具和依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    g++ \
    gcc \
    python3-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 进入项目目录
WORKDIR /home/<USER>/mindshake

# 从uv镜像中复制工具并更新环境变量
COPY --from=ghcr.io/astral-sh/uv:0.6.14 /uv /uvx /home/<USER>/bin/
ENV PATH="/home/<USER>/bin/:$PATH"
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

# 分层构建,先同步安装依赖包
COPY ../pyproject.toml ../uv.lock ./
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project

# 复制源码,安装项目
COPY ../ ./
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen

# 设置项目源文件目录
ENV PYTHONPATH=.

# web应用启动并监听8000端口
EXPOSE 8000
CMD ["uv", "run", "uvicorn", "robot_studio.main:app", "--host", "0.0.0.0", "--port", "8000"]



