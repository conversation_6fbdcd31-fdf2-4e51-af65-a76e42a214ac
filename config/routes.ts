export default [
  {
    path: '/user',
    layout: false,
    routes: [
      { name: '登录', path: '/user/login', component: './User/Login' },
      { name: '注册', path: '/user/register', component: './User/Register' },
    ],
  },
  // { path: '/welcome', name: '欢迎', icon: 'smile', component: './Welcome' },
  // {
  //   path: '/admin',
  //   name: '管理页',
  //   icon: 'crown',
  //   access: 'canAdmin',
  //   routes: [
  //     { path: '/admin', redirect: '/admin/sub-page' },
  //     { path: '/admin/sub-page', name: '二级管理页', component: './Admin' },
  //   ],
  // },
  {
    name: '数据资产', icon: 'database', path: '/database', routes: [
              { path: '/database', redirect: '/database/knowledge' },
        { path: '/database/knowledge', name: '知识库', component: './Knowledge/KnowledgeGroup' },
        { path: '/database/knowledge/manage/:groupId', component: './Knowledge/KnowledgeManage' },
        { path: '/database/knowledge/detail/:mode/:id', component: './Knowledge/KnowledgeDetail' },
        { path: '/database/material', name: '素材库', component: './Material/MaterialGroup' },
        { path: '/database/material/group/:groupId', component: './Material/MaterialManage' },
    ]
  },
  { path: '/chat', name: '聊天', icon: 'message', component: './Chat' },
  // { name: '查询表格', icon: 'table', path: '/list', component: './TableList' },
  { path: '/', redirect: '/database' },
  { path: '*', layout: false, component: './404' },
];
